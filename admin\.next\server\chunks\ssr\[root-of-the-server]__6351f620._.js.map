{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/Modal/index.tsx"], "sourcesContent": ["import React, { useState, useEffect, ReactNode } from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './modal.css';\n\nexport interface ModalProps {\n  title?: ReactNode;\n  content?: ReactNode;\n  children?: ReactNode;\n  visible?: boolean;\n  width?: number | string;\n  centered?: boolean;\n  closable?: boolean;\n  maskClosable?: boolean;\n  footer?: ReactNode;\n  okText?: string;\n  cancelText?: string;\n  okType?: 'primary' | 'danger';\n  confirmLoading?: boolean;\n  onOk?: () => void | Promise<void>;\n  onCancel?: () => void;\n  afterClose?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n// Modal 组件\nconst Modal: React.FC<ModalProps> = ({\n  title,\n  content,\n  children,\n  visible = false,\n  width = 520,\n  centered = false,\n  closable = true,\n  maskClosable = true,\n  footer,\n  okText = '确定',\n  cancelText = '取消',\n  okType = 'primary',\n  confirmLoading = false,\n  onOk,\n  onCancel,\n  afterClose,\n  className = '',\n  style = {},\n}) => {\n  const [isVisible, setIsVisible] = useState(visible);\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      setIsVisible(true);\n      setIsAnimating(true);\n      document.body.style.overflow = 'hidden';\n    } else {\n      setIsAnimating(false);\n      setTimeout(() => {\n        setIsVisible(false);\n        document.body.style.overflow = '';\n        afterClose?.();\n      }, 300);\n    }\n  }, [visible, afterClose]);\n\n  const handleMaskClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget && maskClosable) {\n      onCancel?.();\n    }\n  };\n\n  const handleOk = async () => {\n    if (onOk) {\n      try {\n        await onOk();\n      } catch (error) {\n        console.error('Modal onOk error:', error);\n      }\n    }\n  };\n\n  const handleCancel = () => {\n    onCancel?.();\n  };\n\n  const renderFooter = () => {\n    if (footer === null) return null;\n\n    if (footer) return footer;\n\n    return (\n      <div className=\"custom-modal-footer\">\n        {cancelText && (\n          <button\n            className=\"custom-modal-btn custom-modal-btn-default\"\n            onClick={handleCancel}\n          >\n            {cancelText}\n          </button>\n        )}\n        <button\n          className={`custom-modal-btn custom-modal-btn-${okType}`}\n          onClick={handleOk}\n          disabled={confirmLoading}\n        >\n          {confirmLoading && <span className=\"custom-modal-loading\">⟳</span>}\n          {okText}\n        </button>\n      </div>\n    );\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className={`custom-modal-mask ${isAnimating ? 'custom-modal-mask-show' : 'custom-modal-mask-hide'}`}\n      onClick={handleMaskClick}\n    >\n      <div className={`custom-modal-wrap ${centered ? 'custom-modal-centered' : ''}`}>\n        <div\n          className={`custom-modal ${className} ${isAnimating ? 'custom-modal-show' : 'custom-modal-hide'}`}\n          style={{ width, ...style }}\n        >\n          {(title || closable) && (\n            <div className=\"custom-modal-header\">\n              {title && <div className=\"custom-modal-title\">{title}</div>}\n              {closable && (\n                <button\n                  className=\"custom-modal-close\"\n                  onClick={handleCancel}\n                  aria-label=\"Close\"\n                >\n                  ×\n                </button>\n              )}\n            </div>\n          )}\n          \n          <div className=\"custom-modal-body\">\n            {content || children}\n          </div>\n          \n          {renderFooter()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// 确认对话框配置\nexport interface ConfirmConfig {\n  title?: ReactNode;\n  content?: ReactNode;\n  okText?: string;\n  cancelText?: string;\n  okType?: 'primary' | 'danger';\n  onOk?: () => void | Promise<void>;\n  onCancel?: () => void;\n  width?: number | string;\n  centered?: boolean;\n  maskClosable?: boolean;\n}\n\n// 确认对话框管理器\nclass ConfirmManager {\n  private container: HTMLDivElement | null = null;\n  private root: any = null;\n\n  private getContainer() {\n    if (!this.container) {\n      this.container = document.createElement('div');\n      this.container.className = 'custom-modal-container';\n      document.body.appendChild(this.container);\n      this.root = createRoot(this.container);\n    }\n    return this.container;\n  }\n\n  confirm(config: ConfirmConfig) {\n    return new Promise<void>((resolve, reject) => {\n      let isResolved = false;\n\n      const handleOk = async () => {\n        if (isResolved) return;\n        \n        try {\n          if (config.onOk) {\n            await config.onOk();\n          }\n          isResolved = true;\n          this.destroy();\n          resolve();\n        } catch (error) {\n          reject(error);\n        }\n      };\n\n      const handleCancel = () => {\n        if (isResolved) return;\n        \n        isResolved = true;\n        config.onCancel?.();\n        this.destroy();\n        reject(new Error('User cancelled'));\n      };\n\n      this.getContainer();\n      this.root.render(\n        <Modal\n          visible={true}\n          title={config.title}\n          content={config.content}\n          okText={config.okText}\n          cancelText={config.cancelText}\n          okType={config.okType}\n          width={config.width}\n          centered={config.centered}\n          maskClosable={config.maskClosable}\n          onOk={handleOk}\n          onCancel={handleCancel}\n          afterClose={() => this.destroy()}\n        />\n      );\n    });\n  }\n\n  destroy() {\n    if (this.container && document.body.contains(this.container)) {\n      document.body.removeChild(this.container);\n      this.container = null;\n      this.root = null;\n    }\n  }\n}\n\n// 创建带有静态方法的Modal组件\nconst ModalWithStatic = Modal as typeof Modal & {\n  confirm: (config: ConfirmConfig) => Promise<void>;\n  info: (config: ConfirmConfig) => Promise<void>;\n  success: (config: ConfirmConfig) => Promise<void>;\n  error: (config: ConfirmConfig) => Promise<void>;\n  warning: (config: ConfirmConfig) => Promise<void>;\n};\n\n// 静态方法\nModalWithStatic.confirm = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: config.okType || 'primary'\n  });\n};\n\nModalWithStatic.info = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined, // info模式通常只有确定按钮\n  });\n};\n\nModalWithStatic.success = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined,\n  });\n};\n\nModalWithStatic.error = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'danger',\n    cancelText: undefined,\n  });\n};\n\nModalWithStatic.warning = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined,\n  });\n};\n\nexport default ModalWithStatic;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAwBA,WAAW;AACX,MAAM,QAA8B,CAAC,EACnC,KAAK,EACL,OAAO,EACP,QAAQ,EACR,UAAU,KAAK,EACf,QAAQ,GAAG,EACX,WAAW,KAAK,EAChB,WAAW,IAAI,EACf,eAAe,IAAI,EACnB,MAAM,EACN,SAAS,IAAI,EACb,aAAa,IAAI,EACjB,SAAS,SAAS,EAClB,iBAAiB,KAAK,EACtB,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACX;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,aAAa;YACb,eAAe;YACf,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,eAAe;YACf,WAAW;gBACT,aAAa;gBACb,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B;YACF,GAAG;QACL;IACF,GAAG;QAAC;QAAS;KAAW;IAExB,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,cAAc;YAChD;QACF;IACF;IAEA,MAAM,WAAW;QACf,IAAI,MAAM;YACR,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC;QACF;IACF;IAEA,MAAM,eAAe;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,MAAM,OAAO;QAE5B,IAAI,QAAQ,OAAO;QAEnB,qBACE,8OAAC;YAAI,WAAU;;gBACZ,4BACC,8OAAC;oBACC,WAAU;oBACV,SAAS;8BAER;;;;;;8BAGL,8OAAC;oBACC,WAAW,CAAC,kCAAkC,EAAE,QAAQ;oBACxD,SAAS;oBACT,UAAU;;wBAET,gCAAkB,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;wBACzD;;;;;;;;;;;;;IAIT;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAC,kBAAkB,EAAE,cAAc,2BAA2B,0BAA0B;QACnG,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW,0BAA0B,IAAI;sBAC5E,cAAA,8OAAC;gBACC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE,cAAc,sBAAsB,qBAAqB;gBACjG,OAAO;oBAAE;oBAAO,GAAG,KAAK;gBAAC;;oBAExB,CAAC,SAAS,QAAQ,mBACjB,8OAAC;wBAAI,WAAU;;4BACZ,uBAAS,8OAAC;gCAAI,WAAU;0CAAsB;;;;;;4BAC9C,0BACC,8OAAC;gCACC,WAAU;gCACV,SAAS;gCACT,cAAW;0CACZ;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;kCACZ,WAAW;;;;;;oBAGb;;;;;;;;;;;;;;;;;AAKX;AAgBA,WAAW;AACX,MAAM;IACI,YAAmC,KAAK;IACxC,OAAY,KAAK;IAEjB,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,SAAS,aAAa,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,SAAS;QACvC;QACA,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,QAAQ,MAAqB,EAAE;QAC7B,OAAO,IAAI,QAAc,CAAC,SAAS;YACjC,IAAI,aAAa;YAEjB,MAAM,WAAW;gBACf,IAAI,YAAY;gBAEhB,IAAI;oBACF,IAAI,OAAO,IAAI,EAAE;wBACf,MAAM,OAAO,IAAI;oBACnB;oBACA,aAAa;oBACb,IAAI,CAAC,OAAO;oBACZ;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT;YACF;YAEA,MAAM,eAAe;gBACnB,IAAI,YAAY;gBAEhB,aAAa;gBACb,OAAO,QAAQ;gBACf,IAAI,CAAC,OAAO;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,IAAI,CAAC,MAAM,eACd,8OAAC;gBACC,SAAS;gBACT,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,YAAY,OAAO,UAAU;gBAC7B,QAAQ,OAAO,MAAM;gBACrB,OAAO,OAAO,KAAK;gBACnB,UAAU,OAAO,QAAQ;gBACzB,cAAc,OAAO,YAAY;gBACjC,MAAM;gBACN,UAAU;gBACV,YAAY,IAAM,IAAI,CAAC,OAAO;;;;;;QAGpC;IACF;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG;YAC5D,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,IAAI,GAAG;QACd;IACF;AACF;AAEA,mBAAmB;AACnB,MAAM,kBAAkB;AAQxB,OAAO;AACP,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ,OAAO,MAAM,IAAI;IAC3B;AACF;AAEA,gBAAgB,IAAI,GAAG,CAAC;IACtB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,KAAK,GAAG,CAAC;IACvB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/Message/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './message.css';\n\nexport interface MessageConfig {\n  content: string;\n  duration?: number;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  key?: string;\n}\n\ninterface MessageItem extends MessageConfig {\n  id: string;\n  visible: boolean;\n}\n\n// 消息容器组件\nconst MessageContainer: React.FC<{ messages: MessageItem[] }> = ({ messages }) => {\n  return (\n    <div className=\"custom-message-container\">\n      {messages.map((message) => (\n        <div\n          key={message.id}\n          className={`custom-message custom-message-${message.type} ${\n            message.visible ? 'custom-message-show' : 'custom-message-hide'\n          }`}\n        >\n          <div className=\"custom-message-icon\">\n            {message.type === 'success' && '✓'}\n            {message.type === 'error' && '✕'}\n            {message.type === 'warning' && '⚠'}\n            {message.type === 'info' && 'ℹ'}\n          </div>\n          <span className=\"custom-message-content\">{message.content}</span>\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// 消息管理器\nclass MessageManager {\n  private messages: MessageItem[] = [];\n  private container: HTMLDivElement | null = null;\n  private root: any = null;\n\n  private getContainer() {\n    if (!this.container) {\n      this.container = document.createElement('div');\n      this.container.className = 'custom-message-wrapper';\n      document.body.appendChild(this.container);\n      this.root = createRoot(this.container);\n    }\n    return this.container;\n  }\n\n  private render() {\n    if (this.root) {\n      this.root.render(<MessageContainer messages={this.messages} />);\n    }\n  }\n\n  private generateId() {\n    return `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  show(config: MessageConfig) {\n    const id = config.key || this.generateId();\n    const duration = config.duration ?? 3000;\n    \n    // 如果已存在相同key的消息，先移除\n    if (config.key) {\n      this.messages = this.messages.filter(msg => msg.id !== config.key);\n    }\n\n    const messageItem: MessageItem = {\n      ...config,\n      id,\n      visible: true,\n    };\n\n    this.messages.push(messageItem);\n    this.getContainer();\n    this.render();\n\n    // 自动移除\n    if (duration > 0) {\n      setTimeout(() => {\n        this.hide(id);\n      }, duration);\n    }\n\n    return id;\n  }\n\n  hide(id: string) {\n    const messageIndex = this.messages.findIndex(msg => msg.id === id);\n    if (messageIndex > -1) {\n      this.messages[messageIndex].visible = false;\n      this.render();\n      \n      // 动画结束后移除\n      setTimeout(() => {\n        this.messages = this.messages.filter(msg => msg.id !== id);\n        this.render();\n        \n        // 如果没有消息了，清理容器\n        if (this.messages.length === 0 && this.container) {\n          document.body.removeChild(this.container);\n          this.container = null;\n          this.root = null;\n        }\n      }, 300);\n    }\n  }\n\n  destroy() {\n    this.messages = [];\n    if (this.container) {\n      document.body.removeChild(this.container);\n      this.container = null;\n      this.root = null;\n    }\n  }\n}\n\n// 全局消息管理器实例\nconst messageManager = new MessageManager();\n\n// 导出的API\nexport const message = {\n  success: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'success', duration }),\n  \n  error: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'error', duration }),\n  \n  warning: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'warning', duration }),\n  \n  info: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'info', duration }),\n  \n  destroy: () => messageManager.destroy(),\n};\n\nexport default message;\n"], "names": [], "mappings": ";;;;;AACA;;;;AAeA,SAAS;AACT,MAAM,mBAA0D,CAAC,EAAE,QAAQ,EAAE;IAC3E,qBACE,8OAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gBAEC,WAAW,CAAC,8BAA8B,EAAE,QAAQ,IAAI,CAAC,CAAC,EACxD,QAAQ,OAAO,GAAG,wBAAwB,uBAC1C;;kCAEF,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,IAAI,KAAK,aAAa;4BAC9B,QAAQ,IAAI,KAAK,WAAW;4BAC5B,QAAQ,IAAI,KAAK,aAAa;4BAC9B,QAAQ,IAAI,KAAK,UAAU;;;;;;;kCAE9B,8OAAC;wBAAK,WAAU;kCAA0B,QAAQ,OAAO;;;;;;;eAXpD,QAAQ,EAAE;;;;;;;;;;AAgBzB;AAEA,QAAQ;AACR,MAAM;IACI,WAA0B,EAAE,CAAC;IAC7B,YAAmC,KAAK;IACxC,OAAY,KAAK;IAEjB,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,SAAS,aAAa,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,SAAS;QACvC;QACA,OAAO,IAAI,CAAC,SAAS;IACvB;IAEQ,SAAS;QACf,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,eAAC,8OAAC;gBAAiB,UAAU,IAAI,CAAC,QAAQ;;;;;;QAC5D;IACF;IAEQ,aAAa;QACnB,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC3E;IAEA,KAAK,MAAqB,EAAE;QAC1B,MAAM,KAAK,OAAO,GAAG,IAAI,IAAI,CAAC,UAAU;QACxC,MAAM,WAAW,OAAO,QAAQ,IAAI;QAEpC,oBAAoB;QACpB,IAAI,OAAO,GAAG,EAAE;YACd,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,GAAG;QACnE;QAEA,MAAM,cAA2B;YAC/B,GAAG,MAAM;YACT;YACA,SAAS;QACX;QAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,MAAM;QAEX,OAAO;QACP,IAAI,WAAW,GAAG;YAChB,WAAW;gBACT,IAAI,CAAC,IAAI,CAAC;YACZ,GAAG;QACL;QAEA,OAAO;IACT;IAEA,KAAK,EAAU,EAAE;QACf,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/D,IAAI,eAAe,CAAC,GAAG;YACrB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,GAAG;YACtC,IAAI,CAAC,MAAM;YAEX,UAAU;YACV,WAAW;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBACvD,IAAI,CAAC,MAAM;gBAEX,eAAe;gBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;oBAChD,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;oBACxC,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,IAAI,GAAG;gBACd;YACF,GAAG;QACL;IACF;IAEA,UAAU;QACR,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,IAAI,GAAG;QACd;IACF;AACF;AAEA,YAAY;AACZ,MAAM,iBAAiB,IAAI;AAGpB,MAAM,UAAU;IACrB,SAAS,CAAC,SAAiB,WACzB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAW;QAAS;IAE3D,OAAO,CAAC,SAAiB,WACvB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAS;QAAS;IAEzD,SAAS,CAAC,SAAiB,WACzB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAW;QAAS;IAE3D,MAAM,CAAC,SAAiB,WACtB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAQ;QAAS;IAExD,SAAS,IAAM,eAAe,OAAO;AACvC;uCAEe", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/index.ts"], "sourcesContent": ["// 自定义组件统一导出\nexport { default as Modal } from './Modal';\nexport { default as message } from './Message';\n\n// 类型导出\nexport type { ModalProps, ConfirmConfig } from './Modal';\nexport type { MessageConfig } from './Message';\n"], "names": [], "mappings": "AAAA,YAAY;;AACZ;AACA", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/config/api.ts"], "sourcesContent": ["// API配置\r\nexport const API_CONFIG = {\r\n  // 基础URL - 可以根据环境变量动态设置\r\n  BASE_URL:\r\n    (typeof window !== \"undefined\"\r\n      ? process.env.NEXT_PUBLIC_API_BASE_URL\r\n      : null) || \"http://localhost:18891\",\r\n\r\n  // 超时时间\r\n  TIMEOUT: 10000,\r\n\r\n  // API版本前缀（注意：当前使用/admin/前缀，此配置保留用于兼容性）\r\n  API_PREFIX: \"/api/v1/admin\",\r\n\r\n  // 完整的API基础URL\r\n  get FULL_BASE_URL() {\r\n    return `${this.BASE_URL}${this.API_PREFIX}`;\r\n  },\r\n};\r\n\r\n// 环境配置\r\nexport const ENV_CONFIG = {\r\n  isDevelopment: process.env.NODE_ENV === \"development\",\r\n  isProduction: process.env.NODE_ENV === \"production\",\r\n  isTest: process.env.NODE_ENV === \"test\",\r\n};\r\n"], "names": [], "mappings": "AAAA,QAAQ;;;;;AACD,MAAM,aAAa;IACxB,uBAAuB;IACvB,UACE,CAAC,6EAEG,IAAI,KAAK;IAEf,OAAO;IACP,SAAS;IAET,uCAAuC;IACvC,YAAY;IAEZ,cAAc;IACd,IAAI,iBAAgB;QAClB,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE;IAC7C;AACF;AAGO,MAAM,aAAa;IACxB,eAAe,oDAAyB;IACxC,cAAc,oDAAyB;IACvC,QAAQ,oDAAyB;AACnC", "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/request.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from \"axios\";\r\nimport { message } from \"@/components\";\r\nimport { API_CONFIG } from \"@/config/api\";\r\n\r\n// 扩展AxiosRequestConfig以支持错误提示控制\r\ninterface CustomAxiosRequestConfig extends AxiosRequestConfig {\r\n  showError?: boolean; // 是否显示错误提示，默认为true\r\n  showSuccess?: boolean; // 是否显示成功提示，默认为false\r\n  successMessage?: string; // 自定义成功提示信息\r\n}\r\n\r\n// 创建axios实例\r\nconst request: AxiosInstance = axios.create({\r\n  baseURL: API_CONFIG.FULL_BASE_URL,\r\n  timeout: API_CONFIG.TIMEOUT,\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n});\r\n\r\n// 请求拦截器\r\nrequest.interceptors.request.use(\r\n  (config) => {\r\n    // 从localStorage获取token并添加到请求头\r\n    const token = localStorage.getItem(\"admin_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // 添加详细的请求日志\r\n    console.log(\"🚀 发送请求:\", {\r\n      method: config.method?.toUpperCase(),\r\n      url: config.url,\r\n      baseURL: config.baseURL,\r\n      fullURL: `${config.baseURL}${config.url}`,\r\n      data: config.data,\r\n      headers: config.headers,\r\n    });\r\n\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error(\"请求拦截器错误:\", error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 响应拦截器\r\nrequest.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    const config = response.config as CustomAxiosRequestConfig;\r\n\r\n    // 添加响应日志\r\n    console.log(\"✅ 请求成功:\", {\r\n      method: config.method?.toUpperCase(),\r\n      url: config.url,\r\n      status: response.status,\r\n      statusText: response.statusText,\r\n      data: response.data,\r\n    });\r\n\r\n    // 处理成功提示\r\n    if (config.showSuccess && config.successMessage) {\r\n      message.success(config.successMessage);\r\n    }\r\n\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error(\"❌ 请求失败:\", {\r\n      method: error.config?.method?.toUpperCase(),\r\n      url: error.config?.url,\r\n      baseURL: error.config?.baseURL,\r\n      fullURL: `${error.config?.baseURL}${error.config?.url}`,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n    });\r\n\r\n    const config = error.config as CustomAxiosRequestConfig;\r\n    const showError = config?.showError !== false; // 默认显示错误\r\n\r\n    if (!showError) {\r\n      return Promise.reject(error);\r\n    }\r\n\r\n    // 处理常见错误\r\n    if (error.response) {\r\n      const { status, data } = error.response;\r\n      let errorMessage = \"\";\r\n\r\n      switch (status) {\r\n        case 401:\r\n          errorMessage = \"登录已过期，请重新登录\";\r\n          localStorage.removeItem(\"admin_token\");\r\n          // 可以在这里添加跳转到登录页的逻辑\r\n          window.location.href = \"/login\";\r\n          break;\r\n        case 403:\r\n          errorMessage = data?.message || \"没有权限访问该资源\";\r\n          break;\r\n        case 404:\r\n          errorMessage = data?.message || \"请求的资源不存在\";\r\n          break;\r\n        case 422:\r\n          errorMessage = data?.message || \"请求参数验证失败\";\r\n          break;\r\n        case 500:\r\n          errorMessage = data?.message || \"服务器内部错误\";\r\n          break;\r\n        default:\r\n          errorMessage = data?.message || `请求失败 (${status})`;\r\n      }\r\n\r\n      message.error(errorMessage);\r\n    } else if (error.request) {\r\n      message.error(\"网络连接失败，请检查网络\");\r\n    } else {\r\n      message.error(\"请求配置错误\");\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 封装常用的HTTP方法\r\nexport const api = {\r\n  // GET请求\r\n  get: <T = any>(\r\n    url: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.get(url, config);\r\n  },\r\n\r\n  // POST请求\r\n  post: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.post(url, data, config);\r\n  },\r\n\r\n  // PUT请求\r\n  put: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.put(url, data, config);\r\n  },\r\n\r\n  // PATCH请求\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.patch(url, data, config);\r\n  },\r\n\r\n  // DELETE请求\r\n  delete: <T = any>(\r\n    url: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.delete(url, config);\r\n  },\r\n};\r\n\r\n// 便捷方法：不显示错误提示的请求\r\nexport const silentApi = {\r\n  get: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>\r\n    api.get<T>(url, { ...config, showError: false }),\r\n\r\n  post: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>\r\n    api.post<T>(url, data, { ...config, showError: false }),\r\n\r\n  put: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>\r\n    api.put<T>(url, data, { ...config, showError: false }),\r\n\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ) => api.patch<T>(url, data, { ...config, showError: false }),\r\n\r\n  delete: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>\r\n    api.delete<T>(url, { ...config, showError: false }),\r\n};\r\n\r\n// 便捷方法：带成功提示的请求\r\nexport const successApi = {\r\n  post: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.post<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"操作成功\",\r\n    }),\r\n\r\n  put: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.put<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"更新成功\",\r\n    }),\r\n\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.patch<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"更新成功\",\r\n    }),\r\n\r\n  delete: <T = any>(\r\n    url: string,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.delete<T>(url, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"删除成功\",\r\n    }),\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AACA;;;;AASA,YAAY;AACZ,MAAM,UAAyB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1C,SAAS,oHAAA,CAAA,aAAU,CAAC,aAAa;IACjC,SAAS,oHAAA,CAAA,aAAU,CAAC,OAAO;IAC3B,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,QAAQ;AACR,QAAQ,YAAY,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC;IACC,8BAA8B;IAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,YAAY;IACZ,QAAQ,GAAG,CAAC,YAAY;QACtB,QAAQ,OAAO,MAAM,EAAE;QACvB,KAAK,OAAO,GAAG;QACf,SAAS,OAAO,OAAO;QACvB,SAAS,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;QACzC,MAAM,OAAO,IAAI;QACjB,SAAS,OAAO,OAAO;IACzB;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,YAAY;IAC1B,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,QAAQ;AACR,QAAQ,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC/B,CAAC;IACC,MAAM,SAAS,SAAS,MAAM;IAE9B,SAAS;IACT,QAAQ,GAAG,CAAC,WAAW;QACrB,QAAQ,OAAO,MAAM,EAAE;QACvB,KAAK,OAAO,GAAG;QACf,QAAQ,SAAS,MAAM;QACvB,YAAY,SAAS,UAAU;QAC/B,MAAM,SAAS,IAAI;IACrB;IAEA,SAAS;IACT,IAAI,OAAO,WAAW,IAAI,OAAO,cAAc,EAAE;QAC/C,4KAAA,CAAA,UAAO,CAAC,OAAO,CAAC,OAAO,cAAc;IACvC;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,WAAW;QACvB,QAAQ,MAAM,MAAM,EAAE,QAAQ;QAC9B,KAAK,MAAM,MAAM,EAAE;QACnB,SAAS,MAAM,MAAM,EAAE;QACvB,SAAS,GAAG,MAAM,MAAM,EAAE,UAAU,MAAM,MAAM,EAAE,KAAK;QACvD,QAAQ,MAAM,QAAQ,EAAE;QACxB,YAAY,MAAM,QAAQ,EAAE;QAC5B,MAAM,MAAM,QAAQ,EAAE;QACtB,SAAS,MAAM,OAAO;IACxB;IAEA,MAAM,SAAS,MAAM,MAAM;IAC3B,MAAM,YAAY,QAAQ,cAAc,OAAO,SAAS;IAExD,IAAI,CAAC,WAAW;QACd,OAAO,QAAQ,MAAM,CAAC;IACxB;IAEA,SAAS;IACT,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ;QACvC,IAAI,eAAe;QAEnB,OAAQ;YACN,KAAK;gBACH,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,mBAAmB;gBACnB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF;gBACE,eAAe,MAAM,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtD;QAEA,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB,OAAO;QACL,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,MAAM;IACjB,QAAQ;IACR,KAAK,CACH,KACA;QAEA,OAAO,QAAQ,GAAG,CAAC,KAAK;IAC1B;IAEA,SAAS;IACT,MAAM,CACJ,KACA,MACA;QAEA,OAAO,QAAQ,IAAI,CAAC,KAAK,MAAM;IACjC;IAEA,QAAQ;IACR,KAAK,CACH,KACA,MACA;QAEA,OAAO,QAAQ,GAAG,CAAC,KAAK,MAAM;IAChC;IAEA,UAAU;IACV,OAAO,CACL,KACA,MACA;QAEA,OAAO,QAAQ,KAAK,CAAC,KAAK,MAAM;IAClC;IAEA,WAAW;IACX,QAAQ,CACN,KACA;QAEA,OAAO,QAAQ,MAAM,CAAC,KAAK;IAC7B;AACF;AAGO,MAAM,YAAY;IACvB,KAAK,CAAU,KAAa,SAC1B,IAAI,GAAG,CAAI,KAAK;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEhD,MAAM,CAAU,KAAa,MAAY,SACvC,IAAI,IAAI,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEvD,KAAK,CAAU,KAAa,MAAY,SACtC,IAAI,GAAG,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEtD,OAAO,CACL,KACA,MACA,SACG,IAAI,KAAK,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAE3D,QAAQ,CAAU,KAAa,SAC7B,IAAI,MAAM,CAAI,KAAK;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;AACrD;AAGO,MAAM,aAAa;IACxB,MAAM,CACJ,KACA,MACA,gBACA,SAEA,IAAI,IAAI,CAAI,KAAK,MAAM;YACrB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,KAAK,CACH,KACA,MACA,gBACA,SAEA,IAAI,GAAG,CAAI,KAAK,MAAM;YACpB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,OAAO,CACL,KACA,MACA,gBACA,SAEA,IAAI,KAAK,CAAI,KAAK,MAAM;YACtB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,QAAQ,CACN,KACA,gBACA,SAEA,IAAI,MAAM,CAAI,KAAK;YACjB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/authService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 登录请求参数类型\r\nexport interface LoginParams {\r\n  username: string;\r\n  password: string;\r\n}\r\n\r\n// 登录响应类型\r\nexport interface LoginResponse {\r\n  message: string;\r\n  accessToken: string;\r\n}\r\n\r\n// 认证服务\r\nexport const authService = {\r\n  // 登录\r\n  login: async (params: LoginParams): Promise<LoginResponse> => {\r\n    const response = await request.post<LoginResponse>('/auth/login', params);\r\n    return response.data;\r\n  },\r\n\r\n  // 登出\r\n  logout: () => {\r\n    localStorage.removeItem('admin_token');\r\n    window.location.href = '/login';\r\n  },\r\n\r\n  // 获取当前token\r\n  getToken: (): string | null => {\r\n    return localStorage.getItem('admin_token');\r\n  },\r\n\r\n  // 设置token\r\n  setToken: (token: string): void => {\r\n    localStorage.setItem('admin_token', token);\r\n  },\r\n\r\n  // 检查是否已登录\r\n  isLoggedIn: (): boolean => {\r\n    return !!localStorage.getItem('admin_token');\r\n  },\r\n};\r\n\r\n// 保持向后兼容的导出\r\nexport const login = authService.login;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAeO,MAAM,cAAc;IACzB,KAAK;IACL,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAgB,eAAe;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,KAAK;IACL,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,YAAY;IACZ,UAAU;QACR,OAAO,aAAa,OAAO,CAAC;IAC9B;IAEA,UAAU;IACV,UAAU,CAAC;QACT,aAAa,OAAO,CAAC,eAAe;IACtC;IAEA,UAAU;IACV,YAAY;QACV,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;AACF;AAGO,MAAM,QAAQ,YAAY,KAAK", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/LoginPage.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"container\": \"LoginPage-module__dp1EYa__container\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/app/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { Form, Input, Button, Card, Typography, message } from 'antd';\r\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\r\nimport { useRouter } from 'next/navigation';\r\nimport { authService, LoginParams } from '@/services/authService';\r\nimport styles from '@/styles/LoginPage.module.css';\r\n\r\nconst LoginPage: React.FC = () => {\r\n  const router = useRouter();\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleSubmit = async (values: LoginParams) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await authService.login(values);\r\n\r\n      if (response.accessToken) {\r\n        message.success('登录成功！');\r\n        authService.setToken(response.accessToken);\r\n        router.push('/dashboard'); // 跳转到主控面板\r\n      } else {\r\n        message.error(response.message || '登录失败，请检查用户名或密码！');\r\n      }\r\n    } catch (error: any) {\r\n      console.error('登录失败:', error);\r\n      // 错误已经在request拦截器中处理了，这里只需要记录日志\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <Card title={<Typography.Title level={3} style={{ textAlign: 'center', marginBottom: 0 }}>后台登录</Typography.Title>} style={{ width: 400 }}>\r\n        <Form name=\"admin_login\" initialValues={{ remember: true }} onFinish={handleSubmit}>\r\n          <Form.Item\r\n            name=\"username\"\r\n            rules={[{ required: true, message: '请输入用户名!' }]}\r\n          >\r\n            <Input prefix={<UserOutlined />} placeholder=\"用户名 (例如: admin)\" />\r\n          </Form.Item>\r\n          <Form.Item\r\n            name=\"password\"\r\n            rules={[{ required: true, message: '请输入密码!' }]}\r\n          >\r\n            <Input.Password prefix={<LockOutlined />} placeholder=\"密码 (例如: password123)\" />\r\n          </Form.Item>\r\n          <Form.Item>\r\n            <Button type=\"primary\" htmlType=\"submit\" style={{ width: '100%' }} loading={loading}>\r\n              登录\r\n            </Button>\r\n          </Form.Item>\r\n        </Form>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,YAAsB;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,cAAW,CAAC,KAAK,CAAC;YAEzC,IAAI,SAAS,WAAW,EAAE;gBACxB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,8HAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,SAAS,WAAW;gBACzC,OAAO,IAAI,CAAC,eAAe,UAAU;YACvC,OAAO;gBACL,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,SAAS;QACvB,gCAAgC;QAClC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;kBAC9B,cAAA,8OAAC,8KAAA,CAAA,OAAI;YAAC,qBAAO,8OAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gBAAC,OAAO;gBAAG,OAAO;oBAAE,WAAW;oBAAU,cAAc;gBAAE;0BAAG;;;;;;YAAyB,OAAO;gBAAE,OAAO;YAAI;sBACrI,cAAA,8OAAC,8KAAA,CAAA,OAAI;gBAAC,MAAK;gBAAc,eAAe;oBAAE,UAAU;gBAAK;gBAAG,UAAU;;kCACpE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAO;4BAAC;gCAAE,UAAU;gCAAM,SAAS;4BAAU;yBAAE;kCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;4BAAC,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;4BAAK,aAAY;;;;;;;;;;;kCAE/C,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAO;4BAAC;gCAAE,UAAU;gCAAM,SAAS;4BAAS;yBAAE;kCAE9C,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;4BAAC,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;4BAAK,aAAY;;;;;;;;;;;kCAExD,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;kCACR,cAAA,8OAAC,kMAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,UAAS;4BAAS,OAAO;gCAAE,OAAO;4BAAO;4BAAG,SAAS;sCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjG;uCAEe", "debugId": null}}]}