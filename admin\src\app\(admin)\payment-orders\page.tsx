'use client';

import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Tag, message, Card, Row, Col, Input, Select, DatePicker, Tooltip } from 'antd';
import { ReloadOutlined, EyeOutlined, SearchOutlined, DollarOutlined, ShoppingCartOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { paymentOrderService } from '../../../services';
import dayjs, { Dayjs } from 'dayjs';

const { Search } = Input;
const { RangePicker } = DatePicker;

// 安全的数值格式化函数
const safeNumber = (value: any, defaultValue: number = 0): number => {
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
};

// 安全的金额格式化函数（分转元）
const safeAmount = (value: any, defaultValue: number = 0): number => {
  const num = safeNumber(value, defaultValue);
  return num / 100;
};

// 支付订单数据类型
interface PaymentOrder {
  id: string;
  userId: string;
  openid: string;
  out_trade_no: string;
  transaction_id?: string;
  description: string;
  total: number;
  status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'REFUNDED';
  vip_package_id: string;
  paid_at?: string;
  expires_at: string;
  created_at: string;
}

// 删除订单统计数据类型

const PaymentOrdersPage: React.FC = () => {
  const [orders, setOrders] = useState<PaymentOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<PaymentOrder | null>(null);
  // 删除统计数据状态

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 搜索和筛选状态
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);

  // 获取支付订单列表
  const fetchOrders = async (page = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true);
    try {
      const params: any = {
        page,
        pageSize,
      };
      if (searchText) params.search = searchText;
      if (statusFilter) params.status = statusFilter;
      if (dateRange) {
        params.startDate = dateRange[0].format('YYYY-MM-DD');
        params.endDate = dateRange[1].format('YYYY-MM-DD');
      }

      const result = await paymentOrderService.getList(params);
      setOrders(result.orders);
      setPagination({
        current: result.page,
        pageSize: result.pageSize,
        total: result.total,
      });
    } catch {
      message.error('获取支付订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除统计数据获取函数

  useEffect(() => {
    fetchOrders(1); // 重置到第一页
  }, [searchText, statusFilter, dateRange]);

  useEffect(() => {
    fetchOrders();
  }, []);

  // 查看订单详情
  const handleViewDetail = (order: PaymentOrder) => {
    setSelectedOrder(order);
    setDetailModalVisible(true);
  };

  // 刷新订单列表
  const handleRefreshOrder = async () => {
    try {
      await fetchOrders();
      message.success('订单列表已刷新');
    } catch {
      message.error('刷新订单列表失败');
    }
  };

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      PENDING: { color: 'processing', text: '待支付', icon: <ShoppingCartOutlined /> },
      SUCCESS: { color: 'success', text: '支付成功', icon: <CheckCircleOutlined /> },
      FAILED: { color: 'error', text: '支付失败', icon: <CloseCircleOutlined /> },
      CANCELLED: { color: 'default', text: '已取消', icon: <CloseCircleOutlined /> },
      REFUNDED: { color: 'warning', text: '已退款', icon: <CloseCircleOutlined /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.FAILED;
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '订单信息',
      key: 'orderInfo',
      width: 280,
      render: (_: unknown, record: PaymentOrder) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            {record.description}
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginBottom: 2 }}>
            订单号: {record.out_trade_no}
          </div>
          {record.transaction_id && (
            <div style={{ fontSize: '12px', color: '#666', marginBottom: 2 }}>
              微信订单: {record.transaction_id}
            </div>
          )}
          <div style={{ fontSize: '12px', color: '#999' }}>
            用户ID: {record.userId}
          </div>
        </div>
      ),
    },
    {
      title: '金额',
      key: 'amount',
      width: 120,
      render: (_: unknown, record: PaymentOrder) => (
        <div style={{ textAlign: 'right' }}>
          <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#f50' }}>
            ¥{(record.total / 100).toFixed(2)}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_: unknown, record: PaymentOrder) => renderStatusTag(record.status),
    },
    {
      title: '创建时间',
      key: 'created_at',
      width: 160,
      render: (_: unknown, record: PaymentOrder) => (
        <div>
          <div>{dayjs(record.created_at).format('YYYY-MM-DD')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dayjs(record.created_at).format('HH:mm:ss')}
          </div>
        </div>
      ),
    },
    {
      title: '支付时间',
      key: 'paid_at',
      width: 160,
      render: (_: unknown, record: PaymentOrder) => (
        record.paid_at ? (
          <div>
            <div>{dayjs(record.paid_at).format('YYYY-MM-DD')}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {dayjs(record.paid_at).format('HH:mm:ss')}
            </div>
          </div>
        ) : (
          <span style={{ color: '#999' }}>-</span>
        )
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: unknown, record: PaymentOrder) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            >
              详情
            </Button>
          </Tooltip>
          {record.status === 'PENDING' && (
            <Tooltip title="刷新状态">
              <Button
                type="link"
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => handleRefreshOrder()}
              >
                刷新
              </Button>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          <DollarOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
          支付订单管理
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          查看和管理所有支付订单，监控支付状态和统计数据
        </p>
      </div>

      {/* 删除统计卡片 */}

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索订单号、用户ID或商品名称"
              allowClear
              onSearch={setSearchText}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="订单状态"
              allowClear
              style={{ width: '100%' }}
              value={statusFilter || undefined}
              onChange={setStatusFilter}
            >
              <Select.Option value="PENDING">待支付</Select.Option>
              <Select.Option value="SUCCESS">支付成功</Select.Option>
              <Select.Option value="FAILED">支付失败</Select.Option>
              <Select.Option value="CANCELLED">已取消</Select.Option>
              <Select.Option value="REFUNDED">已退款</Select.Option>
            </Select>
          </Col>
          <Col span={8}>
            <RangePicker
              style={{ width: '100%' }}
              value={dateRange}
              onChange={(dates) => setDateRange(dates as [Dayjs, Dayjs] | null)}
              placeholder={['开始日期', '结束日期']}
            />
          </Col>
          <Col span={4}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchOrders(1)}
              loading={loading}
              style={{ width: '100%' }}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 订单表格 */}
      <Table
        columns={columns}
        dataSource={orders}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 个订单`,
          onChange: (page, pageSize) => {
            fetchOrders(page, pageSize);
          },
          onShowSizeChange: (current, size) => {
            fetchOrders(1, size);
          },
        }}
        scroll={{ x: 1200 }}
      />

      {/* 订单详情模态框 */}
      <Modal
        title="订单详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedOrder && (
          <div>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <div><strong>订单ID:</strong> {selectedOrder.id}</div>
              </Col>
              <Col span={12}>
                <div><strong>状态:</strong> {renderStatusTag(selectedOrder.status)}</div>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <div><strong>商户订单号:</strong> {selectedOrder.out_trade_no}</div>
              </Col>
              <Col span={12}>
                <div><strong>微信订单号:</strong> {selectedOrder.transaction_id || '-'}</div>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <div><strong>用户ID:</strong> {selectedOrder.userId}</div>
              </Col>
              <Col span={12}>
                <div><strong>OpenID:</strong> {selectedOrder.openid}</div>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <div><strong>商品描述:</strong> {selectedOrder.description}</div>
              </Col>
              <Col span={12}>
                <div><strong>VIP套餐ID:</strong> {selectedOrder.vip_package_id}</div>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <div><strong>订单金额:</strong> ¥{(selectedOrder.total / 100).toFixed(2)}</div>
              </Col>
              <Col span={12}>
                <div><strong>过期时间:</strong> {dayjs(selectedOrder.expires_at).format('YYYY-MM-DD HH:mm:ss')}</div>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <div><strong>创建时间:</strong> {dayjs(selectedOrder.created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
              </Col>
              <Col span={12}>
                <div><strong>支付时间:</strong> {selectedOrder.paid_at ? dayjs(selectedOrder.paid_at).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PaymentOrdersPage;
