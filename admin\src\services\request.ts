import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { message } from "@/components";
import { API_CONFIG } from "@/config/api";

// 扩展AxiosRequestConfig以支持错误提示控制
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  showError?: boolean; // 是否显示错误提示，默认为true
  showSuccess?: boolean; // 是否显示成功提示，默认为false
  successMessage?: string; // 自定义成功提示信息
}

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: API_CONFIG.FULL_BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem("admin_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加详细的请求日志
    console.log("🚀 发送请求:", {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      data: config.data,
      headers: config.headers,
    });

    return config;
  },
  (error) => {
    console.error("请求拦截器错误:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const config = response.config as CustomAxiosRequestConfig;

    // 添加响应日志
    console.log("✅ 请求成功:", {
      method: config.method?.toUpperCase(),
      url: config.url,
      status: response.status,
      statusText: response.statusText,
      data: response.data,
    });

    // 处理成功提示
    if (config.showSuccess && config.successMessage) {
      message.success(config.successMessage);
    }

    return response;
  },
  (error) => {
    console.error("❌ 请求失败:", {
      method: error.config?.method?.toUpperCase(),
      url: error.config?.url,
      baseURL: error.config?.baseURL,
      fullURL: `${error.config?.baseURL}${error.config?.url}`,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
    });

    const config = error.config as CustomAxiosRequestConfig;
    const showError = config?.showError !== false; // 默认显示错误

    if (!showError) {
      return Promise.reject(error);
    }

    // 处理常见错误
    if (error.response) {
      const { status, data } = error.response;
      let errorMessage = "";

      switch (status) {
        case 401:
          errorMessage = "登录已过期，请重新登录";
          localStorage.removeItem("admin_token");
          // 可以在这里添加跳转到登录页的逻辑
          window.location.href = "/login";
          break;
        case 403:
          errorMessage = data?.message || "没有权限访问该资源";
          break;
        case 404:
          errorMessage = data?.message || "请求的资源不存在";
          break;
        case 422:
          errorMessage = data?.message || "请求参数验证失败";
          break;
        case 500:
          errorMessage = data?.message || "服务器内部错误";
          break;
        default:
          errorMessage = data?.message || `请求失败 (${status})`;
      }

      message.error(errorMessage);
    } else if (error.request) {
      message.error("网络连接失败，请检查网络");
    } else {
      message.error("请求配置错误");
    }

    return Promise.reject(error);
  }
);

// 封装常用的HTTP方法
export const api = {
  // GET请求
  get: <T = any>(
    url: string,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> => {
    return request.get(url, config);
  },

  // POST请求
  post: <T = any>(
    url: string,
    data?: any,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> => {
    return request.post(url, data, config);
  },

  // PUT请求
  put: <T = any>(
    url: string,
    data?: any,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> => {
    return request.put(url, data, config);
  },

  // PATCH请求
  patch: <T = any>(
    url: string,
    data?: any,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> => {
    return request.patch(url, data, config);
  },

  // DELETE请求
  delete: <T = any>(
    url: string,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> => {
    return request.delete(url, config);
  },
};

// 便捷方法：不显示错误提示的请求
export const silentApi = {
  get: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>
    api.get<T>(url, { ...config, showError: false }),

  post: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>
    api.post<T>(url, data, { ...config, showError: false }),

  put: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>
    api.put<T>(url, data, { ...config, showError: false }),

  patch: <T = any>(
    url: string,
    data?: any,
    config?: CustomAxiosRequestConfig
  ) => api.patch<T>(url, data, { ...config, showError: false }),

  delete: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>
    api.delete<T>(url, { ...config, showError: false }),
};

// 便捷方法：带成功提示的请求
export const successApi = {
  post: <T = any>(
    url: string,
    data?: any,
    successMessage?: string,
    config?: CustomAxiosRequestConfig
  ) =>
    api.post<T>(url, data, {
      ...config,
      showSuccess: true,
      successMessage: successMessage || "操作成功",
    }),

  put: <T = any>(
    url: string,
    data?: any,
    successMessage?: string,
    config?: CustomAxiosRequestConfig
  ) =>
    api.put<T>(url, data, {
      ...config,
      showSuccess: true,
      successMessage: successMessage || "更新成功",
    }),

  patch: <T = any>(
    url: string,
    data?: any,
    successMessage?: string,
    config?: CustomAxiosRequestConfig
  ) =>
    api.patch<T>(url, data, {
      ...config,
      showSuccess: true,
      successMessage: successMessage || "更新成功",
    }),

  delete: <T = any>(
    url: string,
    successMessage?: string,
    config?: CustomAxiosRequestConfig
  ) =>
    api.delete<T>(url, {
      ...config,
      showSuccess: true,
      successMessage: successMessage || "删除成功",
    }),
};

export default api;
