"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8063],{34095:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(79630),c=n(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 310H732.4c13.6-21.4 21.6-46.8 21.6-74 0-76.1-61.9-138-138-138-41.4 0-78.7 18.4-104 47.4-25.3-29-62.6-47.4-104-47.4-76.1 0-138 61.9-138 138 0 27.2 7.9 52.6 21.6 74H144c-17.7 0-32 14.3-32 32v200c0 4.4 3.6 8 8 8h40v344c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V550h40c4.4 0 8-3.6 8-8V342c0-17.7-14.3-32-32-32zm-334-74c0-38.6 31.4-70 70-70s70 31.4 70 70-31.4 70-70 70h-70v-70zm-138-70c38.6 0 70 31.4 70 70v70h-70c-38.6 0-70-31.4-70-70s31.4-70 70-70zM180 482V378h298v104H180zm48 68h250v308H228V550zm568 308H546V550h250v308zm48-376H546V378h298v104z"}}]},name:"gift",theme:"outlined"};var l=n(62764);let o=c.forwardRef(function(e,t){return c.createElement(l.A,(0,a.A)({},e,{ref:t,icon:r}))})},34140:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(79630),c=n(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var l=n(62764);let o=c.forwardRef(function(e,t){return c.createElement(l.A,(0,a.A)({},e,{ref:t,icon:r}))})},44297:(e,t,n)=>{n.d(t,{A:()=>S});var a=n(12115),c=n(11719),r=n(16962),l=n(80163),o=n(29300),s=n.n(o),i=n(40032),u=n(15982),f=n(70802);let m=e=>{let t,{value:n,formatter:c,precision:r,decimalSeparator:l,groupSeparator:o="",prefixCls:s}=e;if("function"==typeof c)t=c(n);else{let e=String(n),c=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(c&&"-"!==e){let e=c[1],n=c[2]||"0",i=c[4]||"";n=n.replace(/\B(?=(\d{3})+(?!\d))/g,o),"number"==typeof r&&(i=i.padEnd(r,"0").slice(0,r>0?r:0)),i&&(i="".concat(l).concat(i)),t=[a.createElement("span",{key:"int",className:"".concat(s,"-content-value-int")},e,n),i&&a.createElement("span",{key:"decimal",className:"".concat(s,"-content-value-decimal")},i)]}else t=e}return a.createElement("span",{className:"".concat(s,"-content-value")},t)};var p=n(18184),d=n(45431),v=n(61388);let g=e=>{let{componentCls:t,marginXXS:n,padding:a,colorTextDescription:c,titleFontSize:r,colorTextHeading:l,contentFontSize:o,fontFamily:s}=e;return{[t]:Object.assign(Object.assign({},(0,p.dF)(e)),{["".concat(t,"-title")]:{marginBottom:n,color:c,fontSize:r},["".concat(t,"-skeleton")]:{paddingTop:a},["".concat(t,"-content")]:{color:l,fontSize:o,fontFamily:s,["".concat(t,"-content-value")]:{display:"inline-block",direction:"ltr"},["".concat(t,"-content-prefix, ").concat(t,"-content-suffix")]:{display:"inline-block"},["".concat(t,"-content-prefix")]:{marginInlineEnd:n},["".concat(t,"-content-suffix")]:{marginInlineStart:n}}})}},h=(0,d.OF)("Statistic",e=>[g((0,v.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:n}=e;return{titleFontSize:n,contentFontSize:t}});var b=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,a=Object.getOwnPropertySymbols(e);c<a.length;c++)0>t.indexOf(a[c])&&Object.prototype.propertyIsEnumerable.call(e,a[c])&&(n[a[c]]=e[a[c]]);return n};let y=a.forwardRef((e,t)=>{let{prefixCls:n,className:c,rootClassName:r,style:l,valueStyle:o,value:p=0,title:d,valueRender:v,prefix:g,suffix:y,loading:O=!1,formatter:E,precision:w,decimalSeparator:x=".",groupSeparator:S=",",onMouseEnter:z,onMouseLeave:A}=e,j=b(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:M,direction:N,className:H,style:k}=(0,u.TP)("statistic"),L=M("statistic",n),[C,R,P]=h(L),B=a.createElement(m,{decimalSeparator:x,groupSeparator:S,prefixCls:L,formatter:E,precision:w,value:p}),V=s()(L,{["".concat(L,"-rtl")]:"rtl"===N},H,c,r,R,P),D=a.useRef(null);a.useImperativeHandle(t,()=>({nativeElement:D.current}));let F=(0,i.A)(j,{aria:!0,data:!0});return C(a.createElement("div",Object.assign({},F,{ref:D,className:V,style:Object.assign(Object.assign({},k),l),onMouseEnter:z,onMouseLeave:A}),d&&a.createElement("div",{className:"".concat(L,"-title")},d),a.createElement(f.A,{paragraph:!1,loading:O,className:"".concat(L,"-skeleton")},a.createElement("div",{style:o,className:"".concat(L,"-content")},g&&a.createElement("span",{className:"".concat(L,"-content-prefix")},g),v?v(B):B,y&&a.createElement("span",{className:"".concat(L,"-content-suffix")},y)))))}),O=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var E=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,a=Object.getOwnPropertySymbols(e);c<a.length;c++)0>t.indexOf(a[c])&&Object.prototype.propertyIsEnumerable.call(e,a[c])&&(n[a[c]]=e[a[c]]);return n};let w=e=>{let{value:t,format:n="HH:mm:ss",onChange:o,onFinish:s,type:i}=e,u=E(e,["value","format","onChange","onFinish","type"]),f="countdown"===i,[m,p]=a.useState(null),d=(0,c._q)(()=>{let e=Date.now(),n=new Date(t).getTime();return p({}),null==o||o(f?n-e:e-n),!f||!(n<e)||(null==s||s(),!1)});return a.useEffect(()=>{let e,t=()=>{e=(0,r.A)(()=>{d()&&t()})};return t(),()=>r.A.cancel(e)},[t,f]),a.useEffect(()=>{p({})},[]),a.createElement(y,Object.assign({},u,{value:t,valueRender:e=>(0,l.Ob)(e,{title:void 0}),formatter:(e,t)=>m?function(e,t,n){let{format:a=""}=t,c=new Date(e).getTime(),r=Date.now();return function(e,t){let n=e,a=/\[[^\]]*]/g,c=(t.match(a)||[]).map(e=>e.slice(1,-1)),r=t.replace(a,"[]"),l=O.reduce((e,t)=>{let[a,c]=t;if(e.includes(a)){let t=Math.floor(n/c);return n-=t*c,e.replace(RegExp("".concat(a,"+"),"g"),e=>{let n=e.length;return t.toString().padStart(n,"0")})}return e},r),o=0;return l.replace(a,()=>{let e=c[o];return o+=1,e})}(n?Math.max(c-r,0):Math.max(r-c,0),a)}(e,Object.assign(Object.assign({},t),{format:n}),f):"-"}))},x=a.memo(e=>a.createElement(w,Object.assign({},e,{type:"countdown"})));y.Timer=w,y.Countdown=x;let S=y},79659:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(79630),c=n(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var l=n(62764);let o=c.forwardRef(function(e,t){return c.createElement(l.A,(0,a.A)({},e,{ref:t,icon:r}))})},97550:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(79630),c=n(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};var l=n(62764);let o=c.forwardRef(function(e,t){return c.createElement(l.A,(0,a.A)({},e,{ref:t,icon:r}))})}}]);