(()=>{var e={};e.id=1771,e.ids=[1771],e.modules={128:(e,t,r)=>{"use strict";r.d(t,{f:()=>a});var s=r(49895);let a={getAll:async e=>(await s.Ay.get("/user-stars",{params:e})).data,exportData:async e=>(await s.Ay.get("/user-stars/export",{params:e,responseType:"blob"})).data};a.getAll},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3404:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(80828),a=r(43210);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};var n=r(21898);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,s.A)({},e,{ref:t,icon:l}))})},5754:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(80828),a=r(43210);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var n=r(21898);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,s.A)({},e,{ref:t,icon:l}))})},7077:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(80828),a=r(43210);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};var n=r(21898);let i=a.forwardRef(function(e,t){return a.createElement(n.A,(0,s.A)({},e,{ref:t,icon:l}))})},10814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15444:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,r)=>{Promise.resolve().then(r.bind(r,10814))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>q});var s=r(60687),a=r(43210),l=r(85814),n=r.n(l),i=r(16189),o=r(98836),d=r(99053),c=r(63736),p=r(56072),u=r(78620);r(15444);var h=r(60203),x=r(81945),m=r(53788),v=r(9242),y=r(3788),f=r(73237),g=r(47453),j=r(31189),A=r(62727),b=r(14723),w=r(72061),k=r(80461),z=r(71103);let{Header:S,Content:C,Sider:P,Footer:I}=o.A,D=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,s.jsx)(h.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,s.jsx)(x.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,s.jsx)(m.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,s.jsx)(v.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,s.jsx)(y.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,s.jsx)(f.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,s.jsx)(g.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,s.jsx)(j.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,s.jsx)(A.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,s.jsx)(b.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,s.jsx)(w.A,{})}];function q({children:e}){let t=(0,i.useRouter)(),r=(0,i.usePathname)(),[l,h]=(0,a.useState)(!1),x=[{key:"logout",icon:(0,s.jsx)(k.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],m=D.find(e=>r.startsWith(e.path))?.key||"dashboard";return(0,s.jsxs)(o.A,{style:{minHeight:"100vh"},children:[(0,s.jsxs)(P,{collapsible:!0,collapsed:l,onCollapse:e=>h(e),children:[(0,s.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(d.A.Text,{style:{color:"white",fontSize:l?"10px":"16px",transition:"font-size 0.2s"},children:l?"后台":"游戏管理后台"})}),(0,s.jsx)(c.A,{theme:"dark",selectedKeys:[m],mode:"inline",items:D.map(e=>({key:e.key,icon:e.icon,label:(0,s.jsx)(n(),{href:e.path,children:e.label})}))})]}),(0,s.jsxs)(o.A,{children:[(0,s.jsxs)(S,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,s.jsx)(p.A,{menu:{items:x},placement:"bottomRight",children:(0,s.jsx)(u.A,{style:{cursor:"pointer"},icon:(0,s.jsx)(z.A,{})})}),(0,s.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,s.jsx)(C,{style:{margin:"16px"},children:e}),(0,s.jsxs)(I,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},49663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\user-stars\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-stars\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59448:(e,t,r)=>{Promise.resolve().then(r.bind(r,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65616:(e,t,r)=>{Promise.resolve().then(r.bind(r,49663))},74075:e=>{"use strict";e.exports=require("zlib")},78905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),n=r.n(l),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["(admin)",{children:["user-stars",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49663)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-stars\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-stars\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(admin)/user-stars/page",pathname:"/user-stars",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84112:(e,t,r)=>{Promise.resolve().then(r.bind(r,88405))},88405:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var s=r(60687),a=r(43210),l=r(70084),n=r(28243),i=r(35899),o=r(48111),d=r(23459),c=r(33519),p=r(77833),u=r(70553),h=r(42585),x=r(96625),m=r(4691),v=r(94733),y=r(27783),f=r(62727),g=r(7077),j=r(3404),A=r(92950),b=r(5754),w=r(128),k=r(16189);let{Option:z}=l.A,{RangePicker:S}=n.A;function C(){let e=(0,k.useRouter)();(0,k.useSearchParams)();let[t,r]=(0,a.useState)([]),[n,C]=(0,a.useState)(!1),[P,I]=(0,a.useState)(null),[D,q]=(0,a.useState)(void 0),[L,_]=(0,a.useState)(void 0),[M,R]=(0,a.useState)(void 0),[T,E]=(0,a.useState)({current:1,pageSize:20,total:0,totalPages:0}),Y=async e=>{C(!0);try{let t=await w.f.getAll({...e,page:e?.page||T.current,pageSize:e?.pageSize||T.pageSize});r(t.data||[]),E({current:t.page,pageSize:t.pageSize,total:t.total,totalPages:t.totalPages})}catch(e){console.error("Error fetching stars:",e),r([{id:"1",userId:"user1",levelId:"level1",stars:5,completedAt:"2024-01-10T10:30:00Z",playTime:120,user:{id:"user1",nickname:"张三",avatar:""},level:{id:"level1",title:"基础词汇练习1",difficulty:1}},{id:"2",userId:"user2",levelId:"level1",stars:3,completedAt:"2024-01-10T11:15:00Z",playTime:180,user:{id:"user2",nickname:"李四",avatar:""},level:{id:"level1",title:"基础词汇练习1",difficulty:1}},{id:"3",userId:"user3",levelId:"level2",stars:4,completedAt:"2024-01-10T14:20:00Z",playTime:150,user:{id:"user3",nickname:"王五",avatar:""},level:{id:"level2",title:"商务英语入门",difficulty:2}}])}finally{C(!1)}},V=async()=>{try{let e={startDate:P?.[0],endDate:P?.[1],levelId:D,stars:L,userId:M,format:"excel"},t=await w.f.exportData(e),r=window.URL.createObjectURL(t),s=document.createElement("a");s.href=r,s.download=`user-stars-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(r),i.Ay.success("导出成功")}catch(e){console.error("Export error:",e),i.Ay.error("导出失败")}},$=(e,t)=>{let r={page:e,pageSize:t||T.pageSize};P&&(r.startDate=P[0],r.endDate=P[1]),D&&(r.levelId=D),L&&(r.stars=L),M&&(r.userId=M),Y(r)},G=(e,t)=>{let r=e/t*100;return(0,s.jsxs)(o.A,{children:[(0,s.jsxs)("span",{children:[e,"/",t]}),(0,s.jsx)("div",{style:{display:"flex",gap:"2px"},children:Array.from({length:t},(t,r)=>(0,s.jsx)(f.A,{style:{color:r<e?"#fadb14":"#d9d9d9",fontSize:"14px"}},r))}),(0,s.jsx)(d.A,{percent:r,size:"small",showInfo:!1,strokeColor:r>=80?"#52c41a":r>=60?"#faad14":"#ff4d4f"})]})},B=[{title:"用户",key:"user",render:(e,t)=>(0,s.jsx)(o.A,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:t.user?.nickname}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",t.userId]})]})})},{title:"关卡",key:"level",render:(t,r)=>(0,s.jsxs)(o.A,{children:[(0,s.jsx)("span",{children:r.level?.title}),(0,s.jsx)(c.A,{title:"跳转到关卡管理",children:(0,s.jsx)(p.Ay,{type:"link",size:"small",icon:(0,s.jsx)(g.A,{}),onClick:()=>e.push(`/levels?highlight=${r.levelId}`)})})]})},{title:"星级评分",key:"stars",render:(e,t)=>G(t.stars,5)},{title:"游戏时长",dataIndex:"playTime",key:"playTime",render:e=>{let t=Math.floor(e/60);return`${t}:${(e%60).toString().padStart(2,"0")}`}},{title:"完成时间",dataIndex:"completedAt",key:"completedAt",render:e=>new Date(e).toLocaleString()}];return n&&0===t.length?(0,s.jsxs)("div",{style:{textAlign:"center",padding:"100px 0"},children:[(0,s.jsx)(u.A,{size:"large"}),(0,s.jsx)("div",{style:{marginTop:16},children:"加载星级数据中..."})]}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{style:{marginBottom:24},children:[(0,s.jsx)("h2",{children:"星级管理"}),(0,s.jsx)("p",{children:"查看和分析用户的关卡完成星级评分"})]}),(0,s.jsx)(h.A,{size:"small",style:{marginBottom:16},children:(0,s.jsxs)(x.A,{gutter:16,align:"middle",children:[(0,s.jsx)(m.A,{span:5,children:(0,s.jsx)(S,{value:P?[P[0],P[1]]:null,onChange:e=>{e?I([e[0].format("YYYY-MM-DD"),e[1].format("YYYY-MM-DD")]):I(null)},placeholder:["开始日期","结束日期"],style:{width:"100%"}})}),(0,s.jsx)(m.A,{span:4,children:(0,s.jsxs)(l.A,{placeholder:"选择关卡",value:D,onChange:q,allowClear:!0,style:{width:"100%"},children:[(0,s.jsx)(z,{value:"level1",children:"基础词汇练习1"}),(0,s.jsx)(z,{value:"level2",children:"商务英语入门"})]})}),(0,s.jsx)(m.A,{span:3,children:(0,s.jsxs)(l.A,{placeholder:"星级筛选",value:L,onChange:_,allowClear:!0,style:{width:"100%"},children:[(0,s.jsx)(z,{value:1,children:"1星"}),(0,s.jsx)(z,{value:2,children:"2星"}),(0,s.jsx)(z,{value:3,children:"3星"}),(0,s.jsx)(z,{value:4,children:"4星"}),(0,s.jsx)(z,{value:5,children:"5星"})]})}),(0,s.jsx)(m.A,{span:4,children:(0,s.jsx)(v.A,{placeholder:"用户ID",value:M,onChange:e=>R(e.target.value||void 0),allowClear:!0,style:{width:"100%"}})}),(0,s.jsx)(m.A,{span:8,children:(0,s.jsxs)(o.A,{children:[(0,s.jsx)(p.Ay,{type:"primary",icon:(0,s.jsx)(j.A,{}),onClick:()=>{let e={page:1};P&&(e.startDate=P[0],e.endDate=P[1]),D&&(e.levelId=D),L&&(e.stars=L),M&&(e.userId=M),Y(e)},children:"筛选"}),(0,s.jsx)(p.Ay,{icon:(0,s.jsx)(A.A,{}),onClick:()=>{I(null),q(void 0),_(void 0),R(void 0),Y({page:1})},children:"重置"}),(0,s.jsx)(p.Ay,{icon:(0,s.jsx)(b.A,{}),onClick:V,children:"导出"})]})})]})}),(0,s.jsx)(h.A,{children:(0,s.jsx)(y.A,{columns:B,dataSource:t,rowKey:"id",loading:n,pagination:{current:T.current,pageSize:T.pageSize,total:T.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条，共 ${e} 条记录`,onChange:$,onShowSizeChange:$,pageSizeOptions:["10","20","50","100"]}})})]})}function P(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(C,{})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,6267,1658,8161,675,5336,9196,5899,8331,553,84,7783,7436,345,976],()=>r(78905));module.exports=s})();