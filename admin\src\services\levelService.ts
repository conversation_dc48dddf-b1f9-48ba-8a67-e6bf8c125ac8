import request from "./request";

// 关卡数据类型（基于API文档LevelResponseDto）
export interface Level {
  id: string;
  name: string;
  difficulty: number;
  description?: string;
  thesaurusIds: string[]; // 保留以兼容API响应
  phraseIds: string[];
  tagIds: string[]; // 新增：关联的标签ID列表
  createdAt: string;
  updatedAt: string;
}

// 创建关卡参数（基于API文档CreateLevelDto）
export interface CreateLevelParams {
  name: string;
  difficulty: number;
  description?: string;
  thesaurusIds?: string[]; // 保留以兼容API，但前端不使用
  phraseIds?: string[];
  phrases?: Array<{ text: string; meaning: string }>; // 新增：直接传递词组数据
  tagIds?: string[]; // 新增：关联的标签ID列表
}

// 更新关卡参数（基于API文档UpdateLevelDto）
export interface UpdateLevelParams {
  name?: string;
  difficulty?: number;
  description?: string;
  thesaurusIds?: string[]; // 保留以兼容API，但前端不使用
  phraseIds?: string[];
  phrases?: Array<{ text: string; meaning: string }>; // 新增：直接传递新词组数据
  tagIds?: string[]; // 新增：关联的标签ID列表
}

// 添加词组到关卡参数
export interface AddPhraseToLevelParams {
  phraseId: string;
}

// 关卡查询参数（基于API文档接口参数）
export interface LevelQueryParams {
  search?: string; // 搜索关键词（标题或描述）
  difficulty?: number; // 难度等级过滤
  isActive?: boolean; // 状态过滤
  tagId?: string; // 标签ID过滤
  page?: number; // 页码，默认1
  pageSize?: number; // 每页数量，默认20
}

// 关卡列表响应类型
export interface LevelListResponse {
  levels: Level[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 关卡星级统计数据类型
export interface LevelStarAnalytics {
  averageStars: number;
  starDistribution: {
    [key: string]: number;
  };
  averageCompletionTime: number;
  completionRate: number;
  difficultyRating: number;
  totalAttempts: number;
  totalCompletions: number;
}

// 关卡服务
export const levelService = {
  // 获取关卡列表（支持筛选和分页）
  getAll: async (params?: LevelQueryParams): Promise<LevelListResponse> => {
    const response = await request.get<LevelListResponse>("/levels", {
      params,
    });
    return response.data;
  },

  // 获取所有关卡（简单版本，保持向后兼容）
  getAllSimple: async (): Promise<Level[]> => {
    const response = await request.get<Level[]>("/levels");
    return response.data;
  },

  // 根据ID获取关卡
  getById: async (id: string): Promise<Level> => {
    const response = await request.get<Level>(`/levels/${id}`);
    return response.data;
  },

  // 创建关卡
  create: async (params: CreateLevelParams): Promise<Level> => {
    const response = await request.post<Level>("/levels", params);
    return response.data;
  },

  // 更新关卡
  update: async (id: string, params: UpdateLevelParams): Promise<Level> => {
    const response = await request.patch<Level>(`/levels/${id}`, params);
    return response.data;
  },

  // 删除关卡
  delete: async (id: string): Promise<void> => {
    await request.delete(`/levels/${id}`);
  },

  // 向关卡添加词组
  addPhrase: async (
    levelId: string,
    params: AddPhraseToLevelParams
  ): Promise<Level> => {
    const response = await request.post<Level>(
      `/levels/${levelId}/phrases`,
      params
    );
    return response.data;
  },

  // 从关卡移除词组
  removePhrase: async (levelId: string, phraseId: string): Promise<Level> => {
    const response = await request.delete<Level>(
      `/levels/${levelId}/phrases/${phraseId}`
    );
    return response.data;
  },

  // 获取关卡统计
  getCount: async (): Promise<{
    total: number;
    maxLevels: number;
    remaining: number;
  }> => {
    const response = await request.get<{
      total: number;
      maxLevels: number;
      remaining: number;
    }>("/levels/count");
    return response.data;
  },

  // 根据难度获取关卡
  getByDifficulty: async (difficulty: number): Promise<Level[]> => {
    const response = await request.get<Level[]>(
      `/levels/difficulty/${difficulty}`
    );
    return response.data;
  },

  // 获取关卡详细信息（包含词组详情）
  getWithPhrases: async (id: string): Promise<Level & { phrases: any[] }> => {
    const response = await request.get<Level & { phrases: any[] }>(
      `/levels/${id}`
    );
    return response.data;
  },

  // 获取关卡星级统计
  getLevelStarAnalytics: async (
    levelId: string
  ): Promise<LevelStarAnalytics> => {
    const response = await request.get<LevelStarAnalytics>(
      `/levels/${levelId}/star-analytics`
    );
    return response.data;
  },
};

// 保持向后兼容的导出
export const getLevels = levelService.getAll;
