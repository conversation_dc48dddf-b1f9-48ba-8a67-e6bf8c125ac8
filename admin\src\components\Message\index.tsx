import React from 'react';
import { createRoot } from 'react-dom/client';
import './message.css';

export interface MessageConfig {
  content: string;
  duration?: number;
  type?: 'success' | 'error' | 'warning' | 'info';
  key?: string;
}

interface MessageItem extends MessageConfig {
  id: string;
  visible: boolean;
}

// 消息容器组件
const MessageContainer: React.FC<{ messages: MessageItem[] }> = ({ messages }) => {
  return (
    <div className="custom-message-container">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`custom-message custom-message-${message.type} ${
            message.visible ? 'custom-message-show' : 'custom-message-hide'
          }`}
        >
          <div className="custom-message-icon">
            {message.type === 'success' && '✓'}
            {message.type === 'error' && '✕'}
            {message.type === 'warning' && '⚠'}
            {message.type === 'info' && 'ℹ'}
          </div>
          <span className="custom-message-content">{message.content}</span>
        </div>
      ))}
    </div>
  );
};

// 消息管理器
class MessageManager {
  private messages: MessageItem[] = [];
  private container: HTMLDivElement | null = null;
  private root: any = null;

  private getContainer() {
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.className = 'custom-message-wrapper';
      document.body.appendChild(this.container);
      this.root = createRoot(this.container);
    }
    return this.container;
  }

  private render() {
    if (this.root) {
      this.root.render(<MessageContainer messages={this.messages} />);
    }
  }

  private generateId() {
    return `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  show(config: MessageConfig) {
    const id = config.key || this.generateId();
    const duration = config.duration ?? 3000;
    
    // 如果已存在相同key的消息，先移除
    if (config.key) {
      this.messages = this.messages.filter(msg => msg.id !== config.key);
    }

    const messageItem: MessageItem = {
      ...config,
      id,
      visible: true,
    };

    this.messages.push(messageItem);
    this.getContainer();
    this.render();

    // 自动移除
    if (duration > 0) {
      setTimeout(() => {
        this.hide(id);
      }, duration);
    }

    return id;
  }

  hide(id: string) {
    const messageIndex = this.messages.findIndex(msg => msg.id === id);
    if (messageIndex > -1) {
      this.messages[messageIndex].visible = false;
      this.render();
      
      // 动画结束后移除
      setTimeout(() => {
        this.messages = this.messages.filter(msg => msg.id !== id);
        this.render();
        
        // 如果没有消息了，清理容器
        if (this.messages.length === 0 && this.container) {
          document.body.removeChild(this.container);
          this.container = null;
          this.root = null;
        }
      }, 300);
    }
  }

  destroy() {
    this.messages = [];
    if (this.container) {
      document.body.removeChild(this.container);
      this.container = null;
      this.root = null;
    }
  }
}

// 全局消息管理器实例
const messageManager = new MessageManager();

// 导出的API
export const message = {
  success: (content: string, duration?: number) => 
    messageManager.show({ content, type: 'success', duration }),
  
  error: (content: string, duration?: number) => 
    messageManager.show({ content, type: 'error', duration }),
  
  warning: (content: string, duration?: number) => 
    messageManager.show({ content, type: 'warning', duration }),
  
  info: (content: string, duration?: number) => 
    messageManager.show({ content, type: 'info', duration }),
  
  destroy: () => messageManager.destroy(),
};

export default message;
