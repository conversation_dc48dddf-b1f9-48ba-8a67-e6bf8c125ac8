/* Message 组件样式 */
.custom-message-wrapper {
  position: fixed;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1010;
  pointer-events: none;
}

.custom-message-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.custom-message {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  background: #fff;
  font-size: 14px;
  line-height: 1.5715;
  pointer-events: auto;
  transition: all 0.3s ease;
  max-width: 400px;
  word-wrap: break-word;
}

.custom-message-icon {
  margin-right: 8px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.custom-message-content {
  flex: 1;
}

/* 不同类型的消息样式 */
.custom-message-success {
  border: 1px solid #b7eb8f;
  background-color: #f6ffed;
}

.custom-message-success .custom-message-icon {
  color: #52c41a;
}

.custom-message-success .custom-message-content {
  color: #52c41a;
}

.custom-message-error {
  border: 1px solid #ffccc7;
  background-color: #fff2f0;
}

.custom-message-error .custom-message-icon {
  color: #ff4d4f;
}

.custom-message-error .custom-message-content {
  color: #ff4d4f;
}

.custom-message-warning {
  border: 1px solid #ffe58f;
  background-color: #fffbe6;
}

.custom-message-warning .custom-message-icon {
  color: #faad14;
}

.custom-message-warning .custom-message-content {
  color: #faad14;
}

.custom-message-info {
  border: 1px solid #91caff;
  background-color: #f0f5ff;
}

.custom-message-info .custom-message-icon {
  color: #1677ff;
}

.custom-message-info .custom-message-content {
  color: #1677ff;
}

/* 动画效果 */
.custom-message-show {
  opacity: 1;
  transform: translateY(0);
  animation: customMessageSlideIn 0.3s ease;
}

.custom-message-hide {
  opacity: 0;
  transform: translateY(-100%);
  animation: customMessageSlideOut 0.3s ease;
}

@keyframes customMessageSlideIn {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes customMessageSlideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-message {
    max-width: calc(100vw - 32px);
    margin: 0 16px;
  }
}
