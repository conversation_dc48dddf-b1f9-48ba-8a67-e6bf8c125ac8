'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Button,
  Space,
  Typography,
  Row,
  Col,
  message,
  Spin,
  Alert,
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import { ShareService, SHARE_TYPE_OPTIONS, SHARE_PATH_TEMPLATES } from '../../../../../services/shareService';
import type { ShareConfig, UpdateShareConfigRequest } from '../../../../../types/share';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function EditSharePage() {
  const params = useParams();
  const router = useRouter();
  const [share, setShare] = useState<ShareConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [form] = Form.useForm();

  const shareId = params.id as string;

  // 获取分享配置详情
  const fetchShareDetail = async () => {
    if (!shareId) return;
    
    setLoading(true);
    try {
      const data = await ShareService.getShareConfigById(shareId);
      setShare(data);
      
      // 设置表单初始值
      form.setFieldsValue({
        name: data.name,
        title: data.title,
        path: data.path,
        imageUrl: data.imageUrl,
        description: data.description,
        type: data.type,
        isActive: data.isActive,
        sortOrder: data.sortOrder,
      });
    } catch (error) {
      message.error('获取分享配置详情失败');
      console.error('获取分享配置详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchShareDetail();
  }, [shareId]);

  // 返回详情页
  const goBack = () => {
    router.push(`/shares/${shareId}`);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      
      await ShareService.updateShareConfig(shareId, values as UpdateShareConfigRequest);
      message.success('分享配置更新成功');
      
      // 返回详情页
      router.push(`/shares/${shareId}`);
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        message.error('请检查表单输入');
      } else {
        const errorMessage = error && typeof error === 'object' && 'message' in error
          ? (error as { message: string }).message
          : '更新失败';
        message.error(errorMessage);
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!share) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="分享配置不存在"
          description="请检查URL是否正确，或者该配置已被删除。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => router.push('/shares')}>
              返回列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        {/* 页面头部 */}
        <div style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Button icon={<ArrowLeftOutlined />} onClick={goBack}>
                  返回
                </Button>
                <Title level={3} style={{ margin: 0 }}>
                  <ShareAltOutlined style={{ marginRight: '8px' }} />
                  编辑分享配置
                </Title>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button onClick={goBack}>
                  取消
                </Button>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  loading={submitting}
                  onClick={handleSubmit}
                >
                  保存
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 编辑表单 */}
        <Form
          form={form}
          layout="vertical"
          size="large"
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="配置名称"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="请输入配置名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="分享类型"
                rules={[{ required: true, message: '请选择分享类型' }]}
              >
                <Select placeholder="请选择分享类型">
                  {SHARE_TYPE_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      <div>
                        <div>{option.label}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {option.description}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="title"
            label="分享标题"
            rules={[{ required: true, message: '请输入分享标题' }]}
          >
            <Input placeholder="请输入分享标题" />
          </Form.Item>

          <Form.Item
            name="path"
            label="分享路径"
            rules={[{ required: true, message: '请输入分享路径' }]}
          >
            <Select
              placeholder="请选择或输入分享路径"
              mode="tags"
              allowClear
            >
              {SHARE_PATH_TEMPLATES.map(template => (
                <Option key={template.value} value={template.value}>
                  <div>
                    <div>{template.label}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {template.description}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="imageUrl"
            label="分享图片URL"
            rules={[
              { type: 'url', message: '请输入有效的URL' }
            ]}
          >
            <Input placeholder="请输入分享图片URL（可选）" />
          </Form.Item>

          <Form.Item
            name="description"
            label="分享描述"
          >
            <TextArea
              placeholder="请输入分享描述（可选）"
              rows={4}
              maxLength={200}
              showCount
            />
          </Form.Item>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="sortOrder"
                label="排序权重"
                rules={[{ required: true, message: '请输入排序权重' }]}
              >
                <InputNumber
                  min={1}
                  max={999}
                  placeholder="排序权重"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="启用状态"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren="启用" 
                  unCheckedChildren="禁用"
                  size="default"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 提示信息 */}
        <Alert
          message="编辑提示"
          description={
            <div>
              <p>• 分享标题和路径是必填项，将直接影响小程序的分享效果</p>
              <p>• 分享图片建议使用5:4比例，推荐尺寸500x400px</p>
              <p>• 路径中可以使用变量，如 {'{levelId}'} 会被替换为实际的关卡ID</p>
              <p>• 默认类型的配置不能删除，但可以禁用</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginTop: '24px' }}
        />
      </Card>
    </div>
  );
}
