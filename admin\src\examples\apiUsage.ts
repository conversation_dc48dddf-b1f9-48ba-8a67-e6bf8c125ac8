// API使用示例
import {
  authService,
  phraseService,
  thesaurusService,
  levelService,
} from "@/services";

// 认证示例
export const authExamples = {
  // 登录
  async login() {
    try {
      const response = await authService.login({
        username: "admin",
        password: "password123",
      });
      console.log("登录成功:", response);
      return response;
    } catch (error) {
      console.error("登录失败:", error);
      throw error;
    }
  },

  // 登出
  logout() {
    authService.logout();
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = authService.isLoggedIn();
    const token = authService.getToken();
    console.log("登录状态:", isLoggedIn, "令牌:", token);
    return { isLoggedIn, token };
  },
};

// 词组管理示例
export const phraseExamples = {
  // 获取所有词组
  async getAllPhrases() {
    try {
      const phrases = await phraseService.getAll();
      console.log("所有词组:", phrases);
      return phrases;
    } catch (error) {
      console.error("获取词组失败:", error);
      throw error;
    }
  },

  // 创建词组
  async createPhrase() {
    try {
      const newPhrase = await phraseService.create({
        text: "示例词组",
        meaning: "这是一个示例词组的含义",
        exampleSentence: "这是一个使用示例",
        tags: ["示例", "测试"],
      });
      console.log("创建的词组:", newPhrase);
      return newPhrase;
    } catch (error) {
      console.error("创建词组失败:", error);
      throw error;
    }
  },

  // 更新词组
  async updatePhrase(id: string) {
    try {
      const updatedPhrase = await phraseService.update(id, {
        meaning: "更新后的含义",
      });
      console.log("更新的词组:", updatedPhrase);
      return updatedPhrase;
    } catch (error) {
      console.error("更新词组失败:", error);
      throw error;
    }
  },

  // 删除词组
  async deletePhrase(id: string) {
    try {
      await phraseService.delete(id);
      console.log("词组删除成功");
    } catch (error) {
      console.error("删除词组失败:", error);
      throw error;
    }
  },
};

// 词库管理示例
export const thesaurusExamples = {
  // 获取所有词库
  async getAllThesauruses() {
    try {
      const thesauruses = await thesaurusService.getAll();
      console.log("所有词库:", thesauruses);
      return thesauruses;
    } catch (error) {
      console.error("获取词库失败:", error);
      throw error;
    }
  },

  // 创建词库
  async createThesaurus() {
    try {
      const newThesaurus = await thesaurusService.create({
        name: "示例词库",
        description: "这是一个示例词库",
      });
      console.log("创建的词库:", newThesaurus);
      return newThesaurus;
    } catch (error) {
      console.error("创建词库失败:", error);
      throw error;
    }
  },

  // 向词库添加词组
  async addPhraseToThesaurus(thesaurusId: string, phraseId: string) {
    try {
      const updatedThesaurus = await thesaurusService.addPhrase(thesaurusId, {
        phraseId,
      });
      console.log("更新的词库:", updatedThesaurus);
      return updatedThesaurus;
    } catch (error) {
      console.error("添加词组到词库失败:", error);
      throw error;
    }
  },
};

// 关卡管理示例
export const levelExamples = {
  // 获取所有关卡
  async getAllLevels() {
    try {
      const levels = await levelService.getAll();
      console.log("所有关卡:", levels);
      return levels;
    } catch (error) {
      console.error("获取关卡失败:", error);
      throw error;
    }
  },

  // 创建关卡
  async createLevel() {
    try {
      const newLevel = await levelService.create({
        name: "示例关卡",
        difficulty: 1,
        description: "这是一个示例关卡",
        thesaurusIds: [], // 保持为空数组以兼容API
        phraseIds: [], // 需要先有词组ID
      });
      console.log("创建的关卡:", newLevel);
      return newLevel;
    } catch (error) {
      console.error("创建关卡失败:", error);
      throw error;
    }
  },

  // 向关卡添加词组
  async addPhraseToLevel(levelId: string, phraseId: string) {
    try {
      const updatedLevel = await levelService.addPhrase(levelId, { phraseId });
      console.log("更新的关卡:", updatedLevel);
      return updatedLevel;
    } catch (error) {
      console.error("添加词组到关卡失败:", error);
      throw error;
    }
  },
};
