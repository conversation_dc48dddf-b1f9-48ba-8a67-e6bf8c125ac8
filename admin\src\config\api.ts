// API配置
export const API_CONFIG = {
  // 基础URL - 可以根据环境变量动态设置
  BASE_URL:
    (typeof window !== "undefined"
      ? process.env.NEXT_PUBLIC_API_BASE_URL
      : null) || "http://localhost:18891",

  // 超时时间
  TIMEOUT: 10000,

  // API版本前缀（注意：当前使用/admin/前缀，此配置保留用于兼容性）
  API_PREFIX: "/api/v1/admin",

  // 完整的API基础URL
  get FULL_BASE_URL() {
    return `${this.BASE_URL}${this.API_PREFIX}`;
  },
};

// 环境配置
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",
  isTest: process.env.NODE_ENV === "test",
};
