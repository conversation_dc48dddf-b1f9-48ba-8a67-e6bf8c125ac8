(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6001],{22918:(e,s,t)=>{"use strict";t.d(s,{f:()=>o});var a=t(83899);let o={getAll:async()=>(await a.Ay.get("/settings")).data,getById:async e=>(await a.Ay.get("/settings/".concat(e))).data,getByKey:async e=>(await a.Ay.get("/settings/key/".concat(e))).data,update:async(e,s)=>(await a.Ay.patch("/settings/".concat(e),s)).data,updateByKey:async(e,s)=>(await a.Ay.patch("/settings/key/".concat(e),{value:s})).data,async initializeDefaults(){await a.Ay.post("/settings/initialize")},async updateAppConfig(e){let s=[];void 0!==e.helpUrl&&s.push(this.updateByKey("help_url",e.helpUrl)),void 0!==e.backgroundMusicUrl&&s.push(this.updateByKey("background_music_url",e.backgroundMusicUrl)),await Promise.all(s)},async getAppConfig(){try{let[e,s]=await Promise.all([this.getByKey("help_url").catch(()=>null),this.getByKey("background_music_url").catch(()=>null)]);return{helpUrl:(null==e?void 0:e.value)||"",backgroundMusicUrl:(null==s?void 0:s.value)||""}}catch(e){return console.error("获取小程序配置失败:",e),{helpUrl:"",backgroundMusicUrl:""}}},async testWeixinAppSettings(){try{return(await a.Ay.get("/weixin/app-settings")).data}catch(e){throw console.error("测试微信小程序设置接口失败:",e),e}},async testWeixinGlobalConfig(){try{return(await a.Ay.get("/weixin/global-config")).data}catch(e){throw console.error("测试微信小程序全局配置接口失败:",e),e}}}},36449:()=>{},41008:(e,s,t)=>{"use strict";t.d(s,{i:()=>a});let a={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},41623:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(95155),o=t(12115),n=t(97605),i=t(86615),r=t(19868),l=t(16467),c=t(6124),d=t(95108),u=t(12320),h=t(56020),m=t(94600),g=t(77325),y=t(92611),p=t(23130),x=t(89781),f=t(19558),v=t(34140),A=t(50747),w=t(22918);let{Title:b,Paragraph:j}=n.A;function k(){let[e]=i.A.useForm(),[s,t]=(0,o.useState)(!1),[n,k]=(0,o.useState)(!1),[C,U]=(0,o.useState)(!0),[T,_]=(0,o.useState)(!1),N=async()=>{U(!0);try{let s=await w.f.getAppConfig();e.setFieldsValue(s),console.log("✅ 配置加载成功:",s)}catch(s){console.error("❌ 获取配置失败:",s);try{await w.f.initializeDefaults(),r.Ay.success("已自动初始化默认设置");let s=await w.f.getAppConfig();e.setFieldsValue(s)}catch(e){console.error("❌ 初始化设置失败:",e),r.Ay.error("获取配置失败，请检查服务器连接")}}finally{U(!1)}},R=async e=>{t(!0);try{await w.f.updateAppConfig(e),r.Ay.success("设置保存成功"),console.log("✅ 设置保存成功:",e)}catch(e){console.error("❌ 保存设置失败:",e),r.Ay.error("保存设置失败，请检查网络连接")}finally{t(!1)}},S=async()=>{k(!0);try{await w.f.initializeDefaults(),r.Ay.success("默认设置初始化成功"),console.log("✅ 默认设置初始化成功"),await N()}catch(e){console.error("❌ 初始化设置失败:",e),r.Ay.error("初始化设置失败，请检查服务器连接")}finally{k(!1)}},L=async()=>{_(!0);try{let[e,s]=await Promise.all([w.f.testWeixinAppSettings(),w.f.testWeixinGlobalConfig()]);console.log("✅ 微信小程序设置接口测试成功:",e),console.log("✅ 微信小程序全局配置接口测试成功:",s),r.Ay.success("微信小程序接口测试成功，请查看控制台输出")}catch(e){console.error("❌ 微信小程序接口测试失败:",e),r.Ay.error("微信小程序接口测试失败，请检查服务器连接")}finally{_(!1)}};return((0,o.useEffect)(()=>{N()},[]),C)?(0,a.jsxs)("div",{style:{padding:"24px",textAlign:"center"},children:[(0,a.jsx)(l.A,{size:"large"}),(0,a.jsx)("div",{style:{marginTop:16},children:(0,a.jsx)(j,{children:"正在加载设置..."})})]}):(0,a.jsx)("div",{children:(0,a.jsxs)(c.A,{children:[(0,a.jsxs)("div",{style:{marginBottom:24},children:[(0,a.jsxs)(b,{level:2,children:[(0,a.jsx)(y.A,{style:{marginRight:8}}),"小程序设置"]}),(0,a.jsx)(j,{children:"配置小程序的功能设置，包括帮助链接和背景音乐等。"})]}),(0,a.jsx)(d.A,{message:"设置说明",description:"这些设置将影响小程序的功能表现。系统已自动初始化默认配置，您可以根据需要修改。请确保链接有效且可访问。",type:"info",showIcon:!0,style:{marginBottom:24}}),(0,a.jsxs)(i.A,{form:e,layout:"vertical",onFinish:R,autoComplete:"off",children:[(0,a.jsx)(c.A,{title:(0,a.jsxs)(u.A,{children:[(0,a.jsx)(p.A,{}),"帮助功能设置"]}),size:"small",style:{marginBottom:16},children:(0,a.jsx)(i.A.Item,{label:"帮助页面链接",name:"helpUrl",rules:[{required:!0,message:"请输入帮助页面链接"},{type:"url",message:"请输入有效的URL地址"}],extra:"用户点击帮助按钮时跳转的页面链接",children:(0,a.jsx)(h.A,{placeholder:"https://help.example.com",prefix:(0,a.jsx)(p.A,{})})})}),(0,a.jsx)(c.A,{title:(0,a.jsxs)(u.A,{children:[(0,a.jsx)(x.A,{}),"音乐功能设置"]}),size:"small",style:{marginBottom:24},children:(0,a.jsx)(i.A.Item,{label:"背景音乐链接",name:"backgroundMusicUrl",rules:[{required:!0,message:"请输入背景音乐链接"},{type:"url",message:"请输入有效的URL地址"}],extra:"小程序背景音乐文件的链接地址，支持 MP3 格式",children:(0,a.jsx)(h.A,{placeholder:"https://music.example.com/background.mp3",prefix:(0,a.jsx)(x.A,{})})})}),(0,a.jsx)(m.A,{}),(0,a.jsx)("div",{style:{textAlign:"center"},children:(0,a.jsxs)(u.A,{size:"middle",children:[(0,a.jsx)(g.Ay,{type:"primary",htmlType:"submit",icon:(0,a.jsx)(f.A,{}),loading:s,size:"large",children:"保存设置"}),(0,a.jsx)(g.Ay,{icon:(0,a.jsx)(v.A,{}),onClick:()=>{e.resetFields(),N()},disabled:s,size:"large",children:"重置"}),(0,a.jsx)(g.Ay,{onClick:S,loading:n,disabled:s,size:"large",children:"重置为默认设置"}),(0,a.jsx)(g.Ay,{icon:(0,a.jsx)(A.A,{}),onClick:L,loading:T,disabled:s||n,size:"large",children:"测试微信接口"})]})})]})]})})}},73629:()=>{},82446:(e,s,t)=>{Promise.resolve().then(t.bind(t,41623))},83899:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>c,FH:()=>r,KY:()=>l});var a=t(23464),o=t(90285),n=t(41008);let i=a.A.create({baseURL:n.i.FULL_BASE_URL,timeout:n.i.TIMEOUT,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{var s;let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),console.log("\uD83D\uDE80 发送请求:",{method:null==(s=e.method)?void 0:s.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),i.interceptors.response.use(e=>{var s;let t=e.config;return console.log("✅ 请求成功:",{method:null==(s=t.method)?void 0:s.toUpperCase(),url:t.url,status:e.status,statusText:e.statusText,data:e.data}),t.showSuccess&&t.successMessage&&o.i.success(t.successMessage),e},e=>{var s,t,a,n,i,r,l,c,d;console.error("❌ 请求失败:",{method:null==(t=e.config)||null==(s=t.method)?void 0:s.toUpperCase(),url:null==(a=e.config)?void 0:a.url,baseURL:null==(n=e.config)?void 0:n.baseURL,fullURL:"".concat(null==(i=e.config)?void 0:i.baseURL).concat(null==(r=e.config)?void 0:r.url),status:null==(l=e.response)?void 0:l.status,statusText:null==(c=e.response)?void 0:c.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:s,data:t}=e.response,a="";switch(s){case 401:a="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a=(null==t?void 0:t.message)||"没有权限访问该资源";break;case 404:a=(null==t?void 0:t.message)||"请求的资源不存在";break;case 422:a=(null==t?void 0:t.message)||"请求参数验证失败";break;case 500:a=(null==t?void 0:t.message)||"服务器内部错误";break;default:a=(null==t?void 0:t.message)||"请求失败 (".concat(s,")")}o.i.error(a)}else e.request?o.i.error("网络连接失败，请检查网络"):o.i.error("请求配置错误");return Promise.reject(e)});let r={get:(e,s)=>i.get(e,s),post:(e,s,t)=>i.post(e,s,t),put:(e,s,t)=>i.put(e,s,t),patch:(e,s,t)=>i.patch(e,s,t),delete:(e,s)=>i.delete(e,s)},l={post:(e,s,t,a)=>r.post(e,s,{...a,showSuccess:!0,successMessage:t||"操作成功"}),put:(e,s,t,a)=>r.put(e,s,{...a,showSuccess:!0,successMessage:t||"更新成功"}),patch:(e,s,t,a)=>r.patch(e,s,{...a,showSuccess:!0,successMessage:t||"更新成功"}),delete:(e,s,t)=>r.delete(e,{...t,showSuccess:!0,successMessage:s||"删除成功"})},c=r},90285:(e,s,t)=>{"use strict";t.d(s,{a:()=>l,i:()=>h});var a=t(95155),o=t(12115),n=t(12669);t(36449);let i=e=>{let{title:s,content:t,children:n,visible:i=!1,width:r=520,centered:l=!1,closable:c=!0,maskClosable:d=!0,footer:u,okText:h="确定",cancelText:m="取消",okType:g="primary",confirmLoading:y=!1,onOk:p,onCancel:x,afterClose:f,className:v="",style:A={}}=e,[w,b]=(0,o.useState)(i),[j,k]=(0,o.useState)(!1);(0,o.useEffect)(()=>{i?(b(!0),k(!0),document.body.style.overflow="hidden"):(k(!1),setTimeout(()=>{b(!1),document.body.style.overflow="",null==f||f()},300))},[i,f]);let C=async()=>{if(p)try{await p()}catch(e){console.error("Modal onOk error:",e)}},U=()=>{null==x||x()};return w?(0,a.jsx)("div",{className:"custom-modal-mask ".concat(j?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==x||x())},children:(0,a.jsx)("div",{className:"custom-modal-wrap ".concat(l?"custom-modal-centered":""),children:(0,a.jsxs)("div",{className:"custom-modal ".concat(v," ").concat(j?"custom-modal-show":"custom-modal-hide"),style:{width:r,...A},children:[(s||c)&&(0,a.jsxs)("div",{className:"custom-modal-header",children:[s&&(0,a.jsx)("div",{className:"custom-modal-title",children:s}),c&&(0,a.jsx)("button",{className:"custom-modal-close",onClick:U,"aria-label":"Close",children:"\xd7"})]}),(0,a.jsx)("div",{className:"custom-modal-body",children:t||n}),null===u?null:u||(0,a.jsxs)("div",{className:"custom-modal-footer",children:[m&&(0,a.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:U,children:m}),(0,a.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(g),onClick:C,disabled:y,children:[y&&(0,a.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),h]})]})]})})}):null};class r{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}confirm(e){return new Promise((s,t)=>{let o=!1,n=async()=>{if(!o)try{e.onOk&&await e.onOk(),o=!0,this.destroy(),s()}catch(e){t(e)}};this.getContainer(),this.root.render((0,a.jsx)(i,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:n,onCancel:()=>{var s;o||(o=!0,null==(s=e.onCancel)||s.call(e),this.destroy(),t(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}i.confirm=e=>new r().confirm({...e,okType:e.okType||"primary"}),i.info=e=>new r().confirm({...e,okType:"primary",cancelText:void 0}),i.success=e=>new r().confirm({...e,okType:"primary",cancelText:void 0}),i.error=e=>new r().confirm({...e,okType:"danger",cancelText:void 0}),i.warning=e=>new r().confirm({...e,okType:"primary",cancelText:void 0});let l=i;t(73629);let c=e=>{let{messages:s}=e;return(0,a.jsx)("div",{className:"custom-message-container",children:s.map(e=>(0,a.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,a.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,a.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,a.jsx)(c,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var s;let t=e.key||this.generateId(),a=null!=(s=e.duration)?s:3e3;e.key&&(this.messages=this.messages.filter(s=>s.id!==e.key));let o={...e,id:t,visible:!0};return this.messages.push(o),this.getContainer(),this.render(),a>0&&setTimeout(()=>{this.hide(t)},a),t}hide(e){let s=this.messages.findIndex(s=>s.id===e);s>-1&&(this.messages[s].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(s=>s.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,h={success:(e,s)=>u.show({content:e,type:"success",duration:s}),error:(e,s)=>u.show({content:e,type:"error",duration:s}),warning:(e,s)=>u.show({content:e,type:"warning",duration:s}),info:(e,s)=>u.show({content:e,type:"info",duration:s}),destroy:()=>u.destroy()}}},e=>{var s=s=>e(e.s=s);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,6615,7605,2696,8441,1684,7358],()=>s(82446)),_N_E=e.O()}]);