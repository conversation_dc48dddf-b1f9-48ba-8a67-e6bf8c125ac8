{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/Modal/index.tsx"], "sourcesContent": ["import React, { useState, useEffect, ReactNode } from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './modal.css';\n\nexport interface ModalProps {\n  title?: ReactNode;\n  content?: ReactNode;\n  children?: ReactNode;\n  visible?: boolean;\n  width?: number | string;\n  centered?: boolean;\n  closable?: boolean;\n  maskClosable?: boolean;\n  footer?: ReactNode;\n  okText?: string;\n  cancelText?: string;\n  okType?: 'primary' | 'danger';\n  confirmLoading?: boolean;\n  onOk?: () => void | Promise<void>;\n  onCancel?: () => void;\n  afterClose?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n// Modal 组件\nconst Modal: React.FC<ModalProps> = ({\n  title,\n  content,\n  children,\n  visible = false,\n  width = 520,\n  centered = false,\n  closable = true,\n  maskClosable = true,\n  footer,\n  okText = '确定',\n  cancelText = '取消',\n  okType = 'primary',\n  confirmLoading = false,\n  onOk,\n  onCancel,\n  afterClose,\n  className = '',\n  style = {},\n}) => {\n  const [isVisible, setIsVisible] = useState(visible);\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      setIsVisible(true);\n      setIsAnimating(true);\n      document.body.style.overflow = 'hidden';\n    } else {\n      setIsAnimating(false);\n      setTimeout(() => {\n        setIsVisible(false);\n        document.body.style.overflow = '';\n        afterClose?.();\n      }, 300);\n    }\n  }, [visible, afterClose]);\n\n  const handleMaskClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget && maskClosable) {\n      onCancel?.();\n    }\n  };\n\n  const handleOk = async () => {\n    if (onOk) {\n      try {\n        await onOk();\n      } catch (error) {\n        console.error('Modal onOk error:', error);\n      }\n    }\n  };\n\n  const handleCancel = () => {\n    onCancel?.();\n  };\n\n  const renderFooter = () => {\n    if (footer === null) return null;\n\n    if (footer) return footer;\n\n    return (\n      <div className=\"custom-modal-footer\">\n        {cancelText && (\n          <button\n            className=\"custom-modal-btn custom-modal-btn-default\"\n            onClick={handleCancel}\n          >\n            {cancelText}\n          </button>\n        )}\n        <button\n          className={`custom-modal-btn custom-modal-btn-${okType}`}\n          onClick={handleOk}\n          disabled={confirmLoading}\n        >\n          {confirmLoading && <span className=\"custom-modal-loading\">⟳</span>}\n          {okText}\n        </button>\n      </div>\n    );\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className={`custom-modal-mask ${isAnimating ? 'custom-modal-mask-show' : 'custom-modal-mask-hide'}`}\n      onClick={handleMaskClick}\n    >\n      <div className={`custom-modal-wrap ${centered ? 'custom-modal-centered' : ''}`}>\n        <div\n          className={`custom-modal ${className} ${isAnimating ? 'custom-modal-show' : 'custom-modal-hide'}`}\n          style={{ width, ...style }}\n        >\n          {(title || closable) && (\n            <div className=\"custom-modal-header\">\n              {title && <div className=\"custom-modal-title\">{title}</div>}\n              {closable && (\n                <button\n                  className=\"custom-modal-close\"\n                  onClick={handleCancel}\n                  aria-label=\"Close\"\n                >\n                  ×\n                </button>\n              )}\n            </div>\n          )}\n          \n          <div className=\"custom-modal-body\">\n            {content || children}\n          </div>\n          \n          {renderFooter()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// 确认对话框配置\nexport interface ConfirmConfig {\n  title?: ReactNode;\n  content?: ReactNode;\n  okText?: string;\n  cancelText?: string;\n  okType?: 'primary' | 'danger';\n  onOk?: () => void | Promise<void>;\n  onCancel?: () => void;\n  width?: number | string;\n  centered?: boolean;\n  maskClosable?: boolean;\n}\n\n// 确认对话框管理器\nclass ConfirmManager {\n  private container: HTMLDivElement | null = null;\n  private root: any = null;\n\n  private getContainer() {\n    if (!this.container) {\n      this.container = document.createElement('div');\n      this.container.className = 'custom-modal-container';\n      document.body.appendChild(this.container);\n      this.root = createRoot(this.container);\n    }\n    return this.container;\n  }\n\n  confirm(config: ConfirmConfig) {\n    return new Promise<void>((resolve, reject) => {\n      let isResolved = false;\n\n      const handleOk = async () => {\n        if (isResolved) return;\n        \n        try {\n          if (config.onOk) {\n            await config.onOk();\n          }\n          isResolved = true;\n          this.destroy();\n          resolve();\n        } catch (error) {\n          reject(error);\n        }\n      };\n\n      const handleCancel = () => {\n        if (isResolved) return;\n        \n        isResolved = true;\n        config.onCancel?.();\n        this.destroy();\n        reject(new Error('User cancelled'));\n      };\n\n      this.getContainer();\n      this.root.render(\n        <Modal\n          visible={true}\n          title={config.title}\n          content={config.content}\n          okText={config.okText}\n          cancelText={config.cancelText}\n          okType={config.okType}\n          width={config.width}\n          centered={config.centered}\n          maskClosable={config.maskClosable}\n          onOk={handleOk}\n          onCancel={handleCancel}\n          afterClose={() => this.destroy()}\n        />\n      );\n    });\n  }\n\n  destroy() {\n    if (this.container && document.body.contains(this.container)) {\n      document.body.removeChild(this.container);\n      this.container = null;\n      this.root = null;\n    }\n  }\n}\n\n// 创建带有静态方法的Modal组件\nconst ModalWithStatic = Modal as typeof Modal & {\n  confirm: (config: ConfirmConfig) => Promise<void>;\n  info: (config: ConfirmConfig) => Promise<void>;\n  success: (config: ConfirmConfig) => Promise<void>;\n  error: (config: ConfirmConfig) => Promise<void>;\n  warning: (config: ConfirmConfig) => Promise<void>;\n};\n\n// 静态方法\nModalWithStatic.confirm = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: config.okType || 'primary'\n  });\n};\n\nModalWithStatic.info = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined, // info模式通常只有确定按钮\n  });\n};\n\nModalWithStatic.success = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined,\n  });\n};\n\nModalWithStatic.error = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'danger',\n    cancelText: undefined,\n  });\n};\n\nModalWithStatic.warning = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined,\n  });\n};\n\nexport default ModalWithStatic;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;AAwBA,WAAW;AACX,MAAM,QAA8B,CAAC,EACnC,KAAK,EACL,OAAO,EACP,QAAQ,EACR,UAAU,KAAK,EACf,QAAQ,GAAG,EACX,WAAW,KAAK,EAChB,WAAW,IAAI,EACf,eAAe,IAAI,EACnB,MAAM,EACN,SAAS,IAAI,EACb,aAAa,IAAI,EACjB,SAAS,SAAS,EAClB,iBAAiB,KAAK,EACtB,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACX;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,SAAS;gBACX,aAAa;gBACb,eAAe;gBACf,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,eAAe;gBACf;uCAAW;wBACT,aAAa;wBACb,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;wBAC/B;oBACF;sCAAG;YACL;QACF;0BAAG;QAAC;QAAS;KAAW;IAExB,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,cAAc;YAChD;QACF;IACF;IAEA,MAAM,WAAW;QACf,IAAI,MAAM;YACR,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC;QACF;IACF;IAEA,MAAM,eAAe;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,MAAM,OAAO;QAE5B,IAAI,QAAQ,OAAO;QAEnB,qBACE,6LAAC;YAAI,WAAU;;gBACZ,4BACC,6LAAC;oBACC,WAAU;oBACV,SAAS;8BAER;;;;;;8BAGL,6LAAC;oBACC,WAAW,CAAC,kCAAkC,EAAE,QAAQ;oBACxD,SAAS;oBACT,UAAU;;wBAET,gCAAkB,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;wBACzD;;;;;;;;;;;;;IAIT;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QACC,WAAW,CAAC,kBAAkB,EAAE,cAAc,2BAA2B,0BAA0B;QACnG,SAAS;kBAET,cAAA,6LAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW,0BAA0B,IAAI;sBAC5E,cAAA,6LAAC;gBACC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE,cAAc,sBAAsB,qBAAqB;gBACjG,OAAO;oBAAE;oBAAO,GAAG,KAAK;gBAAC;;oBAExB,CAAC,SAAS,QAAQ,mBACjB,6LAAC;wBAAI,WAAU;;4BACZ,uBAAS,6LAAC;gCAAI,WAAU;0CAAsB;;;;;;4BAC9C,0BACC,6LAAC;gCACC,WAAU;gCACV,SAAS;gCACT,cAAW;0CACZ;;;;;;;;;;;;kCAOP,6LAAC;wBAAI,WAAU;kCACZ,WAAW;;;;;;oBAGb;;;;;;;;;;;;;;;;;AAKX;GAzHM;KAAA;AAyIN,WAAW;AACX,MAAM;IACI,YAAmC,KAAK;IACxC,OAAY,KAAK;IAEjB,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,SAAS,aAAa,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,qKAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,SAAS;QACvC;QACA,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,QAAQ,MAAqB,EAAE;QAC7B,OAAO,IAAI,QAAc,CAAC,SAAS;YACjC,IAAI,aAAa;YAEjB,MAAM,WAAW;gBACf,IAAI,YAAY;gBAEhB,IAAI;oBACF,IAAI,OAAO,IAAI,EAAE;wBACf,MAAM,OAAO,IAAI;oBACnB;oBACA,aAAa;oBACb,IAAI,CAAC,OAAO;oBACZ;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT;YACF;YAEA,MAAM,eAAe;gBACnB,IAAI,YAAY;gBAEhB,aAAa;gBACb,OAAO,QAAQ;gBACf,IAAI,CAAC,OAAO;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,IAAI,CAAC,MAAM,eACd,6LAAC;gBACC,SAAS;gBACT,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,YAAY,OAAO,UAAU;gBAC7B,QAAQ,OAAO,MAAM;gBACrB,OAAO,OAAO,KAAK;gBACnB,UAAU,OAAO,QAAQ;gBACzB,cAAc,OAAO,YAAY;gBACjC,MAAM;gBACN,UAAU;gBACV,YAAY,IAAM,IAAI,CAAC,OAAO;;;;;;QAGpC;IACF;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG;YAC5D,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,IAAI,GAAG;QACd;IACF;AACF;AAEA,mBAAmB;AACnB,MAAM,kBAAkB;AAQxB,OAAO;AACP,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ,OAAO,MAAM,IAAI;IAC3B;AACF;AAEA,gBAAgB,IAAI,GAAG,CAAC;IACtB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,KAAK,GAAG,CAAC;IACvB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/Message/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './message.css';\n\nexport interface MessageConfig {\n  content: string;\n  duration?: number;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  key?: string;\n}\n\ninterface MessageItem extends MessageConfig {\n  id: string;\n  visible: boolean;\n}\n\n// 消息容器组件\nconst MessageContainer: React.FC<{ messages: MessageItem[] }> = ({ messages }) => {\n  return (\n    <div className=\"custom-message-container\">\n      {messages.map((message) => (\n        <div\n          key={message.id}\n          className={`custom-message custom-message-${message.type} ${\n            message.visible ? 'custom-message-show' : 'custom-message-hide'\n          }`}\n        >\n          <div className=\"custom-message-icon\">\n            {message.type === 'success' && '✓'}\n            {message.type === 'error' && '✕'}\n            {message.type === 'warning' && '⚠'}\n            {message.type === 'info' && 'ℹ'}\n          </div>\n          <span className=\"custom-message-content\">{message.content}</span>\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// 消息管理器\nclass MessageManager {\n  private messages: MessageItem[] = [];\n  private container: HTMLDivElement | null = null;\n  private root: any = null;\n\n  private getContainer() {\n    if (!this.container) {\n      this.container = document.createElement('div');\n      this.container.className = 'custom-message-wrapper';\n      document.body.appendChild(this.container);\n      this.root = createRoot(this.container);\n    }\n    return this.container;\n  }\n\n  private render() {\n    if (this.root) {\n      this.root.render(<MessageContainer messages={this.messages} />);\n    }\n  }\n\n  private generateId() {\n    return `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  show(config: MessageConfig) {\n    const id = config.key || this.generateId();\n    const duration = config.duration ?? 3000;\n    \n    // 如果已存在相同key的消息，先移除\n    if (config.key) {\n      this.messages = this.messages.filter(msg => msg.id !== config.key);\n    }\n\n    const messageItem: MessageItem = {\n      ...config,\n      id,\n      visible: true,\n    };\n\n    this.messages.push(messageItem);\n    this.getContainer();\n    this.render();\n\n    // 自动移除\n    if (duration > 0) {\n      setTimeout(() => {\n        this.hide(id);\n      }, duration);\n    }\n\n    return id;\n  }\n\n  hide(id: string) {\n    const messageIndex = this.messages.findIndex(msg => msg.id === id);\n    if (messageIndex > -1) {\n      this.messages[messageIndex].visible = false;\n      this.render();\n      \n      // 动画结束后移除\n      setTimeout(() => {\n        this.messages = this.messages.filter(msg => msg.id !== id);\n        this.render();\n        \n        // 如果没有消息了，清理容器\n        if (this.messages.length === 0 && this.container) {\n          document.body.removeChild(this.container);\n          this.container = null;\n          this.root = null;\n        }\n      }, 300);\n    }\n  }\n\n  destroy() {\n    this.messages = [];\n    if (this.container) {\n      document.body.removeChild(this.container);\n      this.container = null;\n      this.root = null;\n    }\n  }\n}\n\n// 全局消息管理器实例\nconst messageManager = new MessageManager();\n\n// 导出的API\nexport const message = {\n  success: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'success', duration }),\n  \n  error: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'error', duration }),\n  \n  warning: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'warning', duration }),\n  \n  info: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'info', duration }),\n  \n  destroy: () => messageManager.destroy(),\n};\n\nexport default message;\n"], "names": [], "mappings": ";;;;;AACA;;;;AAeA,SAAS;AACT,MAAM,mBAA0D,CAAC,EAAE,QAAQ,EAAE;IAC3E,qBACE,6LAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gBAEC,WAAW,CAAC,8BAA8B,EAAE,QAAQ,IAAI,CAAC,CAAC,EACxD,QAAQ,OAAO,GAAG,wBAAwB,uBAC1C;;kCAEF,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,IAAI,KAAK,aAAa;4BAC9B,QAAQ,IAAI,KAAK,WAAW;4BAC5B,QAAQ,IAAI,KAAK,aAAa;4BAC9B,QAAQ,IAAI,KAAK,UAAU;;;;;;;kCAE9B,6LAAC;wBAAK,WAAU;kCAA0B,QAAQ,OAAO;;;;;;;eAXpD,QAAQ,EAAE;;;;;;;;;;AAgBzB;KArBM;AAuBN,QAAQ;AACR,MAAM;IACI,WAA0B,EAAE,CAAC;IAC7B,YAAmC,KAAK;IACxC,OAAY,KAAK;IAEjB,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,SAAS,aAAa,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,qKAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,SAAS;QACvC;QACA,OAAO,IAAI,CAAC,SAAS;IACvB;IAEQ,SAAS;QACf,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,eAAC,6LAAC;gBAAiB,UAAU,IAAI,CAAC,QAAQ;;;;;;QAC5D;IACF;IAEQ,aAAa;QACnB,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC3E;IAEA,KAAK,MAAqB,EAAE;QAC1B,MAAM,KAAK,OAAO,GAAG,IAAI,IAAI,CAAC,UAAU;QACxC,MAAM,WAAW,OAAO,QAAQ,IAAI;QAEpC,oBAAoB;QACpB,IAAI,OAAO,GAAG,EAAE;YACd,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,GAAG;QACnE;QAEA,MAAM,cAA2B;YAC/B,GAAG,MAAM;YACT;YACA,SAAS;QACX;QAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,MAAM;QAEX,OAAO;QACP,IAAI,WAAW,GAAG;YAChB,WAAW;gBACT,IAAI,CAAC,IAAI,CAAC;YACZ,GAAG;QACL;QAEA,OAAO;IACT;IAEA,KAAK,EAAU,EAAE;QACf,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/D,IAAI,eAAe,CAAC,GAAG;YACrB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,GAAG;YACtC,IAAI,CAAC,MAAM;YAEX,UAAU;YACV,WAAW;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBACvD,IAAI,CAAC,MAAM;gBAEX,eAAe;gBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;oBAChD,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;oBACxC,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,IAAI,GAAG;gBACd;YACF,GAAG;QACL;IACF;IAEA,UAAU;QACR,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,IAAI,GAAG;QACd;IACF;AACF;AAEA,YAAY;AACZ,MAAM,iBAAiB,IAAI;AAGpB,MAAM,UAAU;IACrB,SAAS,CAAC,SAAiB,WACzB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAW;QAAS;IAE3D,OAAO,CAAC,SAAiB,WACvB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAS;QAAS;IAEzD,SAAS,CAAC,SAAiB,WACzB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAW;QAAS;IAE3D,MAAM,CAAC,SAAiB,WACtB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAQ;QAAS;IAExD,SAAS,IAAM,eAAe,OAAO;AACvC;uCAEe", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/index.ts"], "sourcesContent": ["// 自定义组件统一导出\nexport { default as Modal } from './Modal';\nexport { default as message } from './Message';\n\n// 类型导出\nexport type { ModalProps, ConfirmConfig } from './Modal';\nexport type { MessageConfig } from './Message';\n"], "names": [], "mappings": "AAAA,YAAY;;AACZ;AACA", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/config/api.ts"], "sourcesContent": ["// API配置\r\nexport const API_CONFIG = {\r\n  // 基础URL - 可以根据环境变量动态设置\r\n  BASE_URL:\r\n    (typeof window !== \"undefined\"\r\n      ? process.env.NEXT_PUBLIC_API_BASE_URL\r\n      : null) || \"http://localhost:18891\",\r\n\r\n  // 超时时间\r\n  TIMEOUT: 10000,\r\n\r\n  // API版本前缀（注意：当前使用/admin/前缀，此配置保留用于兼容性）\r\n  API_PREFIX: \"/api/v1/admin\",\r\n\r\n  // 完整的API基础URL\r\n  get FULL_BASE_URL() {\r\n    return `${this.BASE_URL}${this.API_PREFIX}`;\r\n  },\r\n};\r\n\r\n// 环境配置\r\nexport const ENV_CONFIG = {\r\n  isDevelopment: process.env.NODE_ENV === \"development\",\r\n  isProduction: process.env.NODE_ENV === \"production\",\r\n  isTest: process.env.NODE_ENV === \"test\",\r\n};\r\n"], "names": [], "mappings": "AAAA,QAAQ;;;;;AAKA;AAJD,MAAM,aAAa;IACxB,uBAAuB;IACvB,UACE,CAAC,4IAEO,KAAK;IAEf,OAAO;IACP,SAAS;IAET,uCAAuC;IACvC,YAAY;IAEZ,cAAc;IACd,IAAI,iBAAgB;QAClB,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE;IAC7C;AACF;AAGO,MAAM,aAAa;IACxB,eAAe,oDAAyB;IACxC,cAAc,oDAAyB;IACvC,QAAQ,oDAAyB;AACnC", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/request.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from \"axios\";\r\nimport { message } from \"@/components\";\r\nimport { API_CONFIG } from \"@/config/api\";\r\n\r\n// 扩展AxiosRequestConfig以支持错误提示控制\r\ninterface CustomAxiosRequestConfig extends AxiosRequestConfig {\r\n  showError?: boolean; // 是否显示错误提示，默认为true\r\n  showSuccess?: boolean; // 是否显示成功提示，默认为false\r\n  successMessage?: string; // 自定义成功提示信息\r\n}\r\n\r\n// 创建axios实例\r\nconst request: AxiosInstance = axios.create({\r\n  baseURL: API_CONFIG.FULL_BASE_URL,\r\n  timeout: API_CONFIG.TIMEOUT,\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n});\r\n\r\n// 请求拦截器\r\nrequest.interceptors.request.use(\r\n  (config) => {\r\n    // 从localStorage获取token并添加到请求头\r\n    const token = localStorage.getItem(\"admin_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // 添加详细的请求日志\r\n    console.log(\"🚀 发送请求:\", {\r\n      method: config.method?.toUpperCase(),\r\n      url: config.url,\r\n      baseURL: config.baseURL,\r\n      fullURL: `${config.baseURL}${config.url}`,\r\n      data: config.data,\r\n      headers: config.headers,\r\n    });\r\n\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error(\"请求拦截器错误:\", error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 响应拦截器\r\nrequest.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    const config = response.config as CustomAxiosRequestConfig;\r\n\r\n    // 添加响应日志\r\n    console.log(\"✅ 请求成功:\", {\r\n      method: config.method?.toUpperCase(),\r\n      url: config.url,\r\n      status: response.status,\r\n      statusText: response.statusText,\r\n      data: response.data,\r\n    });\r\n\r\n    // 处理成功提示\r\n    if (config.showSuccess && config.successMessage) {\r\n      message.success(config.successMessage);\r\n    }\r\n\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error(\"❌ 请求失败:\", {\r\n      method: error.config?.method?.toUpperCase(),\r\n      url: error.config?.url,\r\n      baseURL: error.config?.baseURL,\r\n      fullURL: `${error.config?.baseURL}${error.config?.url}`,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n    });\r\n\r\n    const config = error.config as CustomAxiosRequestConfig;\r\n    const showError = config?.showError !== false; // 默认显示错误\r\n\r\n    if (!showError) {\r\n      return Promise.reject(error);\r\n    }\r\n\r\n    // 处理常见错误\r\n    if (error.response) {\r\n      const { status, data } = error.response;\r\n      let errorMessage = \"\";\r\n\r\n      switch (status) {\r\n        case 401:\r\n          errorMessage = \"登录已过期，请重新登录\";\r\n          localStorage.removeItem(\"admin_token\");\r\n          // 可以在这里添加跳转到登录页的逻辑\r\n          window.location.href = \"/login\";\r\n          break;\r\n        case 403:\r\n          errorMessage = data?.message || \"没有权限访问该资源\";\r\n          break;\r\n        case 404:\r\n          errorMessage = data?.message || \"请求的资源不存在\";\r\n          break;\r\n        case 422:\r\n          errorMessage = data?.message || \"请求参数验证失败\";\r\n          break;\r\n        case 500:\r\n          errorMessage = data?.message || \"服务器内部错误\";\r\n          break;\r\n        default:\r\n          errorMessage = data?.message || `请求失败 (${status})`;\r\n      }\r\n\r\n      message.error(errorMessage);\r\n    } else if (error.request) {\r\n      message.error(\"网络连接失败，请检查网络\");\r\n    } else {\r\n      message.error(\"请求配置错误\");\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 封装常用的HTTP方法\r\nexport const api = {\r\n  // GET请求\r\n  get: <T = any>(\r\n    url: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.get(url, config);\r\n  },\r\n\r\n  // POST请求\r\n  post: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.post(url, data, config);\r\n  },\r\n\r\n  // PUT请求\r\n  put: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.put(url, data, config);\r\n  },\r\n\r\n  // PATCH请求\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.patch(url, data, config);\r\n  },\r\n\r\n  // DELETE请求\r\n  delete: <T = any>(\r\n    url: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.delete(url, config);\r\n  },\r\n};\r\n\r\n// 便捷方法：不显示错误提示的请求\r\nexport const silentApi = {\r\n  get: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>\r\n    api.get<T>(url, { ...config, showError: false }),\r\n\r\n  post: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>\r\n    api.post<T>(url, data, { ...config, showError: false }),\r\n\r\n  put: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>\r\n    api.put<T>(url, data, { ...config, showError: false }),\r\n\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ) => api.patch<T>(url, data, { ...config, showError: false }),\r\n\r\n  delete: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>\r\n    api.delete<T>(url, { ...config, showError: false }),\r\n};\r\n\r\n// 便捷方法：带成功提示的请求\r\nexport const successApi = {\r\n  post: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.post<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"操作成功\",\r\n    }),\r\n\r\n  put: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.put<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"更新成功\",\r\n    }),\r\n\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.patch<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"更新成功\",\r\n    }),\r\n\r\n  delete: <T = any>(\r\n    url: string,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.delete<T>(url, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"删除成功\",\r\n    }),\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AACA;;;;AASA,YAAY;AACZ,MAAM,UAAyB,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1C,SAAS,uHAAA,CAAA,aAAU,CAAC,aAAa;IACjC,SAAS,uHAAA,CAAA,aAAU,CAAC,OAAO;IAC3B,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,QAAQ;AACR,QAAQ,YAAY,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC;IACC,8BAA8B;IAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,YAAY;IACZ,QAAQ,GAAG,CAAC,YAAY;QACtB,QAAQ,OAAO,MAAM,EAAE;QACvB,KAAK,OAAO,GAAG;QACf,SAAS,OAAO,OAAO;QACvB,SAAS,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;QACzC,MAAM,OAAO,IAAI;QACjB,SAAS,OAAO,OAAO;IACzB;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,YAAY;IAC1B,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,QAAQ;AACR,QAAQ,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC/B,CAAC;IACC,MAAM,SAAS,SAAS,MAAM;IAE9B,SAAS;IACT,QAAQ,GAAG,CAAC,WAAW;QACrB,QAAQ,OAAO,MAAM,EAAE;QACvB,KAAK,OAAO,GAAG;QACf,QAAQ,SAAS,MAAM;QACvB,YAAY,SAAS,UAAU;QAC/B,MAAM,SAAS,IAAI;IACrB;IAEA,SAAS;IACT,IAAI,OAAO,WAAW,IAAI,OAAO,cAAc,EAAE;QAC/C,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC,OAAO,cAAc;IACvC;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,WAAW;QACvB,QAAQ,MAAM,MAAM,EAAE,QAAQ;QAC9B,KAAK,MAAM,MAAM,EAAE;QACnB,SAAS,MAAM,MAAM,EAAE;QACvB,SAAS,GAAG,MAAM,MAAM,EAAE,UAAU,MAAM,MAAM,EAAE,KAAK;QACvD,QAAQ,MAAM,QAAQ,EAAE;QACxB,YAAY,MAAM,QAAQ,EAAE;QAC5B,MAAM,MAAM,QAAQ,EAAE;QACtB,SAAS,MAAM,OAAO;IACxB;IAEA,MAAM,SAAS,MAAM,MAAM;IAC3B,MAAM,YAAY,QAAQ,cAAc,OAAO,SAAS;IAExD,IAAI,CAAC,WAAW;QACd,OAAO,QAAQ,MAAM,CAAC;IACxB;IAEA,SAAS;IACT,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ;QACvC,IAAI,eAAe;QAEnB,OAAQ;YACN,KAAK;gBACH,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,mBAAmB;gBACnB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF;gBACE,eAAe,MAAM,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtD;QAEA,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB,OAAO;QACL,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,MAAM;IACjB,QAAQ;IACR,KAAK,CACH,KACA;QAEA,OAAO,QAAQ,GAAG,CAAC,KAAK;IAC1B;IAEA,SAAS;IACT,MAAM,CACJ,KACA,MACA;QAEA,OAAO,QAAQ,IAAI,CAAC,KAAK,MAAM;IACjC;IAEA,QAAQ;IACR,KAAK,CACH,KACA,MACA;QAEA,OAAO,QAAQ,GAAG,CAAC,KAAK,MAAM;IAChC;IAEA,UAAU;IACV,OAAO,CACL,KACA,MACA;QAEA,OAAO,QAAQ,KAAK,CAAC,KAAK,MAAM;IAClC;IAEA,WAAW;IACX,QAAQ,CACN,KACA;QAEA,OAAO,QAAQ,MAAM,CAAC,KAAK;IAC7B;AACF;AAGO,MAAM,YAAY;IACvB,KAAK,CAAU,KAAa,SAC1B,IAAI,GAAG,CAAI,KAAK;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEhD,MAAM,CAAU,KAAa,MAAY,SACvC,IAAI,IAAI,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEvD,KAAK,CAAU,KAAa,MAAY,SACtC,IAAI,GAAG,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEtD,OAAO,CACL,KACA,MACA,SACG,IAAI,KAAK,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAE3D,QAAQ,CAAU,KAAa,SAC7B,IAAI,MAAM,CAAI,KAAK;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;AACrD;AAGO,MAAM,aAAa;IACxB,MAAM,CACJ,KACA,MACA,gBACA,SAEA,IAAI,IAAI,CAAI,KAAK,MAAM;YACrB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,KAAK,CACH,KACA,MACA,gBACA,SAEA,IAAI,GAAG,CAAI,KAAK,MAAM;YACpB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,OAAO,CACL,KACA,MACA,gBACA,SAEA,IAAI,KAAK,CAAI,KAAK,MAAM;YACtB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,QAAQ,CACN,KACA,gBACA,SAEA,IAAI,MAAM,CAAI,KAAK;YACjB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/authService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 登录请求参数类型\r\nexport interface LoginParams {\r\n  username: string;\r\n  password: string;\r\n}\r\n\r\n// 登录响应类型\r\nexport interface LoginResponse {\r\n  message: string;\r\n  accessToken: string;\r\n}\r\n\r\n// 认证服务\r\nexport const authService = {\r\n  // 登录\r\n  login: async (params: LoginParams): Promise<LoginResponse> => {\r\n    const response = await request.post<LoginResponse>('/auth/login', params);\r\n    return response.data;\r\n  },\r\n\r\n  // 登出\r\n  logout: () => {\r\n    localStorage.removeItem('admin_token');\r\n    window.location.href = '/login';\r\n  },\r\n\r\n  // 获取当前token\r\n  getToken: (): string | null => {\r\n    return localStorage.getItem('admin_token');\r\n  },\r\n\r\n  // 设置token\r\n  setToken: (token: string): void => {\r\n    localStorage.setItem('admin_token', token);\r\n  },\r\n\r\n  // 检查是否已登录\r\n  isLoggedIn: (): boolean => {\r\n    return !!localStorage.getItem('admin_token');\r\n  },\r\n};\r\n\r\n// 保持向后兼容的导出\r\nexport const login = authService.login;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAeO,MAAM,cAAc;IACzB,KAAK;IACL,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAgB,eAAe;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,KAAK;IACL,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,YAAY;IACZ,UAAU;QACR,OAAO,aAAa,OAAO,CAAC;IAC9B;IAEA,UAAU;IACV,UAAU,CAAC;QACT,aAAa,OAAO,CAAC,eAAe;IACtC;IAEA,UAAU;IACV,YAAY;QACV,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;AACF;AAGO,MAAM,QAAQ,YAAY,KAAK", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/phraseService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 词组数据类型\r\nexport interface Phrase {\r\n  id: string;\r\n  text: string;\r\n  meaning: string;\r\n  exampleSentence?: string;\r\n  tags?: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建词组参数\r\nexport interface CreatePhraseParams {\r\n  text: string;\r\n  meaning: string;\r\n  exampleSentence?: string;\r\n  tags?: string[];\r\n}\r\n\r\n// 更新词组参数\r\nexport interface UpdatePhraseParams {\r\n  text?: string;\r\n  meaning?: string;\r\n  exampleSentence?: string;\r\n  tags?: string[];\r\n}\r\n\r\n// 词组服务\r\nexport const phraseService = {\r\n  // 获取所有词组\r\n  getAll: async (): Promise<Phrase[]> => {\r\n    const response = await request.get<Phrase[]>('/phrases');\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取词组\r\n  getById: async (id: string): Promise<Phrase> => {\r\n    const response = await request.get<Phrase>(`/phrases/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建词组\r\n  create: async (params: CreatePhraseParams): Promise<Phrase> => {\r\n    const response = await request.post<Phrase>('/phrases', params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新词组\r\n  update: async (id: string, params: UpdatePhraseParams): Promise<Phrase> => {\r\n    const response = await request.patch<Phrase>(`/phrases/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除词组\r\n  delete: async (id: string): Promise<void> => {\r\n    await request.delete(`/phrases/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AA8BO,MAAM,gBAAgB;IAC3B,SAAS;IACT,QAAQ;QACN,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAW;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAS,CAAC,SAAS,EAAE,IAAI;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAS,YAAY;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,KAAK,CAAS,CAAC,SAAS,EAAE,IAAI,EAAE;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,IAAI;IACvC;AACF", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/thesaurusService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 词库数据类型\r\nexport interface Thesaurus {\r\n  id: string;\r\n  name: string;\r\n  description?: string;\r\n  phraseIds: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建词库参数\r\nexport interface CreateThesaurusParams {\r\n  name: string;\r\n  description?: string;\r\n}\r\n\r\n// 更新词库参数\r\nexport interface UpdateThesaurusParams {\r\n  name?: string;\r\n  description?: string;\r\n}\r\n\r\n// 添加词组到词库参数\r\nexport interface AddPhraseToThesaurusParams {\r\n  phraseId: string;\r\n}\r\n\r\n// 词库服务\r\nexport const thesaurusService = {\r\n  // 获取所有词库\r\n  getAll: async (): Promise<Thesaurus[]> => {\r\n    const response = await request.get<Thesaurus[]>('/thesauruses');\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取词库\r\n  getById: async (id: string): Promise<Thesaurus> => {\r\n    const response = await request.get<Thesaurus>(`/thesauruses/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建词库\r\n  create: async (params: CreateThesaurusParams): Promise<Thesaurus> => {\r\n    const response = await request.post<Thesaurus>('/thesauruses', params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新词库\r\n  update: async (id: string, params: UpdateThesaurusParams): Promise<Thesaurus> => {\r\n    const response = await request.patch<Thesaurus>(`/thesauruses/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除词库\r\n  delete: async (id: string): Promise<void> => {\r\n    await request.delete(`/thesauruses/${id}`);\r\n  },\r\n\r\n  // 向词库添加词组\r\n  addPhrase: async (thesaurusId: string, params: AddPhraseToThesaurusParams): Promise<Thesaurus> => {\r\n    const response = await request.post<Thesaurus>(`/thesauruses/${thesaurusId}/phrases`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 从词库移除词组\r\n  removePhrase: async (thesaurusId: string, phraseId: string): Promise<Thesaurus> => {\r\n    const response = await request.delete<Thesaurus>(`/thesauruses/${thesaurusId}/phrases/${phraseId}`);\r\n    return response.data;\r\n  },\r\n};\r\n\r\n// 导出便捷函数以保持向后兼容\r\nexport const createThesaurus = thesaurusService.create;\r\nexport const updateThesaurus = thesaurusService.update;\r\nexport const deleteThesaurus = thesaurusService.delete;\r\nexport const deleteThesaurusById = thesaurusService.delete; // 别名\r\nexport const getThesaurusById = thesaurusService.getById;\r\nexport const getAllThesauruses = thesaurusService.getAll;\r\nexport const getThesauruses = thesaurusService.getAll; // 别名\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AA8BO,MAAM,mBAAmB;IAC9B,SAAS;IACT,QAAQ;QACN,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAc;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAY,CAAC,aAAa,EAAE,IAAI;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAY,gBAAgB;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,KAAK,CAAY,CAAC,aAAa,EAAE,IAAI,EAAE;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;IAC3C;IAEA,UAAU;IACV,WAAW,OAAO,aAAqB;QACrC,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAY,CAAC,aAAa,EAAE,YAAY,QAAQ,CAAC,EAAE;QACtF,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,cAAc,OAAO,aAAqB;QACxC,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CAAY,CAAC,aAAa,EAAE,YAAY,SAAS,EAAE,UAAU;QAClG,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,kBAAkB,iBAAiB,MAAM;AAC/C,MAAM,kBAAkB,iBAAiB,MAAM;AAC/C,MAAM,kBAAkB,iBAAiB,MAAM;AAC/C,MAAM,sBAAsB,iBAAiB,MAAM,EAAE,KAAK;AAC1D,MAAM,mBAAmB,iBAAiB,OAAO;AACjD,MAAM,oBAAoB,iBAAiB,MAAM;AACjD,MAAM,iBAAiB,iBAAiB,MAAM,EAAE,KAAK", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/levelService.ts"], "sourcesContent": ["import request from \"./request\";\r\n\r\n// 关卡数据类型（基于API文档LevelResponseDto）\r\nexport interface Level {\r\n  id: string;\r\n  name: string;\r\n  difficulty: number;\r\n  description?: string;\r\n  thesaurusIds: string[]; // 保留以兼容API响应\r\n  phraseIds: string[];\r\n  tagIds: string[]; // 新增：关联的标签ID列表\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建关卡参数（基于API文档CreateLevelDto）\r\nexport interface CreateLevelParams {\r\n  name: string;\r\n  difficulty: number;\r\n  description?: string;\r\n  thesaurusIds?: string[]; // 保留以兼容API，但前端不使用\r\n  phraseIds?: string[];\r\n  phrases?: Array<{ text: string; meaning: string }>; // 新增：直接传递词组数据\r\n  tagIds?: string[]; // 新增：关联的标签ID列表\r\n}\r\n\r\n// 更新关卡参数（基于API文档UpdateLevelDto）\r\nexport interface UpdateLevelParams {\r\n  name?: string;\r\n  difficulty?: number;\r\n  description?: string;\r\n  thesaurusIds?: string[]; // 保留以兼容API，但前端不使用\r\n  phraseIds?: string[];\r\n  phrases?: Array<{ text: string; meaning: string }>; // 新增：直接传递新词组数据\r\n  tagIds?: string[]; // 新增：关联的标签ID列表\r\n}\r\n\r\n// 添加词组到关卡参数\r\nexport interface AddPhraseToLevelParams {\r\n  phraseId: string;\r\n}\r\n\r\n// 关卡查询参数（基于API文档接口参数）\r\nexport interface LevelQueryParams {\r\n  search?: string; // 搜索关键词（标题或描述）\r\n  difficulty?: number; // 难度等级过滤\r\n  isActive?: boolean; // 状态过滤\r\n  tagId?: string; // 标签ID过滤\r\n  page?: number; // 页码，默认1\r\n  pageSize?: number; // 每页数量，默认20\r\n}\r\n\r\n// 关卡列表响应类型\r\nexport interface LevelListResponse {\r\n  levels: Level[];\r\n  total: number;\r\n  page: number;\r\n  pageSize: number;\r\n  totalPages: number;\r\n}\r\n\r\n// 关卡星级统计数据类型\r\nexport interface LevelStarAnalytics {\r\n  averageStars: number;\r\n  starDistribution: {\r\n    [key: string]: number;\r\n  };\r\n  averageCompletionTime: number;\r\n  completionRate: number;\r\n  difficultyRating: number;\r\n  totalAttempts: number;\r\n  totalCompletions: number;\r\n}\r\n\r\n// 关卡服务\r\nexport const levelService = {\r\n  // 获取关卡列表（支持筛选和分页）\r\n  getAll: async (params?: LevelQueryParams): Promise<LevelListResponse> => {\r\n    const response = await request.get<LevelListResponse>(\"/levels\", {\r\n      params,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 获取所有关卡（简单版本，保持向后兼容）\r\n  getAllSimple: async (): Promise<Level[]> => {\r\n    const response = await request.get<Level[]>(\"/levels\");\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取关卡\r\n  getById: async (id: string): Promise<Level> => {\r\n    const response = await request.get<Level>(`/levels/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建关卡\r\n  create: async (params: CreateLevelParams): Promise<Level> => {\r\n    const response = await request.post<Level>(\"/levels\", params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新关卡\r\n  update: async (id: string, params: UpdateLevelParams): Promise<Level> => {\r\n    const response = await request.patch<Level>(`/levels/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除关卡\r\n  delete: async (id: string): Promise<void> => {\r\n    await request.delete(`/levels/${id}`);\r\n  },\r\n\r\n  // 向关卡添加词组\r\n  addPhrase: async (\r\n    levelId: string,\r\n    params: AddPhraseToLevelParams\r\n  ): Promise<Level> => {\r\n    const response = await request.post<Level>(\r\n      `/levels/${levelId}/phrases`,\r\n      params\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 从关卡移除词组\r\n  removePhrase: async (levelId: string, phraseId: string): Promise<Level> => {\r\n    const response = await request.delete<Level>(\r\n      `/levels/${levelId}/phrases/${phraseId}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 获取关卡统计\r\n  getCount: async (): Promise<{\r\n    total: number;\r\n    maxLevels: number;\r\n    remaining: number;\r\n  }> => {\r\n    const response = await request.get<{\r\n      total: number;\r\n      maxLevels: number;\r\n      remaining: number;\r\n    }>(\"/levels/count\");\r\n    return response.data;\r\n  },\r\n\r\n  // 根据难度获取关卡\r\n  getByDifficulty: async (difficulty: number): Promise<Level[]> => {\r\n    const response = await request.get<Level[]>(\r\n      `/levels/difficulty/${difficulty}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 获取关卡详细信息（包含词组详情）\r\n  getWithPhrases: async (id: string): Promise<Level & { phrases: any[] }> => {\r\n    const response = await request.get<Level & { phrases: any[] }>(\r\n      `/levels/${id}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 获取关卡星级统计\r\n  getLevelStarAnalytics: async (\r\n    levelId: string\r\n  ): Promise<LevelStarAnalytics> => {\r\n    const response = await request.get<LevelStarAnalytics>(\r\n      `/levels/${levelId}/star-analytics`\r\n    );\r\n    return response.data;\r\n  },\r\n};\r\n\r\n// 保持向后兼容的导出\r\nexport const getLevels = levelService.getAll;\r\n"], "names": [], "mappings": ";;;;AAAA;;AA2EO,MAAM,eAAe;IAC1B,kBAAkB;IAClB,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAoB,WAAW;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,cAAc;QACZ,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAU;QAC5C,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAQ,CAAC,QAAQ,EAAE,IAAI;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAQ,WAAW;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,KAAK,CAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI;IACtC;IAEA,UAAU;IACV,WAAW,OACT,SACA;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CACjC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAC5B;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,cAAc,OAAO,SAAiB;QACpC,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CACnC,CAAC,QAAQ,EAAE,QAAQ,SAAS,EAAE,UAAU;QAE1C,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,UAAU;QAKR,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAI/B;QACH,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,iBAAiB,OAAO;QACtB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,CAAC,mBAAmB,EAAE,YAAY;QAEpC,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,CAAC,QAAQ,EAAE,IAAI;QAEjB,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,uBAAuB,OACrB;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,CAAC,QAAQ,EAAE,QAAQ,eAAe,CAAC;QAErC,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY,aAAa,MAAM", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/userService.ts"], "sourcesContent": ["import { api, successApi } from \"./request\";\r\n\r\n// 用户数据类型\r\nexport interface User {\r\n  id: string; // 8位随机数字ID\r\n  phone: string; // 手机号（必填）\r\n  openid?: string; // 微信openid（可选）\r\n  nickname?: string;\r\n  avatarUrl?: string;\r\n  unlockedLevels: number;\r\n  completedLevelIds: string[];\r\n  totalGames: number;\r\n  totalCompletions: number;\r\n  lastPlayTime: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  // 每日解锁限制相关字段\r\n  isVip: boolean; // VIP状态\r\n  dailyUnlockLimit: number; // 每日解锁限制\r\n  dailyUnlockCount: number; // 当日解锁次数\r\n  dailyShared: boolean; // 当日是否已分享\r\n  lastPlayDate: string; // 最后游戏日期\r\n  totalShares: number; // 总分享次数\r\n}\r\n\r\n// 创建用户参数\r\nexport interface CreateUserParams {\r\n  phone: string; // 手机号（必填）\r\n  openid?: string; // 微信openid（可选）\r\n  nickname?: string;\r\n  avatarUrl?: string;\r\n}\r\n\r\n// 更新用户参数\r\nexport interface UpdateUserParams {\r\n  phone?: string; // 手机号\r\n  openid?: string; // 微信openid\r\n  nickname?: string;\r\n  avatarUrl?: string;\r\n  unlockedLevels?: number;\r\n  completedLevelIds?: string[];\r\n  totalGames?: number;\r\n  totalCompletions?: number;\r\n  isVip?: boolean; // VIP状态\r\n  dailyUnlockLimit?: number; // 每日解锁限制\r\n}\r\n\r\n// 完成关卡参数\r\nexport interface CompleteLevelParams {\r\n  levelId: string;\r\n}\r\n\r\n// 设置VIP套餐参数（对应API的SetVipPackageDto）\r\nexport interface SetVipParams {\r\n  packageId: string; // VIP套餐ID，如 \"vip_custom_111111d_nfow\"\r\n  reason?: string; // 操作原因\r\n}\r\n\r\n// 取消VIP参数（对应API的CancelVipDto）\r\nexport interface CancelVipParams {\r\n  reason?: string; // 取消原因\r\n  immediate: boolean; // 是否立即生效\r\n}\r\n\r\n// VIP状态响应（对应API的VipStatusResponseDto）\r\nexport interface VipStatusResponse {\r\n  id: string;\r\n  nickname?: string;\r\n  isVip: boolean;\r\n  packageId?: string;\r\n  packageName?: string;\r\n  vipExpiresAt?: string;\r\n  dailyUnlockLimit: number;\r\n  updatedAt: string;\r\n  message: string;\r\n}\r\n\r\n// 批量VIP套餐操作参数（对应API的BatchVipPackageOperationDto）\r\nexport interface BatchVipOperationParams {\r\n  userIds: string[];\r\n  packageId: string; // VIP套餐ID，必需\r\n  reason?: string; // 操作原因\r\n}\r\n\r\n// 用户统计数据\r\nexport interface UserStats {\r\n  totalGames: number;\r\n  totalCompletions: number;\r\n  unlockedLevels: number;\r\n  completedLevels: number;\r\n  completionRate: number;\r\n  // 每日解锁统计\r\n  dailyUnlockCount: number;\r\n  dailyUnlockLimit: number;\r\n  remainingUnlocks: number;\r\n  totalShares: number;\r\n  isVip: boolean;\r\n}\r\n\r\n// 用户服务\r\nexport const userService = {\r\n  // 获取所有用户\r\n  getAll: async (): Promise<{\r\n    users: User[];\r\n    total: number;\r\n  }> => {\r\n    const response = await api.get<any>(\"/users\");\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取用户\r\n  getById: async (id: string): Promise<User> => {\r\n    const response = await api.get<User>(`/users/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据openid获取用户\r\n  getByOpenid: async (openid: string): Promise<User> => {\r\n    const response = await api.get<User>(`/users/by-openid?openid=${openid}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据手机号获取用户\r\n  getByPhone: async (phone: string): Promise<User> => {\r\n    const response = await api.get<User>(`/users/by-phone?phone=${phone}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建用户\r\n  create: async (params: CreateUserParams): Promise<User> => {\r\n    const response = await successApi.post<User>(\r\n      \"/users\",\r\n      params,\r\n      \"用户创建成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 更新用户\r\n  update: async (id: string, params: UpdateUserParams): Promise<User> => {\r\n    const response = await successApi.patch<User>(\r\n      `/users/${id}`,\r\n      params,\r\n      \"用户更新成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 删除用户\r\n  delete: async (id: string): Promise<void> => {\r\n    await successApi.delete(`/users/${id}`, \"用户删除成功\");\r\n  },\r\n\r\n  // 用户完成关卡\r\n  completeLevel: async (\r\n    id: string,\r\n    params: CompleteLevelParams\r\n  ): Promise<User> => {\r\n    const response = await api.post<User>(\r\n      `/users/${id}/complete-level`,\r\n      params\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 用户开始游戏\r\n  startGame: async (id: string): Promise<User> => {\r\n    const response = await api.post<User>(`/users/${id}/start-game`);\r\n    return response.data;\r\n  },\r\n\r\n  // 获取用户统计\r\n  getStats: async (id: string): Promise<UserStats> => {\r\n    const response = await api.get<UserStats>(`/users/${id}/stats`);\r\n    return response.data;\r\n  },\r\n\r\n  // 重置用户进度\r\n  resetProgress: async (id: string): Promise<User> => {\r\n    const response = await successApi.post<User>(\r\n      `/users/${id}/reset-progress`,\r\n      {},\r\n      \"用户进度重置成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 设置用户VIP套餐\r\n  setVipStatus: async (\r\n    id: string,\r\n    params: SetVipParams\r\n  ): Promise<VipStatusResponse> => {\r\n    const response = await successApi.post<VipStatusResponse>(\r\n      `/users/${id}/set-vip-package`,\r\n      {\r\n        packageId: params.packageId,\r\n        reason: params.reason || \"管理员手动设置\",\r\n      },\r\n      \"设置VIP成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 取消用户VIP状态\r\n  cancelVipStatus: async (\r\n    id: string,\r\n    params: CancelVipParams\r\n  ): Promise<VipStatusResponse> => {\r\n    const response = await successApi.post<VipStatusResponse>(\r\n      `/users/${id}/cancel-vip`,\r\n      params,\r\n      \"取消VIP成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 批量VIP操作（统一接口）\r\n  batchVipOperation: async (params: BatchVipOperationParams): Promise<void> => {\r\n    await api.post(\"/users/batch-vip-package\", params);\r\n  },\r\n\r\n  // 批量设置用户VIP套餐\r\n  batchSetVipStatus: async (\r\n    userIds: string[],\r\n    params: SetVipParams\r\n  ): Promise<void> => {\r\n    await userService.batchVipOperation({\r\n      userIds,\r\n      packageId: params.packageId,\r\n      reason: params.reason || \"管理员批量设置\",\r\n    });\r\n  },\r\n\r\n  // 批量取消用户VIP状态（使用取消VIP接口）\r\n  batchCancelVipStatus: async (\r\n    userIds: string[],\r\n    params: CancelVipParams\r\n  ): Promise<void> => {\r\n    // 批量取消VIP需要循环调用单个取消接口\r\n    const promises = userIds.map((userId) =>\r\n      userService.cancelVipStatus(userId, params)\r\n    );\r\n    await Promise.all(promises);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAoGO,MAAM,cAAc;IACzB,SAAS;IACT,QAAQ;QAIN,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAM;QACpC,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,CAAC,sBAAsB,EAAE,OAAO;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,UACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,KAAK,CACrC,CAAC,OAAO,EAAE,IAAI,EACd,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,6HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IAC1C;IAEA,SAAS;IACT,eAAe,OACb,IACA;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,IAAI,CAC7B,CAAC,OAAO,EAAE,GAAG,eAAe,CAAC,EAC7B;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,IAAI,CAAO,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,CAAC,OAAO,EAAE,GAAG,eAAe,CAAC,EAC7B,CAAC,GACD;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,cAAc,OACZ,IACA;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,CAAC,OAAO,EAAE,GAAG,gBAAgB,CAAC,EAC9B;YACE,WAAW,OAAO,SAAS;YAC3B,QAAQ,OAAO,MAAM,IAAI;QAC3B,GACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,iBAAiB,OACf,IACA;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,EACzB,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,mBAAmB,OAAO;QACxB,MAAM,6HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,4BAA4B;IAC7C;IAEA,cAAc;IACd,mBAAmB,OACjB,SACA;QAEA,MAAM,YAAY,iBAAiB,CAAC;YAClC;YACA,WAAW,OAAO,SAAS;YAC3B,QAAQ,OAAO,MAAM,IAAI;QAC3B;IACF;IAEA,yBAAyB;IACzB,sBAAsB,OACpB,SACA;QAEA,sBAAsB;QACtB,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAC,SAC5B,YAAY,eAAe,CAAC,QAAQ;QAEtC,MAAM,QAAQ,GAAG,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/shareService.ts"], "sourcesContent": ["import request from './request';\r\nimport type {\r\n  CreateShareConfigRequest,\r\n  UpdateShareConfigRequest,\r\n  ShareConfigResponse,\r\n} from '../types/share';\r\n\r\n// 分享管理服务\r\nexport class ShareService {\r\n  // 获取所有分享配置\r\n  static async getAllShareConfigs(): Promise<ShareConfigResponse[]> {\r\n    const response = await request.get('/share');\r\n    return response.data;\r\n  }\r\n\r\n  // 获取启用的分享配置\r\n  static async getActiveShareConfigs(): Promise<ShareConfigResponse[]> {\r\n    const response = await request.get('/share/active');\r\n    return response.data;\r\n  }\r\n\r\n  // 获取默认分享配置\r\n  static async getDefaultShareConfig(): Promise<ShareConfigResponse | null> {\r\n    const response = await request.get('/share/default');\r\n    return response.data;\r\n  }\r\n\r\n  // 根据类型获取分享配置\r\n  static async getShareConfigByType(type: string): Promise<ShareConfigResponse | null> {\r\n    const response = await request.get(`/share/type/${type}`);\r\n    return response.data;\r\n  }\r\n\r\n  // 根据ID获取分享配置\r\n  static async getShareConfigById(id: string): Promise<ShareConfigResponse> {\r\n    const response = await request.get(`/share/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  // 创建分享配置\r\n  static async createShareConfig(data: CreateShareConfigRequest): Promise<ShareConfigResponse> {\r\n    const response = await request.post('/share', data);\r\n    return response.data;\r\n  }\r\n\r\n  // 更新分享配置\r\n  static async updateShareConfig(id: string, data: UpdateShareConfigRequest): Promise<ShareConfigResponse> {\r\n    const response = await request.patch(`/share/${id}`, data);\r\n    return response.data;\r\n  }\r\n\r\n  // 启用/禁用分享配置\r\n  static async toggleShareConfig(id: string): Promise<ShareConfigResponse> {\r\n    const response = await request.put(`/share/${id}/toggle`);\r\n    return response.data;\r\n  }\r\n\r\n  // 删除分享配置\r\n  static async deleteShareConfig(id: string): Promise<{ message: string }> {\r\n    const response = await request.delete(`/share/${id}`);\r\n    return response.data;\r\n  }\r\n}\r\n\r\n// 分享类型选项\r\nexport const SHARE_TYPE_OPTIONS = [\r\n  {\r\n    value: 'default',\r\n    label: '默认分享',\r\n    description: '通用分享，适用于首页、游戏页等'\r\n  },\r\n  {\r\n    value: 'result',\r\n    label: '结果分享',\r\n    description: '展示用户成绩的分享'\r\n  },\r\n  {\r\n    value: 'level',\r\n    label: '关卡分享',\r\n    description: '邀请好友挑战特定关卡'\r\n  },\r\n  {\r\n    value: 'achievement',\r\n    label: '成就分享',\r\n    description: '展示用户获得的成就'\r\n  },\r\n  {\r\n    value: 'custom',\r\n    label: '自定义分享',\r\n    description: '特殊活动或自定义场景'\r\n  }\r\n];\r\n\r\n// 分享路径模板\r\nexport const SHARE_PATH_TEMPLATES = [\r\n  {\r\n    label: '首页',\r\n    value: '/pages/index/index',\r\n    description: '小程序首页'\r\n  },\r\n  {\r\n    label: '游戏页',\r\n    value: '/pages/game/game',\r\n    description: '游戏主页面'\r\n  },\r\n  {\r\n    label: '关卡页',\r\n    value: '/pages/level/level?id={levelId}',\r\n    description: '特定关卡页面，{levelId}会被替换为实际关卡ID'\r\n  },\r\n  {\r\n    label: '结果页',\r\n    value: '/pages/result/result?score={score}',\r\n    description: '结果展示页面，{score}会被替换为实际分数'\r\n  },\r\n  {\r\n    label: '排行榜',\r\n    value: '/pages/rank/rank',\r\n    description: '排行榜页面'\r\n  }\r\n];\r\n\r\nexport default ShareService;\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAQO,MAAM;IACX,WAAW;IACX,aAAa,qBAAqD;QAChE,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,aAAa,wBAAwD;QACnE,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,aAAa,wBAA6D;QACxE,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,aAAa,qBAAqB,IAAY,EAAuC;QACnF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,aAAa,mBAAmB,EAAU,EAAgC;QACxE,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,kBAAkB,IAA8B,EAAgC;QAC3F,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,UAAU;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,kBAAkB,EAAU,EAAE,IAA8B,EAAgC;QACvG,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,aAAa,kBAAkB,EAAU,EAAgC;QACvE,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,kBAAkB,EAAU,EAAgC;QACvE,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,qBAAqB;IAChC;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAGM,MAAM,uBAAuB;IAClC;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/vipService.ts"], "sourcesContent": ["import request from \"./request\";\r\n\r\n// VIP套餐数据类型\r\nexport interface VipPackage {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  price: number;\r\n  duration: number;\r\n  sortOrder: number;\r\n  isActive: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建/更新VIP套餐参数\r\nexport interface VipPackageParams {\r\n  id?: string;\r\n  name: string;\r\n  description: string;\r\n  price: number;\r\n  duration: number;\r\n  sortOrder: number;\r\n  isActive: boolean;\r\n}\r\n\r\n// 支付订单数据类型\r\nexport interface PaymentOrder {\r\n  id: string;\r\n  userId: string;\r\n  openid: string;\r\n  out_trade_no: string;\r\n  transaction_id?: string;\r\n  description: string;\r\n  total: number;\r\n  status: \"PENDING\" | \"SUCCESS\" | \"FAILED\" | \"CANCELLED\" | \"REFUNDED\";\r\n  vip_package_id: string;\r\n  prepay_id?: string;\r\n  detail?: string;\r\n  attach?: string;\r\n  paid_at?: string;\r\n  expires_at: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\n// VIP套餐服务\r\nexport const vipPackageService = {\r\n  // 获取VIP套餐列表\r\n  async getList(params?: {\r\n    isActive?: boolean;\r\n    sortBy?: string;\r\n    sortOrder?: string;\r\n  }): Promise<VipPackage[]> {\r\n    const response = await request.get<VipPackage[]>(\"/payment/packages\", {\r\n      params,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取VIP套餐\r\n  async getById(id: string): Promise<VipPackage> {\r\n    const response = await request.get(`/payment/packages/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建VIP套餐\r\n  async create(params: VipPackageParams): Promise<VipPackage> {\r\n    const response = await request.post(\"/payment/packages\", params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新VIP套餐\r\n  async update(\r\n    id: string,\r\n    params: Partial<VipPackageParams>\r\n  ): Promise<VipPackage> {\r\n    const response = await request.put(`/payment/packages/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除VIP套餐\r\n  async delete(id: string): Promise<void> {\r\n    await request.delete(`/payment/packages/${id}`);\r\n  },\r\n\r\n  // 切换套餐状态（通过更新接口实现）\r\n  async toggleStatus(id: string, isActive: boolean): Promise<VipPackage> {\r\n    const response = await request.put(`/payment/packages/${id}`, { isActive });\r\n    return response.data;\r\n  },\r\n\r\n  // 删除套餐统计方法\r\n};\r\n\r\n// 支付订单服务\r\nexport const paymentOrderService = {\r\n  // 获取支付订单列表\r\n  async getList(params?: {\r\n    search?: string;\r\n    status?: string;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    page?: number;\r\n    pageSize?: number;\r\n    userId?: string;\r\n    packageId?: string;\r\n  }): Promise<{\r\n    orders: PaymentOrder[];\r\n    total: number;\r\n    page: number;\r\n    pageSize: number;\r\n    totalPages: number;\r\n  }> {\r\n    const response = await request.get(\"/payment/orders\", { params });\r\n    return response.data;\r\n  },\r\n\r\n  // 删除支付订单统计方法\r\n\r\n  // 根据ID获取支付订单\r\n  async getById(id: string): Promise<PaymentOrder> {\r\n    const response = await request.get(`/payment/orders/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 申请退款\r\n  async refund(\r\n    id: string,\r\n    reason?: string\r\n  ): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    refundId?: string;\r\n  }> {\r\n    const response = await request.post(`/payment/orders/${id}/refund`, {\r\n      reason,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 取消订单\r\n  async cancel(out_trade_no: string, userId: string): Promise<void> {\r\n    await request.post(`/payment/cancel/${out_trade_no}`, { userId });\r\n  },\r\n};\r\n\r\n// 删除VIP用户服务，功能已合并到用户管理\r\n\r\n// 导出所有服务\r\nexport default {\r\n  vipPackageService,\r\n  paymentOrderService,\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AA+CO,MAAM,oBAAoB;IAC/B,YAAY;IACZ,MAAM,SAAQ,MAIb;QACC,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAe,qBAAqB;YACpE;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,MAAM,QAAO,MAAwB;QACnC,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,qBAAqB;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,MAAM,QACJ,EAAU,EACV,MAAiC;QAEjC,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,MAAM,QAAO,EAAU;QACrB,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,IAAI;IAChD;IAEA,mBAAmB;IACnB,MAAM,cAAa,EAAU,EAAE,QAAiB;QAC9C,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE;YAAE;QAAS;QACzE,OAAO,SAAS,IAAI;IACtB;AAGF;AAGO,MAAM,sBAAsB;IACjC,WAAW;IACX,MAAM,SAAQ,MASb;QAOC,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;YAAE;QAAO;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IAEb,aAAa;IACb,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QACJ,EAAU,EACV,MAAe;QAMf,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;YAClE;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,YAAoB,EAAE,MAAc;QAC/C,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,cAAc,EAAE;YAAE;QAAO;IACjE;AACF;uCAKe;IACb;IACA;AACF", "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/settingsService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 设置数据类型\r\nexport interface Settings {\r\n  id: string;\r\n  key: string;\r\n  value: string;\r\n  description?: string;\r\n  type: 'string' | 'number' | 'boolean' | 'url';\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建设置参数\r\nexport interface CreateSettingsParams {\r\n  key: string;\r\n  value: string;\r\n  description?: string;\r\n  type: 'string' | 'number' | 'boolean' | 'url';\r\n}\r\n\r\n// 更新设置参数\r\nexport interface UpdateSettingsParams {\r\n  key?: string;\r\n  value?: string;\r\n  description?: string;\r\n  type?: 'string' | 'number' | 'boolean' | 'url';\r\n}\r\n\r\n// 设置服务\r\nexport const settingsService = {\r\n  // 获取所有设置\r\n  async getAll(): Promise<Settings[]> {\r\n    const response = await request.get('/settings');\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取设置\r\n  async getById(id: string): Promise<Settings> {\r\n    const response = await request.get(`/settings/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据键名获取设置\r\n  async getByKey(key: string): Promise<Settings> {\r\n    const response = await request.get(`/settings/key/${key}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新设置\r\n  async update(id: string, params: UpdateSettingsParams): Promise<Settings> {\r\n    const response = await request.patch(`/settings/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据键名更新设置值\r\n  async updateByKey(key: string, value: string): Promise<Settings> {\r\n    const response = await request.patch(`/settings/key/${key}`, { value });\r\n    return response.data;\r\n  },\r\n\r\n  // 初始化默认设置（保留用于手动触发）\r\n  async initializeDefaults(): Promise<void> {\r\n    await request.post('/settings/initialize');\r\n  },\r\n\r\n  // 批量更新小程序配置\r\n  async updateAppConfig(config: {\r\n    helpUrl?: string;\r\n    backgroundMusicUrl?: string;\r\n  }): Promise<void> {\r\n    const promises = [];\r\n    \r\n    if (config.helpUrl !== undefined) {\r\n      promises.push(this.updateByKey('help_url', config.helpUrl));\r\n    }\r\n    \r\n    if (config.backgroundMusicUrl !== undefined) {\r\n      promises.push(this.updateByKey('background_music_url', config.backgroundMusicUrl));\r\n    }\r\n    \r\n    await Promise.all(promises);\r\n  },\r\n\r\n  // 获取小程序配置\r\n  async getAppConfig(): Promise<{\r\n    helpUrl: string;\r\n    backgroundMusicUrl: string;\r\n  }> {\r\n    try {\r\n      const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([\r\n        this.getByKey('help_url').catch(() => null),\r\n        this.getByKey('background_music_url').catch(() => null),\r\n      ]);\r\n\r\n      return {\r\n        helpUrl: helpUrlSetting?.value || '',\r\n        backgroundMusicUrl: backgroundMusicSetting?.value || '',\r\n      };\r\n    } catch (error) {\r\n      console.error('获取小程序配置失败:', error);\r\n      // 如果设置不存在，返回默认值\r\n      return {\r\n        helpUrl: '',\r\n        backgroundMusicUrl: '',\r\n      };\r\n    }\r\n  },\r\n\r\n  // 测试微信小程序获取设置接口\r\n  async testWeixinAppSettings(): Promise<{\r\n    helpUrl: string;\r\n    backgroundMusicUrl: string;\r\n  }> {\r\n    try {\r\n      const response = await request.get('/weixin/app-settings');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('测试微信小程序设置接口失败:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // 测试微信小程序全局配置接口\r\n  async testWeixinGlobalConfig(): Promise<any> {\r\n    try {\r\n      const response = await request.get('/weixin/global-config');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('测试微信小程序全局配置接口失败:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AA8BO,MAAM,kBAAkB;IAC7B,SAAS;IACT,MAAM;QACJ,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,MAAM,UAAS,GAAW;QACxB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,EAAU,EAAE,MAA4B;QACnD,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,aAAY,GAAW,EAAE,KAAa;QAC1C,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE;YAAE;QAAM;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,MAAM;QACJ,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACrB;IAEA,YAAY;IACZ,MAAM,iBAAgB,MAGrB;QACC,MAAM,WAAW,EAAE;QAEnB,IAAI,OAAO,OAAO,KAAK,WAAW;YAChC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,OAAO,OAAO;QAC3D;QAEA,IAAI,OAAO,kBAAkB,KAAK,WAAW;YAC3C,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,OAAO,kBAAkB;QAClF;QAEA,MAAM,QAAQ,GAAG,CAAC;IACpB;IAEA,UAAU;IACV,MAAM;QAIJ,IAAI;YACF,MAAM,CAAC,gBAAgB,uBAAuB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjE,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,CAAC,IAAM;gBACtC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,KAAK,CAAC,IAAM;aACnD;YAED,OAAO;gBACL,SAAS,gBAAgB,SAAS;gBAClC,oBAAoB,wBAAwB,SAAS;YACvD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,gBAAgB;YAChB,OAAO;gBACL,SAAS;gBACT,oBAAoB;YACtB;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM;QAIJ,IAAI;YACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YACnC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YACnC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/levelTagService.ts"], "sourcesContent": ["import request from './request';\n\n// 标签数据处理函数，确保所有字段都有合适的默认值\nconst processTagData = (tag: any): LevelTag => ({\n  id: tag.id || '',\n  name: tag.name || '',\n  description: tag.description || '',\n  color: tag.color || '#1890ff',\n  isVip: Bo<PERSON>an(tag.isVip),\n  status: tag.status || 'active',\n  icon: tag.icon,\n  createdAt: tag.createdAt || new Date().toISOString(),\n  updatedAt: tag.updatedAt || new Date().toISOString(),\n});\n\n// 标签数据类型\nexport interface LevelTag {\n  id: string;\n  name: string;\n  description?: string;\n  color: string;\n  isVip: boolean;\n  status: string; // 'active' | 'inactive'\n  icon?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n\n\n// 创建标签参数\nexport interface CreateLevelTagParams {\n  name: string;\n  description?: string;\n  color?: string;\n  isVip?: boolean;\n  status?: string; // 'active' | 'inactive'\n  icon?: string;\n}\n\n// 更新标签参数\nexport interface UpdateLevelTagParams {\n  name?: string;\n  description?: string;\n  color?: string;\n  isVip?: boolean;\n  status?: string; // 'active' | 'inactive'\n  icon?: string;\n}\n\n// 标签管理服务\nexport const levelTagService = {\n  // 获取所有标签\n  getAll: async (): Promise<LevelTag[]> => {\n    const response = await request.get<any[]>('/tags');\n    // 确保每个标签都有正确的字段和默认值\n    const tags = response.data || [];\n    return tags.map(processTagData);\n  },\n\n  // 根据ID获取标签\n  getById: async (id: string): Promise<LevelTag> => {\n    const response = await request.get<any>(`/tags/${id}`);\n    return processTagData(response.data);\n  },\n\n  // 创建标签\n  create: async (params: CreateLevelTagParams): Promise<LevelTag> => {\n    const response = await request.post<any>('/tags', params);\n    return processTagData(response.data);\n  },\n\n  // 更新标签\n  update: async (id: string, params: UpdateLevelTagParams): Promise<LevelTag> => {\n    const response = await request.put<any>(`/tags/${id}`, params);\n    return processTagData(response.data);\n  },\n\n  // 删除标签\n  delete: async (id: string): Promise<void> => {\n    await request.delete(`/tags/${id}`);\n  },\n};\n\n// 保持向后兼容的导出\nexport const getLevelTags = levelTagService.getAll;\nexport const createLevelTag = levelTagService.create;\nexport const updateLevelTag = levelTagService.update;\nexport const deleteLevelTag = levelTagService.delete;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,0BAA0B;AAC1B,MAAM,iBAAiB,CAAC,MAAuB,CAAC;QAC9C,IAAI,IAAI,EAAE,IAAI;QACd,MAAM,IAAI,IAAI,IAAI;QAClB,aAAa,IAAI,WAAW,IAAI;QAChC,OAAO,IAAI,KAAK,IAAI;QACpB,OAAO,QAAQ,IAAI,KAAK;QACxB,QAAQ,IAAI,MAAM,IAAI;QACtB,MAAM,IAAI,IAAI;QACd,WAAW,IAAI,SAAS,IAAI,IAAI,OAAO,WAAW;QAClD,WAAW,IAAI,SAAS,IAAI,IAAI,OAAO,WAAW;IACpD,CAAC;AAsCM,MAAM,kBAAkB;IAC7B,SAAS;IACT,QAAQ;QACN,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAQ;QAC1C,oBAAoB;QACpB,MAAM,OAAO,SAAS,IAAI,IAAI,EAAE;QAChC,OAAO,KAAK,GAAG,CAAC;IAClB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAM,CAAC,MAAM,EAAE,IAAI;QACrD,OAAO,eAAe,SAAS,IAAI;IACrC;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAM,SAAS;QAClD,OAAO,eAAe,SAAS,IAAI;IACrC;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAM,CAAC,MAAM,EAAE,IAAI,EAAE;QACvD,OAAO,eAAe,SAAS,IAAI;IACrC;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,6HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI;IACpC;AACF;AAGO,MAAM,eAAe,gBAAgB,MAAM;AAC3C,MAAM,iBAAiB,gBAAgB,MAAM;AAC7C,MAAM,iBAAiB,gBAAgB,MAAM;AAC7C,MAAM,iBAAiB,gBAAgB,MAAM", "debugId": null}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/userStarService.ts"], "sourcesContent": ["import request from \"./request\";\n\n// 用户星级数据类型（基于API文档UserStarItemDto）\nexport interface UserStar {\n  id: string;\n  userId: string;\n  levelId: string;\n  stars: number; // 1-5星级评分\n  completedAt: string; // ISO日期时间格式\n  playTime: number; // 游戏时长（秒）\n  user?: {\n    id: string;\n    nickname: string;\n    avatar?: string;\n  };\n  level?: {\n    id: string;\n    title: string;\n    difficulty: number;\n  };\n}\n\n// 用户星级列表响应类型（基于API文档UserStarListResponseDto）\nexport interface UserStarListResponse {\n  data: UserStar[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\n// 删除统计相关类型定义\n\n// 查询参数类型（基于API文档接口参数）\nexport interface UserStarQueryParams {\n  startDate?: string; // 开始日期，格式：YYYY-MM-DD\n  endDate?: string; // 结束日期，格式：YYYY-MM-DD\n  levelId?: string; // 关卡ID\n  stars?: number; // 星级筛选，1-5\n  userId?: string; // 用户ID\n  page?: number; // 页码，默认1\n  pageSize?: number; // 每页数量，默认20，最大100\n}\n\n// 删除统计查询参数类型\n\n// 导出参数类型\nexport interface UserStarExportParams {\n  startDate?: string;\n  endDate?: string;\n  levelId?: string;\n  stars?: number;\n  userId?: string;\n  format?: \"csv\" | \"excel\";\n}\n\n// 用户星级管理服务\nexport const userStarService = {\n  // 获取用户星级数据（基于API文档：GET /api/v1/admin/user-stars）\n  getAll: async (\n    params?: UserStarQueryParams\n  ): Promise<UserStarListResponse> => {\n    const response = await request.get<UserStarListResponse>(\"/user-stars\", {\n      params,\n    });\n    return response.data;\n  },\n\n  // 删除用户星级统计方法\n\n  // 导出用户星级数据（基于API文档：GET /api/v1/admin/user-stars/export）\n  exportData: async (params?: UserStarExportParams): Promise<Blob> => {\n    const response = await request.get(\"/user-stars/export\", {\n      params,\n      responseType: \"blob\",\n    });\n    return response.data;\n  },\n\n  // 删除关卡星级分析方法\n};\n\n// 保持向后兼容的导出\nexport const getUserStars = userStarService.getAll;\n"], "names": [], "mappings": ";;;;AAAA;;AAyDO,MAAM,kBAAkB;IAC7B,iDAAiD;IACjD,QAAQ,OACN;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAuB,eAAe;YACtE;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IAEb,wDAAwD;IACxD,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;YACvD;YACA,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;AAGF;AAGO,MAAM,eAAe,gBAAgB,MAAM", "debugId": null}}, {"offset": {"line": 1436, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/userFavoriteService.ts"], "sourcesContent": ["import request from \"./request\";\n\n// 用户收藏数据类型（基于API文档UserFavoriteAdminItemDto）\nexport interface UserFavorite {\n  id: string;\n  userId: string;\n  levelId: string;\n  createdAt: string; // ISO日期时间格式\n  user?: {\n    id: string;\n    nickname: string;\n    avatar?: string;\n  };\n  level?: {\n    id: string;\n    title: string;\n    difficulty: number;\n    isVip?: boolean;\n  };\n}\n\n// 用户收藏列表响应类型（基于API文档UserFavoriteAdminListResponseDto）\nexport interface UserFavoriteListResponse {\n  data: UserFavorite[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\n// 删除统计相关类型定义\n\n// 查询参数类型（基于API文档接口参数）\nexport interface UserFavoriteQueryParams {\n  startDate?: string; // 开始日期，格式：YYYY-MM-DD\n  endDate?: string; // 结束日期，格式：YYYY-MM-DD\n  levelId?: string; // 关卡ID\n  userId?: string; // 用户ID\n  difficulty?: number; // 关卡难度，1-5\n  page?: number; // 页码，默认1\n  pageSize?: number; // 每页数量，默认20，最大100\n}\n\n// 删除统计查询参数类型\n\n// 导出参数类型\nexport interface UserFavoriteExportParams {\n  startDate?: string;\n  endDate?: string;\n  levelId?: string;\n  userId?: string;\n  difficulty?: number;\n  format?: \"csv\" | \"excel\";\n}\n\n// 用户收藏管理服务\nexport const userFavoriteService = {\n  // 获取用户收藏数据（基于API文档：GET /api/v1/admin/user-favorites）\n  getAll: async (\n    params?: UserFavoriteQueryParams\n  ): Promise<UserFavoriteListResponse> => {\n    const response = await request.get<UserFavoriteListResponse>(\n      \"/user-favorites\",\n      { params }\n    );\n    return response.data;\n  },\n\n  // 删除收藏统计方法\n\n  // 导出收藏数据（基于API文档：GET /api/v1/admin/user-favorites/export）\n  exportData: async (params?: UserFavoriteExportParams): Promise<Blob> => {\n    const response = await request.get(\"/user-favorites/export\", {\n      params,\n      responseType: \"blob\",\n    });\n    return response.data;\n  },\n};\n\n// 保持向后兼容的导出\nexport const getUserFavorites = userFavoriteService.getAll;\n"], "names": [], "mappings": ";;;;AAAA;;AAwDO,MAAM,sBAAsB;IACjC,qDAAqD;IACrD,QAAQ,OACN;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,mBACA;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IAEX,0DAA0D;IAC1D,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B;YAC3D;YACA,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,mBAAmB,oBAAoB,MAAM", "debugId": null}}, {"offset": {"line": 1470, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/activationCodeService.ts"], "sourcesContent": ["import { api, successApi } from \"./request\";\n\n// 激活码数据类型（根据B端API文档）\nexport interface ActivationCode {\n  code: string;\n  packageId: string;\n  packageName: string;\n  status: string; // 'unused' | 'used' | 'expired' | 'disabled'\n  maxRedemptions: number; // 最大兑换次数，-1表示无限次\n  currentRedemptions: number; // 当前已兑换次数\n  redemptionHistory?: string[]; // 兑换记录\n  usedBy?: string; // 已废弃，保留兼容性\n  usedAt?: string; // 已废弃，保留兼容性\n  expireDate: string;\n  source?: string;\n  batchId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 生成激活码参数（根据B端API文档）\nexport interface GenerateActivationCodeParams {\n  packageId: string;\n  count: number;\n  expireDate?: string;\n  source?: string;\n}\n\n// 手动创建激活码参数\nexport interface CreateActivationCodeParams {\n  customCode?: string; // 自定义激活码，如果不提供则自动生成\n  packageId: string;\n  expireDate?: string;\n  source?: string;\n  maxRedemptions?: number; // 最大兑换次数，-1表示无限次，默认为1\n}\n\n// 删除激活码参数\nexport interface DeleteActivationCodeParams {\n  reason?: string; // 删除原因\n}\n\n// 批量生成激活码参数\nexport interface BatchGenerateParams {\n  packageId: string;\n  count: number;\n  expireDate?: string;\n  source?: string;\n  prefix?: string; // 激活码前缀\n  maxRedemptions?: number; // 最大兑换次数，-1表示无限次，默认为1\n}\n\n// 套餐数据类型\nexport interface Package {\n  id: string;\n  name: string;\n  description?: string;\n  type: string;\n  duration: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数类型（根据B端API文档）\nexport interface ActivationCodeQueryParams {\n  status?: \"unused\" | \"used\" | \"expired\" | \"disabled\";\n  packageId?: string;\n  startDate?: string;\n  endDate?: string;\n  page?: number;\n  pageSize?: number;\n}\n\n// 激活码管理服务\nexport const activationCodeService = {\n  // 获取所有激活码\n  getAll: async (\n    params?: ActivationCodeQueryParams\n  ): Promise<{\n    codes: ActivationCode[];\n    total: number;\n  }> => {\n    const response = await api.get(\"/activation-codes\", { params });\n    return response.data;\n  },\n\n  // 根据激活码获取详情\n  getByCode: async (code: string): Promise<ActivationCode> => {\n    const response = await api.get<ActivationCode>(`/activation-codes/${code}`);\n    return response.data;\n  },\n\n  // 生成激活码\n  generate: async (\n    params: GenerateActivationCodeParams\n  ): Promise<{\n    codes: string[];\n    batchId: string;\n    count: number;\n    package: Package;\n  }> => {\n    const response = await successApi.post(\n      \"/activation-codes/generate\",\n      params,\n      \"激活码生成成功\"\n    );\n    return response.data;\n  },\n\n  // 手动创建单个激活码\n  create: async (\n    params: CreateActivationCodeParams\n  ): Promise<ActivationCode> => {\n    const response = await successApi.post<ActivationCode>(\n      \"/activation-codes/create\",\n      params,\n      \"激活码创建成功\"\n    );\n    return response.data;\n  },\n\n  // 批量生成激活码\n  batchGenerate: async (\n    params: BatchGenerateParams\n  ): Promise<{\n    codes: string[];\n    batchId: string;\n    count: number;\n    package: Package;\n    successCount: number;\n    failedCount: number;\n  }> => {\n    const response = await successApi.post(\n      \"/activation-codes/generate\",\n      params,\n      \"批量生成激活码成功\"\n    );\n    return response.data;\n  },\n\n  // 禁用激活码\n  disable: async (code: string): Promise<void> => {\n    await successApi.put(\n      `/activation-codes/${code}/disable`,\n      {},\n      \"激活码已禁用\"\n    );\n  },\n\n  // 启用激活码\n  enable: async (code: string): Promise<void> => {\n    await successApi.put(\n      `/activation-codes/${code}/enable`,\n      {},\n      \"激活码已启用\"\n    );\n  },\n\n  // 删除统计相关方法\n\n  // 获取所有套餐（VIP套餐，用于激活码兑换）\n  getAllPackages: async (): Promise<Package[]> => {\n    const response = await api.get<Package[]>(\"/payment/packages\");\n    return response.data || [];\n  },\n\n  // 获取热门套餐\n  getPopularPackages: async (limit?: number): Promise<Package[]> => {\n    const response = await api.get<Package[]>(\"/payment/packages/popular\", {\n      params: { limit },\n    });\n    return response.data;\n  },\n\n  // 删除激活码\n  delete: async (\n    code: string,\n    params?: DeleteActivationCodeParams\n  ): Promise<{ message: string }> => {\n    const response = await successApi.delete<{ message: string }>(\n      `/activation-codes/${code}`,\n      \"激活码删除成功\",\n      {\n        data: params || {},\n      }\n    );\n    return response.data;\n  },\n};\n\n// 保持向后兼容的导出\nexport const getActivationCodes = activationCodeService.getAll;\nexport const generateActivationCodes = activationCodeService.generate;\nexport const createActivationCode = activationCodeService.create;\nexport const batchGenerateActivationCodes = activationCodeService.batchGenerate;\nexport const disableActivationCode = activationCodeService.disable;\nexport const enableActivationCode = activationCodeService.enable;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AA2EO,MAAM,wBAAwB;IACnC,UAAU;IACV,QAAQ,OACN;QAKA,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAC,qBAAqB;YAAE;QAAO;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAiB,CAAC,kBAAkB,EAAE,MAAM;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,UAAU,OACR;QAOA,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,8BACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,QAAQ,OACN;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,4BACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,eAAe,OACb;QASA,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,8BACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,SAAS,OAAO;QACd,MAAM,6HAAA,CAAA,aAAU,CAAC,GAAG,CAClB,CAAC,kBAAkB,EAAE,KAAK,QAAQ,CAAC,EACnC,CAAC,GACD;IAEJ;IAEA,QAAQ;IACR,QAAQ,OAAO;QACb,MAAM,6HAAA,CAAA,aAAU,CAAC,GAAG,CAClB,CAAC,kBAAkB,EAAE,KAAK,OAAO,CAAC,EAClC,CAAC,GACD;IAEJ;IAEA,WAAW;IAEX,wBAAwB;IACxB,gBAAgB;QACd,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAY;QAC1C,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA,SAAS;IACT,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,MAAG,CAAC,GAAG,CAAY,6BAA6B;YACrE,QAAQ;gBAAE;YAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,QAAQ,OACN,MACA;QAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,aAAU,CAAC,MAAM,CACtC,CAAC,kBAAkB,EAAE,MAAM,EAC3B,WACA;YACE,MAAM,UAAU,CAAC;QACnB;QAEF,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,qBAAqB,sBAAsB,MAAM;AACvD,MAAM,0BAA0B,sBAAsB,QAAQ;AAC9D,MAAM,uBAAuB,sBAAsB,MAAM;AACzD,MAAM,+BAA+B,sBAAsB,aAAa;AACxE,MAAM,wBAAwB,sBAAsB,OAAO;AAC3D,MAAM,uBAAuB,sBAAsB,MAAM", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/index.ts"], "sourcesContent": ["// 统一导出所有API服务\r\nexport * from './request';\r\nexport * from './authService';\r\nexport * from './phraseService';\r\nexport * from './thesaurusService';\r\nexport * from './levelService';\r\nexport * from './userService';\r\nexport * from './shareService';\r\nexport * from './vipService';\r\nexport * from './settingsService';\r\nexport * from './levelTagService';\r\nexport * from './userStarService';\r\nexport * from './userFavoriteService';\r\nexport * from './activationCodeService';\r\n\r\n// 也可以作为默认导出\r\nimport { authService } from './authService';\r\nimport { phraseService } from './phraseService';\r\nimport { thesaurusService } from './thesaurusService';\r\nimport { levelService } from './levelService';\r\nimport { userService } from './userService';\r\nimport { ShareService } from './shareService';\r\nimport { settingsService } from './settingsService';\r\nimport { levelTagService } from './levelTagService';\r\nimport { userStarService } from './userStarService';\r\nimport { userFavoriteService } from './userFavoriteService';\r\nimport { activationCodeService } from './activationCodeService';\r\n\r\nexport const services = {\r\n  auth: authService,\r\n  phrase: phraseService,\r\n  thesaurus: thesaurusService,\r\n  level: levelService,\r\n  user: userService,\r\n  share: ShareService,\r\n  settings: settingsService,\r\n  levelTag: levelTagService,\r\n  userStar: userStarService,\r\n  userFavorite: userFavoriteService,\r\n  activationCode: activationCodeService,\r\n};\r\n"], "names": [], "mappings": "AAAA,cAAc;;;;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AAeO,MAAM,WAAW;IACtB,MAAM,iIAAA,CAAA,cAAW;IACjB,QAAQ,mIAAA,CAAA,gBAAa;IACrB,WAAW,sIAAA,CAAA,mBAAgB;IAC3B,OAAO,kIAAA,CAAA,eAAY;IACnB,MAAM,iIAAA,CAAA,cAAW;IACjB,OAAO,kIAAA,CAAA,eAAY;IACnB,UAAU,qIAAA,CAAA,kBAAe;IACzB,UAAU,qIAAA,CAAA,kBAAe;IACzB,UAAU,qIAAA,CAAA,kBAAe;IACzB,cAAc,yIAAA,CAAA,sBAAmB;IACjC,gBAAgB,2IAAA,CAAA,wBAAqB;AACvC", "debugId": null}}, {"offset": {"line": 1639, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/app/%28admin%29/users/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport {\r\n  Table, Button, Space, Form, Input, Popconfirm, Card, Tag, Avatar,\r\n  Statistic, Row, Col, Switch, InputNumber, Tooltip, Select, DatePicker, Drawer,\r\n  Timeline, Descriptions, Progress, Tabs, Badge, Checkbox, Upload, Divider\r\n} from 'antd';\r\nimport { Modal, message } from '@/components';\r\nimport {\r\n  PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, ReloadOutlined,\r\n  TrophyOutlined, CrownOutlined, GiftOutlined, SearchOutlined, FilterOutlined,\r\n  ExportOutlined, ImportOutlined, EyeOutlined, HistoryOutlined, SettingOutlined,\r\n  DownloadOutlined, UploadOutlined\r\n} from '@ant-design/icons';\r\nimport type { ColumnsType } from 'antd/es/table';\r\nimport request from '@/services/request';\r\nimport { userService, User, CreateUserParams, UpdateUserParams, UserStats, SetVipParams, CancelVipParams, vipPackageService, VipPackage } from '@/services';\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\n\r\n// VIP统计数据类型\r\ninterface VipStats {\r\n  totalUsers: number;\r\n  vipUsers: number;\r\n  normalUsers: number;\r\n  vipRate: number;\r\n  totalRevenue: number;\r\n  avgDailyUnlocks: number;\r\n}\r\n\r\nexport default function UsersPage() {\r\n  const [users, setUsers] = useState<User[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [statsModalVisible, setStatsModalVisible] = useState(false);\r\n  const [editingUser, setEditingUser] = useState<User | null>(null);\r\n  const [selectedUserStats, setSelectedUserStats] = useState<UserStats | null>(null);\r\n  const [form] = Form.useForm();\r\n\r\n  // 新增状态变量\r\n  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);\r\n  const [searchText, setSearchText] = useState('');\r\n  const [filterVip, setFilterVip] = useState<boolean | undefined>(undefined);\r\n  const [dateRange, setDateRange] = useState<[string, string] | null>(null);\r\n  const [userDetailVisible, setUserDetailVisible] = useState(false);\r\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\r\n  const [userActivityVisible, setUserActivityVisible] = useState(false);\r\n  const [userActivity, setUserActivity] = useState<any[]>([]);\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 20,\r\n    total: 0,\r\n  });\r\n  // 删除不再使用的userStatistics\r\n\r\n  // VIP统计数据\r\n  const [vipStats, setVipStats] = useState<VipStats>({\r\n    totalUsers: 0,\r\n    vipUsers: 0,\r\n    normalUsers: 0,\r\n    vipRate: 0,\r\n    totalRevenue: 0,\r\n    avgDailyUnlocks: 0,\r\n  });\r\n\r\n  // VIP设置相关状态\r\n  const [vipModalVisible, setVipModalVisible] = useState(false);\r\n  const [vipSettingUser, setVipSettingUser] = useState<User | null>(null);\r\n  const [vipPackages, setVipPackages] = useState<VipPackage[]>([]);\r\n  const [vipForm] = Form.useForm();\r\n\r\n  // 获取VIP套餐列表\r\n  const fetchVipPackages = async () => {\r\n    try {\r\n      const packages = await vipPackageService.getList({ isActive: true }) || [];\r\n      // 确保返回的是数组\r\n      setVipPackages(Array.isArray(packages) ? packages : []);\r\n    } catch (error) {\r\n      console.error('获取VIP套餐失败:', error);\r\n      setVipPackages([]); // 出错时设置为空数组\r\n    }\r\n  };\r\n\r\n  // 获取用户列表（支持搜索和分页）\r\n  const fetchUsers = async (params?: {\r\n    search?: string;\r\n    isVip?: boolean;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    page?: number;\r\n    pageSize?: number;\r\n  }) => {\r\n    setLoading(true);\r\n    try {\r\n      const requestParams = {\r\n        ...(params?.search && { search: params.search }),\r\n        ...(params?.isVip !== undefined && { isVip: params.isVip.toString() }),\r\n        ...(params?.startDate && { startDate: params.startDate }),\r\n        ...(params?.endDate && { endDate: params.endDate }),\r\n        page: (params?.page || pagination.current).toString(),\r\n        pageSize: (params?.pageSize || pagination.pageSize).toString(),\r\n      };\r\n\r\n      const response = await request.get('/users', { params: requestParams });\r\n      const result = response.data;\r\n\r\n      setUsers(result.users || []);\r\n      setPagination(prev => ({\r\n        ...prev,\r\n        total: result.total || 0,\r\n        current: params?.page || prev.current,\r\n      }));\r\n\r\n      // 计算VIP统计数据\r\n      const newVipStats = calculateVipStats(result.users || []);\r\n      setVipStats(newVipStats);\r\n    } catch (error) {\r\n      message.error('获取用户列表失败');\r\n      console.error('Error fetching users:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 计算VIP统计数据\r\n  const calculateVipStats = (userList: User[]): VipStats => {\r\n    const totalUsers = userList.length;\r\n    const vipUsers = userList.filter(u => u.isVip).length;\r\n    const normalUsers = totalUsers - vipUsers;\r\n    const vipRate = totalUsers > 0 ? (vipUsers / totalUsers) * 100 : 0;\r\n    const totalRevenue = 0; // 这里需要从支付订单中计算\r\n    const avgDailyUnlocks = totalUsers > 0 ? userList.reduce((sum, u) => sum + u.dailyUnlockCount, 0) / totalUsers : 0;\r\n\r\n    return {\r\n      totalUsers,\r\n      vipUsers,\r\n      normalUsers,\r\n      vipRate,\r\n      totalRevenue,\r\n      avgDailyUnlocks,\r\n    };\r\n  };\r\n\r\n  // 删除不再使用的fetchUserStatistics函数\r\n\r\n  useEffect(() => {\r\n    fetchUsers();\r\n    fetchVipPackages();\r\n  }, []);\r\n\r\n  // 搜索处理\r\n  const handleSearch = () => {\r\n    const params: any = {};\r\n    if (searchText) params.search = searchText;\r\n    if (filterVip !== undefined) params.isVip = filterVip;\r\n    if (dateRange) {\r\n      params.startDate = dateRange[0];\r\n      params.endDate = dateRange[1];\r\n    }\r\n    params.page = 1;\r\n\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n    fetchUsers(params);\r\n  };\r\n\r\n  // 重置搜索\r\n  const handleReset = () => {\r\n    setSearchText('');\r\n    setFilterVip(undefined);\r\n    setDateRange(null);\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n    fetchUsers({ page: 1 });\r\n  };\r\n\r\n  // 分页处理\r\n  const handleTableChange = (page: number, pageSize?: number) => {\r\n    const params: any = { page, pageSize };\r\n    if (searchText) params.search = searchText;\r\n    if (filterVip !== undefined) params.isVip = filterVip;\r\n    if (dateRange) {\r\n      params.startDate = dateRange[0];\r\n      params.endDate = dateRange[1];\r\n    }\r\n\r\n    setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || prev.pageSize }));\r\n    fetchUsers(params);\r\n  };\r\n\r\n  // 查看用户详情\r\n  const handleViewUser = async (user: User) => {\r\n    setSelectedUser(user);\r\n    setUserDetailVisible(true);\r\n  };\r\n\r\n  // 查看用户活动日志\r\n  const handleViewActivity = async (user: User) => {\r\n    try {\r\n      const response = await request.get(`/users/${user.id}/activity-log`);\r\n      setUserActivity(response.data.logs || []);\r\n      setSelectedUser(user);\r\n      setUserActivityVisible(true);\r\n    } catch (error) {\r\n      message.error('获取用户活动日志失败');\r\n    }\r\n  };\r\n\r\n  // 批量更新VIP状态\r\n  const handleBatchUpdateVip = async (isVip: boolean) => {\r\n    if (selectedRowKeys.length === 0) {\r\n      message.warning('请选择要操作的用户');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (isVip) {\r\n        // 批量设置VIP时，使用默认套餐或让用户选择\r\n        // 这里先获取第一个可用的套餐作为默认套餐\r\n        const availablePackages = vipPackages.filter(pkg => pkg.isActive);\r\n        if (availablePackages.length === 0) {\r\n          message.error('没有可用的VIP套餐');\r\n          return;\r\n        }\r\n\r\n        const defaultPackage = availablePackages[0];\r\n\r\n        const params: SetVipParams = {\r\n          packageId: defaultPackage.id,\r\n          reason: \"管理员批量设置\"\r\n        };\r\n        await userService.batchSetVipStatus(selectedRowKeys, params);\r\n      } else {\r\n        // 批量取消VIP时只传递必要参数\r\n        const params: CancelVipParams = {\r\n          reason: \"管理员批量设置\",\r\n          immediate: true\r\n        };\r\n        await userService.batchCancelVipStatus(selectedRowKeys, params);\r\n      }\r\n      message.success(`批量${isVip ? '设置' : '取消'}VIP成功`);\r\n      setSelectedRowKeys([]);\r\n      fetchUsers();\r\n    } catch (error) {\r\n      console.error('批量更新VIP状态失败:', error);\r\n      // 错误信息已由request统一处理\r\n    }\r\n  };\r\n\r\n  // 批量重置进度\r\n  const handleBatchResetProgress = async () => {\r\n    if (selectedRowKeys.length === 0) {\r\n      message.warning('请选择要操作的用户');\r\n      return;\r\n    }\r\n\r\n    Modal.confirm({\r\n      title: '确认重置用户进度',\r\n      content: `确定要重置选中的 ${selectedRowKeys.length} 个用户的游戏进度吗？此操作不可恢复。`,\r\n      onOk: async () => {\r\n        try {\r\n          const promises = selectedRowKeys.map(userId =>\r\n            request.post(`/users/${userId}/reset-progress`)\r\n          );\r\n\r\n          await Promise.all(promises);\r\n          message.success('批量重置进度成功');\r\n          setSelectedRowKeys([]);\r\n          fetchUsers();\r\n        } catch (error) {\r\n          message.error('批量重置进度失败');\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  // 处理创建/编辑用户\r\n  const handleSubmit = async (values: CreateUserParams | UpdateUserParams) => {\r\n    try {\r\n      if (editingUser) {\r\n        await userService.update(editingUser.id, values as UpdateUserParams);\r\n        message.success('用户更新成功');\r\n      } else {\r\n        await userService.create(values as CreateUserParams);\r\n        message.success('用户创建成功');\r\n      }\r\n\r\n      setModalVisible(false);\r\n      setEditingUser(null);\r\n      form.resetFields();\r\n      fetchUsers();\r\n    } catch {\r\n      message.error(editingUser ? '更新用户失败' : '创建用户失败');\r\n    }\r\n  };\r\n\r\n  // 处理删除用户\r\n  const handleDelete = async (id: string) => {\r\n    try {\r\n      await userService.delete(id);\r\n      message.success('用户删除成功');\r\n      fetchUsers();\r\n    } catch {\r\n      message.error('删除用户失败');\r\n    }\r\n  };\r\n\r\n  // 重置用户进度\r\n  const handleResetProgress = async (id: string) => {\r\n    try {\r\n      await userService.resetProgress(id);\r\n      message.success('用户进度重置成功');\r\n      fetchUsers();\r\n    } catch {\r\n      message.error('重置用户进度失败');\r\n    }\r\n  };\r\n\r\n  // 查看用户统计\r\n  const handleViewStats = async (user: User) => {\r\n    try {\r\n      const stats = await userService.getStats(user.id);\r\n      setSelectedUserStats(stats);\r\n      setStatsModalVisible(true);\r\n    } catch {\r\n      message.error('获取用户统计失败');\r\n    }\r\n  };\r\n\r\n  // 打开VIP设置弹窗\r\n  const handleOpenVipModal = (user: User) => {\r\n    if (user.isVip) {\r\n      // 如果用户已经是VIP，直接取消VIP\r\n      handleCancelVip(user);\r\n    } else {\r\n      // 如果用户不是VIP，打开设置弹窗\r\n      setVipSettingUser(user);\r\n      setVipModalVisible(true);\r\n      vipForm.resetFields();\r\n    }\r\n  };\r\n\r\n  // 取消VIP\r\n  const handleCancelVip = async (user: User) => {\r\n    try {\r\n      const params: CancelVipParams = {\r\n        reason: \"管理员手动取消\",\r\n        immediate: true\r\n      };\r\n\r\n      await userService.cancelVipStatus(user.id, params);\r\n      fetchUsers();\r\n    } catch (error) {\r\n      console.error('取消VIP失败:', error);\r\n      // 错误信息已由request统一处理\r\n    }\r\n  };\r\n\r\n  // 设置VIP\r\n  const handleSetVip = async (values: any) => {\r\n    if (!vipSettingUser) return;\r\n\r\n    try {\r\n      if (!values.packageId) {\r\n        message.error('请选择VIP套餐');\r\n        return;\r\n      }\r\n\r\n      const params: SetVipParams = {\r\n        packageId: values.packageId,\r\n        reason: values.reason || \"管理员手动设置\"\r\n      };\r\n\r\n      await userService.setVipStatus(vipSettingUser.id, params);\r\n      setVipModalVisible(false);\r\n      setVipSettingUser(null);\r\n      vipForm.resetFields();\r\n      fetchUsers();\r\n    } catch (error) {\r\n      console.error('设置VIP失败:', error);\r\n      // 错误信息已由request统一处理\r\n    }\r\n  };\r\n\r\n  // 打开编辑模态框\r\n  const handleEdit = (user: User) => {\r\n    setEditingUser(user);\r\n    form.setFieldsValue({\r\n      phone: user.phone,\r\n      openid: user.openid,\r\n      nickname: user.nickname,\r\n      avatarUrl: user.avatarUrl,\r\n      unlockedLevels: user.unlockedLevels,\r\n      isVip: user.isVip,\r\n      dailyUnlockLimit: user.dailyUnlockLimit,\r\n    });\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 打开创建模态框\r\n  const handleCreate = () => {\r\n    setEditingUser(null);\r\n    form.resetFields();\r\n    setModalVisible(true);\r\n  };\r\n\r\n  const columns: ColumnsType<User> = [\r\n    {\r\n      title: '用户信息',\r\n      key: 'user',\r\n      width: 250,\r\n      render: (_, record) => (\r\n        <Space>\r\n          <Avatar src={record.avatarUrl} icon={<UserOutlined />} />\r\n          <div>\r\n            <div>{record.nickname || '未设置昵称'}</div>\r\n            <div style={{ fontSize: '12px', color: '#666' }}>\r\n              ID: {record.id}\r\n            </div>\r\n            <div style={{ fontSize: '12px', color: '#666' }}>\r\n              手机: {record.phone}\r\n            </div>\r\n            {record.openid && (\r\n              <div style={{ fontSize: '12px', color: '#999' }}>\r\n                OpenID: {record.openid.substring(0, 8)}...\r\n              </div>\r\n            )}\r\n            {record.isVip && (\r\n              <Tag color=\"gold\" icon={<CrownOutlined />} style={{ marginTop: 4 }}>\r\n                VIP\r\n              </Tag>\r\n            )}\r\n          </div>\r\n        </Space>\r\n      ),\r\n    },\r\n    {\r\n      title: '游戏进度',\r\n      key: 'progress',\r\n      width: 150,\r\n      render: (_, record) => (\r\n        <div>\r\n          <div>已开启: {record.unlockedLevels} 关</div>\r\n          <div>已通关: {record.completedLevelIds.length} 关</div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '游戏统计',\r\n      key: 'stats',\r\n      width: 150,\r\n      render: (_, record) => (\r\n        <div>\r\n          <div>总游戏: {record.totalGames} 次</div>\r\n          <div>总通关: {record.totalCompletions} 次</div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '每日解锁',\r\n      key: 'dailyUnlock',\r\n      width: 150,\r\n      render: (_, record) => (\r\n        <div>\r\n          <div>\r\n            今日: {record.dailyUnlockCount || 0}/{record.dailyUnlockLimit || 15}\r\n            {record.isVip && (\r\n              <Tag color=\"gold\" style={{ marginLeft: 4, fontSize: '12px' }}>\r\n                无限制\r\n              </Tag>\r\n            )}\r\n          </div>\r\n          <div style={{ fontSize: '12px', color: '#666' }}>\r\n            分享: {record.totalShares || 0} 次\r\n          </div>\r\n          {record.dailyShared && (\r\n            <Tag color=\"green\" style={{ fontSize: '12px' }}>\r\n              今日已分享\r\n            </Tag>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '最后游戏',\r\n      dataIndex: 'lastPlayTime',\r\n      key: 'lastPlayTime',\r\n      width: 180,\r\n    },\r\n    {\r\n      title: '注册时间',\r\n      dataIndex: 'createdAt',\r\n      key: 'createdAt',\r\n      width: 180,\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: 280,\r\n      render: (_, record) => (\r\n        <Space size=\"small\" wrap>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<EyeOutlined />}\r\n            onClick={() => handleViewUser(record)}\r\n          >\r\n            详情\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<HistoryOutlined />}\r\n            onClick={() => handleViewActivity(record)}\r\n          >\r\n            日志\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<TrophyOutlined />}\r\n            onClick={() => handleViewStats(record)}\r\n          >\r\n            统计\r\n          </Button>\r\n          <Tooltip title={record.isVip ? '取消VIP' : '设为VIP'}>\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              icon={<CrownOutlined />}\r\n              style={{ color: record.isVip ? '#faad14' : '#d9d9d9' }}\r\n              onClick={() => handleOpenVipModal(record)}\r\n            >\r\n              VIP\r\n            </Button>\r\n          </Tooltip>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<EditOutlined />}\r\n            onClick={() => handleEdit(record)}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Popconfirm\r\n            title=\"确定要重置用户进度吗？\"\r\n            onConfirm={() => handleResetProgress(record.id)}\r\n            okText=\"确定\"\r\n            cancelText=\"取消\"\r\n          >\r\n            <Button type=\"link\" size=\"small\" icon={<ReloadOutlined />}>\r\n              重置\r\n            </Button>\r\n          </Popconfirm>\r\n          <Popconfirm\r\n            title=\"确定要删除这个用户吗？\"\r\n            onConfirm={() => handleDelete(record.id)}\r\n            okText=\"确定\"\r\n            cancelText=\"取消\"\r\n          >\r\n            <Button type=\"link\" danger size=\"small\" icon={<DeleteOutlined />}>\r\n              删除\r\n            </Button>\r\n          </Popconfirm>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 行选择配置\r\n  const rowSelection = {\r\n    selectedRowKeys,\r\n    onChange: (selectedKeys: React.Key[]) => {\r\n      setSelectedRowKeys(selectedKeys as string[]);\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {/* VIP统计卡片 */}\r\n      <Row gutter={16} style={{ marginBottom: 16 }}>\r\n        <Col span={6}>\r\n          <Card>\r\n            <Statistic\r\n              title=\"总用户数\"\r\n              value={vipStats.totalUsers}\r\n              prefix={<UserOutlined />}\r\n            />\r\n          </Card>\r\n        </Col>\r\n        <Col span={6}>\r\n          <Card>\r\n            <Statistic\r\n              title=\"VIP用户\"\r\n              value={vipStats.vipUsers}\r\n              valueStyle={{ color: '#faad14' }}\r\n              prefix={<CrownOutlined />}\r\n            />\r\n          </Card>\r\n        </Col>\r\n        <Col span={6}>\r\n          <Card>\r\n            <Statistic\r\n              title=\"VIP转化率\"\r\n              value={vipStats.vipRate}\r\n              precision={1}\r\n              suffix=\"%\"\r\n              valueStyle={{ color: vipStats.vipRate >= 10 ? '#3f8600' : '#cf1322' }}\r\n            />\r\n          </Card>\r\n        </Col>\r\n        <Col span={6}>\r\n          <Card>\r\n            <Statistic\r\n              title=\"平均解锁数\"\r\n              value={vipStats.avgDailyUnlocks}\r\n              precision={1}\r\n              suffix=\"次/日\"\r\n              prefix={<GiftOutlined />}\r\n            />\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      <Card>\r\n        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <h2>用户管理</h2>\r\n          <Space>\r\n            <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleCreate}>\r\n              创建用户\r\n            </Button>\r\n          </Space>\r\n        </div>\r\n\r\n        {/* 搜索和筛选栏 */}\r\n        <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n          <Row gutter={16} align=\"middle\">\r\n            <Col span={6}>\r\n              <Input\r\n                placeholder=\"搜索用户昵称、手机号\"\r\n                value={searchText}\r\n                onChange={(e) => setSearchText(e.target.value)}\r\n                prefix={<SearchOutlined />}\r\n                allowClear\r\n              />\r\n            </Col>\r\n            <Col span={4}>\r\n              <Select\r\n                placeholder=\"筛选VIP状态\"\r\n                value={filterVip}\r\n                onChange={setFilterVip}\r\n                allowClear\r\n                style={{ width: '100%' }}\r\n              >\r\n                <Option value={true}>\r\n                  <CrownOutlined style={{ color: '#faad14', marginRight: 4 }} />\r\n                  VIP用户\r\n                </Option>\r\n                <Option value={false}>\r\n                  <UserOutlined style={{ color: '#d9d9d9', marginRight: 4 }} />\r\n                  普通用户\r\n                </Option>\r\n              </Select>\r\n            </Col>\r\n            <Col span={6}>\r\n              <RangePicker\r\n                placeholder={['开始日期', '结束日期']}\r\n                value={dateRange as any}\r\n                onChange={(dates) => {\r\n                  if (dates) {\r\n                    setDateRange([dates[0]!.format('YYYY-MM-DD'), dates[1]!.format('YYYY-MM-DD')]);\r\n                  } else {\r\n                    setDateRange(null);\r\n                  }\r\n                }}\r\n                style={{ width: '100%' }}\r\n              />\r\n            </Col>\r\n            <Col span={8}>\r\n              <Space>\r\n                <Button type=\"primary\" icon={<SearchOutlined />} onClick={handleSearch}>\r\n                  搜索\r\n                </Button>\r\n                <Button icon={<ReloadOutlined />} onClick={handleReset}>\r\n                  重置\r\n                </Button>\r\n              </Space>\r\n            </Col>\r\n          </Row>\r\n        </Card>\r\n\r\n        {/* 批量操作栏 */}\r\n        {selectedRowKeys.length > 0 && (\r\n          <Card size=\"small\" style={{ marginBottom: 16, backgroundColor: '#f6ffed' }}>\r\n            <Row justify=\"space-between\" align=\"middle\">\r\n              <Col>\r\n                <span>已选择 {selectedRowKeys.length} 个用户</span>\r\n              </Col>\r\n              <Col>\r\n                <Space>\r\n                  <Button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon={<CrownOutlined />}\r\n                    onClick={() => handleBatchUpdateVip(true)}\r\n                  >\r\n                    批量设为VIP\r\n                  </Button>\r\n                  <Button\r\n                    size=\"small\"\r\n                    onClick={() => handleBatchUpdateVip(false)}\r\n                  >\r\n                    批量取消VIP\r\n                  </Button>\r\n                  <Button\r\n                    danger\r\n                    size=\"small\"\r\n                    icon={<ReloadOutlined />}\r\n                    onClick={handleBatchResetProgress}\r\n                  >\r\n                    批量重置进度\r\n                  </Button>\r\n                  <Button\r\n                    size=\"small\"\r\n                    onClick={() => setSelectedRowKeys([])}\r\n                  >\r\n                    取消选择\r\n                  </Button>\r\n                </Space>\r\n              </Col>\r\n            </Row>\r\n          </Card>\r\n        )}\r\n\r\n        <Table\r\n          columns={columns}\r\n          dataSource={users}\r\n          rowKey=\"id\"\r\n          loading={loading}\r\n          rowSelection={rowSelection}\r\n          pagination={{\r\n            current: pagination.current,\r\n            pageSize: pagination.pageSize,\r\n            total: pagination.total,\r\n            showSizeChanger: true,\r\n            showQuickJumper: true,\r\n            showTotal: (total) => `共 ${total} 条记录`,\r\n            onChange: handleTableChange,\r\n            onShowSizeChange: handleTableChange,\r\n          }}\r\n        />\r\n      </Card>\r\n\r\n      {/* 创建/编辑用户模态框 */}\r\n      <Modal\r\n        title={editingUser ? '编辑用户' : '创建用户'}\r\n        visible={modalVisible}\r\n        onCancel={() => {\r\n          setModalVisible(false);\r\n          setEditingUser(null);\r\n          form.resetFields();\r\n        }}\r\n        footer={null}\r\n        width={500}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n          onFinish={handleSubmit}\r\n        >\r\n          {!editingUser && (\r\n            <Form.Item\r\n              name=\"phone\"\r\n              label=\"手机号\"\r\n              rules={[\r\n                { required: true, message: '请输入手机号' },\r\n                { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号码' }\r\n              ]}\r\n            >\r\n              <Input placeholder=\"请输入手机号\" />\r\n            </Form.Item>\r\n          )}\r\n\r\n          {editingUser && (\r\n            <Form.Item\r\n              name=\"phone\"\r\n              label=\"手机号\"\r\n              rules={[\r\n                { required: true, message: '请输入手机号' },\r\n                { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号码' }\r\n              ]}\r\n            >\r\n              <Input placeholder=\"请输入手机号\" />\r\n            </Form.Item>\r\n          )}\r\n\r\n          <Form.Item\r\n            name=\"openid\"\r\n            label=\"微信OpenID\"\r\n          >\r\n            <Input placeholder=\"请输入微信用户OpenID（可选）\" />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"nickname\"\r\n            label=\"昵称\"\r\n          >\r\n            <Input placeholder=\"请输入用户昵称\" />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"avatarUrl\"\r\n            label=\"头像URL\"\r\n          >\r\n            <Input placeholder=\"请输入头像URL\" />\r\n          </Form.Item>\r\n\r\n          {editingUser && (\r\n            <>\r\n              <Form.Item\r\n                name=\"unlockedLevels\"\r\n                label=\"已开启关卡数\"\r\n              >\r\n                <InputNumber min={1} max={1000} placeholder=\"已开启关卡数\" style={{ width: '100%' }} />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"isVip\"\r\n                label=\"VIP状态\"\r\n                valuePropName=\"checked\"\r\n              >\r\n                <Switch\r\n                  checkedChildren={<CrownOutlined />}\r\n                  unCheckedChildren=\"普通\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"dailyUnlockLimit\"\r\n                label=\"每日解锁限制\"\r\n                tooltip=\"VIP用户无限制，普通用户默认15次\"\r\n              >\r\n                <InputNumber\r\n                  min={1}\r\n                  max={999}\r\n                  placeholder=\"每日解锁限制次数\"\r\n                  style={{ width: '100%' }}\r\n                  addonAfter=\"次/天\"\r\n                />\r\n              </Form.Item>\r\n            </>\r\n          )}\r\n\r\n          <Form.Item>\r\n            <Space>\r\n              <Button type=\"primary\" htmlType=\"submit\">\r\n                {editingUser ? '更新' : '创建'}\r\n              </Button>\r\n              <Button onClick={() => {\r\n                setModalVisible(false);\r\n                setEditingUser(null);\r\n                form.resetFields();\r\n              }}>\r\n                取消\r\n              </Button>\r\n            </Space>\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 用户统计模态框 */}\r\n      <Modal\r\n        title=\"用户游戏统计\"\r\n        visible={statsModalVisible}\r\n        onCancel={() => setStatsModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"close\" onClick={() => setStatsModalVisible(false)}>\r\n            关闭\r\n          </Button>\r\n        ]}\r\n        width={600}\r\n      >\r\n        {selectedUserStats && (\r\n          <div>\r\n            <Row gutter={16} style={{ marginBottom: 24 }}>\r\n              <Col span={12}>\r\n                <Statistic title=\"总游戏次数\" value={selectedUserStats.totalGames} />\r\n              </Col>\r\n              <Col span={12}>\r\n                <Statistic title=\"总通关次数\" value={selectedUserStats.totalCompletions} />\r\n              </Col>\r\n              <Col span={12}>\r\n                <Statistic title=\"已解锁关卡\" value={selectedUserStats.unlockedLevels} />\r\n              </Col>\r\n              <Col span={12}>\r\n                <Statistic title=\"已完成关卡\" value={selectedUserStats.completedLevels} />\r\n              </Col>\r\n              <Col span={24}>\r\n                <Statistic\r\n                  title=\"通关率\"\r\n                  value={selectedUserStats.completionRate}\r\n                  precision={2}\r\n                  suffix=\"%\"\r\n                />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Card title=\"每日解锁统计\" size=\"small\">\r\n              <Row gutter={16}>\r\n                <Col span={8}>\r\n                  <Statistic\r\n                    title=\"今日解锁\"\r\n                    value={selectedUserStats.dailyUnlockCount}\r\n                    suffix={`/ ${selectedUserStats.isVip ? '∞' : selectedUserStats.dailyUnlockLimit}`}\r\n                  />\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Statistic\r\n                    title=\"剩余次数\"\r\n                    value={selectedUserStats.isVip ? '∞' : selectedUserStats.remainingUnlocks}\r\n                  />\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Statistic title=\"总分享次数\" value={selectedUserStats.totalShares} />\r\n                </Col>\r\n                <Col span={24} style={{ marginTop: 16 }}>\r\n                  {selectedUserStats.isVip ? (\r\n                    <Tag color=\"gold\" icon={<CrownOutlined />} style={{ fontSize: '14px', padding: '4px 8px' }}>\r\n                      VIP用户 - 无限制解锁\r\n                    </Tag>\r\n                  ) : (\r\n                    <Tag color=\"blue\" icon={<GiftOutlined />} style={{ fontSize: '14px', padding: '4px 8px' }}>\r\n                      普通用户 - 每日{selectedUserStats.dailyUnlockLimit}次解锁\r\n                    </Tag>\r\n                  )}\r\n                </Col>\r\n              </Row>\r\n            </Card>\r\n          </div>\r\n        )}\r\n      </Modal>\r\n\r\n      {/* VIP设置弹窗 */}\r\n      <Modal\r\n        title={`设置VIP - ${vipSettingUser?.nickname || '未设置昵称'}`}\r\n        visible={vipModalVisible}\r\n        onCancel={() => {\r\n          setVipModalVisible(false);\r\n          setVipSettingUser(null);\r\n          vipForm.resetFields();\r\n        }}\r\n        footer={null}\r\n        width={500}\r\n      >\r\n        <Form\r\n          form={vipForm}\r\n          layout=\"vertical\"\r\n          onFinish={handleSetVip}\r\n        >\r\n          <Form.Item\r\n            name=\"packageId\"\r\n            label=\"选择VIP套餐\"\r\n            rules={[{ required: true, message: '请选择VIP套餐' }]}\r\n          >\r\n            <Select placeholder=\"请选择VIP套餐\" size=\"large\">\r\n              {Array.isArray(vipPackages) && vipPackages.map(pkg => (\r\n                <Select.Option key={pkg.id} value={pkg.id}>\r\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                    <div>\r\n                      <div style={{ fontWeight: 'bold' }}>{pkg.name}</div>\r\n                      <div style={{ fontSize: '12px', color: '#666' }}>{pkg.description}</div>\r\n                    </div>\r\n                    <div style={{ textAlign: 'right' }}>\r\n                      <div style={{ color: '#f50', fontWeight: 'bold' }}>¥{(pkg.price / 100).toFixed(2)}</div>\r\n                      <div style={{ fontSize: '12px', color: '#666' }}>{pkg.duration}天</div>\r\n                    </div>\r\n                  </div>\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"reason\"\r\n            label=\"操作原因\"\r\n            initialValue=\"管理员手动设置\"\r\n          >\r\n            <Input.TextArea\r\n              rows={3}\r\n              placeholder=\"请输入设置VIP的原因\"\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\r\n            <Space>\r\n              <Button onClick={() => {\r\n                setVipModalVisible(false);\r\n                setVipSettingUser(null);\r\n                vipForm.resetFields();\r\n              }}>\r\n                取消\r\n              </Button>\r\n              <Button type=\"primary\" htmlType=\"submit\">\r\n                确认设置\r\n              </Button>\r\n            </Space>\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 用户详情抽屉 */}\r\n      <Drawer\r\n        title=\"用户详情\"\r\n        placement=\"right\"\r\n        width={600}\r\n        open={userDetailVisible}\r\n        onClose={() => setUserDetailVisible(false)}\r\n      >\r\n        {selectedUser && (\r\n          <Tabs\r\n            defaultActiveKey=\"basic\"\r\n            items={[\r\n              {\r\n                key: 'basic',\r\n                label: '基本信息',\r\n                children: (\r\n                  <Descriptions column={1} bordered>\r\n                    <Descriptions.Item label=\"用户ID\">{selectedUser.id}</Descriptions.Item>\r\n                    <Descriptions.Item label=\"昵称\">{selectedUser.nickname || '未设置'}</Descriptions.Item>\r\n                    <Descriptions.Item label=\"手机号\">{selectedUser.phone}</Descriptions.Item>\r\n                    <Descriptions.Item label=\"OpenID\">{selectedUser.openid}</Descriptions.Item>\r\n                    <Descriptions.Item label=\"头像\">\r\n                      <Avatar src={selectedUser.avatarUrl} size={64} icon={<UserOutlined />} />\r\n                    </Descriptions.Item>\r\n                    <Descriptions.Item label=\"VIP状态\">\r\n                      <Badge\r\n                        status={selectedUser.isVip ? \"success\" : \"default\"}\r\n                        text={selectedUser.isVip ? \"VIP用户\" : \"普通用户\"}\r\n                      />\r\n                    </Descriptions.Item>\r\n                    <Descriptions.Item label=\"注册时间\">\r\n                      {new Date(selectedUser.createdAt).toLocaleString()}\r\n                    </Descriptions.Item>\r\n                    <Descriptions.Item label=\"最后登录\">\r\n                      {selectedUser.lastPlayTime ? new Date(selectedUser.lastPlayTime).toLocaleString() : '从未登录'}\r\n                    </Descriptions.Item>\r\n                  </Descriptions>\r\n                ),\r\n              },\r\n              {\r\n                key: 'progress',\r\n                label: '游戏进度',\r\n                children: (\r\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\r\n                    <Card size=\"small\">\r\n                      <Statistic title=\"已解锁关卡\" value={selectedUser.unlockedLevels} suffix=\"关\" />\r\n                    </Card>\r\n                    <Card size=\"small\">\r\n                      <Statistic title=\"已完成关卡\" value={selectedUser.completedLevelIds.length} suffix=\"关\" />\r\n                    </Card>\r\n                    <Card size=\"small\">\r\n                      <div>\r\n                        <div style={{ marginBottom: 8 }}>游戏进度</div>\r\n                        <Progress\r\n                          percent={selectedUser.unlockedLevels > 0 ? Math.round((selectedUser.completedLevelIds.length / selectedUser.unlockedLevels) * 100) : 0}\r\n                          status=\"active\"\r\n                        />\r\n                      </div>\r\n                    </Card>\r\n                  </Space>\r\n                ),\r\n              },\r\n              {\r\n                key: 'stats',\r\n                label: '使用统计',\r\n                children: (\r\n                  <Row gutter={16}>\r\n                    <Col span={12}>\r\n                      <Card size=\"small\">\r\n                        <Statistic title=\"总游戏次数\" value={selectedUser.totalGames} />\r\n                      </Card>\r\n                    </Col>\r\n                    <Col span={12}>\r\n                      <Card size=\"small\">\r\n                        <Statistic title=\"总通关次数\" value={selectedUser.totalCompletions} />\r\n                      </Card>\r\n                    </Col>\r\n                    <Col span={12}>\r\n                      <Card size=\"small\">\r\n                        <Statistic title=\"分享次数\" value={selectedUser.totalShares || 0} />\r\n                      </Card>\r\n                    </Col>\r\n                    <Col span={12}>\r\n                      <Card size=\"small\">\r\n                        <Statistic\r\n                          title=\"每日解锁\"\r\n                          value={selectedUser.dailyUnlockCount || 0}\r\n                          suffix={`/ ${selectedUser.dailyUnlockLimit || 15}`}\r\n                        />\r\n                      </Card>\r\n                    </Col>\r\n                  </Row>\r\n                ),\r\n              },\r\n            ]}\r\n          />\r\n        )}\r\n      </Drawer>\r\n\r\n      {/* 用户活动日志抽屉 */}\r\n      <Drawer\r\n        title=\"用户活动日志\"\r\n        placement=\"right\"\r\n        width={800}\r\n        open={userActivityVisible}\r\n        onClose={() => setUserActivityVisible(false)}\r\n      >\r\n        {selectedUser && (\r\n          <div>\r\n            <div style={{ marginBottom: 16 }}>\r\n              <Space>\r\n                <Avatar src={selectedUser.avatarUrl} icon={<UserOutlined />} />\r\n                <div>\r\n                  <div>{selectedUser.nickname || '未设置昵称'}</div>\r\n                  <div style={{ fontSize: '12px', color: '#666' }}>ID: {selectedUser.id}</div>\r\n                </div>\r\n              </Space>\r\n            </div>\r\n\r\n            <Timeline>\r\n              {userActivity.map((activity, index) => (\r\n                <Timeline.Item key={index} color={activity.action === 'login' ? 'green' : 'blue'}>\r\n                  <div>\r\n                    <div style={{ fontWeight: 'bold' }}>{activity.description}</div>\r\n                    <div style={{ fontSize: '12px', color: '#666' }}>\r\n                      {new Date(activity.timestamp).toLocaleString()}\r\n                    </div>\r\n                    {activity.ip && (\r\n                      <div style={{ fontSize: '12px', color: '#999' }}>\r\n                        IP: {activity.ip}\r\n                      </div>\r\n                    )}\r\n                    {activity.details && (\r\n                      <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>\r\n                        {JSON.stringify(activity.details)}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </Timeline.Item>\r\n              ))}\r\n            </Timeline>\r\n\r\n            {userActivity.length === 0 && (\r\n              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>\r\n                暂无活动记录\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </Drawer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AAAA;AAAA;;;AAjBA;;;;;;;AAmBA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,WAAW,EAAE,GAAG,iMAAA,CAAA,aAAU;AAYnB,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7E,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,SAAS;IACT,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,SAAS;QACT,UAAU;QACV,OAAO;IACT;IACA,wBAAwB;IAExB,UAAU;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,YAAY;QACZ,UAAU;QACV,aAAa;QACb,SAAS;QACT,cAAc;QACd,iBAAiB;IACnB;IAEA,YAAY;IACZ,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,QAAQ,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE9B,YAAY;IACZ,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,gIAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC;gBAAE,UAAU;YAAK,MAAM,EAAE;YAC1E,WAAW;YACX,eAAe,MAAM,OAAO,CAAC,YAAY,WAAW,EAAE;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,eAAe,EAAE,GAAG,YAAY;QAClC;IACF;IAEA,kBAAkB;IAClB,MAAM,aAAa,OAAO;QAQxB,WAAW;QACX,IAAI;YACF,MAAM,gBAAgB;gBACpB,GAAI,QAAQ,UAAU;oBAAE,QAAQ,OAAO,MAAM;gBAAC,CAAC;gBAC/C,GAAI,QAAQ,UAAU,aAAa;oBAAE,OAAO,OAAO,KAAK,CAAC,QAAQ;gBAAG,CAAC;gBACrE,GAAI,QAAQ,aAAa;oBAAE,WAAW,OAAO,SAAS;gBAAC,CAAC;gBACxD,GAAI,QAAQ,WAAW;oBAAE,SAAS,OAAO,OAAO;gBAAC,CAAC;gBAClD,MAAM,CAAC,QAAQ,QAAQ,WAAW,OAAO,EAAE,QAAQ;gBACnD,UAAU,CAAC,QAAQ,YAAY,WAAW,QAAQ,EAAE,QAAQ;YAC9D;YAEA,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU;gBAAE,QAAQ;YAAc;YACrE,MAAM,SAAS,SAAS,IAAI;YAE5B,SAAS,OAAO,KAAK,IAAI,EAAE;YAC3B,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,OAAO,OAAO,KAAK,IAAI;oBACvB,SAAS,QAAQ,QAAQ,KAAK,OAAO;gBACvC,CAAC;YAED,YAAY;YACZ,MAAM,cAAc,kBAAkB,OAAO,KAAK,IAAI,EAAE;YACxD,YAAY;QACd,EAAE,OAAO,OAAO;YACd,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,YAAY;IACZ,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,SAAS,MAAM;QAClC,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,EAAE,MAAM;QACrD,MAAM,cAAc,aAAa;QACjC,MAAM,UAAU,aAAa,IAAI,AAAC,WAAW,aAAc,MAAM;QACjE,MAAM,eAAe,GAAG,eAAe;QACvC,MAAM,kBAAkB,aAAa,IAAI,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,gBAAgB,EAAE,KAAK,aAAa;QAEjH,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,+BAA+B;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;QACF;8BAAG,EAAE;IAEL,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,SAAc,CAAC;QACrB,IAAI,YAAY,OAAO,MAAM,GAAG;QAChC,IAAI,cAAc,WAAW,OAAO,KAAK,GAAG;QAC5C,IAAI,WAAW;YACb,OAAO,SAAS,GAAG,SAAS,CAAC,EAAE;YAC/B,OAAO,OAAO,GAAG,SAAS,CAAC,EAAE;QAC/B;QACA,OAAO,IAAI,GAAG;QAEd,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAE,CAAC;QAC9C,WAAW;IACb;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,cAAc;QACd,aAAa;QACb,aAAa;QACb,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAE,CAAC;QAC9C,WAAW;YAAE,MAAM;QAAE;IACvB;IAEA,OAAO;IACP,MAAM,oBAAoB,CAAC,MAAc;QACvC,MAAM,SAAc;YAAE;YAAM;QAAS;QACrC,IAAI,YAAY,OAAO,MAAM,GAAG;QAChC,IAAI,cAAc,WAAW,OAAO,KAAK,GAAG;QAC5C,IAAI,WAAW;YACb,OAAO,SAAS,GAAG,SAAS,CAAC,EAAE;YAC/B,OAAO,OAAO,GAAG,SAAS,CAAC,EAAE;QAC/B;QAEA,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,UAAU,YAAY,KAAK,QAAQ;YAAC,CAAC;QACtF,WAAW;IACb;IAEA,SAAS;IACT,MAAM,iBAAiB,OAAO;QAC5B,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,WAAW;IACX,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,aAAa,CAAC;YACnE,gBAAgB,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;YACxC,gBAAgB;YAChB,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,YAAY;IACZ,MAAM,uBAAuB,OAAO;QAClC,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,IAAI;YACF,IAAI,OAAO;gBACT,wBAAwB;gBACxB,sBAAsB;gBACtB,MAAM,oBAAoB,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ;gBAChE,IAAI,kBAAkB,MAAM,KAAK,GAAG;oBAClC,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;oBACd;gBACF;gBAEA,MAAM,iBAAiB,iBAAiB,CAAC,EAAE;gBAE3C,MAAM,SAAuB;oBAC3B,WAAW,eAAe,EAAE;oBAC5B,QAAQ;gBACV;gBACA,MAAM,iIAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC,iBAAiB;YACvD,OAAO;gBACL,kBAAkB;gBAClB,MAAM,SAA0B;oBAC9B,QAAQ;oBACR,WAAW;gBACb;gBACA,MAAM,iIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC,iBAAiB;YAC1D;YACA,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,OAAO,KAAK,KAAK,CAAC;YAC/C,mBAAmB,EAAE;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,oBAAoB;QACtB;IACF;IAEA,SAAS;IACT,MAAM,2BAA2B;QAC/B,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,2KAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,mBAAmB,CAAC;YAChE,MAAM;gBACJ,IAAI;oBACF,MAAM,WAAW,gBAAgB,GAAG,CAAC,CAAA,SACnC,6HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,eAAe,CAAC;oBAGhD,MAAM,QAAQ,GAAG,CAAC;oBAClB,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;oBAChB,mBAAmB,EAAE;oBACrB;gBACF,EAAE,OAAO,OAAO;oBACd,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,YAAY;IACZ,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,IAAI,aAAa;gBACf,MAAM,iIAAA,CAAA,cAAW,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;gBACzC,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,MAAM,iIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACzB,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,gBAAgB;YAChB,eAAe;YACf,KAAK,WAAW;YAChB;QACF,EAAE,OAAM;YACN,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC,cAAc,WAAW;QACzC;IACF;IAEA,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,iIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACzB,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAM;YACN,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,iIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YAChC,+KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAM;YACN,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,QAAQ,MAAM,iIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;YAChD,qBAAqB;YACrB,qBAAqB;QACvB,EAAE,OAAM;YACN,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,YAAY;IACZ,MAAM,qBAAqB,CAAC;QAC1B,IAAI,KAAK,KAAK,EAAE;YACd,qBAAqB;YACrB,gBAAgB;QAClB,OAAO;YACL,mBAAmB;YACnB,kBAAkB;YAClB,mBAAmB;YACnB,QAAQ,WAAW;QACrB;IACF;IAEA,QAAQ;IACR,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,SAA0B;gBAC9B,QAAQ;gBACR,WAAW;YACb;YAEA,MAAM,iIAAA,CAAA,cAAW,CAAC,eAAe,CAAC,KAAK,EAAE,EAAE;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,oBAAoB;QACtB;IACF;IAEA,QAAQ;IACR,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,IAAI,CAAC,OAAO,SAAS,EAAE;gBACrB,+KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd;YACF;YAEA,MAAM,SAAuB;gBAC3B,WAAW,OAAO,SAAS;gBAC3B,QAAQ,OAAO,MAAM,IAAI;YAC3B;YAEA,MAAM,iIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,eAAe,EAAE,EAAE;YAClD,mBAAmB;YACnB,kBAAkB;YAClB,QAAQ,WAAW;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,oBAAoB;QACtB;IACF;IAEA,UAAU;IACV,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,KAAK,cAAc,CAAC;YAClB,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,UAAU,KAAK,QAAQ;YACvB,WAAW,KAAK,SAAS;YACzB,gBAAgB,KAAK,cAAc;YACnC,OAAO,KAAK,KAAK;YACjB,kBAAkB,KAAK,gBAAgB;QACzC;QACA,gBAAgB;IAClB;IAEA,UAAU;IACV,MAAM,eAAe;QACnB,eAAe;QACf,KAAK,WAAW;QAChB,gBAAgB;IAClB;IAEA,MAAM,UAA6B;QACjC;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC,mMAAA,CAAA,QAAK;;sCACJ,6LAAC,qLAAA,CAAA,SAAM;4BAAC,KAAK,OAAO,SAAS;4BAAE,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;sCAClD,6LAAC;;8CACC,6LAAC;8CAAK,OAAO,QAAQ,IAAI;;;;;;8CACzB,6LAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAQ,OAAO;oCAAO;;wCAAG;wCAC1C,OAAO,EAAE;;;;;;;8CAEhB,6LAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAQ,OAAO;oCAAO;;wCAAG;wCAC1C,OAAO,KAAK;;;;;;;gCAElB,OAAO,MAAM,kBACZ,6LAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAQ,OAAO;oCAAO;;wCAAG;wCACtC,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG;wCAAG;;;;;;;gCAG1C,OAAO,KAAK,kBACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,OAAM;oCAAO,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCAAK,OAAO;wCAAE,WAAW;oCAAE;8CAAG;;;;;;;;;;;;;;;;;;QAO9E;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC;;sCACC,6LAAC;;gCAAI;gCAAM,OAAO,cAAc;gCAAC;;;;;;;sCACjC,6LAAC;;gCAAI;gCAAM,OAAO,iBAAiB,CAAC,MAAM;gCAAC;;;;;;;;;;;;;QAGjD;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC;;sCACC,6LAAC;;gCAAI;gCAAM,OAAO,UAAU;gCAAC;;;;;;;sCAC7B,6LAAC;;gCAAI;gCAAM,OAAO,gBAAgB;gCAAC;;;;;;;;;;;;;QAGzC;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC;;sCACC,6LAAC;;gCAAI;gCACE,OAAO,gBAAgB,IAAI;gCAAE;gCAAE,OAAO,gBAAgB,IAAI;gCAC9D,OAAO,KAAK,kBACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,OAAM;oCAAO,OAAO;wCAAE,YAAY;wCAAG,UAAU;oCAAO;8CAAG;;;;;;;;;;;;sCAKlE,6LAAC;4BAAI,OAAO;gCAAE,UAAU;gCAAQ,OAAO;4BAAO;;gCAAG;gCAC1C,OAAO,WAAW,IAAI;gCAAE;;;;;;;wBAE9B,OAAO,WAAW,kBACjB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,OAAM;4BAAQ,OAAO;gCAAE,UAAU;4BAAO;sCAAG;;;;;;;;;;;;QAMxD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;QACT;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;oBAAQ,IAAI;;sCACtB,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;4BAClB,SAAS,IAAM,eAAe;sCAC/B;;;;;;sCAGD,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;4BACtB,SAAS,IAAM,mBAAmB;sCACnC;;;;;;sCAGD,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4BACrB,SAAS,IAAM,gBAAgB;sCAChC;;;;;;sCAGD,6LAAC,uLAAA,CAAA,UAAO;4BAAC,OAAO,OAAO,KAAK,GAAG,UAAU;sCACvC,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;gCACpB,OAAO;oCAAE,OAAO,OAAO,KAAK,GAAG,YAAY;gCAAU;gCACrD,SAAS,IAAM,mBAAmB;0CACnC;;;;;;;;;;;sCAIH,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,WAAW;sCAC3B;;;;;;sCAGD,6LAAC,6LAAA,CAAA,aAAU;4BACT,OAAM;4BACN,WAAW,IAAM,oBAAoB,OAAO,EAAE;4BAC9C,QAAO;4BACP,YAAW;sCAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAO,MAAK;gCAAQ,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;0CAAK;;;;;;;;;;;sCAI7D,6LAAC,6LAAA,CAAA,aAAU;4BACT,OAAM;4BACN,WAAW,IAAM,aAAa,OAAO,EAAE;4BACvC,QAAO;4BACP,YAAW;sCAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAO,MAAM;gCAAC,MAAK;gCAAQ,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;0CAAK;;;;;;;;;;;;;;;;;QAM1E;KACD;IAED,QAAQ;IACR,MAAM,eAAe;QACnB;QACA,UAAU,CAAC;YACT,mBAAmB;QACrB;IACF;IAEA,qBACE,6LAAC;;0BAEC,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCACzC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,SAAS,UAAU;gCAC1B,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,SAAS,QAAQ;gCACxB,YAAY;oCAAE,OAAO;gCAAU;gCAC/B,sBAAQ,6LAAC,uNAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;kCAI5B,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,SAAS,OAAO;gCACvB,WAAW;gCACX,QAAO;gCACP,YAAY;oCAAE,OAAO,SAAS,OAAO,IAAI,KAAK,YAAY;gCAAU;;;;;;;;;;;;;;;;kCAI1E,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,SAAS,eAAe;gCAC/B,WAAW;gCACX,QAAO;gCACP,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC,iLAAA,CAAA,OAAI;;kCACH,6LAAC;wBAAI,OAAO;4BAAE,cAAc;4BAAI,SAAS;4BAAQ,gBAAgB;4BAAiB,YAAY;wBAAS;;0CACrG,6LAAC;0CAAG;;;;;;0CACJ,6LAAC,mMAAA,CAAA,QAAK;0CACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,MAAK;oCAAU,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCAAK,SAAS;8CAAc;;;;;;;;;;;;;;;;;kCAO1E,6LAAC,iLAAA,CAAA,OAAI;wBAAC,MAAK;wBAAQ,OAAO;4BAAE,cAAc;wBAAG;kCAC3C,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;4BAAI,OAAM;;8CACrB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACvB,UAAU;;;;;;;;;;;8CAGd,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU;wCACV,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;;0DAEvB,6LAAC;gDAAO,OAAO;;kEACb,6LAAC,uNAAA,CAAA,gBAAa;wDAAC,OAAO;4DAAE,OAAO;4DAAW,aAAa;wDAAE;;;;;;oDAAK;;;;;;;0DAGhE,6LAAC;gDAAO,OAAO;;kEACb,6LAAC,qNAAA,CAAA,eAAY;wDAAC,OAAO;4DAAE,OAAO;4DAAW,aAAa;wDAAE;;;;;;oDAAK;;;;;;;;;;;;;;;;;;8CAKnE,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC;wCACC,aAAa;4CAAC;4CAAQ;yCAAO;wCAC7B,OAAO;wCACP,UAAU,CAAC;4CACT,IAAI,OAAO;gDACT,aAAa;oDAAC,KAAK,CAAC,EAAE,CAAE,MAAM,CAAC;oDAAe,KAAK,CAAC,EAAE,CAAE,MAAM,CAAC;iDAAc;4CAC/E,OAAO;gDACL,aAAa;4CACf;wCACF;wCACA,OAAO;4CAAE,OAAO;wCAAO;;;;;;;;;;;8CAG3B,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0DACJ,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAU,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gDAAK,SAAS;0DAAc;;;;;;0DAGxE,6LAAC,qMAAA,CAAA,SAAM;gDAAC,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gDAAK,SAAS;0DAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS/D,gBAAgB,MAAM,GAAG,mBACxB,6LAAC,iLAAA,CAAA,OAAI;wBAAC,MAAK;wBAAQ,OAAO;4BAAE,cAAc;4BAAI,iBAAiB;wBAAU;kCACvE,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,SAAQ;4BAAgB,OAAM;;8CACjC,6LAAC,+KAAA,CAAA,MAAG;8CACF,cAAA,6LAAC;;4CAAK;4CAAK,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;8CAEpC,6LAAC,+KAAA,CAAA,MAAG;8CACF,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0DACJ,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;gDACpB,SAAS,IAAM,qBAAqB;0DACrC;;;;;;0DAGD,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,qBAAqB;0DACrC;;;;;;0DAGD,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAM;gDACN,MAAK;gDACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gDACrB,SAAS;0DACV;;;;;;0DAGD,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,mBAAmB,EAAE;0DACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASX,6LAAC,mLAAA,CAAA,QAAK;wBACJ,SAAS;wBACT,YAAY;wBACZ,QAAO;wBACP,SAAS;wBACT,cAAc;wBACd,YAAY;4BACV,SAAS,WAAW,OAAO;4BAC3B,UAAU,WAAW,QAAQ;4BAC7B,OAAO,WAAW,KAAK;4BACvB,iBAAiB;4BACjB,iBAAiB;4BACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;4BACtC,UAAU;4BACV,kBAAkB;wBACpB;;;;;;;;;;;;0BAKJ,6LAAC,2KAAA,CAAA,QAAK;gBACJ,OAAO,cAAc,SAAS;gBAC9B,SAAS;gBACT,UAAU;oBACR,gBAAgB;oBAChB,eAAe;oBACf,KAAK,WAAW;gBAClB;gBACA,QAAQ;gBACR,OAAO;0BAEP,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;wBAET,CAAC,6BACA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAS;gCACpC;oCAAE,SAAS;oCAAiB,SAAS;gCAAa;6BACnD;sCAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;wBAItB,6BACC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAS;gCACpC;oCAAE,SAAS;oCAAiB,SAAS;gCAAa;6BACnD;sCAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAIvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;sCAEN,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;sCAEN,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;sCAEN,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;wBAGpB,6BACC;;8CACE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCAAC,KAAK;wCAAG,KAAK;wCAAM,aAAY;wCAAS,OAAO;4CAAE,OAAO;wCAAO;;;;;;;;;;;8CAG9E,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;8CAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,+BAAiB,6LAAC,uNAAA,CAAA,gBAAa;;;;;wCAC/B,mBAAkB;;;;;;;;;;;8CAItB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,SAAQ;8CAER,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;wCACvB,YAAW;;;;;;;;;;;;;sCAMnB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;sCACR,cAAA,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qMAAA,CAAA,SAAM;wCAAC,MAAK;wCAAU,UAAS;kDAC7B,cAAc,OAAO;;;;;;kDAExB,6LAAC,qMAAA,CAAA,SAAM;wCAAC,SAAS;4CACf,gBAAgB;4CAChB,eAAe;4CACf,KAAK,WAAW;wCAClB;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,6LAAC,2KAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,SAAS;gBACT,UAAU,IAAM,qBAAqB;gBACrC,QAAQ;kCACN,6LAAC,qMAAA,CAAA,SAAM;wBAAa,SAAS,IAAM,qBAAqB;kCAAQ;uBAApD;;;;;iBAGb;gBACD,OAAO;0BAEN,mCACC,6LAAC;;sCACC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CACzC,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;wCAAC,OAAM;wCAAQ,OAAO,kBAAkB,UAAU;;;;;;;;;;;8CAE9D,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;wCAAC,OAAM;wCAAQ,OAAO,kBAAkB,gBAAgB;;;;;;;;;;;8CAEpE,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;wCAAC,OAAM;wCAAQ,OAAO,kBAAkB,cAAc;;;;;;;;;;;8CAElE,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;wCAAC,OAAM;wCAAQ,OAAO,kBAAkB,eAAe;;;;;;;;;;;8CAEnE,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,kBAAkB,cAAc;wCACvC,WAAW;wCACX,QAAO;;;;;;;;;;;;;;;;;sCAKb,6LAAC,iLAAA,CAAA,OAAI;4BAAC,OAAM;4BAAS,MAAK;sCACxB,cAAA,6LAAC,+KAAA,CAAA,MAAG;gCAAC,QAAQ;;kDACX,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,kBAAkB,gBAAgB;4CACzC,QAAQ,CAAC,EAAE,EAAE,kBAAkB,KAAK,GAAG,MAAM,kBAAkB,gBAAgB,EAAE;;;;;;;;;;;kDAGrF,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,kBAAkB,KAAK,GAAG,MAAM,kBAAkB,gBAAgB;;;;;;;;;;;kDAG7E,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4CAAC,OAAM;4CAAQ,OAAO,kBAAkB,WAAW;;;;;;;;;;;kDAE/D,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;wCAAI,OAAO;4CAAE,WAAW;wCAAG;kDACnC,kBAAkB,KAAK,iBACtB,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;4CAAO,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;4CAAK,OAAO;gDAAE,UAAU;gDAAQ,SAAS;4CAAU;sDAAG;;;;;iEAI5F,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;4CAAO,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CAAK,OAAO;gDAAE,UAAU;gDAAQ,SAAS;4CAAU;;gDAAG;gDAC/E,kBAAkB,gBAAgB;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW7D,6LAAC,2KAAA,CAAA,QAAK;gBACJ,OAAO,CAAC,QAAQ,EAAE,gBAAgB,YAAY,SAAS;gBACvD,SAAS;gBACT,UAAU;oBACR,mBAAmB;oBACnB,kBAAkB;oBAClB,QAAQ,WAAW;gBACrB;gBACA,QAAQ;gBACR,OAAO;0BAEP,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAW;6BAAE;sCAEhD,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCAAC,aAAY;gCAAW,MAAK;0CACjC,MAAM,OAAO,CAAC,gBAAgB,YAAY,GAAG,CAAC,CAAA,oBAC7C,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;wCAAc,OAAO,IAAI,EAAE;kDACvC,cAAA,6LAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,gBAAgB;gDAAiB,YAAY;4CAAS;;8DACnF,6LAAC;;sEACC,6LAAC;4DAAI,OAAO;gEAAE,YAAY;4DAAO;sEAAI,IAAI,IAAI;;;;;;sEAC7C,6LAAC;4DAAI,OAAO;gEAAE,UAAU;gEAAQ,OAAO;4DAAO;sEAAI,IAAI,WAAW;;;;;;;;;;;;8DAEnE,6LAAC;oDAAI,OAAO;wDAAE,WAAW;oDAAQ;;sEAC/B,6LAAC;4DAAI,OAAO;gEAAE,OAAO;gEAAQ,YAAY;4DAAO;;gEAAG;gEAAE,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;;;;;;;sEAC/E,6LAAC;4DAAI,OAAO;gEAAE,UAAU;gEAAQ,OAAO;4DAAO;;gEAAI,IAAI,QAAQ;gEAAC;;;;;;;;;;;;;;;;;;;uCARjD,IAAI,EAAE;;;;;;;;;;;;;;;sCAgBhC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,cAAa;sCAEb,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;gCACb,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qMAAA,CAAA,SAAM;wCAAC,SAAS;4CACf,mBAAmB;4CACnB,kBAAkB;4CAClB,QAAQ,WAAW;wCACrB;kDAAG;;;;;;kDAGH,6LAAC,qMAAA,CAAA,SAAM;wCAAC,MAAK;wCAAU,UAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,6LAAC,qLAAA,CAAA,SAAM;gBACL,OAAM;gBACN,WAAU;gBACV,OAAO;gBACP,MAAM;gBACN,SAAS,IAAM,qBAAqB;0BAEnC,8BACC,6LAAC,iLAAA,CAAA,OAAI;oBACH,kBAAiB;oBACjB,OAAO;wBACL;4BACE,KAAK;4BACL,OAAO;4BACP,wBACE,6LAAC,iNAAA,CAAA,eAAY;gCAAC,QAAQ;gCAAG,QAAQ;;kDAC/B,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDAAQ,aAAa,EAAE;;;;;;kDAChD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDAAM,aAAa,QAAQ,IAAI;;;;;;kDACxD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDAAO,aAAa,KAAK;;;;;;kDAClD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDAAU,aAAa,MAAM;;;;;;kDACtD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDACvB,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,KAAK,aAAa,SAAS;4CAAE,MAAM;4CAAI,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;kDAEpE,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDACvB,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CACJ,QAAQ,aAAa,KAAK,GAAG,YAAY;4CACzC,MAAM,aAAa,KAAK,GAAG,UAAU;;;;;;;;;;;kDAGzC,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;kDAElD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,aAAa,YAAY,GAAG,IAAI,KAAK,aAAa,YAAY,EAAE,cAAc,KAAK;;;;;;;;;;;;wBAI5F;wBACA;4BACE,KAAK;4BACL,OAAO;4BACP,wBACE,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;gCAAW,OAAO;oCAAE,OAAO;gCAAO;;kDACjD,6LAAC,iLAAA,CAAA,OAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4CAAC,OAAM;4CAAQ,OAAO,aAAa,cAAc;4CAAE,QAAO;;;;;;;;;;;kDAEtE,6LAAC,iLAAA,CAAA,OAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4CAAC,OAAM;4CAAQ,OAAO,aAAa,iBAAiB,CAAC,MAAM;4CAAE,QAAO;;;;;;;;;;;kDAEhF,6LAAC,iLAAA,CAAA,OAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC;;8DACC,6LAAC;oDAAI,OAAO;wDAAE,cAAc;oDAAE;8DAAG;;;;;;8DACjC,6LAAC,yLAAA,CAAA,WAAQ;oDACP,SAAS,aAAa,cAAc,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,aAAa,iBAAiB,CAAC,MAAM,GAAG,aAAa,cAAc,GAAI,OAAO;oDACrI,QAAO;;;;;;;;;;;;;;;;;;;;;;;wBAMnB;wBACA;4BACE,KAAK;4BACL,OAAO;4BACP,wBACE,6LAAC,+KAAA,CAAA,MAAG;gCAAC,QAAQ;;kDACX,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;gDAAC,OAAM;gDAAQ,OAAO,aAAa,UAAU;;;;;;;;;;;;;;;;kDAG3D,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;gDAAC,OAAM;gDAAQ,OAAO,aAAa,gBAAgB;;;;;;;;;;;;;;;;kDAGjE,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;gDAAC,OAAM;gDAAO,OAAO,aAAa,WAAW,IAAI;;;;;;;;;;;;;;;;kDAG/D,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;gDACR,OAAM;gDACN,OAAO,aAAa,gBAAgB,IAAI;gDACxC,QAAQ,CAAC,EAAE,EAAE,aAAa,gBAAgB,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;wBAM9D;qBACD;;;;;;;;;;;0BAMP,6LAAC,qLAAA,CAAA,SAAM;gBACL,OAAM;gBACN,WAAU;gBACV,OAAO;gBACP,MAAM;gBACN,SAAS,IAAM,uBAAuB;0BAErC,8BACC,6LAAC;;sCACC,6LAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;sCAC7B,cAAA,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qLAAA,CAAA,SAAM;wCAAC,KAAK,aAAa,SAAS;wCAAE,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;kDACxD,6LAAC;;0DACC,6LAAC;0DAAK,aAAa,QAAQ,IAAI;;;;;;0DAC/B,6LAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAQ,OAAO;gDAAO;;oDAAG;oDAAK,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAK3E,6LAAC,yLAAA,CAAA,WAAQ;sCACN,aAAa,GAAG,CAAC,CAAC,UAAU,sBAC3B,6LAAC,yLAAA,CAAA,WAAQ,CAAC,IAAI;oCAAa,OAAO,SAAS,MAAM,KAAK,UAAU,UAAU;8CACxE,cAAA,6LAAC;;0DACC,6LAAC;gDAAI,OAAO;oDAAE,YAAY;gDAAO;0DAAI,SAAS,WAAW;;;;;;0DACzD,6LAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAQ,OAAO;gDAAO;0DAC3C,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;;;;;;4CAE7C,SAAS,EAAE,kBACV,6LAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAQ,OAAO;gDAAO;;oDAAG;oDAC1C,SAAS,EAAE;;;;;;;4CAGnB,SAAS,OAAO,kBACf,6LAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAQ,OAAO;oDAAQ,WAAW;gDAAE;0DACzD,KAAK,SAAS,CAAC,SAAS,OAAO;;;;;;;;;;;;mCAbpB;;;;;;;;;;wBAqBvB,aAAa,MAAM,KAAK,mBACvB,6LAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;gCAAU,OAAO;4BAAO;sCAAG;;;;;;;;;;;;;;;;;;;;;;;AASrF;GA1mCwB;;QAOP,iLAAA,CAAA,OAAI,CAAC;QAgCF,iLAAA,CAAA,OAAI,CAAC;;;KAvCD", "debugId": null}}]}