(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1311],{36449:()=>{},41008:(e,t,s)=>{"use strict";s.d(t,{i:()=>a});let a={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},48526:(e,t,s)=>{"use strict";s.d(t,{L8:()=>n});var a=s(83899);let n={getAll:async e=>(await a.FH.get("/activation-codes",{params:e})).data,getByCode:async e=>(await a.FH.get("/activation-codes/".concat(e))).data,generate:async e=>(await a.KY.post("/activation-codes/generate",e,"激活码生成成功")).data,create:async e=>(await a.KY.post("/activation-codes/create",e,"激活码创建成功")).data,batchGenerate:async e=>(await a.KY.post("/activation-codes/generate",e,"批量生成激活码成功")).data,disable:async e=>{await a.KY.put("/activation-codes/".concat(e,"/disable"),{},"激活码已禁用")},enable:async e=>{await a.KY.put("/activation-codes/".concat(e,"/enable"),{},"激活码已启用")},getAllPackages:async()=>(await a.FH.get("/payment/packages")).data||[],getPopularPackages:async e=>(await a.FH.get("/payment/packages/popular",{params:{limit:e}})).data,delete:async(e,t)=>(await a.KY.delete("/activation-codes/".concat(e),"激活码删除成功",{data:t||{}})).data};n.getAll,n.generate,n.create,n.batchGenerate,n.disable,n.enable},62500:(e,t,s)=>{Promise.resolve().then(s.bind(s,76101))},73629:()=>{},76101:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var a=s(95155),n=s(12115),o=s(20778),c=s(86615),i=s(12320),r=s(77325),l=s(37974),d=s(6124),u=s(51087),m=s(56020),h=s(24971),p=s(10642),x=s(90285),y=s(70129),g=s(56170),k=s(46996),b=s(43454),v=s(34140),f=s(48526),j=s(30832),w=s.n(j);let{Option:A}=o.A;function C(){let[e,t]=(0,n.useState)([]),[s,j]=(0,n.useState)([]),[C,I]=(0,n.useState)(!1),[T,_]=(0,n.useState)(!1),[L]=c.A.useForm(),[N,R]=(0,n.useState)(!1),[S,U]=(0,n.useState)(!1),[D]=c.A.useForm(),[E,M]=(0,n.useState)(!1),F=async()=>{I(!0);try{let e=await f.L8.getAll();t(e.codes||[])}catch(e){console.error("Error fetching activation codes:",e),x.i.error("获取激活码列表失败"),t([{code:"VIP2024001",packageId:"pkg_vip_monthly_30d_a1b2",packageName:"VIP月卡",status:"unused",maxRedemptions:1,currentRedemptions:0,expireDate:"2024-12-31T23:59:59Z",source:"双十一活动",batchId:"batch_20241101_001",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{code:"UNLOCK50",packageId:"pkg_unlock_levels_50",packageName:"解锁50关卡",status:"used",maxRedemptions:3,currentRedemptions:2,redemptionHistory:["user123","user456"],usedBy:"12345678",usedAt:"2024-06-15T10:30:00Z",expireDate:"2024-06-30T23:59:59Z",source:"新用户福利",batchId:"batch_20240601_002",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-06-15T10:30:00Z"}])}finally{I(!1)}},P=async()=>{try{let e=await f.L8.getAllPackages();j(e||[])}catch(e){console.error("Error fetching packages:",e),j([{id:"pkg_vip_monthly_30d_a1b2",name:"VIP月卡",type:"vip",duration:30},{id:"pkg_unlock_levels_50",name:"解锁50关卡",type:"unlock",duration:0},{id:"pkg_bonus_points_1000",name:"1000积分",type:"points",duration:0}])}};(0,n.useEffect)(()=>{F(),P()},[]);let Y=e=>{navigator.clipboard.writeText(e).then(()=>{x.i.success("激活码已复制到剪贴板")})},O=async e=>{try{await f.L8.disable(e),x.i.success("激活码已禁用"),F()}catch(e){x.i.error("禁用激活码失败")}},B=async e=>{try{await f.L8.enable(e),x.i.success("激活码已启用"),F()}catch(e){x.i.error("启用激活码失败")}},K=e=>{try{x.a.confirm({title:"确认删除",content:"确定要删除激活码 ".concat(e," 吗？此操作不可撤销。"),okText:"确认删除",okType:"danger",cancelText:"取消",onOk:async()=>{try{await f.L8.delete(e,{reason:"管理员手动删除"}),F()}catch(e){console.error("删除激活码失败:",e)}}})}catch(t){console.warn("Modal.confirm compatibility issue, using native confirm"),window.confirm("确定要删除激活码 ".concat(e," 吗？此操作不可撤销。"))&&z(e)}},z=async e=>{try{await f.L8.delete(e,{reason:"管理员手动删除"}),F()}catch(e){console.error("删除激活码失败:",e)}},V=async()=>{try{let e=await L.validateFields();R(!0);let t={packageId:e.packageId,customCode:e.customCode||void 0,expireDate:e.expireDate?w()(e.expireDate).format("YYYY-MM-DD"):void 0,source:e.source||"manual",maxRedemptions:e.maxRedemptions||1};await f.L8.create(t),_(!1),L.resetFields(),F()}catch(e){console.error("创建激活码失败:",e)}finally{R(!1)}},Z=async()=>{try{let e=await D.validateFields();M(!0);let t={packageId:e.packageId,count:e.count,expireDate:e.expireDate?w()(e.expireDate).format("YYYY-MM-DD"):void 0,source:e.source||"batch",prefix:e.prefix||void 0,maxRedemptions:e.maxRedemptions||1},s=await f.L8.batchGenerate(t);x.i.success("批量生成成功！成功生成 ".concat(s.successCount," 个激活码").concat(s.failedCount>0?"，失败 ".concat(s.failedCount," 个"):"")),U(!1),D.resetFields(),F()}catch(e){console.error("批量生成激活码失败:",e)}finally{M(!1)}},H=[{title:"激活码",dataIndex:"code",key:"code",render:e=>(0,a.jsxs)(i.A,{children:[(0,a.jsx)("code",{style:{backgroundColor:"#f5f5f5",padding:"2px 6px",borderRadius:"4px"},children:e}),(0,a.jsx)(r.Ay,{type:"link",size:"small",icon:(0,a.jsx)(y.A,{}),onClick:()=>Y(e)})]})},{title:"套餐",dataIndex:"packageName",key:"packageName",render:e=>(0,a.jsx)(l.A,{color:"blue",children:e})},{title:"状态",dataIndex:"status",key:"status",render:e=>{let t={unused:{text:"未使用",color:"green"},used:{text:"已使用",color:"blue"},expired:{text:"已过期",color:"red"},disabled:{text:"已禁用",color:"default"}}[e]||{text:e,color:"default"};return(0,a.jsx)(l.A,{color:t.color,children:t.text})}},{title:"兑换次数",key:"redemptions",render:(e,t)=>{let{maxRedemptions:s,currentRedemptions:n}=t,o=-1===s;return(0,a.jsxs)("span",{children:[n," / ",o?"∞":s,!o&&n>=s&&(0,a.jsx)(l.A,{color:"red",style:{marginLeft:4,fontSize:"12px"},children:"已满"})]})}},{title:"使用者",dataIndex:"usedBy",key:"usedBy",render:e=>e||"-"},{title:"过期时间",dataIndex:"expireDate",key:"expireDate",render:e=>new Date(e).toLocaleDateString()},{title:"操作",key:"action",render:(e,t)=>(0,a.jsxs)(i.A,{children:["unused"===t.status?(0,a.jsx)(r.Ay,{type:"link",size:"small",onClick:()=>O(t.code),children:"禁用"}):"disabled"===t.status?(0,a.jsx)(r.Ay,{type:"link",size:"small",onClick:()=>B(t.code),children:"启用"}):null,(0,a.jsx)(r.Ay,{type:"link",size:"small",icon:(0,a.jsx)(y.A,{}),onClick:()=>Y(t.code),children:"复制"}),(0,a.jsx)(r.Ay,{type:"link",size:"small",danger:!0,icon:(0,a.jsx)(g.A,{}),onClick:()=>K(t.code),children:"删除"})]})}];return(0,a.jsxs)("div",{children:[(0,a.jsxs)(d.A,{children:[(0,a.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsx)("h2",{children:"激活码管理"}),(0,a.jsxs)(i.A,{children:[(0,a.jsx)(r.Ay,{type:"primary",icon:(0,a.jsx)(k.A,{}),onClick:()=>_(!0),children:"手动创建"}),(0,a.jsx)(r.Ay,{icon:(0,a.jsx)(b.A,{}),onClick:()=>U(!0),children:"批量生成"}),(0,a.jsx)(r.Ay,{icon:(0,a.jsx)(v.A,{}),onClick:()=>{F()},children:"刷新"})]})]}),(0,a.jsx)(u.A,{columns:H,dataSource:e,rowKey:"id",loading:C,pagination:{showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")}})]}),(0,a.jsx)(x.a,{title:"手动创建激活码",visible:T,onOk:V,onCancel:()=>{_(!1),L.resetFields()},confirmLoading:N,width:600,children:(0,a.jsxs)(c.A,{form:L,layout:"vertical",initialValues:{source:"manual"},children:[(0,a.jsx)(c.A.Item,{label:"套餐",name:"packageId",rules:[{required:!0,message:"请选择套餐"}],children:(0,a.jsx)(o.A,{placeholder:"选择套餐",children:s.map(e=>(0,a.jsx)(A,{value:e.id,children:e.name},e.id))})}),(0,a.jsx)(c.A.Item,{label:"自定义激活码",name:"customCode",help:"留空则自动生成",children:(0,a.jsx)(m.A,{placeholder:"输入自定义激活码（可选）"})}),(0,a.jsx)(c.A.Item,{label:"过期时间",name:"expireDate",help:"留空则使用套餐默认过期时间",children:(0,a.jsx)(h.A,{style:{width:"100%"},placeholder:"选择过期时间（可选）",disabledDate:e=>e&&e<w()().endOf("day")})}),(0,a.jsx)(c.A.Item,{label:"兑换次数限制",name:"maxRedemptions",help:"设置激活码最大兑换次数，-1表示无限次，默认为1次",initialValue:1,children:(0,a.jsx)(p.A,{min:-1,max:999,style:{width:"100%"},placeholder:"输入最大兑换次数",formatter:e=>-1===e?"无限次":"".concat(e),parser:e=>{if("无限次"===e)return -1;let t=parseInt(e||"1");return isNaN(t)?1:Math.max(-1,Math.min(999,t))}})}),(0,a.jsx)(c.A.Item,{label:"来源标识",name:"source",children:(0,a.jsx)(m.A,{placeholder:"如：手动创建、客服处理等"})})]})}),(0,a.jsx)(x.a,{title:"批量生成激活码",visible:S,onOk:Z,onCancel:()=>{U(!1),D.resetFields()},confirmLoading:E,width:600,children:(0,a.jsxs)(c.A,{form:D,layout:"vertical",initialValues:{count:10,source:"batch"},children:[(0,a.jsx)(c.A.Item,{label:"套餐",name:"packageId",rules:[{required:!0,message:"请选择套餐"}],children:(0,a.jsx)(o.A,{placeholder:"选择套餐",children:s.map(e=>(0,a.jsx)(A,{value:e.id,children:e.name},e.id))})}),(0,a.jsx)(c.A.Item,{label:"生成数量",name:"count",rules:[{required:!0,message:"请输入生成数量"},{type:"number",min:1,max:1e3,message:"数量必须在1-1000之间"}],children:(0,a.jsx)(p.A,{min:1,max:1e3,style:{width:"100%"},placeholder:"输入生成数量"})}),(0,a.jsx)(c.A.Item,{label:"激活码前缀",name:"prefix",help:"可选，为生成的激活码添加统一前缀",children:(0,a.jsx)(m.A,{placeholder:"如：VIP2024、SALE等（可选）"})}),(0,a.jsx)(c.A.Item,{label:"过期时间",name:"expireDate",help:"留空则使用套餐默认过期时间",children:(0,a.jsx)(h.A,{style:{width:"100%"},placeholder:"选择过期时间（可选）",disabledDate:e=>e&&e<w()().endOf("day")})}),(0,a.jsx)(c.A.Item,{label:"兑换次数限制",name:"maxRedemptions",help:"设置激活码最大兑换次数，-1表示无限次，默认为1次",initialValue:1,children:(0,a.jsx)(p.A,{min:-1,max:999,style:{width:"100%"},placeholder:"输入最大兑换次数",formatter:e=>-1===e?"无限次":"".concat(e),parser:e=>{if("无限次"===e)return -1;let t=parseInt(e||"1");return isNaN(t)?1:Math.max(-1,Math.min(999,t))}})}),(0,a.jsx)(c.A.Item,{label:"来源标识",name:"source",children:(0,a.jsx)(m.A,{placeholder:"如：双十一活动、新用户福利等"})})]})})]})}},83899:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,FH:()=>i,KY:()=>r});var a=s(23464),n=s(90285),o=s(41008);let c=a.A.create({baseURL:o.i.FULL_BASE_URL,timeout:o.i.TIMEOUT,headers:{"Content-Type":"application/json"}});c.interceptors.request.use(e=>{var t;let s=localStorage.getItem("admin_token");return s&&(e.headers.Authorization="Bearer ".concat(s)),console.log("\uD83D\uDE80 发送请求:",{method:null==(t=e.method)?void 0:t.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),c.interceptors.response.use(e=>{var t;let s=e.config;return console.log("✅ 请求成功:",{method:null==(t=s.method)?void 0:t.toUpperCase(),url:s.url,status:e.status,statusText:e.statusText,data:e.data}),s.showSuccess&&s.successMessage&&n.i.success(s.successMessage),e},e=>{var t,s,a,o,c,i,r,l,d;console.error("❌ 请求失败:",{method:null==(s=e.config)||null==(t=s.method)?void 0:t.toUpperCase(),url:null==(a=e.config)?void 0:a.url,baseURL:null==(o=e.config)?void 0:o.baseURL,fullURL:"".concat(null==(c=e.config)?void 0:c.baseURL).concat(null==(i=e.config)?void 0:i.url),status:null==(r=e.response)?void 0:r.status,statusText:null==(l=e.response)?void 0:l.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:t,data:s}=e.response,a="";switch(t){case 401:a="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a=(null==s?void 0:s.message)||"没有权限访问该资源";break;case 404:a=(null==s?void 0:s.message)||"请求的资源不存在";break;case 422:a=(null==s?void 0:s.message)||"请求参数验证失败";break;case 500:a=(null==s?void 0:s.message)||"服务器内部错误";break;default:a=(null==s?void 0:s.message)||"请求失败 (".concat(t,")")}n.i.error(a)}else e.request?n.i.error("网络连接失败，请检查网络"):n.i.error("请求配置错误");return Promise.reject(e)});let i={get:(e,t)=>c.get(e,t),post:(e,t,s)=>c.post(e,t,s),put:(e,t,s)=>c.put(e,t,s),patch:(e,t,s)=>c.patch(e,t,s),delete:(e,t)=>c.delete(e,t)},r={post:(e,t,s,a)=>i.post(e,t,{...a,showSuccess:!0,successMessage:s||"操作成功"}),put:(e,t,s,a)=>i.put(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),patch:(e,t,s,a)=>i.patch(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),delete:(e,t,s)=>i.delete(e,{...s,showSuccess:!0,successMessage:t||"删除成功"})},l=i},90285:(e,t,s)=>{"use strict";s.d(t,{a:()=>r,i:()=>m});var a=s(95155),n=s(12115),o=s(12669);s(36449);let c=e=>{let{title:t,content:s,children:o,visible:c=!1,width:i=520,centered:r=!1,closable:l=!0,maskClosable:d=!0,footer:u,okText:m="确定",cancelText:h="取消",okType:p="primary",confirmLoading:x=!1,onOk:y,onCancel:g,afterClose:k,className:b="",style:v={}}=e,[f,j]=(0,n.useState)(c),[w,A]=(0,n.useState)(!1);(0,n.useEffect)(()=>{c?(j(!0),A(!0),document.body.style.overflow="hidden"):(A(!1),setTimeout(()=>{j(!1),document.body.style.overflow="",null==k||k()},300))},[c,k]);let C=async()=>{if(y)try{await y()}catch(e){console.error("Modal onOk error:",e)}},I=()=>{null==g||g()};return f?(0,a.jsx)("div",{className:"custom-modal-mask ".concat(w?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==g||g())},children:(0,a.jsx)("div",{className:"custom-modal-wrap ".concat(r?"custom-modal-centered":""),children:(0,a.jsxs)("div",{className:"custom-modal ".concat(b," ").concat(w?"custom-modal-show":"custom-modal-hide"),style:{width:i,...v},children:[(t||l)&&(0,a.jsxs)("div",{className:"custom-modal-header",children:[t&&(0,a.jsx)("div",{className:"custom-modal-title",children:t}),l&&(0,a.jsx)("button",{className:"custom-modal-close",onClick:I,"aria-label":"Close",children:"\xd7"})]}),(0,a.jsx)("div",{className:"custom-modal-body",children:s||o}),null===u?null:u||(0,a.jsxs)("div",{className:"custom-modal-footer",children:[h&&(0,a.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:I,children:h}),(0,a.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(p),onClick:C,disabled:x,children:[x&&(0,a.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),m]})]})]})})}):null};class i{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,o.createRoot)(this.container)),this.container}confirm(e){return new Promise((t,s)=>{let n=!1,o=async()=>{if(!n)try{e.onOk&&await e.onOk(),n=!0,this.destroy(),t()}catch(e){s(e)}};this.getContainer(),this.root.render((0,a.jsx)(c,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:o,onCancel:()=>{var t;n||(n=!0,null==(t=e.onCancel)||t.call(e),this.destroy(),s(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}c.confirm=e=>new i().confirm({...e,okType:e.okType||"primary"}),c.info=e=>new i().confirm({...e,okType:"primary",cancelText:void 0}),c.success=e=>new i().confirm({...e,okType:"primary",cancelText:void 0}),c.error=e=>new i().confirm({...e,okType:"danger",cancelText:void 0}),c.warning=e=>new i().confirm({...e,okType:"primary",cancelText:void 0});let r=c;s(73629);let l=e=>{let{messages:t}=e;return(0,a.jsx)("div",{className:"custom-message-container",children:t.map(e=>(0,a.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,a.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,a.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,o.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,a.jsx)(l,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var t;let s=e.key||this.generateId(),a=null!=(t=e.duration)?t:3e3;e.key&&(this.messages=this.messages.filter(t=>t.id!==e.key));let n={...e,id:s,visible:!0};return this.messages.push(n),this.getContainer(),this.render(),a>0&&setTimeout(()=>{this.hide(s)},a),s}hide(e){let t=this.messages.findIndex(t=>t.id===e);t>-1&&(this.messages[t].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(t=>t.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,m={success:(e,t)=>u.show({content:e,type:"success",duration:t}),error:(e,t)=>u.show({content:e,type:"error",duration:t}),warning:(e,t)=>u.show({content:e,type:"warning",duration:t}),info:(e,t)=>u.show({content:e,type:"info",duration:t}),destroy:()=>u.destroy()}}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9301,6312,778,2343,1087,6615,642,6226,9531,8441,1684,7358],()=>t(62500)),_N_E=e.O()}]);