import request from './request';

// 设置数据类型
export interface Settings {
  id: string;
  key: string;
  value: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'url';
  createdAt: string;
  updatedAt: string;
}

// 创建设置参数
export interface CreateSettingsParams {
  key: string;
  value: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'url';
}

// 更新设置参数
export interface UpdateSettingsParams {
  key?: string;
  value?: string;
  description?: string;
  type?: 'string' | 'number' | 'boolean' | 'url';
}

// 设置服务
export const settingsService = {
  // 获取所有设置
  async getAll(): Promise<Settings[]> {
    const response = await request.get('/settings');
    return response.data;
  },

  // 根据ID获取设置
  async getById(id: string): Promise<Settings> {
    const response = await request.get(`/settings/${id}`);
    return response.data;
  },

  // 根据键名获取设置
  async getByKey(key: string): Promise<Settings> {
    const response = await request.get(`/settings/key/${key}`);
    return response.data;
  },

  // 更新设置
  async update(id: string, params: UpdateSettingsParams): Promise<Settings> {
    const response = await request.patch(`/settings/${id}`, params);
    return response.data;
  },

  // 根据键名更新设置值
  async updateByKey(key: string, value: string): Promise<Settings> {
    const response = await request.patch(`/settings/key/${key}`, { value });
    return response.data;
  },

  // 初始化默认设置（保留用于手动触发）
  async initializeDefaults(): Promise<void> {
    await request.post('/settings/initialize');
  },

  // 批量更新小程序配置
  async updateAppConfig(config: {
    helpUrl?: string;
    backgroundMusicUrl?: string;
  }): Promise<void> {
    const promises = [];
    
    if (config.helpUrl !== undefined) {
      promises.push(this.updateByKey('help_url', config.helpUrl));
    }
    
    if (config.backgroundMusicUrl !== undefined) {
      promises.push(this.updateByKey('background_music_url', config.backgroundMusicUrl));
    }
    
    await Promise.all(promises);
  },

  // 获取小程序配置
  async getAppConfig(): Promise<{
    helpUrl: string;
    backgroundMusicUrl: string;
  }> {
    try {
      const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([
        this.getByKey('help_url').catch(() => null),
        this.getByKey('background_music_url').catch(() => null),
      ]);

      return {
        helpUrl: helpUrlSetting?.value || '',
        backgroundMusicUrl: backgroundMusicSetting?.value || '',
      };
    } catch (error) {
      console.error('获取小程序配置失败:', error);
      // 如果设置不存在，返回默认值
      return {
        helpUrl: '',
        backgroundMusicUrl: '',
      };
    }
  },

  // 测试微信小程序获取设置接口
  async testWeixinAppSettings(): Promise<{
    helpUrl: string;
    backgroundMusicUrl: string;
  }> {
    try {
      const response = await request.get('/weixin/app-settings');
      return response.data;
    } catch (error) {
      console.error('测试微信小程序设置接口失败:', error);
      throw error;
    }
  },

  // 测试微信小程序全局配置接口
  async testWeixinGlobalConfig(): Promise<any> {
    try {
      const response = await request.get('/weixin/global-config');
      return response.data;
    } catch (error) {
      console.error('测试微信小程序全局配置接口失败:', error);
      throw error;
    }
  },
};
