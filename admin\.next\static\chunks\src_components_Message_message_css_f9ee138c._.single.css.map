{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Message/message.css"], "sourcesContent": ["/* Message 组件样式 */\n.custom-message-wrapper {\n  position: fixed;\n  top: 16px;\n  left: 50%;\n  transform: translateX(-50%);\n  z-index: 1010;\n  pointer-events: none;\n}\n\n.custom-message-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.custom-message {\n  display: flex;\n  align-items: center;\n  padding: 8px 16px;\n  border-radius: 6px;\n  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);\n  background: #fff;\n  font-size: 14px;\n  line-height: 1.5715;\n  pointer-events: auto;\n  transition: all 0.3s ease;\n  max-width: 400px;\n  word-wrap: break-word;\n}\n\n.custom-message-icon {\n  margin-right: 8px;\n  font-size: 16px;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 16px;\n  height: 16px;\n}\n\n.custom-message-content {\n  flex: 1;\n}\n\n/* 不同类型的消息样式 */\n.custom-message-success {\n  border: 1px solid #b7eb8f;\n  background-color: #f6ffed;\n}\n\n.custom-message-success .custom-message-icon {\n  color: #52c41a;\n}\n\n.custom-message-success .custom-message-content {\n  color: #52c41a;\n}\n\n.custom-message-error {\n  border: 1px solid #ffccc7;\n  background-color: #fff2f0;\n}\n\n.custom-message-error .custom-message-icon {\n  color: #ff4d4f;\n}\n\n.custom-message-error .custom-message-content {\n  color: #ff4d4f;\n}\n\n.custom-message-warning {\n  border: 1px solid #ffe58f;\n  background-color: #fffbe6;\n}\n\n.custom-message-warning .custom-message-icon {\n  color: #faad14;\n}\n\n.custom-message-warning .custom-message-content {\n  color: #faad14;\n}\n\n.custom-message-info {\n  border: 1px solid #91caff;\n  background-color: #f0f5ff;\n}\n\n.custom-message-info .custom-message-icon {\n  color: #1677ff;\n}\n\n.custom-message-info .custom-message-content {\n  color: #1677ff;\n}\n\n/* 动画效果 */\n.custom-message-show {\n  opacity: 1;\n  transform: translateY(0);\n  animation: customMessageSlideIn 0.3s ease;\n}\n\n.custom-message-hide {\n  opacity: 0;\n  transform: translateY(-100%);\n  animation: customMessageSlideOut 0.3s ease;\n}\n\n@keyframes customMessageSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes customMessageSlideOut {\n  from {\n    opacity: 1;\n    transform: translateY(0);\n  }\n  to {\n    opacity: 0;\n    transform: translateY(-100%);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .custom-message {\n    max-width: calc(100vw - 32px);\n    margin: 0 16px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;AAWA;;;;AAKA;;;;;AAKA;;;;AAQA;;;;;AAKA;;;;AAQA;;;;;AAKA;;;;AAQA;;;;;AAKA;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;EACE"}}]}