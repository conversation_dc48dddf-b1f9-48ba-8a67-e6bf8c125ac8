/* Modal 组件样式 */
.custom-modal-container {
  position: relative;
  z-index: 1000;
}

.custom-modal-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  transition: opacity 0.3s ease;
}

.custom-modal-mask-show {
  opacity: 1;
}

.custom-modal-mask-hide {
  opacity: 0;
}

.custom-modal-wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  outline: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 100px 0;
}

.custom-modal-centered {
  align-items: center;
  padding: 0;
}

.custom-modal {
  position: relative;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  max-width: calc(100vw - 32px);
  transition: all 0.3s ease;
}

.custom-modal-show {
  opacity: 1;
  transform: scale(1);
}

.custom-modal-hide {
  opacity: 0;
  transform: scale(0.9);
}

.custom-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 6px 6px 0 0;
}

.custom-modal-title {
  margin: 0;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
}

.custom-modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
  padding: 0;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 700;
  line-height: 1;
  text-decoration: none;
  background: transparent;
  border: 0;
  outline: 0;
  cursor: pointer;
  transition: color 0.3s;
  font-size: 22px;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-modal-close:hover {
  color: rgba(0, 0, 0, 0.75);
}

.custom-modal-body {
  padding: 24px;
  font-size: 14px;
  line-height: 1.5715;
  word-wrap: break-word;
}

.custom-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 16px;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 6px 6px;
}

/* 按钮样式 */
.custom-modal-btn {
  position: relative;
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;
  touch-action: manipulation;
  height: 32px;
  padding: 4px 15px;
  font-size: 14px;
  border-radius: 6px;
  outline: 0;
  text-decoration: none;
}

.custom-modal-btn:hover {
  text-decoration: none;
}

.custom-modal-btn:focus {
  outline: 0;
}

.custom-modal-btn-default {
  color: rgba(0, 0, 0, 0.88);
  background: #fff;
  border-color: #d9d9d9;
}

.custom-modal-btn-default:hover {
  color: #4096ff;
  background: #fff;
  border-color: #4096ff;
}

.custom-modal-btn-primary {
  color: #fff;
  background: #1677ff;
  border-color: #1677ff;
}

.custom-modal-btn-primary:hover {
  background: #4096ff;
  border-color: #4096ff;
}

.custom-modal-btn-danger {
  color: #fff;
  background: #ff4d4f;
  border-color: #ff4d4f;
}

.custom-modal-btn-danger:hover {
  background: #ff7875;
  border-color: #ff7875;
}

.custom-modal-btn:disabled {
  color: rgba(0, 0, 0, 0.25);
  background: #f5f5f5;
  border-color: #d9d9d9;
  cursor: not-allowed;
}

.custom-modal-loading {
  display: inline-block;
  margin-right: 8px;
  animation: customModalSpin 1s infinite linear;
}

@keyframes customModalSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-modal {
    max-width: calc(100vw - 16px);
    margin: 8px;
  }
  
  .custom-modal-wrap {
    padding: 16px 0;
  }
  
  .custom-modal-body {
    padding: 16px;
  }
  
  .custom-modal-header {
    padding: 12px 16px;
  }
  
  .custom-modal-footer {
    padding: 8px 12px;
  }
}
