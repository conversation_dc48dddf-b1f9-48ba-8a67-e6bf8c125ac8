(()=>{var e={};e.id=1368,e.ids=[1368],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5948:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var o=r(43210),n=r(69662),i=r.n(n),l=r(71802),a=r(40908),s=r(42411),c=r(32476),d=r(13581),p=r(60254);let u=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},m=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:o,lineWidth:n,textPaddingInline:i,orientationMargin:l,verticalMarginInline:a}=e;return{[t]:Object.assign(Object.assign({},(0,c.dF)(e)),{borderBlockStart:`${(0,s.zA)(n)} solid ${o}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:a,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,s.zA)(n)} solid ${o}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,s.zA)(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,s.zA)(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${o}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,s.zA)(n)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${l} * 100%)`},"&::after":{width:`calc(100% - ${l} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${l} * 100%)`},"&::after":{width:`calc(${l} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:i},"&-dashed":{background:"none",borderColor:o,borderStyle:"dashed",borderWidth:`${(0,s.zA)(n)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:n,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:o,borderStyle:"dotted",borderWidth:`${(0,s.zA)(n)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:n,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:r}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:r}}})}},h=(0,d.OF)("Divider",e=>{let t=(0,p.oX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[m(t),u(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var g=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let b={small:"sm",middle:"md"},f=e=>{let{getPrefixCls:t,direction:r,className:n,style:s}=(0,l.TP)("divider"),{prefixCls:c,type:d="horizontal",orientation:p="center",orientationMargin:u,className:m,rootClassName:f,children:x,dashed:y,variant:v="solid",plain:$,style:w,size:A}=e,j=g(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),C=t("divider",c),[k,S,O]=h(C),I=b[(0,a.A)(A)],z=!!x,E=o.useMemo(()=>"left"===p?"rtl"===r?"end":"start":"right"===p?"rtl"===r?"start":"end":p,[r,p]),P="start"===E&&null!=u,B="end"===E&&null!=u,N=i()(C,n,S,O,`${C}-${d}`,{[`${C}-with-text`]:z,[`${C}-with-text-${E}`]:z,[`${C}-dashed`]:!!y,[`${C}-${v}`]:"solid"!==v,[`${C}-plain`]:!!$,[`${C}-rtl`]:"rtl"===r,[`${C}-no-default-orientation-margin-start`]:P,[`${C}-no-default-orientation-margin-end`]:B,[`${C}-${I}`]:!!I},m,f),M=o.useMemo(()=>"number"==typeof u?u:/^\d+$/.test(u)?Number(u):u,[u]);return k(o.createElement("div",Object.assign({className:N,style:Object.assign(Object.assign({},s),w)},j,{role:"separator"}),x&&"vertical"!==d&&o.createElement("span",{className:`${C}-inner-text`,style:{marginInlineStart:P?M:void 0,marginInlineEnd:B?M:void 0}},x)))}},8298:(e,t,r)=>{Promise.resolve().then(r.bind(r,73396))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23575:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(80828),n=r(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var l=r(21898);let a=n.forwardRef(function(e,t){return n.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29220:(e,t,r)=>{"use strict";r.d(t,{A:()=>q});var o=r(43210),n=r(91039),i=r(41514),l=r(15693),a=r(51297),s=r(74550),c=r(69662),d=r.n(c),p=r(13934),u=r(44666),m=r(7224),h=r(56883),g=r(71802),b=r(42411),f=r(32476),x=r(13581);let y=(e,t,r,o,n)=>({background:e,border:`${(0,b.zA)(o.lineWidth)} ${o.lineType} ${t}`,[`${n}-icon`]:{color:r}}),v=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:o,marginSM:n,fontSize:i,fontSizeLG:l,lineHeight:a,borderRadiusLG:s,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:p,colorTextHeading:u,withDescriptionPadding:m,defaultPadding:h}=e;return{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:h,wordWrap:"break-word",borderRadius:s,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:o,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:a},"&-message":{color:u},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${c}, opacity ${r} ${c},
        padding-top ${r} ${c}, padding-bottom ${r} ${c},
        margin-bottom ${r} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:m,[`${t}-icon`]:{marginInlineEnd:n,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:o,color:u,fontSize:l},[`${t}-description`]:{display:"block",color:p}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},$=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:o,colorSuccessBg:n,colorWarning:i,colorWarningBorder:l,colorWarningBg:a,colorError:s,colorErrorBorder:c,colorErrorBg:d,colorInfo:p,colorInfoBorder:u,colorInfoBg:m}=e;return{[t]:{"&-success":y(n,o,r,e,t),"&-info":y(m,u,p,e,t),"&-warning":y(a,l,i,e,t),"&-error":Object.assign(Object.assign({},y(d,c,s,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},w=e=>{let{componentCls:t,iconCls:r,motionDurationMid:o,marginXS:n,fontSizeIcon:i,colorIcon:l,colorIconHover:a}=e;return{[t]:{"&-action":{marginInlineStart:n},[`${t}-close-icon`]:{marginInlineStart:n,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,b.zA)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:l,transition:`color ${o}`,"&:hover":{color:a}}},"&-close-text":{color:l,transition:`color ${o}`,"&:hover":{color:a}}}}},A=(0,x.OF)("Alert",e=>[v(e),$(e),w(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var j=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let C={success:n.A,info:s.A,error:i.A,warning:a.A},k=e=>{let{icon:t,prefixCls:r,type:n}=e,i=C[n]||null;return t?(0,h.fx)(t,o.createElement("span",{className:`${r}-icon`},t),()=>({className:d()(`${r}-icon`,t.props.className)})):o.createElement(i,{className:`${r}-icon`})},S=e=>{let{isClosable:t,prefixCls:r,closeIcon:n,handleClose:i,ariaProps:a}=e,s=!0===n||void 0===n?o.createElement(l.A,null):n;return t?o.createElement("button",Object.assign({type:"button",onClick:i,className:`${r}-close-icon`,tabIndex:0},a),s):null},O=o.forwardRef((e,t)=>{let{description:r,prefixCls:n,message:i,banner:l,className:a,rootClassName:s,style:c,onMouseEnter:h,onMouseLeave:b,onClick:f,afterClose:x,showIcon:y,closable:v,closeText:$,closeIcon:w,action:C,id:O}=e,I=j(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[z,E]=o.useState(!1),P=o.useRef(null);o.useImperativeHandle(t,()=>({nativeElement:P.current}));let{getPrefixCls:B,direction:N,closable:M,closeIcon:q,className:H,style:T}=(0,g.TP)("alert"),L=B("alert",n),[R,_,D]=A(L),F=t=>{var r;E(!0),null==(r=e.onClose)||r.call(e,t)},W=o.useMemo(()=>void 0!==e.type?e.type:l?"warning":"info",[e.type,l]),G=o.useMemo(()=>"object"==typeof v&&!!v.closeIcon||!!$||("boolean"==typeof v?v:!1!==w&&null!=w||!!M),[$,w,v,M]),X=!!l&&void 0===y||y,V=d()(L,`${L}-${W}`,{[`${L}-with-description`]:!!r,[`${L}-no-icon`]:!X,[`${L}-banner`]:!!l,[`${L}-rtl`]:"rtl"===N},H,a,s,D,_),K=(0,u.A)(I,{aria:!0,data:!0}),Q=o.useMemo(()=>"object"==typeof v&&v.closeIcon?v.closeIcon:$||(void 0!==w?w:"object"==typeof M&&M.closeIcon?M.closeIcon:q),[w,v,$,q]),Y=o.useMemo(()=>{let e=null!=v?v:M;if("object"==typeof e){let{closeIcon:t}=e;return j(e,["closeIcon"])}return{}},[v,M]);return R(o.createElement(p.Ay,{visible:!z,motionName:`${L}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:x},({className:t,style:n},l)=>o.createElement("div",Object.assign({id:O,ref:(0,m.K4)(P,l),"data-show":!z,className:d()(V,t),style:Object.assign(Object.assign(Object.assign({},T),c),n),onMouseEnter:h,onMouseLeave:b,onClick:f,role:"alert"},K),X?o.createElement(k,{description:r,icon:e.icon,prefixCls:L,type:W}):null,o.createElement("div",{className:`${L}-content`},i?o.createElement("div",{className:`${L}-message`},i):null,r?o.createElement("div",{className:`${L}-description`},r):null),C?o.createElement("div",{className:`${L}-action`},C):null,o.createElement(S,{isClosable:G,prefixCls:L,closeIcon:Q,handleClose:F,ariaProps:Y}))))});var I=r(67737),z=r(49617),E=r(30402),P=r(85764),B=r(1630),N=r(69561);let M=function(e){function t(){var e,r,o;return(0,I.A)(this,t),r=t,o=arguments,r=(0,E.A)(r),(e=(0,B.A)(this,(0,P.A)()?Reflect.construct(r,o||[],(0,E.A)(this).constructor):r.apply(this,o))).state={error:void 0,info:{componentStack:""}},e}return(0,N.A)(t,e),(0,z.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:n}=this.props,{error:i,info:l}=this.state,a=(null==l?void 0:l.componentStack)||null,s=void 0===e?(i||"").toString():e;return i?o.createElement(O,{id:r,type:"error",message:s,description:o.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?a:t)}):n}}])}(o.Component);O.ErrorBoundary=M;let q=O},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52378:(e,t,r)=>{"use strict";r.d(t,{A:()=>z});var o=r(43210),n=r(69662),i=r.n(n),l=r(11056),a=r(41414),s=r(10313),c=r(56883),d=r(17727),p=r(71802),u=r(42411),m=r(73117),h=r(32476),g=r(60254),b=r(13581);let f=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:n,calc:i}=e,l=i(o).sub(r).equal(),a=i(t).sub(r).equal();return{[n]:Object.assign(Object.assign({},(0,h.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,u.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${n}-close-icon`]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${n}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},x=e=>{let{lineWidth:t,fontSizeIcon:r,calc:o}=e,n=e.fontSizeSM;return(0,g.oX)(e,{tagFontSize:n,tagLineHeight:(0,u.zA)(o(e.lineHeightSM).mul(n).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new m.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),v=(0,b.OF)("Tag",e=>f(x(e)),y);var $=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let w=o.forwardRef((e,t)=>{let{prefixCls:r,style:n,className:l,checked:a,onChange:s,onClick:c}=e,d=$(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:u,tag:m}=o.useContext(p.QO),h=u("tag",r),[g,b,f]=v(h),x=i()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:a},null==m?void 0:m.className,l,b,f);return g(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},n),null==m?void 0:m.style),className:x,onClick:e=>{null==s||s(!a),null==c||c(e)}})))});var A=r(21821);let j=e=>(0,A.A)(e,(t,{textColor:r,lightBorderColor:o,lightColor:n,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:n,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),C=(0,b.bf)(["Tag","preset"],e=>j(x(e)),y),k=(e,t,r)=>{let o=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},S=(0,b.bf)(["Tag","status"],e=>{let t=x(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]},y);var O=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let I=o.forwardRef((e,t)=>{let{prefixCls:r,className:n,rootClassName:u,style:m,children:h,icon:g,color:b,onClose:f,bordered:x=!0,visible:y}=e,$=O(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:A,tag:j}=o.useContext(p.QO),[k,I]=o.useState(!0),z=(0,l.A)($,["closeIcon","closable"]);o.useEffect(()=>{void 0!==y&&I(y)},[y]);let E=(0,a.nP)(b),P=(0,a.ZZ)(b),B=E||P,N=Object.assign(Object.assign({backgroundColor:b&&!B?b:void 0},null==j?void 0:j.style),m),M=w("tag",r),[q,H,T]=v(M),L=i()(M,null==j?void 0:j.className,{[`${M}-${b}`]:B,[`${M}-has-color`]:b&&!B,[`${M}-hidden`]:!k,[`${M}-rtl`]:"rtl"===A,[`${M}-borderless`]:!x},n,u,H,T),R=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||I(!1)},[,_]=(0,s.A)((0,s.d)(e),(0,s.d)(j),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:`${M}-close-icon`,onClick:R},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null==(r=null==e?void 0:e.onClick)||r.call(e,t),R(t)},className:i()(null==e?void 0:e.className,`${M}-close-icon`)}))}}),D="function"==typeof $.onClick||h&&"a"===h.type,F=g||null,W=F?o.createElement(o.Fragment,null,F,h&&o.createElement("span",null,h)):h,G=o.createElement("span",Object.assign({},z,{ref:t,className:L,style:N}),W,_,E&&o.createElement(C,{key:"preset",prefixCls:M}),P&&o.createElement(S,{key:"status",prefixCls:M}));return q(D?o.createElement(d.A,{component:"Tag"},G):G)});I.CheckableTag=w;let z=I},53698:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\levels\\\\create\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\create\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64675:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var o=r(65239),n=r(48088),i=r(88170),l=r.n(i),a=r(30893),s={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);r.d(t,s);let c={children:["",{children:["(admin)",{children:["levels",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,53698)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\create\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(admin)/levels/create/page",pathname:"/levels/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},73396:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var o=r(60687),n=r(43210),i=r(16189),l=r(99053),a=r(94733),s=r(10411),c=r(35899),d=r(29220),p=r(42585),u=r(5948),m=r(77833),h=r(27783),g=r(70084),b=r(52378),f=r(48111),x=r(10678),y=r(53082),v=r(23575),$=r(23870);let{Title:w}=l.A,{TextArea:A}=a.A;function j(){let[e]=s.A.useForm(),t=(0,i.useRouter)(),[r,l]=(0,n.useState)(!1),[j,C]=(0,n.useState)([]),[k,S]=(0,n.useState)([]),[O,I]=(0,n.useState)(null),[z]=s.A.useForm(),[E,P]=(0,n.useState)(!1),B=e=>{C(t=>t.filter(t=>t.id!==e)),c.Ay.success("词组删除成功")},N=async e=>{if(0===j.length)return void c.Ay.error("请添加词组");l(!0);try{let r={name:e.name,difficulty:1,description:e.description,thesaurusIds:[],phrases:j.map(e=>({text:e.text,meaning:e.meaning})),tagIds:e.tagIds||[]};await $.k3.create(r),c.Ay.success("关卡创建成功"),t.push("/levels")}catch(e){c.Ay.error("创建关卡失败")}finally{l(!1)}};return(0,o.jsxs)("div",{children:[O&&(0,o.jsx)(d.A,{message:`当前已创建 ${O.total} 个关卡，还可以创建 ${O.remaining} 个关卡（最大限制：${O.maxLevels}）`,type:O.remaining>0?"info":"warning",style:{marginBottom:16},showIcon:!0}),(0,o.jsxs)(p.A,{children:[(0,o.jsx)(w,{level:2,children:"创建新关卡"}),(0,o.jsxs)(s.A,{form:e,layout:"vertical",onFinish:N,initialValues:{difficulty:3},children:[(0,o.jsx)(s.A.Item,{name:"name",label:"关卡名称",rules:[{required:!0,message:"请输入关卡名称"}],children:(0,o.jsx)(a.A,{placeholder:"例如：第1关 - 基础词汇"})}),(0,o.jsx)(s.A.Item,{name:"description",label:"关卡描述",children:(0,o.jsx)(A,{rows:3,placeholder:"请输入关卡描述（可选）"})}),(0,o.jsx)(u.A,{children:"添加关卡词组"}),(0,o.jsx)(d.A,{message:"提示：请添加要包含在关卡中的词组。",type:"info",style:{marginBottom:16}}),(0,o.jsx)("div",{style:{marginBottom:16},children:(0,o.jsx)(m.Ay,{type:"dashed",icon:(0,o.jsx)(y.A,{}),onClick:()=>P(!0),style:{width:"100%"},children:"添加词组"})}),(0,o.jsxs)("div",{style:{marginBottom:16},children:[(0,o.jsxs)("div",{style:{marginBottom:8,fontSize:"12px",color:"#666"},children:["当前词组数量: ",j.length]}),(0,o.jsx)(h.A,{dataSource:j,rowKey:e=>e.id,pagination:!1,size:"small",columns:[{title:"英文",dataIndex:"text",key:"text",render:e=>e||"未设置"},{title:"中文",dataIndex:"meaning",key:"meaning",render:e=>e||"未设置"},{title:"操作",key:"action",width:80,render:(e,t)=>(0,o.jsx)(m.Ay,{type:"link",danger:!0,size:"small",icon:(0,o.jsx)(v.A,{}),onClick:()=>B(t.id)})}],locale:{emptyText:"暂无词组，请点击上方按钮添加"}})]}),(0,o.jsx)(u.A,{children:"关卡标签"}),(0,o.jsx)(s.A.Item,{name:"tagIds",label:"选择标签",help:"为关卡添加标签，便于分类和筛选",children:(0,o.jsx)(g.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择关卡标签",filterOption:(e,t)=>t?.label?.toLowerCase().includes(e.toLowerCase()),optionRender:e=>(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,o.jsx)(b.A,{color:k.find(t=>t.id===e.value)?.color,children:e.label}),k.find(t=>t.id===e.value)?.isVip&&(0,o.jsx)(b.A,{color:"gold",style:{fontSize:"10px",padding:"0 4px"},children:"VIP"})]}),options:k.map(e=>({label:e.name,value:e.id}))})}),(0,o.jsx)(s.A.Item,{children:(0,o.jsxs)(f.A,{children:[(0,o.jsx)(m.Ay,{type:"primary",htmlType:"submit",loading:r,disabled:O?.remaining===0,children:"创建关卡"}),(0,o.jsx)(m.Ay,{onClick:()=>t.back(),children:"取消"})]})})]})]}),(0,o.jsx)(x.A,{title:"添加词组",open:E,onCancel:()=>P(!1),footer:null,width:500,children:(0,o.jsxs)(s.A,{form:z,layout:"vertical",onFinish:e=>{if(j.some(t=>t.text.toLowerCase().trim()===e.text.toLowerCase().trim()))return void c.Ay.error("该英文词组已存在，请勿重复添加");let t={id:`temp_${Date.now()}`,text:e.text.trim(),meaning:e.meaning.trim()};C(e=>[...e,t]),z.resetFields(),P(!1),c.Ay.success("词组添加成功")},children:[(0,o.jsx)(s.A.Item,{name:"text",label:"英文",rules:[{required:!0,message:"请输入英文"},{validator:(e,t)=>t&&j.some(e=>e.text.toLowerCase().trim()===t.toLowerCase().trim())?Promise.reject(Error("该英文词组已存在")):Promise.resolve()}],children:(0,o.jsx)(a.A,{placeholder:"请输入英文词组"})}),(0,o.jsx)(s.A.Item,{name:"meaning",label:"中文",rules:[{required:!0,message:"请输入中文"}],children:(0,o.jsx)(a.A,{placeholder:"请输入中文意思"})}),(0,o.jsx)(s.A.Item,{children:(0,o.jsxs)(f.A,{children:[(0,o.jsx)(m.Ay,{type:"primary",htmlType:"submit",children:"添加词组"}),(0,o.jsx)(m.Ay,{onClick:()=>P(!1),children:"取消"})]})})]})})]})}},73794:(e,t,r)=>{Promise.resolve().then(r.bind(r,53698))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,6267,1658,8161,675,5336,9196,5899,8331,553,84,7783,411,7503,678,976,9778],()=>r(64675));module.exports=o})();