import request from "./request";

// 用户星级数据类型（基于API文档UserStarItemDto）
export interface UserStar {
  id: string;
  userId: string;
  levelId: string;
  stars: number; // 1-5星级评分
  completedAt: string; // ISO日期时间格式
  playTime: number; // 游戏时长（秒）
  user?: {
    id: string;
    nickname: string;
    avatar?: string;
  };
  level?: {
    id: string;
    title: string;
    difficulty: number;
  };
}

// 用户星级列表响应类型（基于API文档UserStarListResponseDto）
export interface UserStarListResponse {
  data: UserStar[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 删除统计相关类型定义

// 查询参数类型（基于API文档接口参数）
export interface UserStarQueryParams {
  startDate?: string; // 开始日期，格式：YYYY-MM-DD
  endDate?: string; // 结束日期，格式：YYYY-MM-DD
  levelId?: string; // 关卡ID
  stars?: number; // 星级筛选，1-5
  userId?: string; // 用户ID
  page?: number; // 页码，默认1
  pageSize?: number; // 每页数量，默认20，最大100
}

// 删除统计查询参数类型

// 导出参数类型
export interface UserStarExportParams {
  startDate?: string;
  endDate?: string;
  levelId?: string;
  stars?: number;
  userId?: string;
  format?: "csv" | "excel";
}

// 用户星级管理服务
export const userStarService = {
  // 获取用户星级数据（基于API文档：GET /api/v1/admin/user-stars）
  getAll: async (
    params?: UserStarQueryParams
  ): Promise<UserStarListResponse> => {
    const response = await request.get<UserStarListResponse>("/user-stars", {
      params,
    });
    return response.data;
  },

  // 删除用户星级统计方法

  // 导出用户星级数据（基于API文档：GET /api/v1/admin/user-stars/export）
  exportData: async (params?: UserStarExportParams): Promise<Blob> => {
    const response = await request.get("/user-stars/export", {
      params,
      responseType: "blob",
    });
    return response.data;
  },

  // 删除关卡星级分析方法
};

// 保持向后兼容的导出
export const getUserStars = userStarService.getAll;
