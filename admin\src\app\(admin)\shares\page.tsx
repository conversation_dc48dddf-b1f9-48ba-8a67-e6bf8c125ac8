'use client';

import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Card,
  Row,
  Col,
  Tooltip,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ShareAltOutlined,
  CheckCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { ShareService, SHARE_TYPE_OPTIONS, SHARE_PATH_TEMPLATES } from '../../../services/shareService';
import type { ShareConfig, CreateShareConfigRequest, UpdateShareConfigRequest } from '../../../types/share';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function SharesPage() {
  const [shares, setShares] = useState<ShareConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingShare, setEditingShare] = useState<ShareConfig | null>(null);
  const [form] = Form.useForm();

  // 获取分享配置列表
  const fetchShares = async () => {
    setLoading(true);
    try {
      const data = await ShareService.getAllShareConfigs();
      setShares(data);
    } catch (error) {
      message.error('获取分享配置失败');
      console.error('获取分享配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchShares();
  }, []);

  // 打开创建/编辑模态框
  const openModal = (share?: ShareConfig) => {
    setEditingShare(share || null);
    setModalVisible(true);
    
    if (share) {
      form.setFieldsValue({
        name: share.name,
        title: share.title,
        path: share.path,
        imageUrl: share.imageUrl,
        description: share.description,
        type: share.type,
        isActive: share.isActive,
        sortOrder: share.sortOrder,
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        type: 'custom',
        isActive: true,
        sortOrder: 1,
      });
    }
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setEditingShare(null);
    form.resetFields();
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingShare) {
        // 更新分享配置
        await ShareService.updateShareConfig(editingShare.id, values as UpdateShareConfigRequest);
        message.success('分享配置更新成功');
      } else {
        // 创建分享配置
        await ShareService.createShareConfig(values as CreateShareConfigRequest);
        message.success('分享配置创建成功');
      }
      
      closeModal();
      fetchShares();
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        message.error('请检查表单输入');
      } else {
        const errorMessage = error && typeof error === 'object' && 'message' in error
          ? (error as { message: string }).message
          : '操作失败';
        message.error(errorMessage);
      }
    }
  };

  // 切换启用状态
  const toggleActive = async (share: ShareConfig) => {
    try {
      await ShareService.toggleShareConfig(share.id);
      message.success(`${share.isActive ? '禁用' : '启用'}成功`);
      fetchShares();
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'message' in error
        ? (error as { message: string }).message
        : '操作失败';
      message.error(errorMessage);
    }
  };

  // 删除分享配置
  const deleteShare = async (share: ShareConfig) => {
    try {
      await ShareService.deleteShareConfig(share.id);
      message.success('删除成功');
      fetchShares();
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'message' in error
        ? (error as { message: string }).message
        : '删除失败';
      message.error(errorMessage);
    }
  };

  // 获取类型标签颜色
  const getTypeTagColor = (type: string) => {
    const colors: Record<string, string> = {
      default: 'blue',
      result: 'green',
      level: 'orange',
      achievement: 'purple',
      custom: 'gray',
    };
    return colors[type] || 'gray';
  };

  // 获取类型标签文本
  const getTypeTagText = (type: string) => {
    const option = SHARE_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  };

  // 表格列定义
  const columns = [
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string, record: ShareConfig) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {record.id}
          </Text>
        </div>
      ),
    },
    {
      title: '分享标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text: string) => (
        <Tooltip title={text}>
          <div style={{ 
            maxWidth: '180px', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '分享路径',
      dataIndex: 'path',
      key: 'path',
      width: 200,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text code style={{ 
            maxWidth: '180px', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            display: 'block'
          }}>
            {text}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={getTypeTagColor(type)}>
          {getTypeTagText(type)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'} icon={isActive ? <CheckCircleOutlined /> : <StopOutlined />}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: unknown, record: ShareConfig) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '禁用' : '启用'}>
            <Button
              type="text"
              icon={record.isActive ? <StopOutlined /> : <CheckCircleOutlined />}
              onClick={() => toggleActive(record)}
            />
          </Tooltip>
          {record.type !== 'default' && (
            <Tooltip title="删除">
              <Popconfirm
                title="确定要删除这个分享配置吗？"
                onConfirm={() => deleteShare(record)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                <ShareAltOutlined style={{ marginRight: '8px' }} />
                分享管理
              </Title>
              <Text type="secondary">
                管理微信小程序的分享配置，包括分享标题、路径和图片等
              </Text>
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => openModal()}
              >
                新建分享配置
              </Button>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={shares}
          rowKey="id"
          loading={loading}
          pagination={{
            total: shares.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingShare ? '编辑分享配置' : '新建分享配置'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={closeModal}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'custom',
            isActive: true,
            sortOrder: 1,
          }}
        >
          <Form.Item
            name="name"
            label="配置名称"
            rules={[{ required: true, message: '请输入配置名称' }]}
          >
            <Input placeholder="请输入配置名称" />
          </Form.Item>

          <Form.Item
            name="title"
            label="分享标题"
            rules={[{ required: true, message: '请输入分享标题' }]}
          >
            <Input placeholder="请输入分享标题" />
          </Form.Item>

          <Form.Item
            name="path"
            label="分享路径"
            rules={[{ required: true, message: '请输入分享路径' }]}
          >
            <Select
              placeholder="请选择或输入分享路径"
              mode="tags"
              allowClear
            >
              {SHARE_PATH_TEMPLATES.map(template => (
                <Option key={template.value} value={template.value}>
                  <div>
                    <div>{template.label}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {template.description}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="imageUrl"
            label="分享图片URL"
            rules={[
              { type: 'url', message: '请输入有效的URL' }
            ]}
          >
            <Input placeholder="请输入分享图片URL（可选）" />
          </Form.Item>

          <Form.Item
            name="description"
            label="分享描述"
          >
            <TextArea
              placeholder="请输入分享描述（可选）"
              rows={3}
              maxLength={200}
              showCount
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="分享类型"
                rules={[{ required: true, message: '请选择分享类型' }]}
              >
                <Select placeholder="请选择分享类型">
                  {SHARE_TYPE_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      <div>
                        <div>{option.label}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {option.description}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sortOrder"
                label="排序权重"
                rules={[{ required: true, message: '请输入排序权重' }]}
              >
                <InputNumber
                  min={1}
                  max={999}
                  placeholder="排序权重"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
