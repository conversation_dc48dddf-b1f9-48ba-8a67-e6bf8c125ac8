import request from './request';

// 词组数据类型
export interface Phrase {
  id: string;
  text: string;
  meaning: string;
  exampleSentence?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

// 创建词组参数
export interface CreatePhraseParams {
  text: string;
  meaning: string;
  exampleSentence?: string;
  tags?: string[];
}

// 更新词组参数
export interface UpdatePhraseParams {
  text?: string;
  meaning?: string;
  exampleSentence?: string;
  tags?: string[];
}

// 词组服务
export const phraseService = {
  // 获取所有词组
  getAll: async (): Promise<Phrase[]> => {
    const response = await request.get<Phrase[]>('/phrases');
    return response.data;
  },

  // 根据ID获取词组
  getById: async (id: string): Promise<Phrase> => {
    const response = await request.get<Phrase>(`/phrases/${id}`);
    return response.data;
  },

  // 创建词组
  create: async (params: CreatePhraseParams): Promise<Phrase> => {
    const response = await request.post<Phrase>('/phrases', params);
    return response.data;
  },

  // 更新词组
  update: async (id: string, params: UpdatePhraseParams): Promise<Phrase> => {
    const response = await request.patch<Phrase>(`/phrases/${id}`, params);
    return response.data;
  },

  // 删除词组
  delete: async (id: string): Promise<void> => {
    await request.delete(`/phrases/${id}`);
  },
};
