(()=>{var e={};e.id=9626,e.ids=[9626],e.modules={1632:(e,t,n)=>{Promise.resolve().then(n.bind(n,69880))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5948:(e,t,n)=>{"use strict";n.d(t,{A:()=>v});var r=n(43210),o=n(69662),i=n.n(o),a=n(71802),l=n(40908),s=n(42411),c=n(32476),d=n(13581),u=n(60254);let m=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},f=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:n,colorSplit:r,lineWidth:o,textPaddingInline:i,orientationMargin:a,verticalMarginInline:l}=e;return{[t]:Object.assign(Object.assign({},(0,c.dF)(e)),{borderBlockStart:`${(0,s.zA)(o)} solid ${r}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,s.zA)(o)} solid ${r}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,s.zA)(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,s.zA)(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${r}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,s.zA)(o)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${a} * 100%)`},"&::after":{width:`calc(100% - ${a} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${a} * 100%)`},"&::after":{width:`calc(${a} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:i},"&-dashed":{background:"none",borderColor:r,borderStyle:"dashed",borderWidth:`${(0,s.zA)(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:r,borderStyle:"dotted",borderWidth:`${(0,s.zA)(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:n}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:n}}})}},p=(0,d.OF)("Divider",e=>{let t=(0,u.oX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[f(t),m(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var g=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let h={small:"sm",middle:"md"},v=e=>{let{getPrefixCls:t,direction:n,className:o,style:s}=(0,a.TP)("divider"),{prefixCls:c,type:d="horizontal",orientation:u="center",orientationMargin:m,className:f,rootClassName:v,children:b,dashed:A,variant:x="solid",plain:y,style:w,size:C}=e,j=g(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),S=t("divider",c),[$,k,E]=p(S),I=h[(0,l.A)(C)],O=!!b,z=r.useMemo(()=>"left"===u?"rtl"===n?"end":"start":"right"===u?"rtl"===n?"start":"end":u,[n,u]),M="start"===z&&null!=m,N="end"===z&&null!=m,P=i()(S,o,k,E,`${S}-${d}`,{[`${S}-with-text`]:O,[`${S}-with-text-${z}`]:O,[`${S}-dashed`]:!!A,[`${S}-${x}`]:"solid"!==x,[`${S}-plain`]:!!y,[`${S}-rtl`]:"rtl"===n,[`${S}-no-default-orientation-margin-start`]:M,[`${S}-no-default-orientation-margin-end`]:N,[`${S}-${I}`]:!!I},f,v),R=r.useMemo(()=>"number"==typeof m?m:/^\d+$/.test(m)?Number(m):m,[m]);return $(r.createElement("div",Object.assign({className:P,style:Object.assign(Object.assign({},s),w)},j,{role:"separator"}),b&&"vertical"!==d&&r.createElement("span",{className:`${S}-inner-text`,style:{marginInlineStart:M?R:void 0,marginInlineEnd:N?R:void 0}},b)))}},7565:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var r=n(43210),o=n(69662),i=n.n(o),a=n(71802),l=n(52604),s=n(76285),c=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function d(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let u=["xs","sm","md","lg","xl","xxl"],m=r.forwardRef((e,t)=>{let{getPrefixCls:n,direction:o}=r.useContext(a.QO),{gutter:m,wrap:f}=r.useContext(l.A),{prefixCls:p,span:g,order:h,offset:v,push:b,pull:A,className:x,children:y,flex:w,style:C}=e,j=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),S=n("col",p),[$,k,E]=(0,s.xV)(S),I={},O={};u.forEach(t=>{let n={},r=e[t];"number"==typeof r?n.span=r:"object"==typeof r&&(n=r||{}),delete j[t],O=Object.assign(Object.assign({},O),{[`${S}-${t}-${n.span}`]:void 0!==n.span,[`${S}-${t}-order-${n.order}`]:n.order||0===n.order,[`${S}-${t}-offset-${n.offset}`]:n.offset||0===n.offset,[`${S}-${t}-push-${n.push}`]:n.push||0===n.push,[`${S}-${t}-pull-${n.pull}`]:n.pull||0===n.pull,[`${S}-rtl`]:"rtl"===o}),n.flex&&(O[`${S}-${t}-flex`]=!0,I[`--${S}-${t}-flex`]=d(n.flex))});let z=i()(S,{[`${S}-${g}`]:void 0!==g,[`${S}-order-${h}`]:h,[`${S}-offset-${v}`]:v,[`${S}-push-${b}`]:b,[`${S}-pull-${A}`]:A},x,O,k,E),M={};if(m&&m[0]>0){let e=m[0]/2;M.paddingLeft=e,M.paddingRight=e}return w&&(M.flex=d(w),!1!==f||M.minWidth||(M.minWidth=0)),$(r.createElement("div",Object.assign({},j,{style:Object.assign(Object.assign(Object.assign({},M),C),I),className:z,ref:t}),y))})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20775:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var r=n(43210),o=n(69662),i=n.n(o),a=n(57266),l=n(71802),s=n(54908),c=n(52604),d=n(76285),u=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function m(e,t){let[n,o]=r.useState("string"==typeof e?e:""),i=()=>{if("string"==typeof e&&o(e),"object"==typeof e)for(let n=0;n<a.ye.length;n++){let r=a.ye[n];if(!t||!t[r])continue;let i=e[r];if(void 0!==i)return void o(i)}};return r.useEffect(()=>{i()},[JSON.stringify(e),t]),n}let f=r.forwardRef((e,t)=>{let{prefixCls:n,justify:o,align:f,className:p,style:g,children:h,gutter:v=0,wrap:b}=e,A=u(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:x,direction:y}=r.useContext(l.QO),w=(0,s.A)(!0,null),C=m(f,w),j=m(o,w),S=x("row",n),[$,k,E]=(0,d.L3)(S),I=function(e,t){let n=[void 0,void 0],r=Array.isArray(e)?e:[e,void 0],o=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return r.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let r=0;r<a.ye.length;r++){let i=a.ye[r];if(o[i]&&void 0!==e[i]){n[t]=e[i];break}}else n[t]=e}),n}(v,w),O=i()(S,{[`${S}-no-wrap`]:!1===b,[`${S}-${j}`]:j,[`${S}-${C}`]:C,[`${S}-rtl`]:"rtl"===y},p,k,E),z={},M=null!=I[0]&&I[0]>0?-(I[0]/2):void 0;M&&(z.marginLeft=M,z.marginRight=M);let[N,P]=I;z.rowGap=P;let R=r.useMemo(()=>({gutter:[N,P],wrap:b}),[N,P,b]);return $(r.createElement(c.A.Provider,{value:R},r.createElement("div",Object.assign({},A,{className:O,style:Object.assign(Object.assign({},z),g),ref:t}),h)))})},21820:e=>{"use strict";e.exports=require("os")},26323:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(80828),o=n(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var a=n(21898);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:i}))})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52378:(e,t,n)=>{"use strict";n.d(t,{A:()=>O});var r=n(43210),o=n(69662),i=n.n(o),a=n(11056),l=n(41414),s=n(10313),c=n(56883),d=n(17727),u=n(71802),m=n(42411),f=n(73117),p=n(32476),g=n(60254),h=n(13581);let v=e=>{let{paddingXXS:t,lineWidth:n,tagPaddingHorizontal:r,componentCls:o,calc:i}=e,a=i(r).sub(n).equal(),l=i(t).sub(n).equal();return{[o]:Object.assign(Object.assign({},(0,p.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},b=e=>{let{lineWidth:t,fontSizeIcon:n,calc:r}=e,o=e.fontSizeSM;return(0,g.oX)(e,{tagFontSize:o,tagLineHeight:(0,m.zA)(r(e.lineHeightSM).mul(o).equal()),tagIconSize:r(n).sub(r(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},A=e=>({defaultBg:new f.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),x=(0,h.OF)("Tag",e=>v(b(e)),A);var y=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let w=r.forwardRef((e,t)=>{let{prefixCls:n,style:o,className:a,checked:l,onChange:s,onClick:c}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:f}=r.useContext(u.QO),p=m("tag",n),[g,h,v]=x(p),b=i()(p,`${p}-checkable`,{[`${p}-checkable-checked`]:l},null==f?void 0:f.className,a,h,v);return g(r.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:b,onClick:e=>{null==s||s(!l),null==c||c(e)}})))});var C=n(21821);let j=e=>(0,C.A)(e,(t,{textColor:n,lightBorderColor:r,lightColor:o,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:o,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),S=(0,h.bf)(["Tag","preset"],e=>j(b(e)),A),$=(e,t,n)=>{let r=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(n);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${n}`],background:e[`color${r}Bg`],borderColor:e[`color${r}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},k=(0,h.bf)(["Tag","status"],e=>{let t=b(e);return[$(t,"success","Success"),$(t,"processing","Info"),$(t,"error","Error"),$(t,"warning","Warning")]},A);var E=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let I=r.forwardRef((e,t)=>{let{prefixCls:n,className:o,rootClassName:m,style:f,children:p,icon:g,color:h,onClose:v,bordered:b=!0,visible:A}=e,y=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:C,tag:j}=r.useContext(u.QO),[$,I]=r.useState(!0),O=(0,a.A)(y,["closeIcon","closable"]);r.useEffect(()=>{void 0!==A&&I(A)},[A]);let z=(0,l.nP)(h),M=(0,l.ZZ)(h),N=z||M,P=Object.assign(Object.assign({backgroundColor:h&&!N?h:void 0},null==j?void 0:j.style),f),R=w("tag",n),[T,L,D]=x(R),B=i()(R,null==j?void 0:j.className,{[`${R}-${h}`]:N,[`${R}-has-color`]:h&&!N,[`${R}-hidden`]:!$,[`${R}-rtl`]:"rtl"===C,[`${R}-borderless`]:!b},o,m,L,D),Y=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||I(!1)},[,H]=(0,s.A)((0,s.d)(e),(0,s.d)(j),{closable:!1,closeIconRender:e=>{let t=r.createElement("span",{className:`${R}-close-icon`,onClick:Y},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var n;null==(n=null==e?void 0:e.onClick)||n.call(e,t),Y(t)},className:i()(null==e?void 0:e.className,`${R}-close-icon`)}))}}),X="function"==typeof y.onClick||p&&"a"===p.type,q=g||null,W=q?r.createElement(r.Fragment,null,q,p&&r.createElement("span",null,p)):p,G=r.createElement("span",Object.assign({},O,{ref:t,className:B,style:P}),W,H,z&&r.createElement(S,{key:"preset",prefixCls:R}),M&&r.createElement(k,{key:"status",prefixCls:R}));return T(X?r.createElement(d.A,{component:"Tag"},G):G)});I.CheckableTag=w;let O=I},52604:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(43210).createContext)({})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56306:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(80828),o=n(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var a=n(21898);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:i}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63715:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=n(65239),o=n(48088),i=n(88170),a=n.n(i),l=n(30893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);n.d(t,s);let c={children:["",{children:["(admin)",{children:["shares",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,69880)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(admin)/shares/[id]/page",pathname:"/shares/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},65184:(e,t,n)=>{Promise.resolve().then(n.bind(n,91375))},69880:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\shares\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79505:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(80828),o=n(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"};var a=n(21898);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,r.A)({},e,{ref:t,icon:i}))})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88849:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(51215),o=n.n(r);function i(e,t,n,r){var i=o().unstable_batchedUpdates?function(e){o().unstable_batchedUpdates(n,e)}:n;return null!=e&&e.addEventListener&&e.addEventListener(t,i,r),{remove:function(){null!=e&&e.removeEventListener&&e.removeEventListener(t,i,r)}}}},91375:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>eJ});var r=n(60687),o=n(43210),i=n.n(o),a=n(16189),l=n(99053),s=n(35899),c=n(70553),d=n(29220),u=n(77833),m=n(42585),f=n(96625),p=n(4691),g=n(48111),h=n(513),v=n(52378),b=n(5948),A=n(79505),x=n(69662),y=n.n(x),w=n(80828),C=n(219),j=n(95243),S=n(82853),$=n(83192),k=n(78135);function E(){return{width:document.documentElement.clientWidth,height:window.innerHeight||document.documentElement.clientHeight}}var I=n(28344),O=n(16286),z=n(88849),M=n(2291),N=n(37427),P=n(13934),R=o.createContext(null);let T=function(e){var t=e.visible,n=e.maskTransitionName,r=e.getContainer,i=e.prefixCls,a=e.rootClassName,l=e.icons,s=e.countRender,c=e.showSwitch,d=e.showProgress,u=e.current,m=e.transform,f=e.count,p=e.scale,g=e.minScale,h=e.maxScale,v=e.closeIcon,b=e.onActive,A=e.onClose,x=e.onZoomIn,w=e.onZoomOut,S=e.onRotateRight,$=e.onRotateLeft,k=e.onFlipX,E=e.onFlipY,I=e.onReset,O=e.toolbarRender,z=e.zIndex,T=e.image,L=(0,o.useContext)(R),D=l.rotateLeft,B=l.rotateRight,Y=l.zoomIn,H=l.zoomOut,X=l.close,q=l.left,W=l.right,G=l.flipX,Q=l.flipY,F="".concat(i,"-operations-operation");o.useEffect(function(){var e=function(e){e.keyCode===M.A.ESC&&A()};return t&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}},[t]);var U=function(e,t){e.preventDefault(),e.stopPropagation(),b(t)},Z=o.useCallback(function(e){var t=e.type,n=e.disabled,r=e.onClick,a=e.icon;return o.createElement("div",{key:t,className:y()(F,"".concat(i,"-operations-operation-").concat(t),(0,j.A)({},"".concat(i,"-operations-operation-disabled"),!!n)),onClick:r},a)},[F,i]),_=c?Z({icon:q,onClick:function(e){return U(e,-1)},type:"prev",disabled:0===u}):void 0,V=c?Z({icon:W,onClick:function(e){return U(e,1)},type:"next",disabled:u===f-1}):void 0,J=Z({icon:Q,onClick:E,type:"flipY"}),K=Z({icon:G,onClick:k,type:"flipX"}),ee=Z({icon:D,onClick:$,type:"rotateLeft"}),et=Z({icon:B,onClick:S,type:"rotateRight"}),en=Z({icon:H,onClick:w,type:"zoomOut",disabled:p<=g}),er=Z({icon:Y,onClick:x,type:"zoomIn",disabled:p===h}),eo=o.createElement("div",{className:"".concat(i,"-operations")},J,K,ee,et,en,er);return o.createElement(P.Ay,{visible:t,motionName:n},function(e){var t=e.className,n=e.style;return o.createElement(N.A,{open:!0,getContainer:null!=r?r:document.body},o.createElement("div",{className:y()("".concat(i,"-operations-wrapper"),t,a),style:(0,C.A)((0,C.A)({},n),{},{zIndex:z})},null===v?null:o.createElement("button",{className:"".concat(i,"-close"),onClick:A},v||X),c&&o.createElement(o.Fragment,null,o.createElement("div",{className:y()("".concat(i,"-switch-left"),(0,j.A)({},"".concat(i,"-switch-left-disabled"),0===u)),onClick:function(e){return U(e,-1)}},q),o.createElement("div",{className:y()("".concat(i,"-switch-right"),(0,j.A)({},"".concat(i,"-switch-right-disabled"),u===f-1)),onClick:function(e){return U(e,1)}},W)),o.createElement("div",{className:"".concat(i,"-footer")},d&&o.createElement("div",{className:"".concat(i,"-progress")},s?s(u+1,f):o.createElement("bdi",null,"".concat(u+1," / ").concat(f))),O?O(eo,(0,C.A)((0,C.A)({icons:{prevIcon:_,nextIcon:V,flipYIcon:J,flipXIcon:K,rotateLeftIcon:ee,rotateRightIcon:et,zoomOutIcon:en,zoomInIcon:er},actions:{onActive:b,onFlipY:E,onFlipX:k,onRotateLeft:$,onRotateRight:S,onZoomOut:w,onZoomIn:x,onReset:I,onClose:A},transform:m},L?{current:u,total:f}:{}),{},{image:T})):eo)))})};var L=n(25725),D=n(53428),B={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1},Y=n(70393);function H(e,t,n,r){var o=t+n,i=(n-r)/2;if(n>r){if(t>0)return(0,j.A)({},e,i);if(t<0&&o<r)return(0,j.A)({},e,-i)}else if(t<0||o>r)return(0,j.A)({},e,t<0?i:-i);return{}}function X(e,t,n,r){var o=E(),i=o.width,a=o.height,l=null;return e<=i&&t<=a?l={x:0,y:0}:(e>i||t>a)&&(l=(0,C.A)((0,C.A)({},H("x",n,e,i)),H("y",r,t,a))),l}function q(e){var t=e.src,n=e.isCustomPlaceholder,r=e.fallback,i=(0,o.useState)(n?"loading":"normal"),a=(0,S.A)(i,2),l=a[0],s=a[1],c=(0,o.useRef)(!1),d="error"===l;(0,o.useEffect)(function(){var e=!0;return new Promise(function(e){if(!t)return void e(!1);var n=document.createElement("img");n.onerror=function(){return e(!1)},n.onload=function(){return e(!0)},n.src=t}).then(function(t){!t&&e&&s("error")}),function(){e=!1}},[t]),(0,o.useEffect)(function(){n&&!c.current?s("loading"):d&&s("normal")},[t]);var u=function(){s("normal")};return[function(e){c.current=!1,"loading"===l&&null!=e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(c.current=!0,u())},d&&r?{src:r}:{onLoad:u,src:t},l]}function W(e,t){return Math.hypot(e.x-t.x,e.y-t.y)}var G=["fallback","src","imgRef"],Q=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],F=function(e){var t=e.fallback,n=e.src,r=e.imgRef,o=(0,k.A)(e,G),a=q({src:n,fallback:t}),l=(0,S.A)(a,2),s=l[0],c=l[1];return i().createElement("img",(0,w.A)({ref:function(e){r.current=e,s(e)}},o,c))};let U=function(e){var t,n,r,a,l,s,c,d,u,m,f,p,g,h,v,b,A,x,$,I,N,P,H,q,G,U,Z,_,V=e.prefixCls,J=e.src,K=e.alt,ee=e.imageInfo,et=e.fallback,en=e.movable,er=void 0===en||en,eo=e.onClose,ei=e.visible,ea=e.icons,el=e.rootClassName,es=e.closeIcon,ec=e.getContainer,ed=e.current,eu=void 0===ed?0:ed,em=e.count,ef=void 0===em?1:em,ep=e.countRender,eg=e.scaleStep,eh=void 0===eg?.5:eg,ev=e.minScale,eb=void 0===ev?1:ev,eA=e.maxScale,ex=void 0===eA?50:eA,ey=e.transitionName,ew=e.maskTransitionName,eC=void 0===ew?"fade":ew,ej=e.imageRender,eS=e.imgCommonProps,e$=e.toolbarRender,ek=e.onTransform,eE=e.onChange,eI=(0,k.A)(e,Q),eO=(0,o.useRef)(),ez=(0,o.useContext)(R),eM=ez&&ef>1,eN=ez&&ef>=1,eP=(0,o.useState)(!0),eR=(0,S.A)(eP,2),eT=eR[0],eL=eR[1],eD=(t=(0,o.useRef)(null),n=(0,o.useRef)([]),r=(0,o.useState)(B),l=(a=(0,S.A)(r,2))[0],s=a[1],c=function(e,r){null===t.current&&(n.current=[],t.current=(0,D.A)(function(){s(function(e){var o=e;return n.current.forEach(function(e){o=(0,C.A)((0,C.A)({},o),e)}),t.current=null,null==ek||ek({transform:o,action:r}),o})})),n.current.push((0,C.A)((0,C.A)({},l),e))},{transform:l,resetTransform:function(e){s(B),(0,L.A)(B,l)||null==ek||ek({transform:B,action:e})},updateTransform:c,dispatchZoomChange:function(e,t,n,r,o){var i=eO.current,a=i.width,s=i.height,d=i.offsetWidth,u=i.offsetHeight,m=i.offsetLeft,f=i.offsetTop,p=e,g=l.scale*e;g>ex?(g=ex,p=ex/l.scale):g<eb&&(p=(g=o?g:eb)/l.scale);var h=null!=r?r:innerHeight/2,v=p-1,b=v*((null!=n?n:innerWidth/2)-l.x-m),A=v*(h-l.y-f),x=l.x-(b-v*a*.5),y=l.y-(A-v*s*.5);if(e<1&&1===g){var w=d*g,C=u*g,j=E(),S=j.width,$=j.height;w<=S&&C<=$&&(x=0,y=0)}c({x:x,y:y,scale:g},t)}}),eB=eD.transform,eY=eD.resetTransform,eH=eD.updateTransform,eX=eD.dispatchZoomChange,eq=(d=eB.rotate,u=eB.scale,m=eB.x,f=eB.y,p=(0,o.useState)(!1),h=(g=(0,S.A)(p,2))[0],v=g[1],b=(0,o.useRef)({diffX:0,diffY:0,transformX:0,transformY:0}),A=function(e){ei&&h&&eH({x:e.pageX-b.current.diffX,y:e.pageY-b.current.diffY},"move")},x=function(){if(ei&&h){v(!1);var e=b.current,t=e.transformX,n=e.transformY;if(m!==t&&f!==n){var r=eO.current.offsetWidth*u,o=eO.current.offsetHeight*u,i=eO.current.getBoundingClientRect(),a=i.left,l=i.top,s=d%180!=0,c=X(s?o:r,s?r:o,a,l);c&&eH((0,C.A)({},c),"dragRebound")}}},(0,o.useEffect)(function(){var e,t,n,r;if(er){n=(0,z.A)(window,"mouseup",x,!1),r=(0,z.A)(window,"mousemove",A,!1);try{window.top!==window.self&&(e=(0,z.A)(window.top,"mouseup",x,!1),t=(0,z.A)(window.top,"mousemove",A,!1))}catch(e){(0,Y.$e)(!1,"[rc-image] ".concat(e))}}return function(){var o,i,a,l;null==(o=n)||o.remove(),null==(i=r)||i.remove(),null==(a=e)||a.remove(),null==(l=t)||l.remove()}},[ei,h,m,f,d,er]),{isMoving:h,onMouseDown:function(e){er&&0===e.button&&(e.preventDefault(),e.stopPropagation(),b.current={diffX:e.pageX-m,diffY:e.pageY-f,transformX:m,transformY:f},v(!0))},onMouseMove:A,onMouseUp:x,onWheel:function(e){if(ei&&0!=e.deltaY){var t=1+Math.min(Math.abs(e.deltaY/100),1)*eh;e.deltaY>0&&(t=1/t),eX(t,"wheel",e.clientX,e.clientY)}}}),eW=eq.isMoving,eG=eq.onMouseDown,eQ=eq.onWheel,eF=($=eB.rotate,I=eB.scale,N=eB.x,P=eB.y,H=(0,o.useState)(!1),G=(q=(0,S.A)(H,2))[0],U=q[1],Z=(0,o.useRef)({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),_=function(e){Z.current=(0,C.A)((0,C.A)({},Z.current),e)},(0,o.useEffect)(function(){var e;return ei&&er&&(e=(0,z.A)(window,"touchmove",function(e){return e.preventDefault()},{passive:!1})),function(){var t;null==(t=e)||t.remove()}},[ei,er]),{isTouching:G,onTouchStart:function(e){if(er){e.stopPropagation(),U(!0);var t=e.touches,n=void 0===t?[]:t;n.length>1?_({point1:{x:n[0].clientX,y:n[0].clientY},point2:{x:n[1].clientX,y:n[1].clientY},eventType:"touchZoom"}):_({point1:{x:n[0].clientX-N,y:n[0].clientY-P},eventType:"move"})}},onTouchMove:function(e){var t=e.touches,n=void 0===t?[]:t,r=Z.current,o=r.point1,i=r.point2,a=r.eventType;if(n.length>1&&"touchZoom"===a){var l={x:n[0].clientX,y:n[0].clientY},s={x:n[1].clientX,y:n[1].clientY},c=function(e,t,n,r){var o=W(e,n),i=W(t,r);if(0===o&&0===i)return[e.x,e.y];var a=o/(o+i);return[e.x+a*(t.x-e.x),e.y+a*(t.y-e.y)]}(o,i,l,s),d=(0,S.A)(c,2),u=d[0],m=d[1];eX(W(l,s)/W(o,i),"touchZoom",u,m,!0),_({point1:l,point2:s,eventType:"touchZoom"})}else"move"===a&&(eH({x:n[0].clientX-o.x,y:n[0].clientY-o.y},"move"),_({eventType:"move"}))},onTouchEnd:function(){if(ei){if(G&&U(!1),_({eventType:"none"}),eb>I)return eH({x:0,y:0,scale:eb},"touchZoom");var e=eO.current.offsetWidth*I,t=eO.current.offsetHeight*I,n=eO.current.getBoundingClientRect(),r=n.left,o=n.top,i=$%180!=0,a=X(i?t:e,i?e:t,r,o);a&&eH((0,C.A)({},a),"dragRebound")}}}),eU=eF.isTouching,eZ=eF.onTouchStart,e_=eF.onTouchMove,eV=eF.onTouchEnd,eJ=eB.rotate,eK=eB.scale,e0=y()((0,j.A)({},"".concat(V,"-moving"),eW));(0,o.useEffect)(function(){eT||eL(!0)},[eT]);var e1=function(e){var t=eu+e;!Number.isInteger(t)||t<0||t>ef-1||(eL(!1),eY(e<0?"prev":"next"),null==eE||eE(t,eu))},e2=function(e){ei&&eM&&(e.keyCode===M.A.LEFT?e1(-1):e.keyCode===M.A.RIGHT&&e1(1))};(0,o.useEffect)(function(){var e=(0,z.A)(window,"keydown",e2,!1);return function(){e.remove()}},[ei,eM,eu]);var e8=i().createElement(F,(0,w.A)({},eS,{width:e.width,height:e.height,imgRef:eO,className:"".concat(V,"-img"),alt:K,style:{transform:"translate3d(".concat(eB.x,"px, ").concat(eB.y,"px, 0) scale3d(").concat(eB.flipX?"-":"").concat(eK,", ").concat(eB.flipY?"-":"").concat(eK,", 1) rotate(").concat(eJ,"deg)"),transitionDuration:(!eT||eU)&&"0s"},fallback:et,src:J,onWheel:eQ,onMouseDown:eG,onDoubleClick:function(e){ei&&(1!==eK?eH({x:0,y:0,scale:1},"doubleClick"):eX(1+eh,"doubleClick",e.clientX,e.clientY))},onTouchStart:eZ,onTouchMove:e_,onTouchEnd:eV,onTouchCancel:eV})),e4=(0,C.A)({url:J,alt:K},ee);return i().createElement(i().Fragment,null,i().createElement(O.A,(0,w.A)({transitionName:void 0===ey?"zoom":ey,maskTransitionName:eC,closable:!1,keyboard:!0,prefixCls:V,onClose:eo,visible:ei,classNames:{wrapper:e0},rootClassName:el,getContainer:ec},eI,{afterClose:function(){eY("close")}}),i().createElement("div",{className:"".concat(V,"-img-wrapper")},ej?ej(e8,(0,C.A)({transform:eB,image:e4},ez?{current:eu}:{})):e8)),i().createElement(T,{visible:ei,transform:eB,maskTransitionName:eC,closeIcon:es,getContainer:ec,prefixCls:V,rootClassName:el,icons:void 0===ea?{}:ea,countRender:ep,showSwitch:eM,showProgress:eN,current:eu,count:ef,scale:eK,minScale:eb,maxScale:ex,toolbarRender:e$,onActive:e1,onZoomIn:function(){eX(1+eh,"zoomIn")},onZoomOut:function(){eX(1/(1+eh),"zoomOut")},onRotateRight:function(){eH({rotate:eJ+90},"rotateRight")},onRotateLeft:function(){eH({rotate:eJ-90},"rotateLeft")},onFlipX:function(){eH({flipX:!eB.flipX},"flipX")},onFlipY:function(){eH({flipY:!eB.flipY},"flipY")},onClose:eo,onReset:function(){eY("reset")},zIndex:void 0!==eI.zIndex?eI.zIndex+1:void 0,image:e4}))};var Z=n(78651),_=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"],V=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],J=["src"],K=0,ee=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],et=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],en=function(e){var t,n,r,i,a=e.src,l=e.alt,s=e.onPreviewClose,c=e.prefixCls,d=void 0===c?"rc-image":c,u=e.previewPrefixCls,m=void 0===u?"".concat(d,"-preview"):u,f=e.placeholder,p=e.fallback,g=e.width,h=e.height,v=e.style,b=e.preview,A=void 0===b||b,x=e.className,E=e.onClick,O=e.onError,z=e.wrapperClassName,M=e.wrapperStyle,N=e.rootClassName,P=(0,k.A)(e,ee),T="object"===(0,$.A)(A)?A:{},L=T.src,D=T.visible,B=void 0===D?void 0:D,Y=T.onVisibleChange,H=T.getContainer,X=T.mask,W=T.maskClassName,G=T.movable,Q=T.icons,F=T.scaleStep,Z=T.minScale,V=T.maxScale,J=T.imageRender,en=T.toolbarRender,er=(0,k.A)(T,et),eo=null!=L?L:a,ei=(0,I.A)(!!B,{value:B,onChange:void 0===Y?s:Y}),ea=(0,S.A)(ei,2),el=ea[0],es=ea[1],ec=q({src:a,isCustomPlaceholder:f&&!0!==f,fallback:p}),ed=(0,S.A)(ec,3),eu=ed[0],em=ed[1],ef=ed[2],ep=(0,o.useState)(null),eg=(0,S.A)(ep,2),eh=eg[0],ev=eg[1],eb=(0,o.useContext)(R),eA=!!A,ex=y()(d,z,N,(0,j.A)({},"".concat(d,"-error"),"error"===ef)),ey=(0,o.useMemo)(function(){var t={};return _.forEach(function(n){void 0!==e[n]&&(t[n]=e[n])}),t},_.map(function(t){return e[t]})),ew=(0,o.useMemo)(function(){return(0,C.A)((0,C.A)({},ey),{},{src:eo})},[eo,ey]),eC=(t=o.useState(function(){return String(K+=1)}),n=(0,S.A)(t,1)[0],r=o.useContext(R),i={data:ew,canPreview:eA},o.useEffect(function(){if(r)return r.register(n,i)},[]),o.useEffect(function(){r&&r.register(n,i)},[eA,ew]),n);return o.createElement(o.Fragment,null,o.createElement("div",(0,w.A)({},P,{className:ex,onClick:eA?function(e){var t,n,r=(t=e.target.getBoundingClientRect(),n=document.documentElement,{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}),o=r.left,i=r.top;eb?eb.onPreview(eC,eo,o,i):(ev({x:o,y:i}),es(!0)),null==E||E(e)}:E,style:(0,C.A)({width:g,height:h},M)}),o.createElement("img",(0,w.A)({},ey,{className:y()("".concat(d,"-img"),(0,j.A)({},"".concat(d,"-img-placeholder"),!0===f),x),style:(0,C.A)({height:h},v),ref:eu},em,{width:g,height:h,onError:O})),"loading"===ef&&o.createElement("div",{"aria-hidden":"true",className:"".concat(d,"-placeholder")},f),X&&eA&&o.createElement("div",{className:y()("".concat(d,"-mask"),W),style:{display:(null==v?void 0:v.display)==="none"?"none":void 0}},X)),!eb&&eA&&o.createElement(U,(0,w.A)({"aria-hidden":!el,visible:el,prefixCls:m,onClose:function(){es(!1),ev(null)},mousePosition:eh,src:eo,alt:l,imageInfo:{width:g,height:h},fallback:p,getContainer:void 0===H?void 0:H,icons:Q,movable:G,scaleStep:F,minScale:Z,maxScale:V,rootClassName:N,imageRender:J,imgCommonProps:ey,toolbarRender:en},er)))};en.PreviewGroup=function(e){var t,n,r,i,a,l,s=e.previewPrefixCls,c=e.children,d=e.icons,u=e.items,m=e.preview,f=e.fallback,p="object"===(0,$.A)(m)?m:{},g=p.visible,h=p.onVisibleChange,v=p.getContainer,b=p.current,A=p.movable,x=p.minScale,y=p.maxScale,E=p.countRender,O=p.closeIcon,z=p.onChange,M=p.onTransform,N=p.toolbarRender,P=p.imageRender,T=(0,k.A)(p,V),L=(t=o.useState({}),r=(n=(0,S.A)(t,2))[0],i=n[1],a=o.useCallback(function(e,t){return i(function(n){return(0,C.A)((0,C.A)({},n),{},(0,j.A)({},e,t))}),function(){i(function(t){var n=(0,C.A)({},t);return delete n[e],n})}},[]),[o.useMemo(function(){return u?u.map(function(e){if("string"==typeof e)return{data:{src:e}};var t={};return Object.keys(e).forEach(function(n){["src"].concat((0,Z.A)(_)).includes(n)&&(t[n]=e[n])}),{data:t}}):Object.keys(r).reduce(function(e,t){var n=r[t],o=n.canPreview,i=n.data;return o&&e.push({data:i,id:t}),e},[])},[u,r]),a,!!u]),D=(0,S.A)(L,3),B=D[0],Y=D[1],H=D[2],X=(0,I.A)(0,{value:b}),q=(0,S.A)(X,2),W=q[0],G=q[1],Q=(0,o.useState)(!1),F=(0,S.A)(Q,2),K=F[0],ee=F[1],et=(null==(l=B[W])?void 0:l.data)||{},en=et.src,er=(0,k.A)(et,J),eo=(0,I.A)(!!g,{value:g,onChange:function(e,t){null==h||h(e,t,W)}}),ei=(0,S.A)(eo,2),ea=ei[0],el=ei[1],es=(0,o.useState)(null),ec=(0,S.A)(es,2),ed=ec[0],eu=ec[1],em=o.useCallback(function(e,t,n,r){var o=H?B.findIndex(function(e){return e.data.src===t}):B.findIndex(function(t){return t.id===e});G(o<0?0:o),el(!0),eu({x:n,y:r}),ee(!0)},[B,H]);o.useEffect(function(){ea?K||G(0):ee(!1)},[ea]);var ef=o.useMemo(function(){return{register:Y,onPreview:em}},[Y,em]);return o.createElement(R.Provider,{value:ef},c,o.createElement(U,(0,w.A)({"aria-hidden":!ea,movable:A,visible:ea,prefixCls:void 0===s?"rc-image-preview":s,closeIcon:O,onClose:function(){el(!1),eu(null)},mousePosition:ed,imgCommonProps:er,src:en,fallback:f,icons:void 0===d?{}:d,minScale:x,maxScale:y,getContainer:v,current:W,count:B.length,countRender:E,onTransform:M,toolbarRender:N,imageRender:P,onChange:function(e,t){G(e),null==z||z(e,t)}},T)))};var er=n(18130),eo=n(50604),ei=n(71802),ea=n(59897),el=n(48232),es=n(15693),ec=n(92799),ed=n(57314),eu={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},em=n(21898),ef=o.forwardRef(function(e,t){return o.createElement(em.A,(0,w.A)({},e,{ref:t,icon:eu}))}),ep={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},eg=o.forwardRef(function(e,t){return o.createElement(em.A,(0,w.A)({},e,{ref:t,icon:ep}))});let eh={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var ev=o.forwardRef(function(e,t){return o.createElement(em.A,(0,w.A)({},e,{ref:t,icon:eh}))});let eb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"};var eA=o.forwardRef(function(e,t){return o.createElement(em.A,(0,w.A)({},e,{ref:t,icon:eb}))});let ex={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"};var ey=o.forwardRef(function(e,t){return o.createElement(em.A,(0,w.A)({},e,{ref:t,icon:ex}))}),ew=n(42411),eC=n(73117),ej=n(55354),eS=n(32476),e$=n(11908),ek=n(31549),eE=n(13581),eI=n(60254);let eO=e=>({position:e||"absolute",inset:0}),ez=e=>{let{iconCls:t,motionDurationSlow:n,paddingXXS:r,marginXXS:o,prefixCls:i,colorTextLightSolid:a}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:a,background:new eC.Y("#000").setA(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${n}`,[`.${i}-mask-info`]:Object.assign(Object.assign({},eS.L9),{padding:`0 ${(0,ew.zA)(r)}`,[t]:{marginInlineEnd:o,svg:{verticalAlign:"baseline"}}})}},eM=e=>{let{previewCls:t,modalMaskBg:n,paddingSM:r,marginXL:o,margin:i,paddingLG:a,previewOperationColorDisabled:l,previewOperationHoverColor:s,motionDurationSlow:c,iconCls:d,colorTextLightSolid:u}=e,m=new eC.Y(n).setA(.1),f=m.clone().setA(.2);return{[`${t}-footer`]:{position:"fixed",bottom:o,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"},[`${t}-progress`]:{marginBottom:i},[`${t}-close`]:{position:"fixed",top:o,right:{_skip_check_:!0,value:o},display:"flex",color:u,backgroundColor:m.toRgbString(),borderRadius:"50%",padding:r,outline:0,border:0,cursor:"pointer",transition:`all ${c}`,"&:hover":{backgroundColor:f.toRgbString()},[`& > ${d}`]:{fontSize:e.previewOperationSize}},[`${t}-operations`]:{display:"flex",alignItems:"center",padding:`0 ${(0,ew.zA)(a)}`,backgroundColor:m.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:r,padding:r,cursor:"pointer",transition:`all ${c}`,userSelect:"none",[`&:not(${t}-operations-operation-disabled):hover > ${d}`]:{color:s},"&-disabled":{color:l,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},[`& > ${d}`]:{fontSize:e.previewOperationSize}}}}},eN=e=>{let{modalMaskBg:t,iconCls:n,previewOperationColorDisabled:r,previewCls:o,zIndexPopup:i,motionDurationSlow:a}=e,l=new eC.Y(t).setA(.1),s=l.clone().setA(.2);return{[`${o}-switch-left, ${o}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(i).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:l.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${a}`,userSelect:"none","&:hover":{background:s.toRgbString()},"&-disabled":{"&, &:hover":{color:r,background:"transparent",cursor:"not-allowed",[`> ${n}`]:{cursor:"not-allowed"}}},[`> ${n}`]:{fontSize:e.previewOperationSize}},[`${o}-switch-left`]:{insetInlineStart:e.marginSM},[`${o}-switch-right`]:{insetInlineEnd:e.marginSM}}},eP=e=>{let{motionEaseOut:t,previewCls:n,motionDurationSlow:r,componentCls:o}=e;return[{[`${o}-preview-root`]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${n}-body`]:Object.assign(Object.assign({},eO()),{overflow:"hidden"}),[`${n}-img`]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${r} ${t} 0s`,userSelect:"none","&-wrapper":Object.assign(Object.assign({},eO()),{transition:`transform ${r} ${t} 0s`,display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${n}-moving`]:{[`${n}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${o}-preview-root`]:{[`${n}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${o}-preview-operations-wrapper`]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()},"&":[eM(e),eN(e)]}]},eR=e=>{let{componentCls:t}=e;return{[t]:{position:"relative",display:"inline-block",[`${t}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${t}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${t}-mask`]:Object.assign({},ez(e)),[`${t}-mask:hover`]:{opacity:1},[`${t}-placeholder`]:Object.assign({},eO())}}},eT=e=>{let{previewCls:t}=e;return{[`${t}-root`]:(0,e$.aB)(e,"zoom"),"&":(0,ek.p9)(e,!0)}},eL=(0,eE.OF)("Image",e=>{let t=`${e.componentCls}-preview`,n=(0,eI.oX)(e,{previewCls:t,modalMaskBg:new eC.Y("#000").setA(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[eR(n),eP(n),(0,ej.Dk)((0,eI.oX)(n,{componentCls:t})),eT(n)]},e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new eC.Y(e.colorTextLightSolid).setA(.65).toRgbString(),previewOperationHoverColor:new eC.Y(e.colorTextLightSolid).setA(.85).toRgbString(),previewOperationColorDisabled:new eC.Y(e.colorTextLightSolid).setA(.25).toRgbString(),previewOperationSize:1.5*e.fontSizeIcon}));var eD=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let eB={rotateLeft:o.createElement(ef,null),rotateRight:o.createElement(eg,null),zoomIn:o.createElement(eA,null),zoomOut:o.createElement(ey,null),close:o.createElement(es.A,null),left:o.createElement(ec.A,null),right:o.createElement(ed.A,null),flipX:o.createElement(ev,null),flipY:o.createElement(ev,{rotate:90})};var eY=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let eH=e=>{let{prefixCls:t,preview:n,className:r,rootClassName:i,style:a}=e,l=eY(e,["prefixCls","preview","className","rootClassName","style"]),{getPrefixCls:s,getPopupContainer:c,className:d,style:u,preview:m}=(0,ei.TP)("image"),[f]=(0,el.A)("Image"),p=s("image",t),g=s(),h=(0,ea.A)(p),[v,b,x]=eL(p,h),w=y()(i,b,x,h),C=y()(r,b,d),[j]=(0,er.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),S=o.useMemo(()=>{if(!1===n)return n;let e="object"==typeof n?n:{},{getContainer:t,closeIcon:r,rootClassName:i,destroyOnClose:a,destroyOnHidden:l}=e,s=eY(e,["getContainer","closeIcon","rootClassName","destroyOnClose","destroyOnHidden"]);return Object.assign(Object.assign({mask:o.createElement("div",{className:`${p}-mask-info`},o.createElement(A.A,null),null==f?void 0:f.preview),icons:eB},s),{destroyOnClose:null!=l?l:a,rootClassName:y()(w,i),getContainer:null!=t?t:c,transitionName:(0,eo.b)(g,"zoom",e.transitionName),maskTransitionName:(0,eo.b)(g,"fade",e.maskTransitionName),zIndex:j,closeIcon:null!=r?r:null==m?void 0:m.closeIcon})},[n,f,null==m?void 0:m.closeIcon]),$=Object.assign(Object.assign({},u),a);return v(o.createElement(en,Object.assign({prefixCls:p,preview:S,rootClassName:w,className:C,style:$},l)))};eH.PreviewGroup=e=>{var{previewPrefixCls:t,preview:n}=e,r=eD(e,["previewPrefixCls","preview"]);let{getPrefixCls:i,direction:a}=o.useContext(ei.QO),l=i("image",t),s=`${l}-preview`,c=i(),d=(0,ea.A)(l),[u,m,f]=eL(l,d),[p]=(0,er.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),g=o.useMemo(()=>Object.assign(Object.assign({},eB),{left:"rtl"===a?o.createElement(ed.A,null):o.createElement(ec.A,null),right:"rtl"===a?o.createElement(ec.A,null):o.createElement(ed.A,null)}),[a]),h=o.useMemo(()=>{var e;if(!1===n)return n;let t="object"==typeof n?n:{},r=y()(m,f,d,null!=(e=t.rootClassName)?e:"");return Object.assign(Object.assign({},t),{transitionName:(0,eo.b)(c,"zoom",t.transitionName),maskTransitionName:(0,eo.b)(c,"fade",t.maskTransitionName),rootClassName:r,zIndex:p})},[n]);return u(o.createElement(en.PreviewGroup,Object.assign({preview:h,previewPrefixCls:s,icons:g},r)))};var eX=n(85975),eq=n(3788),eW=n(26323),eG=n(56306),eQ=n(34308),eF=n(80282),eU=n(43910);let{Title:eZ,Text:e_,Paragraph:eV}=l.A;function eJ(){let e=(0,a.useParams)(),t=(0,a.useRouter)(),[n,i]=(0,o.useState)(null),[l,A]=(0,o.useState)(!0),x=e.id,y=async()=>{if(x){A(!0);try{let e=await eU.Dw.getShareConfigById(x);i(e)}catch(e){s.Ay.error("获取分享配置详情失败"),console.error("获取分享配置详情失败:",e)}finally{A(!1)}}},w=()=>{t.push("/shares")},C=async()=>{if(n)try{await eU.Dw.toggleShareConfig(n.id),s.Ay.success(`${n.isActive?"禁用":"启用"}成功`),y()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"操作失败";s.Ay.error(e)}},j=e=>{navigator.clipboard.writeText(e).then(()=>{s.Ay.success("已复制到剪贴板")}).catch(()=>{s.Ay.error("复制失败")})};return l?(0,r.jsx)("div",{style:{padding:"24px",textAlign:"center"},children:(0,r.jsx)(c.A,{size:"large"})}):n?(0,r.jsx)("div",{style:{padding:"24px"},children:(0,r.jsxs)(m.A,{children:[(0,r.jsx)("div",{style:{marginBottom:"24px"},children:(0,r.jsxs)(f.A,{justify:"space-between",align:"middle",children:[(0,r.jsx)(p.A,{children:(0,r.jsxs)(g.A,{children:[(0,r.jsx)(u.Ay,{icon:(0,r.jsx)(eX.A,{}),onClick:w,children:"返回"}),(0,r.jsxs)(eZ,{level:3,style:{margin:0},children:[(0,r.jsx)(eq.A,{style:{marginRight:"8px"}}),"分享配置详情"]})]})}),(0,r.jsx)(p.A,{children:(0,r.jsxs)(g.A,{children:[(0,r.jsx)(u.Ay,{type:n.isActive?"default":"primary",icon:n.isActive?(0,r.jsx)(eW.A,{}):(0,r.jsx)(eG.A,{}),onClick:C,children:n.isActive?"禁用":"启用"}),(0,r.jsx)(u.Ay,{type:"primary",icon:(0,r.jsx)(eQ.A,{}),onClick:()=>{t.push(`/shares/${x}/edit`)},children:"编辑"})]})})]})}),(0,r.jsxs)(h.A,{title:"基本信息",bordered:!0,column:2,size:"middle",children:[(0,r.jsx)(h.A.Item,{label:"配置ID",span:2,children:(0,r.jsxs)(g.A,{children:[(0,r.jsx)(e_,{code:!0,children:n.id}),(0,r.jsx)(u.Ay,{type:"text",size:"small",icon:(0,r.jsx)(eF.A,{}),onClick:()=>j(n.id)})]})}),(0,r.jsx)(h.A.Item,{label:"配置名称",children:n.name}),(0,r.jsx)(h.A.Item,{label:"分享类型",children:(0,r.jsx)(v.A,{color:{default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"}[n.type]||"gray",children:(e=>{let t=eU.WS.find(t=>t.value===e);return t?.label||e})(n.type)})}),(0,r.jsx)(h.A.Item,{label:"启用状态",children:(0,r.jsx)(v.A,{color:n.isActive?"success":"default",icon:n.isActive?(0,r.jsx)(eG.A,{}):(0,r.jsx)(eW.A,{}),children:n.isActive?"启用":"禁用"})}),(0,r.jsx)(h.A.Item,{label:"排序权重",children:n.sortOrder}),(0,r.jsx)(h.A.Item,{label:"创建时间",children:new Date(n.createdAt).toLocaleString()}),(0,r.jsx)(h.A.Item,{label:"更新时间",children:new Date(n.updatedAt).toLocaleString()})]}),(0,r.jsx)(b.A,{}),(0,r.jsx)(eZ,{level:4,children:"分享内容"}),(0,r.jsxs)(h.A,{bordered:!0,column:1,size:"middle",children:[(0,r.jsx)(h.A.Item,{label:"分享标题",children:(0,r.jsxs)(g.A,{children:[(0,r.jsx)(e_,{strong:!0,children:n.title}),(0,r.jsx)(u.Ay,{type:"text",size:"small",icon:(0,r.jsx)(eF.A,{}),onClick:()=>j(n.title)})]})}),(0,r.jsx)(h.A.Item,{label:"分享路径",children:(0,r.jsxs)(g.A,{children:[(0,r.jsx)(e_,{code:!0,children:n.path}),(0,r.jsx)(u.Ay,{type:"text",size:"small",icon:(0,r.jsx)(eF.A,{}),onClick:()=>j(n.path)})]})}),n.description&&(0,r.jsx)(h.A.Item,{label:"分享描述",children:(0,r.jsx)(eV,{children:n.description})}),n.imageUrl&&(0,r.jsx)(h.A.Item,{label:"分享图片",children:(0,r.jsxs)(g.A,{direction:"vertical",children:[(0,r.jsxs)(g.A,{children:[(0,r.jsx)(e_,{code:!0,children:n.imageUrl}),(0,r.jsx)(u.Ay,{type:"text",size:"small",icon:(0,r.jsx)(eF.A,{}),onClick:()=>j(n.imageUrl)})]}),(0,r.jsx)(eH,{src:n.imageUrl,alt:"分享图片",style:{maxWidth:"300px",maxHeight:"200px"},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"})]})})]}),(0,r.jsx)(b.A,{}),(0,r.jsx)(eZ,{level:4,children:"使用说明"}),(0,r.jsx)(d.A,{message:"微信小程序分享配置",description:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:"该配置可用于微信小程序的分享功能，小程序端可通过以下API获取："}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsx)(e_,{code:!0,children:"GET /api/v1/weixin/share-config"})," - 获取所有分享配置"]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)(e_,{code:!0,children:["GET /api/v1/weixin/share-config/",n.type]})," - 获取指定类型的分享配置"]})]}),(0,r.jsxs)("p",{children:["在小程序中使用时，可以在页面的 ",(0,r.jsx)(e_,{code:!0,children:"onShareAppMessage"})," 方法中返回这些配置。"]})]}),type:"info",showIcon:!0})]})}):(0,r.jsx)("div",{style:{padding:"24px"},children:(0,r.jsx)(d.A,{message:"分享配置不存在",description:"请检查URL是否正确，或者该配置已被删除。",type:"error",showIcon:!0,action:(0,r.jsx)(u.Ay,{size:"small",onClick:w,children:"返回列表"})})})}},94735:e=>{"use strict";e.exports=require("events")},97058:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=function(...e){let t={};return e.forEach(e=>{e&&Object.keys(e).forEach(n=>{void 0!==e[n]&&(t[n]=e[n])})}),t}}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[4447,6267,1658,8161,675,5336,5899,553,7503,513,976,3135],()=>n(63715));module.exports=r})();