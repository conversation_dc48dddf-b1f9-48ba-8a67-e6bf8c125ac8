(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3987],{41552:(e,l,t)=>{Promise.resolve().then(t.bind(t,67465))},67465:(e,l,t)=>{"use strict";t.r(l),t.d(l,{default:()=>E});var s=t(95155),a=t(11518),i=t.n(a),n=t(12115),r=t(37974),o=t(77325),c=t(58032),d=t(12320),h=t(26922),x=t(27212),g=t(19361),u=t(74947),p=t(6124),j=t(44297),y=t(56020),m=t(20778),v=t(51087),f=t(90285),A=t(64413),S=t(52092),k=t(79659),w=t(80392),b=t(56170),z=t(46996),C=t(88870),I=t(34140),N=t(35695),P=t(49179);function B(){let e=(0,N.useRouter)(),l=(0,N.useSearchParams)(),[t,a]=(0,n.useState)([]),[B,E]=(0,n.useState)(!1),[L,T]=(0,n.useState)(null),[V,_]=(0,n.useState)([]),[O,F]=(0,n.useState)({}),[H,R]=(0,n.useState)({}),[W,D]=(0,n.useState)(null),[J,K]=(0,n.useState)(""),[Q,q]=(0,n.useState)(void 0),[G,M]=(0,n.useState)(void 0),[U,X]=(0,n.useState)({current:1,pageSize:20,total:0,totalPages:0}),Y=async e=>{E(!0);try{let l={search:J||void 0,difficulty:Q,tagId:G,page:(null==e?void 0:e.page)||U.current,pageSize:(null==e?void 0:e.pageSize)||U.pageSize,...e},t=await P.k3.getAll(l);a(t.levels),X({current:t.page,pageSize:t.pageSize,total:t.total,totalPages:t.totalPages})}catch(e){f.i.error("获取关卡列表失败"),a([])}finally{E(!1)}},Z=async()=>{try{let e=await P.k3.getCount();T(e)}catch(e){f.i.error("获取关卡统计失败")}},$=async()=>{try{let e=await P.m9.getAll();_(e.filter(e=>"active"===e.status))}catch(e){console.error("获取标签列表失败:",e)}},ee=async e=>{if(!O[e]&&!H[e]){R(l=>({...l,[e]:!0}));try{let l=await P.k3.getLevelStarAnalytics(e);F(t=>({...t,[e]:l}))}catch(e){console.error("获取关卡星级统计失败:",e)}finally{R(l=>({...l,[e]:!1}))}}};(0,n.useEffect)(()=>{Y(),Z(),$()},[]),(0,n.useEffect)(()=>{Y({page:1})},[J,Q,G]),(0,n.useEffect)(()=>{let e=l.get("highlight");e&&(D(e),setTimeout(()=>{D(null)},3e3))},[l]);let el=async e=>{try{await P.k3.delete(e),f.i.success("关卡删除成功"),Y(),Z()}catch(e){f.i.error("删除关卡失败")}},et=(e,l)=>{Y({page:e,pageSize:l})},es=async e=>{var l,t,a;try{console.log("正在获取关卡详情，ID:",e);let t=await P.k3.getWithPhrases(e);console.log("获取到的关卡详情:",t),f.a.info({title:"关卡详情 - ".concat(t.name),width:800,content:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"难度:"})," ",t.difficulty]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"描述:"})," ",t.description||"无"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"词组数量:"})," ",(null==(l=t.phrases)?void 0:l.length)||0]}),(0,s.jsxs)("div",{style:{marginBottom:16},children:[(0,s.jsx)("strong",{children:"关卡标签:"}),(0,s.jsx)("div",{style:{marginTop:8},children:t.tagIds&&t.tagIds.length>0?t.tagIds.map(e=>{let l=V.find(l=>l.id===e);return l?(0,s.jsxs)(r.A,{color:l.color,style:{margin:4},children:[l.name,l.isVip&&(0,s.jsx)("span",{style:{marginLeft:4,fontSize:"10px"},children:"VIP"})]},e):null}):(0,s.jsx)("span",{style:{color:"#999"},children:"无标签"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"包含的词组:"}),(0,s.jsx)("div",{style:{marginTop:8,maxHeight:300,overflow:"auto"},children:t.phrases&&t.phrases.length>0?t.phrases.map(e=>(0,s.jsx)(r.A,{style:{margin:4},children:e.text},e.id)):(0,s.jsx)("span",{style:{color:"#999"},children:"暂无词组"})})]})]})})}catch(e){console.error("获取关卡详情失败:",e),f.i.error("获取关卡详情失败: ".concat((null==(a=e.response)||null==(t=a.data)?void 0:t.message)||e.message||"未知错误"))}},ea=[{title:"关卡名称",dataIndex:"name",key:"name",width:200},{title:"词组数量",dataIndex:"phraseIds",key:"phraseCount",width:100,render:e=>e.length},{title:"标签",dataIndex:"tagIds",key:"tags",width:200,render:e=>e&&0!==e.length?(0,s.jsxs)("div",{children:[e.slice(0,3).map(e=>{let l=V.find(l=>l.id===e);return l?(0,s.jsxs)(r.A,{color:l.color,style:{marginBottom:4},children:[l.name,l.isVip&&(0,s.jsx)("span",{style:{marginLeft:4,fontSize:"10px"},children:"VIP"})]},e):null}),e.length>3&&(0,s.jsxs)(r.A,{color:"default",children:["+",e.length-3]})]}):(0,s.jsx)("span",{style:{color:"#999"},children:"无标签"})},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180},{title:"星级统计",key:"starAnalytics",width:150,render:(e,l)=>{let t=O[l.id];return H[l.id]?(0,s.jsx)("span",{style:{color:"#999"},children:"加载中..."}):t?(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:4},children:[(0,s.jsx)(c.A,{disabled:!0,value:t.averageStars,allowHalf:!0,style:{fontSize:12}}),(0,s.jsx)("span",{style:{marginLeft:4,fontSize:12,color:"#666"},children:t.averageStars.toFixed(1)})]}),(0,s.jsxs)("div",{style:{fontSize:11,color:"#999"},children:["完成率: ",(100*t.completionRate).toFixed(1),"%"]})]}):(0,s.jsx)(o.Ay,{type:"link",size:"small",icon:(0,s.jsx)(A.A,{}),onClick:()=>ee(l.id),children:"查看星级"})}},{title:"操作",key:"action",width:250,render:(l,t)=>(0,s.jsxs)(d.A,{size:"small",wrap:!0,children:[(0,s.jsx)(o.Ay,{type:"link",size:"small",icon:(0,s.jsx)(S.A,{}),onClick:()=>es(t.id),children:"详情"}),(0,s.jsx)(o.Ay,{type:"link",size:"small",icon:(0,s.jsx)(k.A,{}),onClick:()=>e.push("/levels/".concat(t.id,"/edit")),children:"编辑"}),(0,s.jsx)(h.A,{title:"查看该关卡的星级数据",children:(0,s.jsx)(o.Ay,{type:"link",size:"small",icon:(0,s.jsx)(w.A,{}),onClick:()=>e.push("/user-stars?levelId=".concat(t.id)),children:"星级"})}),(0,s.jsx)(x.A,{title:"确定要删除这个关卡吗？",onConfirm:()=>el(t.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(o.Ay,{type:"link",danger:!0,size:"small",icon:(0,s.jsx)(b.A,{}),children:"删除"})})]})}];return(0,s.jsxs)("div",{className:"jsx-1b5ee15408482720",children:[(0,s.jsx)(i(),{id:"1b5ee15408482720",children:".highlighted-row{background-color:#fff7e6!important;-webkit-transition:background-color 3s ease-out;-moz-transition:background-color 3s ease-out;-o-transition:background-color 3s ease-out;transition:background-color 3s ease-out}.highlighted-row:hover{background-color:#ffd591!important}"}),L&&(0,s.jsxs)(g.A,{gutter:16,style:{marginBottom:16},children:[(0,s.jsx)(u.A,{span:8,children:(0,s.jsx)(p.A,{children:(0,s.jsx)(j.A,{title:"当前关卡总数",value:L.total,prefix:(0,s.jsx)(w.A,{})})})}),(0,s.jsx)(u.A,{span:8,children:(0,s.jsx)(p.A,{children:(0,s.jsx)(j.A,{title:"最大关卡限制",value:L.maxLevels,prefix:(0,s.jsx)(w.A,{})})})}),(0,s.jsx)(u.A,{span:8,children:(0,s.jsx)(p.A,{children:(0,s.jsx)(j.A,{title:"剩余可创建",value:L.remaining,prefix:(0,s.jsx)(w.A,{}),valueStyle:{color:L.remaining>0?"#3f8600":"#cf1322"}})})})]}),(0,s.jsxs)(p.A,{children:[(0,s.jsxs)("div",{style:{marginBottom:16},className:"jsx-1b5ee15408482720",children:[(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},className:"jsx-1b5ee15408482720",children:[(0,s.jsx)("h2",{className:"jsx-1b5ee15408482720",children:"关卡管理"}),(0,s.jsx)(o.Ay,{type:"primary",icon:(0,s.jsx)(z.A,{}),onClick:()=>e.push("/levels/create"),disabled:(null==L?void 0:L.remaining)===0,children:"创建关卡"})]}),(0,s.jsxs)("div",{style:{display:"flex",gap:16,alignItems:"center",flexWrap:"wrap"},className:"jsx-1b5ee15408482720",children:[(0,s.jsx)(y.A.Search,{placeholder:"搜索关卡名称或描述",allowClear:!0,style:{width:250},value:J,onChange:e=>K(e.target.value),onSearch:e=>{K(e)},enterButton:(0,s.jsx)(C.A,{})}),(0,s.jsx)(m.A,{placeholder:"按难度筛选",allowClear:!0,style:{width:120},value:Q,onChange:q,options:[{label:"1级",value:1},{label:"2级",value:2},{label:"3级",value:3},{label:"4级",value:4},{label:"5级",value:5}]}),(0,s.jsx)(m.A,{placeholder:"按标签筛选",allowClear:!0,style:{width:150},value:G,onChange:M,options:V.map(e=>({label:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:4},className:"jsx-1b5ee15408482720",children:[(0,s.jsx)(r.A,{color:e.color,style:{margin:0},children:e.name}),e.isVip&&(0,s.jsx)(r.A,{color:"gold",style:{margin:0,fontSize:"10px",padding:"0 4px"},children:"VIP"})]}),value:e.id}))}),(0,s.jsx)(o.Ay,{icon:(0,s.jsx)(I.A,{}),onClick:()=>{K(""),q(void 0),M(void 0)},children:"重置"})]})]}),(0,s.jsx)(v.A,{columns:ea,dataSource:t,rowKey:"id",loading:B,rowClassName:e=>W===e.id?"highlighted-row":"",pagination:{current:U.current,pageSize:U.pageSize,total:U.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,l)=>"第 ".concat(l[0],"-").concat(l[1]," 条，共 ").concat(e," 条记录"),onChange:et,onShowSizeChange:et,pageSizeOptions:["10","20","50","100"]}})]})]})}function E(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(B,{})})}}},e=>{var l=l=>e(e.s=l);e.O(0,[1291,5573,4492,5669,3464,9484,9301,6312,778,2343,1087,7238,3148,3884,9179,8441,1684,7358],()=>l(41552)),_N_E=e.O()}]);