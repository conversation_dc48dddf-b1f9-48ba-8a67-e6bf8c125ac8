"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1125],{15125:(e,t,o)=>{o.d(t,{A:()=>c});var n=o(79630),r=o(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};var l=o(62764);let c=r.forwardRef(function(e,t){return r.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},16913:(e,t,o)=>{o.d(t,{A:()=>c});var n=o(79630),r=o(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var l=o(62764);let c=r.forwardRef(function(e,t){return r.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},19361:(e,t,o)=>{o.d(t,{A:()=>n});let n=o(90510).A},23130:(e,t,o)=>{o.d(t,{A:()=>c});var n=o(79630),r=o(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};var l=o(62764);let c=r.forwardRef(function(e,t){return r.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},28562:(e,t,o)=>{o.d(t,{A:()=>E});var n=o(12115),r=o(29300),a=o.n(r),l=o(32417),c=o(74686),s=o(39496),i=o(15982),u=o(68151),d=o(9836),p=o(51854);let g=n.createContext({});var b=o(85573),m=o(18184),f=o(45431),v=o(61388);let h=e=>{let{antCls:t,componentCls:o,iconCls:n,avatarBg:r,avatarColor:a,containerSize:l,containerSizeLG:c,containerSizeSM:s,textFontSize:i,textFontSizeLG:u,textFontSizeSM:d,borderRadius:p,borderRadiusLG:g,borderRadiusSM:f,lineWidth:v,lineType:h}=e,y=(e,t,r)=>({width:e,height:e,borderRadius:"50%",["&".concat(o,"-square")]:{borderRadius:r},["&".concat(o,"-icon")]:{fontSize:t,["> ".concat(n)]:{margin:0}}});return{[o]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,m.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:a,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:"".concat((0,b.zA)(v)," ").concat(h," transparent"),"&-image":{background:"transparent"},["".concat(t,"-image-img")]:{display:"block"}}),y(l,i,p)),{"&-lg":Object.assign({},y(c,u,g)),"&-sm":Object.assign({},y(s,d,f)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},y=e=>{let{componentCls:t,groupBorderColor:o,groupOverlapping:n,groupSpace:r}=e;return{["".concat(t,"-group")]:{display:"inline-flex",[t]:{borderColor:o},"> *:not(:first-child)":{marginInlineStart:n}},["".concat(t,"-group-popover")]:{["".concat(t," + ").concat(t)]:{marginInlineStart:r}}}},O=(0,f.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:o}=e,n=(0,v.oX)(e,{avatarBg:o,avatarColor:t});return[h(n),y(n)]},e=>{let{controlHeight:t,controlHeightLG:o,controlHeightSM:n,fontSize:r,fontSizeLG:a,fontSizeXL:l,fontSizeHeading3:c,marginXS:s,marginXXS:i,colorBorderBg:u}=e;return{containerSize:t,containerSizeLG:o,containerSizeSM:n,textFontSize:Math.round((a+l)/2),textFontSizeLG:c,textFontSizeSM:r,groupSpace:i,groupOverlapping:-s,groupBorderColor:u}});var C=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let x=n.forwardRef((e,t)=>{let o,{prefixCls:r,shape:b,size:m,src:f,srcSet:v,icon:h,className:y,rootClassName:x,style:j,alt:S,draggable:k,children:A,crossOrigin:E,gap:w=4,onError:z}=e,P=C(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[N,M]=n.useState(1),[I,B]=n.useState(!1),[L,R]=n.useState(!0),T=n.useRef(null),F=n.useRef(null),H=(0,c.K4)(t,T),{getPrefixCls:W,avatar:V}=n.useContext(i.QO),D=n.useContext(g),_=()=>{if(!F.current||!T.current)return;let e=F.current.offsetWidth,t=T.current.offsetWidth;0!==e&&0!==t&&2*w<t&&M(t-2*w<e?(t-2*w)/e:1)};n.useEffect(()=>{B(!0)},[]),n.useEffect(()=>{R(!0),M(1)},[f]),n.useEffect(_,[w]);let Q=(0,d.A)(e=>{var t,o;return null!=(o=null!=(t=null!=m?m:null==D?void 0:D.size)?t:e)?o:"default"}),q=Object.keys("object"==typeof Q&&Q||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),K=(0,p.A)(q),X=n.useMemo(()=>{if("object"!=typeof Q)return{};let e=Q[s.ye.find(e=>K[e])];return e?{width:e,height:e,fontSize:e&&(h||A)?e/2:18}:{}},[K,Q]),G=W("avatar",r),J=(0,u.A)(G),[U,Y,Z]=O(G,J),$=a()({["".concat(G,"-lg")]:"large"===Q,["".concat(G,"-sm")]:"small"===Q}),ee=n.isValidElement(f),et=b||(null==D?void 0:D.shape)||"circle",eo=a()(G,$,null==V?void 0:V.className,"".concat(G,"-").concat(et),{["".concat(G,"-image")]:ee||f&&L,["".concat(G,"-icon")]:!!h},Z,J,y,x,Y),en="number"==typeof Q?{width:Q,height:Q,fontSize:h?Q/2:18}:{};if("string"==typeof f&&L)o=n.createElement("img",{src:f,draggable:k,srcSet:v,onError:()=>{!1!==(null==z?void 0:z())&&R(!1)},alt:S,crossOrigin:E});else if(ee)o=f;else if(h)o=h;else if(I||1!==N){let e="scale(".concat(N,")");o=n.createElement(l.A,{onResize:_},n.createElement("span",{className:"".concat(G,"-string"),ref:F,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},A))}else o=n.createElement("span",{className:"".concat(G,"-string"),style:{opacity:0},ref:F},A);return U(n.createElement("span",Object.assign({},P,{style:Object.assign(Object.assign(Object.assign(Object.assign({},en),X),null==V?void 0:V.style),j),className:eo,ref:H}),o))});var j=o(63715),S=o(80163),k=o(56200);let A=e=>{let{size:t,shape:o}=n.useContext(g),r=n.useMemo(()=>({size:e.size||t,shape:e.shape||o}),[e.size,e.shape,t,o]);return n.createElement(g.Provider,{value:r},e.children)};x.Group=e=>{var t,o,r,l;let{getPrefixCls:c,direction:s}=n.useContext(i.QO),{prefixCls:d,className:p,rootClassName:g,style:b,maxCount:m,maxStyle:f,size:v,shape:h,maxPopoverPlacement:y,maxPopoverTrigger:C,children:E,max:w}=e,z=c("avatar",d),P="".concat(z,"-group"),N=(0,u.A)(z),[M,I,B]=O(z,N),L=a()(P,{["".concat(P,"-rtl")]:"rtl"===s},B,N,p,g,I),R=(0,j.A)(E).map((e,t)=>(0,S.Ob)(e,{key:"avatar-key-".concat(t)})),T=(null==w?void 0:w.count)||m,F=R.length;if(T&&T<F){let e=R.slice(0,T),c=R.slice(T,F),s=(null==w?void 0:w.style)||f,i=(null==(t=null==w?void 0:w.popover)?void 0:t.trigger)||C||"hover",u=(null==(o=null==w?void 0:w.popover)?void 0:o.placement)||y||"top",d=Object.assign(Object.assign({content:c},null==w?void 0:w.popover),{classNames:{root:a()("".concat(P,"-popover"),null==(l=null==(r=null==w?void 0:w.popover)?void 0:r.classNames)?void 0:l.root)},placement:u,trigger:i});return e.push(n.createElement(k.A,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},d),n.createElement(x,{style:s},"+".concat(F-T)))),M(n.createElement(A,{shape:h,size:v},n.createElement("div",{className:L,style:b},e)))}return M(n.createElement(A,{shape:h,size:v},n.createElement("div",{className:L,style:b},R)))};let E=x},35695:(e,t,o)=>{var n=o(18999);o.o(n,"useParams")&&o.d(t,{useParams:function(){return n.useParams}}),o.o(n,"usePathname")&&o.d(t,{usePathname:function(){return n.usePathname}}),o.o(n,"useRouter")&&o.d(t,{useRouter:function(){return n.useRouter}}),o.o(n,"useSearchParams")&&o.d(t,{useSearchParams:function(){return n.useSearchParams}}),o.o(n,"useServerInsertedHTML")&&o.d(t,{useServerInsertedHTML:function(){return n.useServerInsertedHTML}})},37974:(e,t,o)=>{o.d(t,{A:()=>P});var n=o(12115),r=o(29300),a=o.n(r),l=o(17980),c=o(77696),s=o(50497),i=o(80163),u=o(47195),d=o(15982),p=o(85573),g=o(34162),b=o(18184),m=o(61388),f=o(45431);let v=e=>{let{paddingXXS:t,lineWidth:o,tagPaddingHorizontal:n,componentCls:r,calc:a}=e,l=a(n).sub(o).equal(),c=a(t).sub(o).equal();return{[r]:Object.assign(Object.assign({},(0,b.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(r,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(r,"-close-icon")]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(r,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(r,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:l}}),["".concat(r,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},h=e=>{let{lineWidth:t,fontSizeIcon:o,calc:n}=e,r=e.fontSizeSM;return(0,m.oX)(e,{tagFontSize:r,tagLineHeight:(0,p.zA)(n(e.lineHeightSM).mul(r).equal()),tagIconSize:n(o).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new g.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),O=(0,f.OF)("Tag",e=>v(h(e)),y);var C=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let x=n.forwardRef((e,t)=>{let{prefixCls:o,style:r,className:l,checked:c,onChange:s,onClick:i}=e,u=C(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:g}=n.useContext(d.QO),b=p("tag",o),[m,f,v]=O(b),h=a()(b,"".concat(b,"-checkable"),{["".concat(b,"-checkable-checked")]:c},null==g?void 0:g.className,l,f,v);return m(n.createElement("span",Object.assign({},u,{ref:t,style:Object.assign(Object.assign({},r),null==g?void 0:g.style),className:h,onClick:e=>{null==s||s(!c),null==i||i(e)}})))});var j=o(18741);let S=e=>(0,j.A)(e,(t,o)=>{let{textColor:n,lightBorderColor:r,lightColor:a,darkColor:l}=o;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:n,background:a,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}}),k=(0,f.bf)(["Tag","preset"],e=>S(h(e)),y),A=(e,t,o)=>{let n=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(o);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(o)],background:e["color".concat(n,"Bg")],borderColor:e["color".concat(n,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}},E=(0,f.bf)(["Tag","status"],e=>{let t=h(e);return[A(t,"success","Success"),A(t,"processing","Info"),A(t,"error","Error"),A(t,"warning","Warning")]},y);var w=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let z=n.forwardRef((e,t)=>{let{prefixCls:o,className:r,rootClassName:p,style:g,children:b,icon:m,color:f,onClose:v,bordered:h=!0,visible:y}=e,C=w(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:x,direction:j,tag:S}=n.useContext(d.QO),[A,z]=n.useState(!0),P=(0,l.A)(C,["closeIcon","closable"]);n.useEffect(()=>{void 0!==y&&z(y)},[y]);let N=(0,c.nP)(f),M=(0,c.ZZ)(f),I=N||M,B=Object.assign(Object.assign({backgroundColor:f&&!I?f:void 0},null==S?void 0:S.style),g),L=x("tag",o),[R,T,F]=O(L),H=a()(L,null==S?void 0:S.className,{["".concat(L,"-").concat(f)]:I,["".concat(L,"-has-color")]:f&&!I,["".concat(L,"-hidden")]:!A,["".concat(L,"-rtl")]:"rtl"===j,["".concat(L,"-borderless")]:!h},r,p,T,F),W=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||z(!1)},[,V]=(0,s.A)((0,s.d)(e),(0,s.d)(S),{closable:!1,closeIconRender:e=>{let t=n.createElement("span",{className:"".concat(L,"-close-icon"),onClick:W},e);return(0,i.fx)(e,t,e=>({onClick:t=>{var o;null==(o=null==e?void 0:e.onClick)||o.call(e,t),W(t)},className:a()(null==e?void 0:e.className,"".concat(L,"-close-icon"))}))}}),D="function"==typeof C.onClick||b&&"a"===b.type,_=m||null,Q=_?n.createElement(n.Fragment,null,_,b&&n.createElement("span",null,b)):b,q=n.createElement("span",Object.assign({},P,{ref:t,className:H,style:B}),Q,V,N&&n.createElement(k,{key:"preset",prefixCls:L}),M&&n.createElement(E,{key:"status",prefixCls:L}));return R(D?n.createElement(u.A,{component:"Tag"},q):q)});z.CheckableTag=x;let P=z},44186:(e,t,o)=>{o.d(t,{b:()=>n});let n=e=>e?"function"==typeof e?e():e:null},50274:(e,t,o)=>{o.d(t,{A:()=>c});var n=o(79630),r=o(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var l=o(62764);let c=r.forwardRef(function(e,t){return r.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},50497:(e,t,o)=>{o.d(t,{A:()=>p,d:()=>i});var n=o(12115),r=o(58587),a=o(40032),l=o(8530),c=o(33823),s=o(85382);function i(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function u(e){let{closable:t,closeIcon:o}=e||{};return n.useMemo(()=>{if(!t&&(!1===t||!1===o||null===o))return!1;if(void 0===t&&void 0===o)return null;let e={closeIcon:"boolean"!=typeof o&&null!==o?o:void 0};return t&&"object"==typeof t&&(e=Object.assign(Object.assign({},e),t)),e},[t,o])}let d={};function p(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:d,i=u(e),p=u(t),[g]=(0,l.A)("global",c.A.global),b="boolean"!=typeof i&&!!(null==i?void 0:i.disabled),m=n.useMemo(()=>Object.assign({closeIcon:n.createElement(r.A,null)},o),[o]),f=n.useMemo(()=>!1!==i&&(i?(0,s.A)(m,p,i):!1!==p&&(p?(0,s.A)(m,p):!!m.closable&&m)),[i,p,m]);return n.useMemo(()=>{var e,t;if(!1===f)return[!1,null,b,{}];let{closeIconRender:o}=m,{closeIcon:r}=f,l=r,c=(0,a.A)(f,!0);return null!=l&&(o&&(l=o(r)),l=n.isValidElement(l)?n.cloneElement(l,Object.assign(Object.assign(Object.assign({},l.props),{"aria-label":null!=(t=null==(e=l.props)?void 0:e["aria-label"])?t:g.close}),c)):n.createElement("span",Object.assign({"aria-label":g.close},c),l)),[!0,l,b,c]},[f,m])}},56200:(e,t,o)=>{o.d(t,{A:()=>v});var n=o(12115),r=o(29300),a=o.n(r),l=o(48804),c=o(17233),s=o(44186),i=o(93666),u=o(80163),d=o(15982),p=o(26922),g=o(79092),b=o(60322),m=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let f=n.forwardRef((e,t)=>{var o,r;let{prefixCls:f,title:v,content:h,overlayClassName:y,placement:O="top",trigger:C="hover",children:x,mouseEnterDelay:j=.1,mouseLeaveDelay:S=.1,onOpenChange:k,overlayStyle:A={},styles:E,classNames:w}=e,z=m(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:P,className:N,style:M,classNames:I,styles:B}=(0,d.TP)("popover"),L=P("popover",f),[R,T,F]=(0,b.A)(L),H=P(),W=a()(y,T,F,N,I.root,null==w?void 0:w.root),V=a()(I.body,null==w?void 0:w.body),[D,_]=(0,l.A)(!1,{value:null!=(o=e.open)?o:e.visible,defaultValue:null!=(r=e.defaultOpen)?r:e.defaultVisible}),Q=(e,t)=>{_(e,!0),null==k||k(e,t)},q=e=>{e.keyCode===c.A.ESC&&Q(!1,e)},K=(0,s.b)(v),X=(0,s.b)(h);return R(n.createElement(p.A,Object.assign({placement:O,trigger:C,mouseEnterDelay:j,mouseLeaveDelay:S},z,{prefixCls:L,classNames:{root:W,body:V},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),M),A),null==E?void 0:E.root),body:Object.assign(Object.assign({},B.body),null==E?void 0:E.body)},ref:t,open:D,onOpenChange:e=>{Q(e)},overlay:K||X?n.createElement(g.hJ,{prefixCls:L,title:K,content:X}):null,transitionName:(0,i.b)(H,"zoom-big",z.transitionName),"data-popover-inject":!0}),(0,u.Ob)(x,{onKeyDown:e=>{var t,o;(0,n.isValidElement)(x)&&(null==(o=null==x?void 0:(t=x.props).onKeyDown)||o.call(t,e)),q(e)}})))});f._InternalPanelDoNotUseOrYouWillBeFired=g.Ay;let v=f},60322:(e,t,o)=>{o.d(t,{A:()=>p});var n=o(18184),r=o(47212),a=o(35464),l=o(45902),c=o(68495),s=o(45431),i=o(61388);let u=e=>{let{componentCls:t,popoverColor:o,titleMinWidth:r,fontWeightStrong:l,innerPadding:c,boxShadowSecondary:s,colorTextHeading:i,borderRadiusLG:u,zIndexPopup:d,titleMarginBottom:p,colorBgElevated:g,popoverBg:b,titleBorderBottom:m,innerContentPadding:f,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},(0,n.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:d,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":g,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},["".concat(t,"-content")]:{position:"relative"},["".concat(t,"-inner")]:{backgroundColor:b,backgroundClip:"padding-box",borderRadius:u,boxShadow:s,padding:c},["".concat(t,"-title")]:{minWidth:r,marginBottom:p,color:i,fontWeight:l,borderBottom:m,padding:v},["".concat(t,"-inner-content")]:{color:o,padding:f}})},(0,a.Ay)(e,"var(--antd-arrow-background-color)"),{["".concat(t,"-pure")]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",["".concat(t,"-content")]:{display:"inline-block"}}}]},d=e=>{let{componentCls:t}=e;return{[t]:c.s.map(o=>{let n=e["".concat(o,"6")];return{["&".concat(t,"-").concat(o)]:{"--antd-arrow-background-color":n,["".concat(t,"-inner")]:{backgroundColor:n},["".concat(t,"-arrow")]:{background:"transparent"}}}})}},p=(0,s.OF)("Popover",e=>{let{colorBgElevated:t,colorText:o}=e,n=(0,i.oX)(e,{popoverBg:t,popoverColor:o});return[u(n),d(n),(0,r.aB)(n,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:o,fontHeight:n,padding:r,wireframe:c,zIndexPopupBase:s,borderRadiusLG:i,marginXS:u,lineType:d,colorSplit:p,paddingSM:g}=e,b=o-n;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:s+30},(0,l.n)(e)),(0,a.Ke)({contentRadius:i,limitVerticalRadius:!0})),{innerPadding:12*!c,titleMarginBottom:c?0:u,titlePadding:c?"".concat(b/2,"px ").concat(r,"px ").concat(b/2-t,"px"):0,titleBorderBottom:c?"".concat(t,"px ").concat(d," ").concat(p):"none",innerContentPadding:c?"".concat(g,"px ").concat(r,"px"):0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},74947:(e,t,o)=>{o.d(t,{A:()=>n});let n=o(62623).A},79092:(e,t,o)=>{o.d(t,{Ay:()=>g,hJ:()=>d});var n=o(12115),r=o(29300),a=o.n(r),l=o(16598),c=o(44186),s=o(15982),i=o(60322),u=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let d=e=>{let{title:t,content:o,prefixCls:r}=e;return t||o?n.createElement(n.Fragment,null,t&&n.createElement("div",{className:"".concat(r,"-title")},t),o&&n.createElement("div",{className:"".concat(r,"-inner-content")},o)):null},p=e=>{let{hashId:t,prefixCls:o,className:r,style:s,placement:i="top",title:u,content:p,children:g}=e,b=(0,c.b)(u),m=(0,c.b)(p),f=a()(t,o,"".concat(o,"-pure"),"".concat(o,"-placement-").concat(i),r);return n.createElement("div",{className:f,style:s},n.createElement("div",{className:"".concat(o,"-arrow")}),n.createElement(l.z,Object.assign({},e,{className:t,prefixCls:o}),g||n.createElement(d,{prefixCls:o,title:b,content:m})))},g=e=>{let{prefixCls:t,className:o}=e,r=u(e,["prefixCls","className"]),{getPrefixCls:l}=n.useContext(s.QO),c=l("popover",t),[d,g,b]=(0,i.A)(c);return d(n.createElement(p,Object.assign({},r,{prefixCls:c,hashId:g,className:a()(o,b)})))}}}]);