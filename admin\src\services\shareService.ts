import request from './request';
import type {
  CreateShareConfigRequest,
  UpdateShareConfigRequest,
  ShareConfigResponse,
} from '../types/share';

// 分享管理服务
export class ShareService {
  // 获取所有分享配置
  static async getAllShareConfigs(): Promise<ShareConfigResponse[]> {
    const response = await request.get('/share');
    return response.data;
  }

  // 获取启用的分享配置
  static async getActiveShareConfigs(): Promise<ShareConfigResponse[]> {
    const response = await request.get('/share/active');
    return response.data;
  }

  // 获取默认分享配置
  static async getDefaultShareConfig(): Promise<ShareConfigResponse | null> {
    const response = await request.get('/share/default');
    return response.data;
  }

  // 根据类型获取分享配置
  static async getShareConfigByType(type: string): Promise<ShareConfigResponse | null> {
    const response = await request.get(`/share/type/${type}`);
    return response.data;
  }

  // 根据ID获取分享配置
  static async getShareConfigById(id: string): Promise<ShareConfigResponse> {
    const response = await request.get(`/share/${id}`);
    return response.data;
  }

  // 创建分享配置
  static async createShareConfig(data: CreateShareConfigRequest): Promise<ShareConfigResponse> {
    const response = await request.post('/share', data);
    return response.data;
  }

  // 更新分享配置
  static async updateShareConfig(id: string, data: UpdateShareConfigRequest): Promise<ShareConfigResponse> {
    const response = await request.patch(`/share/${id}`, data);
    return response.data;
  }

  // 启用/禁用分享配置
  static async toggleShareConfig(id: string): Promise<ShareConfigResponse> {
    const response = await request.put(`/share/${id}/toggle`);
    return response.data;
  }

  // 删除分享配置
  static async deleteShareConfig(id: string): Promise<{ message: string }> {
    const response = await request.delete(`/share/${id}`);
    return response.data;
  }
}

// 分享类型选项
export const SHARE_TYPE_OPTIONS = [
  {
    value: 'default',
    label: '默认分享',
    description: '通用分享，适用于首页、游戏页等'
  },
  {
    value: 'result',
    label: '结果分享',
    description: '展示用户成绩的分享'
  },
  {
    value: 'level',
    label: '关卡分享',
    description: '邀请好友挑战特定关卡'
  },
  {
    value: 'achievement',
    label: '成就分享',
    description: '展示用户获得的成就'
  },
  {
    value: 'custom',
    label: '自定义分享',
    description: '特殊活动或自定义场景'
  }
];

// 分享路径模板
export const SHARE_PATH_TEMPLATES = [
  {
    label: '首页',
    value: '/pages/index/index',
    description: '小程序首页'
  },
  {
    label: '游戏页',
    value: '/pages/game/game',
    description: '游戏主页面'
  },
  {
    label: '关卡页',
    value: '/pages/level/level?id={levelId}',
    description: '特定关卡页面，{levelId}会被替换为实际关卡ID'
  },
  {
    label: '结果页',
    value: '/pages/result/result?score={score}',
    description: '结果展示页面，{score}会被替换为实际分数'
  },
  {
    label: '排行榜',
    value: '/pages/rank/rank',
    description: '排行榜页面'
  }
];

export default ShareService;
