"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5634],{13324:(n,e,c)=>{c.d(e,{A:()=>N});var a=c(12115),t=c(33501),i=c(29300),o=c.n(i),l=c(79630),r=c(40419),d=c(21858),s=c(52673),u=c(48804),h=c(17233),g=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],m=a.forwardRef(function(n,e){var c,t=n.prefixCls,i=void 0===t?"rc-switch":t,m=n.className,k=n.checked,b=n.defaultChecked,p=n.disabled,I=n.loadingIcon,S=n.checkedChildren,w=n.unCheckedChildren,f=n.onClick,v=n.onChange,C=n.onKeyDown,A=(0,s.A)(n,g),E=(0,u.A)(!1,{value:k,defaultValue:b}),y=(0,d.A)(E,2),q=y[0],x=y[1];function O(n,e){var c=q;return p||(x(c=n),null==v||v(c,e)),c}var z=o()(i,m,(c={},(0,r.A)(c,"".concat(i,"-checked"),q),(0,r.A)(c,"".concat(i,"-disabled"),p),c));return a.createElement("button",(0,l.A)({},A,{type:"button",role:"switch","aria-checked":q,disabled:p,className:z,ref:e,onKeyDown:function(n){n.which===h.A.LEFT?O(!1,n):n.which===h.A.RIGHT&&O(!0,n),null==C||C(n)},onClick:function(n){var e=O(!q,n);null==f||f(e,n)}}),I,a.createElement("span",{className:"".concat(i,"-inner")},a.createElement("span",{className:"".concat(i,"-inner-checked")},S),a.createElement("span",{className:"".concat(i,"-inner-unchecked")},w)))});m.displayName="Switch";var k=c(47195),b=c(15982),p=c(44494),I=c(9836),S=c(85573),w=c(34162),f=c(18184),v=c(45431),C=c(61388);let A=n=>{let{componentCls:e,trackHeightSM:c,trackPadding:a,trackMinWidthSM:t,innerMinMarginSM:i,innerMaxMarginSM:o,handleSizeSM:l,calc:r}=n,d="".concat(e,"-inner"),s=(0,S.zA)(r(l).add(r(a).mul(2)).equal()),u=(0,S.zA)(r(o).mul(2).equal());return{[e]:{["&".concat(e,"-small")]:{minWidth:t,height:c,lineHeight:(0,S.zA)(c),["".concat(e,"-inner")]:{paddingInlineStart:o,paddingInlineEnd:i,["".concat(d,"-checked, ").concat(d,"-unchecked")]:{minHeight:c},["".concat(d,"-checked")]:{marginInlineStart:"calc(-100% + ".concat(s," - ").concat(u,")"),marginInlineEnd:"calc(100% - ".concat(s," + ").concat(u,")")},["".concat(d,"-unchecked")]:{marginTop:r(c).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},["".concat(e,"-handle")]:{width:l,height:l},["".concat(e,"-loading-icon")]:{top:r(r(l).sub(n.switchLoadingIconSize)).div(2).equal(),fontSize:n.switchLoadingIconSize},["&".concat(e,"-checked")]:{["".concat(e,"-inner")]:{paddingInlineStart:i,paddingInlineEnd:o,["".concat(d,"-checked")]:{marginInlineStart:0,marginInlineEnd:0},["".concat(d,"-unchecked")]:{marginInlineStart:"calc(100% - ".concat(s," + ").concat(u,")"),marginInlineEnd:"calc(-100% + ".concat(s," - ").concat(u,")")}},["".concat(e,"-handle")]:{insetInlineStart:"calc(100% - ".concat((0,S.zA)(r(l).add(a).equal()),")")}},["&:not(".concat(e,"-disabled):active")]:{["&:not(".concat(e,"-checked) ").concat(d)]:{["".concat(d,"-unchecked")]:{marginInlineStart:r(n.marginXXS).div(2).equal(),marginInlineEnd:r(n.marginXXS).mul(-1).div(2).equal()}},["&".concat(e,"-checked ").concat(d)]:{["".concat(d,"-checked")]:{marginInlineStart:r(n.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:r(n.marginXXS).div(2).equal()}}}}}}},E=n=>{let{componentCls:e,handleSize:c,calc:a}=n;return{[e]:{["".concat(e,"-loading-icon").concat(n.iconCls)]:{position:"relative",top:a(a(c).sub(n.fontSize)).div(2).equal(),color:n.switchLoadingIconColor,verticalAlign:"top"},["&".concat(e,"-checked ").concat(e,"-loading-icon")]:{color:n.switchColor}}}},y=n=>{let{componentCls:e,trackPadding:c,handleBg:a,handleShadow:t,handleSize:i,calc:o}=n,l="".concat(e,"-handle");return{[e]:{[l]:{position:"absolute",top:c,insetInlineStart:c,width:i,height:i,transition:"all ".concat(n.switchDuration," ease-in-out"),"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:o(i).div(2).equal(),boxShadow:t,transition:"all ".concat(n.switchDuration," ease-in-out"),content:'""'}},["&".concat(e,"-checked ").concat(l)]:{insetInlineStart:"calc(100% - ".concat((0,S.zA)(o(i).add(c).equal()),")")},["&:not(".concat(e,"-disabled):active")]:{["".concat(l,"::before")]:{insetInlineEnd:n.switchHandleActiveInset,insetInlineStart:0},["&".concat(e,"-checked ").concat(l,"::before")]:{insetInlineEnd:0,insetInlineStart:n.switchHandleActiveInset}}}}},q=n=>{let{componentCls:e,trackHeight:c,trackPadding:a,innerMinMargin:t,innerMaxMargin:i,handleSize:o,calc:l}=n,r="".concat(e,"-inner"),d=(0,S.zA)(l(o).add(l(a).mul(2)).equal()),s=(0,S.zA)(l(i).mul(2).equal());return{[e]:{[r]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:i,paddingInlineEnd:t,transition:"padding-inline-start ".concat(n.switchDuration," ease-in-out, padding-inline-end ").concat(n.switchDuration," ease-in-out"),["".concat(r,"-checked, ").concat(r,"-unchecked")]:{display:"block",color:n.colorTextLightSolid,fontSize:n.fontSizeSM,transition:"margin-inline-start ".concat(n.switchDuration," ease-in-out, margin-inline-end ").concat(n.switchDuration," ease-in-out"),pointerEvents:"none",minHeight:c},["".concat(r,"-checked")]:{marginInlineStart:"calc(-100% + ".concat(d," - ").concat(s,")"),marginInlineEnd:"calc(100% - ".concat(d," + ").concat(s,")")},["".concat(r,"-unchecked")]:{marginTop:l(c).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},["&".concat(e,"-checked ").concat(r)]:{paddingInlineStart:t,paddingInlineEnd:i,["".concat(r,"-checked")]:{marginInlineStart:0,marginInlineEnd:0},["".concat(r,"-unchecked")]:{marginInlineStart:"calc(100% - ".concat(d," + ").concat(s,")"),marginInlineEnd:"calc(-100% + ".concat(d," - ").concat(s,")")}},["&:not(".concat(e,"-disabled):active")]:{["&:not(".concat(e,"-checked) ").concat(r)]:{["".concat(r,"-unchecked")]:{marginInlineStart:l(a).mul(2).equal(),marginInlineEnd:l(a).mul(-1).mul(2).equal()}},["&".concat(e,"-checked ").concat(r)]:{["".concat(r,"-checked")]:{marginInlineStart:l(a).mul(-1).mul(2).equal(),marginInlineEnd:l(a).mul(2).equal()}}}}}},x=n=>{let{componentCls:e,trackHeight:c,trackMinWidth:a}=n;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,f.dF)(n)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:c,lineHeight:(0,S.zA)(c),verticalAlign:"middle",background:n.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:"all ".concat(n.motionDurationMid),userSelect:"none",["&:hover:not(".concat(e,"-disabled)")]:{background:n.colorTextTertiary}}),(0,f.K8)(n)),{["&".concat(e,"-checked")]:{background:n.switchColor,["&:hover:not(".concat(e,"-disabled)")]:{background:n.colorPrimaryHover}},["&".concat(e,"-loading, &").concat(e,"-disabled")]:{cursor:"not-allowed",opacity:n.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},["&".concat(e,"-rtl")]:{direction:"rtl"}})}},O=(0,v.OF)("Switch",n=>{let e=(0,C.oX)(n,{switchDuration:n.motionDurationMid,switchColor:n.colorPrimary,switchDisabledOpacity:n.opacityLoading,switchLoadingIconSize:n.calc(n.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:"rgba(0, 0, 0, ".concat(n.opacityLoading,")"),switchHandleActiveInset:"-30%"});return[x(e),q(e),y(e),E(e),A(e)]},n=>{let{fontSize:e,lineHeight:c,controlHeight:a,colorWhite:t}=n,i=e*c,o=a/2,l=i-4,r=o-4;return{trackHeight:i,trackHeightSM:o,trackMinWidth:2*l+8,trackMinWidthSM:2*r+4,trackPadding:2,handleBg:t,handleSize:l,handleSizeSM:r,handleShadow:"0 2px 4px 0 ".concat(new w.Y("#00230b").setA(.2).toRgbString()),innerMinMargin:l/2,innerMaxMargin:l+2+4,innerMinMarginSM:r/2,innerMaxMarginSM:r+2+4}});var z=function(n,e){var c={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&0>e.indexOf(a)&&(c[a]=n[a]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols)for(var t=0,a=Object.getOwnPropertySymbols(n);t<a.length;t++)0>e.indexOf(a[t])&&Object.prototype.propertyIsEnumerable.call(n,a[t])&&(c[a[t]]=n[a[t]]);return c};let M=a.forwardRef((n,e)=>{let{prefixCls:c,size:i,disabled:l,loading:r,className:d,rootClassName:s,style:h,checked:g,value:S,defaultChecked:w,defaultValue:f,onChange:v}=n,C=z(n,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[A,E]=(0,u.A)(!1,{value:null!=g?g:S,defaultValue:null!=w?w:f}),{getPrefixCls:y,direction:q,switch:x}=a.useContext(b.QO),M=a.useContext(p.A),N=(null!=l?l:M)||r,D=y("switch",c),H=a.createElement("div",{className:"".concat(D,"-handle")},r&&a.createElement(t.A,{className:"".concat(D,"-loading-icon")})),[j,T,L]=O(D),X=(0,I.A)(i),R=o()(null==x?void 0:x.className,{["".concat(D,"-small")]:"small"===X,["".concat(D,"-loading")]:r,["".concat(D,"-rtl")]:"rtl"===q},d,s,T,L),_=Object.assign(Object.assign({},null==x?void 0:x.style),h);return j(a.createElement(k.A,{component:"Switch"},a.createElement(m,Object.assign({},C,{checked:A,onChange:function(){for(var n=arguments.length,e=Array(n),c=0;c<n;c++)e[c]=arguments[c];E(e[0]),null==v||v.apply(void 0,e)},prefixCls:D,className:R,style:_,disabled:N,ref:e,loadingIcon:H}))))});M.__ANT_SWITCH=!0;let N=M},19361:(n,e,c)=>{c.d(e,{A:()=>a});let a=c(90510).A},74947:(n,e,c)=>{c.d(e,{A:()=>a});let a=c(62623).A}}]);