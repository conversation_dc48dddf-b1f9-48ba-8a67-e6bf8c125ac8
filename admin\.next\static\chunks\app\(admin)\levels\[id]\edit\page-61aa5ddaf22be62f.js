(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5144],{16853:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>b});var s=l(95155),i=l(12115),r=l(35695),n=l(97605),a=l(56020),o=l(86615),d=l(19868),c=l(16467),m=l(95108),u=l(6124),h=l(94600),x=l(77325),y=l(51087),p=l(20778),A=l(37974),g=l(12320),j=l(46002),f=l(46996),v=l(56170),I=l(49179),w=l(41008);let{Title:k}=n.A,{TextArea:C}=a.A;function b(){let[e]=o.A.useForm(),t=(0,r.useRouter)(),l=(0,r.useParams)(),[n,b]=(0,i.useState)(!1),[_,L]=(0,i.useState)(!0),[S,F]=(0,i.useState)([]),[P,E]=(0,i.useState)([]),[B,T]=(0,i.useState)(null),[z,R]=(0,i.useState)(null),[V]=o.A.useForm(),[q,D]=(0,i.useState)(!1),N=l.id,O=e=>{F(t=>t.filter(t=>t.id!==e)),d.Ay.success("词组删除成功")},U=async()=>{if(N){L(!0);try{let t=await I.k3.getWithPhrases(N);if(R(t),e.setFieldsValue({name:t.name,difficulty:t.difficulty,description:t.description,tagIds:t.tagIds||[]}),t.phrases&&t.phrases.length>0){let e=t.phrases.map(e=>({id:e.id,text:e.text,meaning:e.meaning}));F(e)}}catch(e){d.Ay.error("获取关卡详情失败"),console.error("获取关卡详情失败:",e)}finally{L(!1)}}},W=async()=>{try{let e=await I.m9.getAll();E(e.filter(e=>"active"===e.status))}catch(e){d.Ay.error("获取标签列表失败")}},H=async()=>{try{let e=await I.k3.getCount();T(e)}catch(e){d.Ay.error("获取关卡统计失败")}};(0,i.useEffect)(()=>{U(),W(),H()},[N]);let K=async e=>{if(!N)return void d.Ay.error("关卡ID不存在");if(0===S.length)return void d.Ay.error("请添加词组");b(!0);try{let l=S.filter(e=>!e.id.startsWith("temp_")).map(e=>e.id),s=S.filter(e=>e.id.startsWith("temp_"));console.log("现有词组ID:",l),console.log("新词组:",s);let i=s.map(e=>({text:e.text,meaning:e.meaning})),r={name:e.name,difficulty:e.difficulty,description:e.description,tagIds:e.tagIds||[],phraseIds:l,phrases:i.length>0?i:void 0,thesaurusIds:(null==z?void 0:z.thesaurusIds)||[]};console.log("更新参数:",r),console.log("请求URL:","".concat(w.i.FULL_BASE_URL,"/levels/").concat(N)),console.log("请求方法: PATCH");let n=await I.k3.update(N,r);console.log("更新结果:",n),s.length>0?d.Ay.success("关卡更新成功！已创建 ".concat(s.length," 个新词组并添加到关卡中")):d.Ay.success("关卡更新成功"),t.push("/levels")}catch(t){var l,s;console.error("更新关卡失败:",t);let e=(null==(s=t.response)||null==(l=s.data)?void 0:l.message)||t.message||"更新关卡失败";d.Ay.error("更新关卡失败: ".concat(e))}finally{b(!1)}};return _?(0,s.jsxs)("div",{style:{padding:"24px",textAlign:"center"},children:[(0,s.jsx)(c.A,{size:"large"}),(0,s.jsx)("div",{style:{marginTop:16},children:"正在加载关卡详情..."})]}):(0,s.jsxs)("div",{children:[z&&(0,s.jsx)(m.A,{message:"正在编辑关卡：".concat(z.name," (难度: ").concat(z.difficulty,")"),type:"info",style:{marginBottom:16},showIcon:!0}),(0,s.jsxs)(u.A,{children:[(0,s.jsx)(k,{level:2,children:"编辑关卡"}),(0,s.jsxs)(o.A,{form:e,layout:"vertical",onFinish:K,initialValues:{difficulty:3},children:[(0,s.jsx)(o.A.Item,{name:"name",label:"关卡名称",rules:[{required:!0,message:"请输入关卡名称"}],children:(0,s.jsx)(a.A,{placeholder:"请输入关卡名称"})}),(0,s.jsx)(o.A.Item,{name:"description",label:"关卡描述",children:(0,s.jsx)(C,{rows:3,placeholder:"请输入关卡描述（可选）"})}),(0,s.jsx)(h.A,{children:"添加关卡词组"}),(0,s.jsx)(m.A,{message:"提示：请添加要包含在关卡中的词组。",type:"info",style:{marginBottom:16}}),(0,s.jsx)("div",{style:{marginBottom:16},children:(0,s.jsx)(x.Ay,{type:"dashed",icon:(0,s.jsx)(f.A,{}),onClick:()=>D(!0),style:{width:"100%"},children:"添加词组"})}),(0,s.jsx)("div",{style:{marginBottom:16},children:(0,s.jsx)(y.A,{dataSource:S,rowKey:"id",pagination:!1,size:"small",columns:[{title:"英文",dataIndex:"text",key:"text",render:e=>e||"未设置"},{title:"中文",dataIndex:"meaning",key:"meaning",render:e=>e||"未设置"},{title:"操作",key:"action",width:80,render:(e,t)=>(0,s.jsx)(x.Ay,{type:"link",danger:!0,size:"small",icon:(0,s.jsx)(v.A,{}),onClick:()=>O(t.id)})}],locale:{emptyText:"暂无词组，请点击上方按钮添加"}})}),(0,s.jsx)(h.A,{children:"关卡标签"}),(0,s.jsx)(o.A.Item,{name:"tagIds",label:"选择标签",help:"为关卡添加标签，便于分类和筛选",children:(0,s.jsx)(p.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择关卡标签",filterOption:(e,t)=>{var l;return null==t||null==(l=t.label)?void 0:l.toLowerCase().includes(e.toLowerCase())},optionRender:e=>{var t,l;return(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,s.jsx)(A.A,{color:null==(t=P.find(t=>t.id===e.value))?void 0:t.color,children:e.label}),(null==(l=P.find(t=>t.id===e.value))?void 0:l.isVip)&&(0,s.jsx)(A.A,{color:"gold",style:{fontSize:"10px",padding:"0 4px"},children:"VIP"})]})},options:P.map(e=>({label:e.name,value:e.id}))})}),(0,s.jsx)(o.A.Item,{children:(0,s.jsxs)(g.A,{children:[(0,s.jsx)(x.Ay,{type:"primary",htmlType:"submit",loading:n,children:"更新关卡"}),(0,s.jsx)(x.Ay,{onClick:()=>t.back(),children:"取消"})]})})]})]}),(0,s.jsx)(j.A,{title:"添加词组",open:q,onCancel:()=>D(!1),footer:null,width:500,children:(0,s.jsxs)(o.A,{form:V,layout:"vertical",onFinish:e=>{if(S.some(t=>t.text.toLowerCase().trim()===e.text.toLowerCase().trim()))return void d.Ay.error("该英文词组已存在，请勿重复添加");let t={id:"temp_".concat(Date.now()),text:e.text.trim(),meaning:e.meaning.trim()};F(e=>[...e,t]),V.resetFields(),D(!1),d.Ay.success("词组添加成功")},children:[(0,s.jsx)(o.A.Item,{name:"text",label:"英文",rules:[{required:!0,message:"请输入英文"},{validator:(e,t)=>t&&S.some(e=>e.text.toLowerCase().trim()===t.toLowerCase().trim())?Promise.reject(Error("该英文词组已存在")):Promise.resolve()}],children:(0,s.jsx)(a.A,{placeholder:"请输入英文词组"})}),(0,s.jsx)(o.A.Item,{name:"meaning",label:"中文",rules:[{required:!0,message:"请输入中文"}],children:(0,s.jsx)(a.A,{placeholder:"请输入中文意思"})}),(0,s.jsx)(o.A.Item,{children:(0,s.jsxs)(g.A,{children:[(0,s.jsx)(x.Ay,{type:"primary",htmlType:"submit",children:"添加词组"}),(0,s.jsx)(x.Ay,{onClick:()=>D(!1),children:"取消"})]})})]})})]})}},63769:(e,t,l)=>{Promise.resolve().then(l.bind(l,16853))}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,2343,1087,6615,7605,404,6002,171,3884,9179,8441,1684,7358],()=>t(63769)),_N_E=e.O()}]);