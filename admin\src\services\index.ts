// 统一导出所有API服务
export * from './request';
export * from './authService';
export * from './phraseService';
export * from './thesaurusService';
export * from './levelService';
export * from './userService';
export * from './shareService';
export * from './vipService';
export * from './settingsService';
export * from './levelTagService';
export * from './userStarService';
export * from './userFavoriteService';
export * from './activationCodeService';

// 也可以作为默认导出
import { authService } from './authService';
import { phraseService } from './phraseService';
import { thesaurusService } from './thesaurusService';
import { levelService } from './levelService';
import { userService } from './userService';
import { ShareService } from './shareService';
import { settingsService } from './settingsService';
import { levelTagService } from './levelTagService';
import { userStarService } from './userStarService';
import { userFavoriteService } from './userFavoriteService';
import { activationCodeService } from './activationCodeService';

export const services = {
  auth: authService,
  phrase: phraseService,
  thesaurus: thesaurusService,
  level: levelService,
  user: userService,
  share: ShareService,
  settings: settingsService,
  levelTag: levelTagService,
  userStar: userStarService,
  userFavorite: userFavoriteService,
  activationCode: activationCodeService,
};
