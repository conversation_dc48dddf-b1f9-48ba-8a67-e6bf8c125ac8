import request from "./request";

// VIP套餐数据类型
export interface VipPackage {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 创建/更新VIP套餐参数
export interface VipPackageParams {
  id?: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  sortOrder: number;
  isActive: boolean;
}

// 支付订单数据类型
export interface PaymentOrder {
  id: string;
  userId: string;
  openid: string;
  out_trade_no: string;
  transaction_id?: string;
  description: string;
  total: number;
  status: "PENDING" | "SUCCESS" | "FAILED" | "CANCELLED" | "REFUNDED";
  vip_package_id: string;
  prepay_id?: string;
  detail?: string;
  attach?: string;
  paid_at?: string;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

// VIP套餐服务
export const vipPackageService = {
  // 获取VIP套餐列表
  async getList(params?: {
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<VipPackage[]> {
    const response = await request.get<VipPackage[]>("/payment/packages", {
      params,
    });
    return response.data;
  },

  // 根据ID获取VIP套餐
  async getById(id: string): Promise<VipPackage> {
    const response = await request.get(`/payment/packages/${id}`);
    return response.data;
  },

  // 创建VIP套餐
  async create(params: VipPackageParams): Promise<VipPackage> {
    const response = await request.post("/payment/packages", params);
    return response.data;
  },

  // 更新VIP套餐
  async update(
    id: string,
    params: Partial<VipPackageParams>
  ): Promise<VipPackage> {
    const response = await request.put(`/payment/packages/${id}`, params);
    return response.data;
  },

  // 删除VIP套餐
  async delete(id: string): Promise<void> {
    await request.delete(`/payment/packages/${id}`);
  },

  // 切换套餐状态（通过更新接口实现）
  async toggleStatus(id: string, isActive: boolean): Promise<VipPackage> {
    const response = await request.put(`/payment/packages/${id}`, { isActive });
    return response.data;
  },

  // 删除套餐统计方法
};

// 支付订单服务
export const paymentOrderService = {
  // 获取支付订单列表
  async getList(params?: {
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
    userId?: string;
    packageId?: string;
  }): Promise<{
    orders: PaymentOrder[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const response = await request.get("/payment/orders", { params });
    return response.data;
  },

  // 删除支付订单统计方法

  // 根据ID获取支付订单
  async getById(id: string): Promise<PaymentOrder> {
    const response = await request.get(`/payment/orders/${id}`);
    return response.data;
  },

  // 申请退款
  async refund(
    id: string,
    reason?: string
  ): Promise<{
    success: boolean;
    message: string;
    refundId?: string;
  }> {
    const response = await request.post(`/payment/orders/${id}/refund`, {
      reason,
    });
    return response.data;
  },

  // 取消订单
  async cancel(out_trade_no: string, userId: string): Promise<void> {
    await request.post(`/payment/cancel/${out_trade_no}`, { userId });
  },
};

// 删除VIP用户服务，功能已合并到用户管理

// 导出所有服务
export default {
  vipPackageService,
  paymentOrderService,
};
