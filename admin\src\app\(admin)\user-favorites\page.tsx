'use client';

import React, { useState, useEffect, Suspense } from 'react';
import {
  Table, Card, Row, Col, Statistic, Select, DatePicker, Button, Space,
  Tag, Avatar, Tooltip, Progress, message, Input, Spin
} from 'antd';
import {
  HeartOutlined, UserOutlined, UnorderedListOutlined, Bar<PERSON>hartOutlined,
  ReloadOutlined, DownloadOutlined, FilterOutlined, EyeOutlined, LinkOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  userFavoriteService,
  type UserFavorite as UserFavoriteType,
  type UserFavoriteQueryParams
} from '@/services/userFavoriteService';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 移除本地类型定义，直接使用API返回的统计数据类型

function UserFavoritesPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [favorites, setFavorites] = useState<UserFavoriteType[]>([]);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<string | undefined>(undefined);
  const [selectedDifficulty, setSelectedDifficulty] = useState<number | undefined>(undefined);
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>(undefined);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  // 获取收藏数据
  const fetchFavorites = async (params?: UserFavoriteQueryParams) => {
    setLoading(true);
    try {
      const result = await userFavoriteService.getAll({
        ...params,
        page: params?.page || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize
      });

      setFavorites(result.data || []);
      setPagination({
        current: result.page,
        pageSize: result.pageSize,
        total: result.total,
        totalPages: result.totalPages
      });
    } catch (error) {
      console.error('Error fetching favorites:', error);
      // 使用模拟数据
      setFavorites([
        {
          id: '1',
          userId: 'user1',
          levelId: 'level1',
          createdAt: '2024-01-10T10:30:00Z',
          user: {
            id: 'user1',
            nickname: '张三',
            avatar: '',
          },
          level: {
            id: 'level1',
            title: '基础词汇练习1',
            difficulty: 1,
            isVip: false,
          },
        },
        {
          id: '2',
          userId: 'user2',
          levelId: 'level2',
          createdAt: '2024-01-10T11:15:00Z',
          user: {
            id: 'user2',
            nickname: '李四',
            avatar: '',
          },
          level: {
            id: 'level2',
            title: '商务英语入门',
            difficulty: 2,
            isVip: true,
          },
        },
        {
          id: '3',
          userId: 'user1',
          levelId: 'level3',
          createdAt: '2024-01-10T14:20:00Z',
          user: {
            id: 'user1',
            nickname: '张三',
            avatar: '',
          },
          level: {
            id: 'level3',
            title: '高级语法练习',
            difficulty: 3,
            isVip: true,
          },
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 删除统计数据获取功能

  useEffect(() => {
    // 处理URL参数
    const levelIdParam = searchParams.get('levelId');
    if (levelIdParam) {
      setSelectedLevel(levelIdParam);
    }

    fetchFavorites();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 当筛选条件改变时重新获取数据
  useEffect(() => {
    if (selectedLevel) {
      handleFilter();
    }
  }, [selectedLevel]); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理筛选
  const handleFilter = () => {
    const params: UserFavoriteQueryParams = {
      page: 1, // 重置到第一页
    };
    if (dateRange) {
      params.startDate = dateRange[0];
      params.endDate = dateRange[1];
    }
    if (selectedLevel) params.levelId = selectedLevel;
    if (selectedDifficulty) params.difficulty = selectedDifficulty;
    if (selectedUserId) params.userId = selectedUserId;

    fetchFavorites(params);
  };

  // 重置筛选
  const handleReset = () => {
    setDateRange(null);
    setSelectedLevel(undefined);
    setSelectedDifficulty(undefined);
    setSelectedUserId(undefined);
    fetchFavorites({ page: 1 });
  };

  // 导出数据
  const handleExport = async () => {
    try {
      const params = {
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        levelId: selectedLevel,
        userId: selectedUserId,
        difficulty: selectedDifficulty,
        format: 'excel' as const,
      };

      const blob = await userFavoriteService.exportData(params);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `user-favorites-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      console.error('Export error:', error);
      message.error('导出失败');
    }
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize?: number) => {
    const params: UserFavoriteQueryParams = {
      page,
      pageSize: pageSize || pagination.pageSize,
    };

    if (dateRange) {
      params.startDate = dateRange[0];
      params.endDate = dateRange[1];
    }
    if (selectedLevel) params.levelId = selectedLevel;
    if (selectedDifficulty) params.difficulty = selectedDifficulty;
    if (selectedUserId) params.userId = selectedUserId;

    fetchFavorites(params);
  };

  // 难度标签颜色
  const getDifficultyColor = (difficulty: number) => {
    if (difficulty <= 1) return 'green';
    if (difficulty <= 2) return 'orange';
    if (difficulty >= 3) return 'red';
    return 'default';
  };

  // 难度标签文本
  const getDifficultyText = (difficulty: number) => {
    if (difficulty <= 1) return '简单';
    if (difficulty <= 2) return '中等';
    if (difficulty >= 3) return '困难';
    return '未知';
  };

  // 表格列定义
  const columns: ColumnsType<UserFavoriteType> = [
    {
      title: '用户',
      key: 'user',
      render: (_, record) => (
        <Space>
          <Avatar src={record.user?.avatar} icon={<UserOutlined />} />
          <div>
            <div>{record.user?.nickname}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ID: {record.userId}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '收藏关卡',
      key: 'level',
      render: (_, record) => (
        <div>
          <Space>
            <span>{record.level?.title}</span>
            <Tooltip title="跳转到关卡管理">
              <Button
                type="link"
                size="small"
                icon={<LinkOutlined />}
                onClick={() => router.push(`/levels?highlight=${record.levelId}`)}
              />
            </Tooltip>
          </Space>
          <div style={{ marginTop: 4 }}>
            <Tag color={getDifficultyColor(record.level?.difficulty || 1)}>
              {getDifficultyText(record.level?.difficulty || 1)}
            </Tag>
            {record.level?.isVip && <Tag color="purple">VIP关卡</Tag>}
          </div>
        </div>
      ),
    },
    {
      title: '收藏时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleString(),
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看用户详情">
            <Button
              type="link"
              size="small"
              icon={<UserOutlined />}
              onClick={() => router.push(`/users?highlight=${record.userId}`)}
            >
              用户
            </Button>
          </Tooltip>
          <Tooltip title="查看关卡详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => router.push(`/levels?highlight=${record.levelId}`)}
            >
              关卡
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (loading && favorites.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载收藏数据中...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <h2>收藏管理</h2>
        <p>查看和分析用户的关卡收藏情况</p>
      </div>

      {/* 删除统计卡片和用户参与度分析 */}

      {/* 筛选和操作栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={5}>
            <RangePicker
              value={dateRange ? [dateRange[0], dateRange[1]] as any : null}
              onChange={(dates) => {
                if (dates) {
                  setDateRange([dates[0]!.format('YYYY-MM-DD'), dates[1]!.format('YYYY-MM-DD')]);
                } else {
                  setDateRange(null);
                }
              }}
              placeholder={['开始日期', '结束日期']}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择关卡"
              value={selectedLevel}
              onChange={setSelectedLevel}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="level1">基础词汇练习1</Option>
              <Option value="level2">商务英语入门</Option>
              <Option value="level3">高级语法练习</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="难度筛选"
              value={selectedDifficulty}
              onChange={setSelectedDifficulty}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value={1}>简单</Option>
              <Option value={2}>中等</Option>
              <Option value={3}>困难</Option>
              <Option value={4}>很难</Option>
              <Option value={5}>极难</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Input
              placeholder="用户ID"
              value={selectedUserId}
              onChange={(e) => setSelectedUserId(e.target.value || undefined)}
              allowClear
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Space>
              <Button type="primary" icon={<FilterOutlined />} onClick={handleFilter}>
                筛选
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 收藏记录表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={favorites}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
        />
      </Card>

      {/* 删除热门关卡分析 */}
    </div>
  );
}

export default function UserFavoritesPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <UserFavoritesPageContent />
    </Suspense>
  );
}
