'use client';

import React, { useState } from 'react';
import { Card, Typography, Form, Input, Button, message } from 'antd';
import { useRouter } from 'next/navigation';
import { createThesaurus } from '@/services/thesaurusService';
import { CreateThesaurusDto } from '@/types/thesaurus';

const { Title } = Typography;

const CreateThesaurusPage: React.FC = () => {
  const [form] = Form.useForm<CreateThesaurusDto>();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: CreateThesaurusDto) => {
    setLoading(true);
    try {
      await createThesaurus(values);
      message.success('词库创建成功！');
      router.push('/thesauruses');
    } catch (error: any) {
      message.error(error.message || '词库创建失败！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Title level={3}>创建新词库</Title>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <Form.Item
          name="name"
          label="词库名称"
          rules={[{ required: true, message: '请输入词库名称' }]}
        >
          <Input placeholder="例如：日常用语" />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述 (可选)"
        >
          <Input.TextArea rows={3} placeholder="词库简介" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            创建词库
          </Button>
          <Button style={{ marginLeft: 8 }} onClick={() => router.back()}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default CreateThesaurusPage;