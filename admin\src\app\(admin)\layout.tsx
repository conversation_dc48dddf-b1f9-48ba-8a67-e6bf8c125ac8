'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Layout, Menu, Avatar, Dropdown, Typography } from 'antd';
import '../../styles/responsive.css';
import {
  UserOutlined,
  DashboardOutlined,
  UnorderedListOutlined,
  ReadOutlined,
  LogoutOutlined,
  TeamOutlined,
  ShareAltOutlined,
  CrownOutlined,
  DollarOutlined,
  GiftOutlined,
  SettingOutlined,
  TagOutlined,
  StarOutlined,
  HeartOutlined,
  CodeOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Header, Content, Sider, Footer } = Layout;

interface MenuItemConfig {
  key: string;
  icon?: React.ReactNode;
  label: React.ReactNode;
  path: string;
}

const menuItemsConfig: MenuItemConfig[] = [
  { key: 'dashboard', label: '主控面板', path: '/dashboard', icon: <DashboardOutlined /> },
  { key: 'users', label: '用户管理', path: '/users', icon: <TeamOutlined /> },
  { key: 'levels', label: '关卡管理', path: '/levels', icon: <UnorderedListOutlined /> },
  // { key: 'phrases', label: '词组管理', path: '/phrases', icon: <ReadOutlined /> }, // 隐藏词组管理
  { key: 'level-tags', label: '标签管理', path: '/level-tags', icon: <TagOutlined /> },
  { key: 'shares', label: '分享管理', path: '/shares', icon: <ShareAltOutlined /> },
  { key: 'vip-packages', label: 'VIP套餐管理', path: '/vip-packages', icon: <GiftOutlined /> },
  { key: 'payment-orders', label: '支付订单管理', path: '/payment-orders', icon: <DollarOutlined /> },
  { key: 'activation-codes', label: '激活码管理', path: '/activation-codes', icon: <CodeOutlined /> },
  { key: 'user-stars', label: '星级管理', path: '/user-stars', icon: <StarOutlined /> },
  { key: 'user-favorites', label: '收藏管理', path: '/user-favorites', icon: <HeartOutlined /> },
  { key: 'settings', label: '小程序设置', path: '/settings', icon: <SettingOutlined /> },
  // { key: 'thesauruses', label: '词库管理', path: '/thesauruses', icon: <BookOutlined /> },
];

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('admin_token');
    if (!token) {
      router.replace('/login');
    }
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    router.push('/login');
  };

  const userMenuItems: MenuProps['items'] = [
    { key: 'logout', icon: <LogoutOutlined />, label: '退出登录', onClick: handleLogout },
  ];

  // 根据当前路径确定选中的菜单项
  const selectedKeys = menuItemsConfig.find(item => pathname.startsWith(item.path))?.key || 'dashboard';

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider collapsible collapsed={collapsed} onCollapse={(value) => setCollapsed(value)}>
        <div style={{ height: 32, margin: 16, background: 'rgba(255, 255, 255, 0.2)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography.Text style={{ color: 'white', fontSize: collapsed ? '10px' : '16px', transition: 'font-size 0.2s' }}>
            {collapsed ? '后台' : '游戏管理后台'}
          </Typography.Text>
        </div>
        <Menu
          theme="dark"
          selectedKeys={[selectedKeys]}
          mode="inline"
          items={menuItemsConfig.map(item => ({
            key: item.key,
            icon: item.icon,
            label: <Link href={item.path}>{item.label}</Link>
          }))}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: '0 16px', background: '#fff', display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <Avatar style={{ cursor: 'pointer' }} icon={<UserOutlined />} />
          </Dropdown>
          <span style={{ marginLeft: 8 }}>Admin</span>
        </Header>
        <Content style={{ margin: '16px' }}>{children}</Content>
        <Footer style={{ textAlign: 'center' }}>游戏管理后台 ©{new Date().getFullYear()}</Footer>
      </Layout>
    </Layout>
  );
}