(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7655],{24709:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>L});var l=s(95155),a=s(12115),n=s(20778),i=s(24971),o=s(19868),r=s(12320),c=s(28562),d=s(26922),u=s(77325),h=s(37974),m=s(16467),v=s(6124),p=s(19361),g=s(74947),x=s(56020),y=s(51087),j=s(50274),f=s(23130),w=s(52092),A=s(15125),k=s(34140),b=s(16913),C=s(35695),S=s(82112);let{Option:T}=n.A,{RangePicker:I}=i.A;function U(){let e=(0,<PERSON>.useRouter)(),t=(0,C.useSearchParams)(),[s,i]=(0,a.useState)([]),[U,L]=(0,a.useState)(!1),[R,E]=(0,a.useState)(null),[z,N]=(0,a.useState)(void 0),[D,_]=(0,a.useState)(void 0),[M,P]=(0,a.useState)(void 0),[O,Y]=(0,a.useState)({current:1,pageSize:20,total:0,totalPages:0}),B=async e=>{L(!0);try{let t=await S.r.getAll({...e,page:(null==e?void 0:e.page)||O.current,pageSize:(null==e?void 0:e.pageSize)||O.pageSize});i(t.data||[]),Y({current:t.page,pageSize:t.pageSize,total:t.total,totalPages:t.totalPages})}catch(e){console.error("Error fetching favorites:",e),i([{id:"1",userId:"user1",levelId:"level1",createdAt:"2024-01-10T10:30:00Z",user:{id:"user1",nickname:"张三",avatar:""},level:{id:"level1",title:"基础词汇练习1",difficulty:1,isVip:!1}},{id:"2",userId:"user2",levelId:"level2",createdAt:"2024-01-10T11:15:00Z",user:{id:"user2",nickname:"李四",avatar:""},level:{id:"level2",title:"商务英语入门",difficulty:2,isVip:!0}},{id:"3",userId:"user1",levelId:"level3",createdAt:"2024-01-10T14:20:00Z",user:{id:"user1",nickname:"张三",avatar:""},level:{id:"level3",title:"高级语法练习",difficulty:3,isVip:!0}}])}finally{L(!1)}};(0,a.useEffect)(()=>{let e=t.get("levelId");e&&N(e),B()},[]),(0,a.useEffect)(()=>{z&&F()},[z]);let F=()=>{let e={page:1};R&&(e.startDate=R[0],e.endDate=R[1]),z&&(e.levelId=z),D&&(e.difficulty=D),M&&(e.userId=M),B(e)},V=async()=>{try{let e={startDate:null==R?void 0:R[0],endDate:null==R?void 0:R[1],levelId:z,userId:M,difficulty:D,format:"excel"},t=await S.r.exportData(e),s=window.URL.createObjectURL(t),l=document.createElement("a");l.href=s,l.download="user-favorites-".concat(new Date().toISOString().split("T")[0],".xlsx"),document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(s),o.Ay.success("导出成功")}catch(e){console.error("Export error:",e),o.Ay.error("导出失败")}},Z=(e,t)=>{let s={page:e,pageSize:t||O.pageSize};R&&(s.startDate=R[0],s.endDate=R[1]),z&&(s.levelId=z),D&&(s.difficulty=D),M&&(s.userId=M),B(s)},q=e=>e<=1?"green":e<=2?"orange":e>=3?"red":"default",K=e=>e<=1?"简单":e<=2?"中等":e>=3?"困难":"未知",X=[{title:"用户",key:"user",render:(e,t)=>{var s,a;return(0,l.jsxs)(r.A,{children:[(0,l.jsx)(c.A,{src:null==(s=t.user)?void 0:s.avatar,icon:(0,l.jsx)(j.A,{})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:null==(a=t.user)?void 0:a.nickname}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",t.userId]})]})]})}},{title:"收藏关卡",key:"level",render:(t,s)=>{var a,n,i,o;return(0,l.jsxs)("div",{children:[(0,l.jsxs)(r.A,{children:[(0,l.jsx)("span",{children:null==(a=s.level)?void 0:a.title}),(0,l.jsx)(d.A,{title:"跳转到关卡管理",children:(0,l.jsx)(u.Ay,{type:"link",size:"small",icon:(0,l.jsx)(f.A,{}),onClick:()=>e.push("/levels?highlight=".concat(s.levelId))})})]}),(0,l.jsxs)("div",{style:{marginTop:4},children:[(0,l.jsx)(h.A,{color:q((null==(n=s.level)?void 0:n.difficulty)||1),children:K((null==(i=s.level)?void 0:i.difficulty)||1)}),(null==(o=s.level)?void 0:o.isVip)&&(0,l.jsx)(h.A,{color:"purple",children:"VIP关卡"})]})]})}},{title:"收藏时间",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleString(),sorter:(e,t)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()},{title:"操作",key:"action",render:(t,s)=>(0,l.jsxs)(r.A,{children:[(0,l.jsx)(d.A,{title:"查看用户详情",children:(0,l.jsx)(u.Ay,{type:"link",size:"small",icon:(0,l.jsx)(j.A,{}),onClick:()=>e.push("/users?highlight=".concat(s.userId)),children:"用户"})}),(0,l.jsx)(d.A,{title:"查看关卡详情",children:(0,l.jsx)(u.Ay,{type:"link",size:"small",icon:(0,l.jsx)(w.A,{}),onClick:()=>e.push("/levels?highlight=".concat(s.levelId)),children:"关卡"})})]})}];return U&&0===s.length?(0,l.jsxs)("div",{style:{textAlign:"center",padding:"100px 0"},children:[(0,l.jsx)(m.A,{size:"large"}),(0,l.jsx)("div",{style:{marginTop:16},children:"加载收藏数据中..."})]}):(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{style:{marginBottom:24},children:[(0,l.jsx)("h2",{children:"收藏管理"}),(0,l.jsx)("p",{children:"查看和分析用户的关卡收藏情况"})]}),(0,l.jsx)(v.A,{size:"small",style:{marginBottom:16},children:(0,l.jsxs)(p.A,{gutter:16,align:"middle",children:[(0,l.jsx)(g.A,{span:5,children:(0,l.jsx)(I,{value:R?[R[0],R[1]]:null,onChange:e=>{e?E([e[0].format("YYYY-MM-DD"),e[1].format("YYYY-MM-DD")]):E(null)},placeholder:["开始日期","结束日期"],style:{width:"100%"}})}),(0,l.jsx)(g.A,{span:4,children:(0,l.jsxs)(n.A,{placeholder:"选择关卡",value:z,onChange:N,allowClear:!0,style:{width:"100%"},children:[(0,l.jsx)(T,{value:"level1",children:"基础词汇练习1"}),(0,l.jsx)(T,{value:"level2",children:"商务英语入门"}),(0,l.jsx)(T,{value:"level3",children:"高级语法练习"})]})}),(0,l.jsx)(g.A,{span:3,children:(0,l.jsxs)(n.A,{placeholder:"难度筛选",value:D,onChange:_,allowClear:!0,style:{width:"100%"},children:[(0,l.jsx)(T,{value:1,children:"简单"}),(0,l.jsx)(T,{value:2,children:"中等"}),(0,l.jsx)(T,{value:3,children:"困难"}),(0,l.jsx)(T,{value:4,children:"很难"}),(0,l.jsx)(T,{value:5,children:"极难"})]})}),(0,l.jsx)(g.A,{span:4,children:(0,l.jsx)(x.A,{placeholder:"用户ID",value:M,onChange:e=>P(e.target.value||void 0),allowClear:!0,style:{width:"100%"}})}),(0,l.jsx)(g.A,{span:8,children:(0,l.jsxs)(r.A,{children:[(0,l.jsx)(u.Ay,{type:"primary",icon:(0,l.jsx)(A.A,{}),onClick:F,children:"筛选"}),(0,l.jsx)(u.Ay,{icon:(0,l.jsx)(k.A,{}),onClick:()=>{E(null),N(void 0),_(void 0),P(void 0),B({page:1})},children:"重置"}),(0,l.jsx)(u.Ay,{icon:(0,l.jsx)(b.A,{}),onClick:V,children:"导出"})]})})]})}),(0,l.jsx)(v.A,{children:(0,l.jsx)(y.A,{columns:X,dataSource:s,rowKey:"id",loading:U,pagination:{current:O.current,pageSize:O.pageSize,total:O.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条，共 ").concat(e," 条记录"),onChange:Z,onShowSizeChange:Z,pageSizeOptions:["10","20","50","100"]}})})]})}function L(){return(0,l.jsx)(a.Suspense,{fallback:(0,l.jsx)("div",{children:"Loading..."}),children:(0,l.jsx)(U,{})})}},36449:()=>{},41008:(e,t,s)=>{"use strict";s.d(t,{i:()=>l});let l={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},73629:()=>{},82112:(e,t,s)=>{"use strict";s.d(t,{r:()=>a});var l=s(83899);let a={getAll:async e=>(await l.Ay.get("/user-favorites",{params:e})).data,exportData:async e=>(await l.Ay.get("/user-favorites/export",{params:e,responseType:"blob"})).data};a.getAll},83899:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>c,FH:()=>o,KY:()=>r});var l=s(23464),a=s(90285),n=s(41008);let i=l.A.create({baseURL:n.i.FULL_BASE_URL,timeout:n.i.TIMEOUT,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{var t;let s=localStorage.getItem("admin_token");return s&&(e.headers.Authorization="Bearer ".concat(s)),console.log("\uD83D\uDE80 发送请求:",{method:null==(t=e.method)?void 0:t.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),i.interceptors.response.use(e=>{var t;let s=e.config;return console.log("✅ 请求成功:",{method:null==(t=s.method)?void 0:t.toUpperCase(),url:s.url,status:e.status,statusText:e.statusText,data:e.data}),s.showSuccess&&s.successMessage&&a.i.success(s.successMessage),e},e=>{var t,s,l,n,i,o,r,c,d;console.error("❌ 请求失败:",{method:null==(s=e.config)||null==(t=s.method)?void 0:t.toUpperCase(),url:null==(l=e.config)?void 0:l.url,baseURL:null==(n=e.config)?void 0:n.baseURL,fullURL:"".concat(null==(i=e.config)?void 0:i.baseURL).concat(null==(o=e.config)?void 0:o.url),status:null==(r=e.response)?void 0:r.status,statusText:null==(c=e.response)?void 0:c.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:t,data:s}=e.response,l="";switch(t){case 401:l="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:l=(null==s?void 0:s.message)||"没有权限访问该资源";break;case 404:l=(null==s?void 0:s.message)||"请求的资源不存在";break;case 422:l=(null==s?void 0:s.message)||"请求参数验证失败";break;case 500:l=(null==s?void 0:s.message)||"服务器内部错误";break;default:l=(null==s?void 0:s.message)||"请求失败 (".concat(t,")")}a.i.error(l)}else e.request?a.i.error("网络连接失败，请检查网络"):a.i.error("请求配置错误");return Promise.reject(e)});let o={get:(e,t)=>i.get(e,t),post:(e,t,s)=>i.post(e,t,s),put:(e,t,s)=>i.put(e,t,s),patch:(e,t,s)=>i.patch(e,t,s),delete:(e,t)=>i.delete(e,t)},r={post:(e,t,s,l)=>o.post(e,t,{...l,showSuccess:!0,successMessage:s||"操作成功"}),put:(e,t,s,l)=>o.put(e,t,{...l,showSuccess:!0,successMessage:s||"更新成功"}),patch:(e,t,s,l)=>o.patch(e,t,{...l,showSuccess:!0,successMessage:s||"更新成功"}),delete:(e,t,s)=>o.delete(e,{...s,showSuccess:!0,successMessage:t||"删除成功"})},c=o},87788:(e,t,s)=>{Promise.resolve().then(s.bind(s,24709))},90285:(e,t,s)=>{"use strict";s.d(t,{a:()=>r,i:()=>h});var l=s(95155),a=s(12115),n=s(12669);s(36449);let i=e=>{let{title:t,content:s,children:n,visible:i=!1,width:o=520,centered:r=!1,closable:c=!0,maskClosable:d=!0,footer:u,okText:h="确定",cancelText:m="取消",okType:v="primary",confirmLoading:p=!1,onOk:g,onCancel:x,afterClose:y,className:j="",style:f={}}=e,[w,A]=(0,a.useState)(i),[k,b]=(0,a.useState)(!1);(0,a.useEffect)(()=>{i?(A(!0),b(!0),document.body.style.overflow="hidden"):(b(!1),setTimeout(()=>{A(!1),document.body.style.overflow="",null==y||y()},300))},[i,y]);let C=async()=>{if(g)try{await g()}catch(e){console.error("Modal onOk error:",e)}},S=()=>{null==x||x()};return w?(0,l.jsx)("div",{className:"custom-modal-mask ".concat(k?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==x||x())},children:(0,l.jsx)("div",{className:"custom-modal-wrap ".concat(r?"custom-modal-centered":""),children:(0,l.jsxs)("div",{className:"custom-modal ".concat(j," ").concat(k?"custom-modal-show":"custom-modal-hide"),style:{width:o,...f},children:[(t||c)&&(0,l.jsxs)("div",{className:"custom-modal-header",children:[t&&(0,l.jsx)("div",{className:"custom-modal-title",children:t}),c&&(0,l.jsx)("button",{className:"custom-modal-close",onClick:S,"aria-label":"Close",children:"\xd7"})]}),(0,l.jsx)("div",{className:"custom-modal-body",children:s||n}),null===u?null:u||(0,l.jsxs)("div",{className:"custom-modal-footer",children:[m&&(0,l.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:S,children:m}),(0,l.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(v),onClick:C,disabled:p,children:[p&&(0,l.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),h]})]})]})})}):null};class o{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}confirm(e){return new Promise((t,s)=>{let a=!1,n=async()=>{if(!a)try{e.onOk&&await e.onOk(),a=!0,this.destroy(),t()}catch(e){s(e)}};this.getContainer(),this.root.render((0,l.jsx)(i,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:n,onCancel:()=>{var t;a||(a=!0,null==(t=e.onCancel)||t.call(e),this.destroy(),s(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}i.confirm=e=>new o().confirm({...e,okType:e.okType||"primary"}),i.info=e=>new o().confirm({...e,okType:"primary",cancelText:void 0}),i.success=e=>new o().confirm({...e,okType:"primary",cancelText:void 0}),i.error=e=>new o().confirm({...e,okType:"danger",cancelText:void 0}),i.warning=e=>new o().confirm({...e,okType:"primary",cancelText:void 0});let r=i;s(73629);let c=e=>{let{messages:t}=e;return(0,l.jsx)("div",{className:"custom-message-container",children:t.map(e=>(0,l.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,l.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,l.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,l.jsx)(c,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var t;let s=e.key||this.generateId(),l=null!=(t=e.duration)?t:3e3;e.key&&(this.messages=this.messages.filter(t=>t.id!==e.key));let a={...e,id:s,visible:!0};return this.messages.push(a),this.getContainer(),this.render(),l>0&&setTimeout(()=>{this.hide(s)},l),s}hide(e){let t=this.messages.findIndex(t=>t.id===e);t>-1&&(this.messages[t].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(t=>t.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,h={success:(e,t)=>u.show({content:e,type:"success",duration:t}),error:(e,t)=>u.show({content:e,type:"error",duration:t}),warning:(e,t)=>u.show({content:e,type:"warning",duration:t}),info:(e,t)=>u.show({content:e,type:"info",duration:t}),destroy:()=>u.destroy()}}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,2343,1087,6226,1125,8441,1684,7358],()=>t(87788)),_N_E=e.O()}]);