{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/app/%28admin%29/dashboard/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport {\r\n  Card, Typography, Row, Col, Button, message\r\n} from 'antd';\r\nimport {\r\n  UserOutlined,\r\n  ReadOutlined,\r\n  UnorderedListOutlined,\r\n  DollarOutlined,\r\n  SettingOutlined,\r\n  TagOutlined\r\n} from '@ant-design/icons';\r\nimport { useRouter } from 'next/navigation';\r\n\r\nconst { Title, Paragraph } = Typography;\r\n\r\nexport default function DashboardPage() {\r\n  const router = useRouter();\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // 简化数据获取\r\n  const fetchStats = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // 简单的加载完成\r\n      message.success('欢迎使用管理后台');\r\n    } catch (error) {\r\n      message.error('加载失败');\r\n      console.error('Error loading dashboard:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchStats();\r\n  }, []);\r\n\r\n  // 删除统计计算代码\r\n\r\n  return (\r\n    <div>\r\n      <div style={{ marginBottom: 24 }}>\r\n        <Title level={2}>主控面板</Title>\r\n        <Paragraph>\r\n          欢迎使用趣护游戏管理后台！这里展示了系统的核心统计信息和快捷操作入口。\r\n        </Paragraph>\r\n      </div>\r\n\r\n      {/* 删除系统警报 */}\r\n\r\n      {/* 简化的欢迎信息 */}\r\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\r\n        <Col span={24}>\r\n          <Card>\r\n            <div style={{ textAlign: 'center', padding: '40px 0' }}>\r\n              <Title level={3}>欢迎使用管理后台</Title>\r\n              <Paragraph>\r\n                这里是趣护游戏的管理中心，您可以通过下方的快捷操作进行各项管理工作。\r\n              </Paragraph>\r\n            </div>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* 快捷操作 */}\r\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\r\n        <Col span={24}>\r\n          <Card title=\"快捷操作\">\r\n            <Row gutter={[16, 16]}>\r\n              <Col xs={24} sm={12} md={8} lg={6}>\r\n                <Button\r\n                  type=\"primary\"\r\n                  icon={<UserOutlined />}\r\n                  block\r\n                  size=\"large\"\r\n                  onClick={() => router.push('/users')}\r\n                >\r\n                  用户管理\r\n                </Button>\r\n              </Col>\r\n              <Col xs={24} sm={12} md={8} lg={6}>\r\n                <Button\r\n                  icon={<ReadOutlined />}\r\n                  block\r\n                  size=\"large\"\r\n                  onClick={() => router.push('/phrases')}\r\n                >\r\n                  词组管理\r\n                </Button>\r\n              </Col>\r\n              <Col xs={24} sm={12} md={8} lg={6}>\r\n                <Button\r\n                  icon={<UnorderedListOutlined />}\r\n                  block\r\n                  size=\"large\"\r\n                  onClick={() => router.push('/levels')}\r\n                >\r\n                  关卡管理\r\n                </Button>\r\n              </Col>\r\n              <Col xs={24} sm={12} md={8} lg={6}>\r\n                <Button\r\n                  icon={<DollarOutlined />}\r\n                  block\r\n                  size=\"large\"\r\n                  onClick={() => router.push('/payment')}\r\n                >\r\n                  支付管理\r\n                </Button>\r\n              </Col>\r\n              <Col xs={24} sm={12} md={8} lg={6}>\r\n                <Button\r\n                  icon={<SettingOutlined />}\r\n                  block\r\n                  size=\"large\"\r\n                  onClick={() => router.push('/settings')}\r\n                >\r\n                  系统设置\r\n                </Button>\r\n              </Col>\r\n              <Col xs={24} sm={12} md={8} lg={6}>\r\n                <Button\r\n                  icon={<TagOutlined />}\r\n                  block\r\n                  size=\"large\"\r\n                  onClick={() => router.push('/level-tags')}\r\n                >\r\n                  标签管理\r\n                </Button>\r\n              </Col>\r\n            </Row>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* 删除最近活动和详细统计部分 */}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAdA;;;;;AAgBA,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAExB,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,SAAS;IACT,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,UAAU;YACV,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,WAAW;IAEX,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCAC7B,6LAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,6LAAC;kCAAU;;;;;;;;;;;;0BAQb,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;0BAC/C,cAAA,6LAAC,+KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;kCACH,cAAA,6LAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAS;;8CACnD,6LAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,6LAAC;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnB,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;0BAC/C,cAAA,6LAAC,+KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;;8CACnB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;8CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,KAAK;wCACL,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;8CAIH,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;8CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,KAAK;wCACL,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;8CAIH,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;8CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,uOAAA,CAAA,wBAAqB;;;;;wCAC5B,KAAK;wCACL,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;8CAIH,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;8CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACrB,KAAK;wCACL,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;8CAIH,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;8CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;wCACtB,KAAK;wCACL,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;8CAIH,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;8CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;wCAClB,KAAK;wCACL,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjB;GA3HwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}