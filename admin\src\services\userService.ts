import { api, successApi } from "./request";

// 用户数据类型
export interface User {
  id: string; // 8位随机数字ID
  phone: string; // 手机号（必填）
  openid?: string; // 微信openid（可选）
  nickname?: string;
  avatarUrl?: string;
  unlockedLevels: number;
  completedLevelIds: string[];
  totalGames: number;
  totalCompletions: number;
  lastPlayTime: string;
  createdAt: string;
  updatedAt: string;
  // 每日解锁限制相关字段
  isVip: boolean; // VIP状态
  dailyUnlockLimit: number; // 每日解锁限制
  dailyUnlockCount: number; // 当日解锁次数
  dailyShared: boolean; // 当日是否已分享
  lastPlayDate: string; // 最后游戏日期
  totalShares: number; // 总分享次数
}

// 创建用户参数
export interface CreateUserParams {
  phone: string; // 手机号（必填）
  openid?: string; // 微信openid（可选）
  nickname?: string;
  avatarUrl?: string;
}

// 更新用户参数
export interface UpdateUserParams {
  phone?: string; // 手机号
  openid?: string; // 微信openid
  nickname?: string;
  avatarUrl?: string;
  unlockedLevels?: number;
  completedLevelIds?: string[];
  totalGames?: number;
  totalCompletions?: number;
  isVip?: boolean; // VIP状态
  dailyUnlockLimit?: number; // 每日解锁限制
}

// 完成关卡参数
export interface CompleteLevelParams {
  levelId: string;
}

// 设置VIP套餐参数（对应API的SetVipPackageDto）
export interface SetVipParams {
  packageId: string; // VIP套餐ID，如 "vip_custom_111111d_nfow"
  reason?: string; // 操作原因
}

// 取消VIP参数（对应API的CancelVipDto）
export interface CancelVipParams {
  reason?: string; // 取消原因
  immediate: boolean; // 是否立即生效
}

// VIP状态响应（对应API的VipStatusResponseDto）
export interface VipStatusResponse {
  id: string;
  nickname?: string;
  isVip: boolean;
  packageId?: string;
  packageName?: string;
  vipExpiresAt?: string;
  dailyUnlockLimit: number;
  updatedAt: string;
  message: string;
}

// 批量VIP套餐操作参数（对应API的BatchVipPackageOperationDto）
export interface BatchVipOperationParams {
  userIds: string[];
  packageId: string; // VIP套餐ID，必需
  reason?: string; // 操作原因
}

// 用户统计数据
export interface UserStats {
  totalGames: number;
  totalCompletions: number;
  unlockedLevels: number;
  completedLevels: number;
  completionRate: number;
  // 每日解锁统计
  dailyUnlockCount: number;
  dailyUnlockLimit: number;
  remainingUnlocks: number;
  totalShares: number;
  isVip: boolean;
}

// 用户服务
export const userService = {
  // 获取所有用户
  getAll: async (): Promise<{
    users: User[];
    total: number;
  }> => {
    const response = await api.get<any>("/users");
    return response.data;
  },

  // 根据ID获取用户
  getById: async (id: string): Promise<User> => {
    const response = await api.get<User>(`/users/${id}`);
    return response.data;
  },

  // 根据openid获取用户
  getByOpenid: async (openid: string): Promise<User> => {
    const response = await api.get<User>(`/users/by-openid?openid=${openid}`);
    return response.data;
  },

  // 根据手机号获取用户
  getByPhone: async (phone: string): Promise<User> => {
    const response = await api.get<User>(`/users/by-phone?phone=${phone}`);
    return response.data;
  },

  // 创建用户
  create: async (params: CreateUserParams): Promise<User> => {
    const response = await successApi.post<User>(
      "/users",
      params,
      "用户创建成功"
    );
    return response.data;
  },

  // 更新用户
  update: async (id: string, params: UpdateUserParams): Promise<User> => {
    const response = await successApi.patch<User>(
      `/users/${id}`,
      params,
      "用户更新成功"
    );
    return response.data;
  },

  // 删除用户
  delete: async (id: string): Promise<void> => {
    await successApi.delete(`/users/${id}`, "用户删除成功");
  },

  // 用户完成关卡
  completeLevel: async (
    id: string,
    params: CompleteLevelParams
  ): Promise<User> => {
    const response = await api.post<User>(
      `/users/${id}/complete-level`,
      params
    );
    return response.data;
  },

  // 用户开始游戏
  startGame: async (id: string): Promise<User> => {
    const response = await api.post<User>(`/users/${id}/start-game`);
    return response.data;
  },

  // 获取用户统计
  getStats: async (id: string): Promise<UserStats> => {
    const response = await api.get<UserStats>(`/users/${id}/stats`);
    return response.data;
  },

  // 重置用户进度
  resetProgress: async (id: string): Promise<User> => {
    const response = await successApi.post<User>(
      `/users/${id}/reset-progress`,
      {},
      "用户进度重置成功"
    );
    return response.data;
  },

  // 设置用户VIP套餐
  setVipStatus: async (
    id: string,
    params: SetVipParams
  ): Promise<VipStatusResponse> => {
    const response = await successApi.post<VipStatusResponse>(
      `/users/${id}/set-vip-package`,
      {
        packageId: params.packageId,
        reason: params.reason || "管理员手动设置",
      },
      "设置VIP成功"
    );
    return response.data;
  },

  // 取消用户VIP状态
  cancelVipStatus: async (
    id: string,
    params: CancelVipParams
  ): Promise<VipStatusResponse> => {
    const response = await successApi.post<VipStatusResponse>(
      `/users/${id}/cancel-vip`,
      params,
      "取消VIP成功"
    );
    return response.data;
  },

  // 批量VIP操作（统一接口）
  batchVipOperation: async (params: BatchVipOperationParams): Promise<void> => {
    await api.post("/users/batch-vip-package", params);
  },

  // 批量设置用户VIP套餐
  batchSetVipStatus: async (
    userIds: string[],
    params: SetVipParams
  ): Promise<void> => {
    await userService.batchVipOperation({
      userIds,
      packageId: params.packageId,
      reason: params.reason || "管理员批量设置",
    });
  },

  // 批量取消用户VIP状态（使用取消VIP接口）
  batchCancelVipStatus: async (
    userIds: string[],
    params: CancelVipParams
  ): Promise<void> => {
    // 批量取消VIP需要循环调用单个取消接口
    const promises = userIds.map((userId) =>
      userService.cancelVipStatus(userId, params)
    );
    await Promise.all(promises);
  },
};
