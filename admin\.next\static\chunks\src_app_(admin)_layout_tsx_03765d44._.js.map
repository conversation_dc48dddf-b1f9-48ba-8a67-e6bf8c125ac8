{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/app/%28admin%29/layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { Layout, Menu, Avatar, Dropdown, Typography } from 'antd';\r\nimport '../../styles/responsive.css';\r\nimport {\r\n  UserOutlined,\r\n  DashboardOutlined,\r\n  UnorderedListOutlined,\r\n  ReadOutlined,\r\n  LogoutOutlined,\r\n  TeamOutlined,\r\n  ShareAltOutlined,\r\n  CrownOutlined,\r\n  DollarOutlined,\r\n  GiftOutlined,\r\n  SettingOutlined,\r\n  TagOutlined,\r\n  StarOutlined,\r\n  HeartOutlined,\r\n  CodeOutlined,\r\n} from '@ant-design/icons';\r\nimport type { MenuProps } from 'antd';\r\n\r\nconst { Header, Content, Sider, Footer } = Layout;\r\n\r\ninterface MenuItemConfig {\r\n  key: string;\r\n  icon?: React.ReactNode;\r\n  label: React.ReactNode;\r\n  path: string;\r\n}\r\n\r\nconst menuItemsConfig: MenuItemConfig[] = [\r\n  { key: 'dashboard', label: '主控面板', path: '/dashboard', icon: <DashboardOutlined /> },\r\n  { key: 'users', label: '用户管理', path: '/users', icon: <TeamOutlined /> },\r\n  { key: 'levels', label: '关卡管理', path: '/levels', icon: <UnorderedListOutlined /> },\r\n  // { key: 'phrases', label: '词组管理', path: '/phrases', icon: <ReadOutlined /> }, // 隐藏词组管理\r\n  { key: 'level-tags', label: '标签管理', path: '/level-tags', icon: <TagOutlined /> },\r\n  { key: 'shares', label: '分享管理', path: '/shares', icon: <ShareAltOutlined /> },\r\n  { key: 'vip-packages', label: 'VIP套餐管理', path: '/vip-packages', icon: <GiftOutlined /> },\r\n  { key: 'payment-orders', label: '支付订单管理', path: '/payment-orders', icon: <DollarOutlined /> },\r\n  { key: 'activation-codes', label: '激活码管理', path: '/activation-codes', icon: <CodeOutlined /> },\r\n  { key: 'user-stars', label: '星级管理', path: '/user-stars', icon: <StarOutlined /> },\r\n  { key: 'user-favorites', label: '收藏管理', path: '/user-favorites', icon: <HeartOutlined /> },\r\n  { key: 'settings', label: '小程序设置', path: '/settings', icon: <SettingOutlined /> },\r\n  // { key: 'thesauruses', label: '词库管理', path: '/thesauruses', icon: <BookOutlined /> },\r\n];\r\n\r\nexport default function AdminLayout({ children }: { children: React.ReactNode }) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [collapsed, setCollapsed] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem('admin_token');\r\n    if (!token) {\r\n      router.replace('/login');\r\n    }\r\n  }, [router]);\r\n\r\n  const handleLogout = () => {\r\n    localStorage.removeItem('admin_token');\r\n    router.push('/login');\r\n  };\r\n\r\n  const userMenuItems: MenuProps['items'] = [\r\n    { key: 'logout', icon: <LogoutOutlined />, label: '退出登录', onClick: handleLogout },\r\n  ];\r\n\r\n  // 根据当前路径确定选中的菜单项\r\n  const selectedKeys = menuItemsConfig.find(item => pathname.startsWith(item.path))?.key || 'dashboard';\r\n\r\n  return (\r\n    <Layout style={{ minHeight: '100vh' }}>\r\n      <Sider collapsible collapsed={collapsed} onCollapse={(value) => setCollapsed(value)}>\r\n        <div style={{ height: 32, margin: 16, background: 'rgba(255, 255, 255, 0.2)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\r\n          <Typography.Text style={{ color: 'white', fontSize: collapsed ? '10px' : '16px', transition: 'font-size 0.2s' }}>\r\n            {collapsed ? '后台' : '游戏管理后台'}\r\n          </Typography.Text>\r\n        </div>\r\n        <Menu\r\n          theme=\"dark\"\r\n          selectedKeys={[selectedKeys]}\r\n          mode=\"inline\"\r\n          items={menuItemsConfig.map(item => ({\r\n            key: item.key,\r\n            icon: item.icon,\r\n            label: <Link href={item.path}>{item.label}</Link>\r\n          }))}\r\n        />\r\n      </Sider>\r\n      <Layout>\r\n        <Header style={{ padding: '0 16px', background: '#fff', display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>\r\n          <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\r\n            <Avatar style={{ cursor: 'pointer' }} icon={<UserOutlined />} />\r\n          </Dropdown>\r\n          <span style={{ marginLeft: 8 }}>Admin</span>\r\n        </Header>\r\n        <Content style={{ margin: '16px' }}>{children}</Content>\r\n        <Footer style={{ textAlign: 'center' }}>游戏管理后台 ©{new Date().getFullYear()}</Footer>\r\n      </Layout>\r\n    </Layout>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AA0BA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AASjD,MAAM,kBAAoC;IACxC;QAAE,KAAK;QAAa,OAAO;QAAQ,MAAM;QAAc,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;IAAI;IACnF;QAAE,KAAK;QAAS,OAAO;QAAQ,MAAM;QAAU,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IAAI;IACtE;QAAE,KAAK;QAAU,OAAO;QAAQ,MAAM;QAAW,oBAAM,6LAAC,uOAAA,CAAA,wBAAqB;;;;;IAAI;IACjF,yFAAyF;IACzF;QAAE,KAAK;QAAc,OAAO;QAAQ,MAAM;QAAe,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;IAAI;IAC/E;QAAE,KAAK;QAAU,OAAO;QAAQ,MAAM;QAAW,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;IAAI;IAC5E;QAAE,KAAK;QAAgB,OAAO;QAAW,MAAM;QAAiB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IAAI;IACvF;QAAE,KAAK;QAAkB,OAAO;QAAU,MAAM;QAAmB,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;IAAI;IAC5F;QAAE,KAAK;QAAoB,OAAO;QAAS,MAAM;QAAqB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IAAI;IAC7F;QAAE,KAAK;QAAc,OAAO;QAAQ,MAAM;QAAe,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IAAI;IAChF;QAAE,KAAK;QAAkB,OAAO;QAAQ,MAAM;QAAmB,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;IAAI;IACzF;QAAE,KAAK;QAAY,OAAO;QAAS,MAAM;QAAa,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;IAAI;CAEjF;AAEc,SAAS,YAAY,EAAE,QAAQ,EAAiC;;IAC7E,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,OAAO,OAAO,CAAC;YACjB;QACF;gCAAG;QAAC;KAAO;IAEX,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAoC;QACxC;YAAE,KAAK;YAAU,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YAAK,OAAO;YAAQ,SAAS;QAAa;KACjF;IAED,iBAAiB;IACjB,MAAM,eAAe,gBAAgB,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,KAAK,IAAI,IAAI,OAAO;IAE1F,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,OAAO;YAAE,WAAW;QAAQ;;0BAClC,6LAAC;gBAAM,WAAW;gBAAC,WAAW;gBAAW,YAAY,CAAC,QAAU,aAAa;;kCAC3E,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAI,QAAQ;4BAAI,YAAY;4BAA4B,SAAS;4BAAQ,YAAY;4BAAU,gBAAgB;wBAAS;kCAC5I,cAAA,6LAAC,6LAAA,CAAA,aAAU,CAAC,IAAI;4BAAC,OAAO;gCAAE,OAAO;gCAAS,UAAU,YAAY,SAAS;gCAAQ,YAAY;4BAAiB;sCAC3G,YAAY,OAAO;;;;;;;;;;;kCAGxB,6LAAC,iLAAA,CAAA,OAAI;wBACH,OAAM;wBACN,cAAc;4BAAC;yBAAa;wBAC5B,MAAK;wBACL,OAAO,gBAAgB,GAAG,CAAC,CAAA,OAAQ,CAAC;gCAClC,KAAK,KAAK,GAAG;gCACb,MAAM,KAAK,IAAI;gCACf,qBAAO,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;8CAAG,KAAK,KAAK;;;;;;4BAC3C,CAAC;;;;;;;;;;;;0BAGL,6LAAC,qLAAA,CAAA,SAAM;;kCACL,6LAAC;wBAAO,OAAO;4BAAE,SAAS;4BAAU,YAAY;4BAAQ,SAAS;4BAAQ,gBAAgB;4BAAY,YAAY;wBAAS;;0CACxH,6LAAC,yLAAA,CAAA,WAAQ;gCAAC,MAAM;oCAAE,OAAO;gCAAc;gCAAG,WAAU;0CAClD,cAAA,6LAAC,qLAAA,CAAA,SAAM;oCAAC,OAAO;wCAAE,QAAQ;oCAAU;oCAAG,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;0CAE3D,6LAAC;gCAAK,OAAO;oCAAE,YAAY;gCAAE;0CAAG;;;;;;;;;;;;kCAElC,6LAAC;wBAAQ,OAAO;4BAAE,QAAQ;wBAAO;kCAAI;;;;;;kCACrC,6LAAC;wBAAO,OAAO;4BAAE,WAAW;wBAAS;;4BAAG;4BAAS,IAAI,OAAO,WAAW;;;;;;;;;;;;;;;;;;;AAI/E;GAvDwB;;QACP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFN", "debugId": null}}]}