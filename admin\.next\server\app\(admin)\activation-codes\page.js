(()=>{var e={};e.id=1311,e.ids=[1311],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),o=r(48088),n=r(88170),s=r.n(n),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let c={children:["",{children:["(admin)",{children:["activation-codes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44963)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\activation-codes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\activation-codes\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(admin)/activation-codes/page",pathname:"/activation-codes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7628:(e,t,r)=>{Promise.resolve().then(r.bind(r,73833))},10313:(e,t,r)=>{"use strict";r.d(t,{A:()=>m,d:()=>d});var a=r(43210),o=r.n(a),n=r(15693),s=r(44666),l=r(48232),i=r(10491),c=r(97058);function d(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function u(e){let{closable:t,closeIcon:r}=e||{};return o().useMemo(()=>{if(!t&&(!1===t||!1===r||null===r))return!1;if(void 0===t&&void 0===r)return null;let e={closeIcon:"boolean"!=typeof r&&null!==r?r:void 0};return t&&"object"==typeof t&&(e=Object.assign(Object.assign({},e),t)),e},[t,r])}let p={};function m(e,t,r=p){let a=u(e),d=u(t),[h]=(0,l.A)("global",i.A.global),x="boolean"!=typeof a&&!!(null==a?void 0:a.disabled),b=o().useMemo(()=>Object.assign({closeIcon:o().createElement(n.A,null)},r),[r]),g=o().useMemo(()=>!1!==a&&(a?(0,c.A)(b,d,a):!1!==d&&(d?(0,c.A)(b,d):!!b.closable&&b)),[a,d,b]);return o().useMemo(()=>{var e,t;if(!1===g)return[!1,null,x,{}];let{closeIconRender:r}=b,{closeIcon:a}=g,n=a,l=(0,s.A)(g,!0);return null!=n&&(r&&(n=r(a)),n=o().isValidElement(n)?o().cloneElement(n,Object.assign(Object.assign(Object.assign({},n.props),{"aria-label":null!=(t=null==(e=n.props)?void 0:e["aria-label"])?t:h.close}),l)):o().createElement("span",Object.assign({"aria-label":h.close},l),n)),[!0,n,x,l]},[g,b])}},10814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15444:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20780:(e,t,r)=>{Promise.resolve().then(r.bind(r,44963))},21820:e=>{"use strict";e.exports=require("os")},23575:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(80828),o=r(43210);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var s=r(21898);let l=o.forwardRef(function(e,t){return o.createElement(s.A,(0,a.A)({},e,{ref:t,icon:n}))})},24600:(e,t,r)=>{Promise.resolve().then(r.bind(r,10814))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>z});var a=r(60687),o=r(43210),n=r(85814),s=r.n(n),l=r(16189),i=r(98836),c=r(99053),d=r(63736),u=r(56072),p=r(78620);r(15444);var m=r(60203),h=r(81945),x=r(53788),b=r(9242),g=r(3788),y=r(73237),f=r(47453),v=r(31189),j=r(62727),A=r(14723),k=r(72061),C=r(80461),w=r(71103);let{Header:I,Content:$,Sider:P,Footer:O}=i.A,S=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,a.jsx)(m.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,a.jsx)(h.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,a.jsx)(x.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,a.jsx)(b.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,a.jsx)(g.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,a.jsx)(y.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,a.jsx)(f.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,a.jsx)(v.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,a.jsx)(j.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,a.jsx)(A.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,a.jsx)(k.A,{})}];function z({children:e}){let t=(0,l.useRouter)(),r=(0,l.usePathname)(),[n,m]=(0,o.useState)(!1),h=[{key:"logout",icon:(0,a.jsx)(C.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],x=S.find(e=>r.startsWith(e.path))?.key||"dashboard";return(0,a.jsxs)(i.A,{style:{minHeight:"100vh"},children:[(0,a.jsxs)(P,{collapsible:!0,collapsed:n,onCollapse:e=>m(e),children:[(0,a.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,a.jsx)(c.A.Text,{style:{color:"white",fontSize:n?"10px":"16px",transition:"font-size 0.2s"},children:n?"后台":"游戏管理后台"})}),(0,a.jsx)(d.A,{theme:"dark",selectedKeys:[x],mode:"inline",items:S.map(e=>({key:e.key,icon:e.icon,label:(0,a.jsx)(s(),{href:e.path,children:e.label})}))})]}),(0,a.jsxs)(i.A,{children:[(0,a.jsxs)(I,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,a.jsx)(u.A,{menu:{items:h},placement:"bottomRight",children:(0,a.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,a.jsx)(w.A,{})})}),(0,a.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,a.jsx)($,{style:{margin:"16px"},children:e}),(0,a.jsxs)(O,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},44963:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\activation-codes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\activation-codes\\page.tsx","default")},52378:(e,t,r)=>{"use strict";r.d(t,{A:()=>S});var a=r(43210),o=r(69662),n=r.n(o),s=r(11056),l=r(41414),i=r(10313),c=r(56883),d=r(17727),u=r(71802),p=r(42411),m=r(73117),h=r(32476),x=r(60254),b=r(13581);let g=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:a,componentCls:o,calc:n}=e,s=n(a).sub(r).equal(),l=n(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,h.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:r,calc:a}=e,o=e.fontSizeSM;return(0,x.oX)(e,{tagFontSize:o,tagLineHeight:(0,p.zA)(a(e.lineHeightSM).mul(o).equal()),tagIconSize:a(r).sub(a(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},f=e=>({defaultBg:new m.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),v=(0,b.OF)("Tag",e=>g(y(e)),f);var j=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let A=a.forwardRef((e,t)=>{let{prefixCls:r,style:o,className:s,checked:l,onChange:i,onClick:c}=e,d=j(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=a.useContext(u.QO),h=p("tag",r),[x,b,g]=v(h),y=n()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:l},null==m?void 0:m.className,s,b,g);return x(a.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==m?void 0:m.style),className:y,onClick:e=>{null==i||i(!l),null==c||c(e)}})))});var k=r(21821);let C=e=>(0,k.A)(e,(t,{textColor:r,lightBorderColor:a,lightColor:o,darkColor:n})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:o,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:n,borderColor:n},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),w=(0,b.bf)(["Tag","preset"],e=>C(y(e)),f),I=(e,t,r)=>{let a=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${a}Bg`],borderColor:e[`color${a}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},$=(0,b.bf)(["Tag","status"],e=>{let t=y(e);return[I(t,"success","Success"),I(t,"processing","Info"),I(t,"error","Error"),I(t,"warning","Warning")]},f);var P=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let O=a.forwardRef((e,t)=>{let{prefixCls:r,className:o,rootClassName:p,style:m,children:h,icon:x,color:b,onClose:g,bordered:y=!0,visible:f}=e,j=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:A,direction:k,tag:C}=a.useContext(u.QO),[I,O]=a.useState(!0),S=(0,s.A)(j,["closeIcon","closable"]);a.useEffect(()=>{void 0!==f&&O(f)},[f]);let z=(0,l.nP)(b),D=(0,l.ZZ)(b),E=z||D,q=Object.assign(Object.assign({backgroundColor:b&&!E?b:void 0},null==C?void 0:C.style),m),H=A("tag",r),[_,M,R]=v(H),T=n()(H,null==C?void 0:C.className,{[`${H}-${b}`]:E,[`${H}-has-color`]:b&&!E,[`${H}-hidden`]:!I,[`${H}-rtl`]:"rtl"===k,[`${H}-borderless`]:!y},o,p,M,R),L=e=>{e.stopPropagation(),null==g||g(e),e.defaultPrevented||O(!1)},[,B]=(0,i.A)((0,i.d)(e),(0,i.d)(C),{closable:!1,closeIconRender:e=>{let t=a.createElement("span",{className:`${H}-close-icon`,onClick:L},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null==(r=null==e?void 0:e.onClick)||r.call(e,t),L(t)},className:n()(null==e?void 0:e.className,`${H}-close-icon`)}))}}),F="function"==typeof j.onClick||h&&"a"===h.type,N=x||null,V=N?a.createElement(a.Fragment,null,N,h&&a.createElement("span",null,h)):h,Y=a.createElement("span",Object.assign({},S,{ref:t,className:T,style:q}),V,B,z&&a.createElement(w,{key:"preset",prefixCls:H}),D&&a.createElement($,{key:"status",prefixCls:H}));return _(F?a.createElement(d.A,{component:"Tag"},Y):Y)});O.CheckableTag=A;let S=O},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59448:(e,t,r)=>{Promise.resolve().then(r.bind(r,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73833:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var a=r(60687),o=r(43210),n=r(70084),s=r(10411),l=r(48111),i=r(77833),c=r(52378),d=r(42585),u=r(27783),p=r(94733),m=r(28243),h=r(2535),x=r(88210),b=r(80282),g=r(23575),y=r(53082),f=r(80828),v={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zm52 132H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200zM424 712H296V584c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v128H104c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h128v128c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V776h128c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}}]},name:"appstore-add",theme:"outlined"},j=r(21898),A=o.forwardRef(function(e,t){return o.createElement(j.A,(0,f.A)({},e,{ref:t,icon:v}))}),k=r(92950),C=r(86722),w=r(85668),I=r.n(w);let{Option:$}=n.A;function P(){let[e,t]=(0,o.useState)([]),[r,f]=(0,o.useState)([]),[v,j]=(0,o.useState)(!1),[w,P]=(0,o.useState)(!1),[O]=s.A.useForm(),[S,z]=(0,o.useState)(!1),[D,E]=(0,o.useState)(!1),[q]=s.A.useForm(),[H,_]=(0,o.useState)(!1),M=async()=>{j(!0);try{let e=await C.L8.getAll();t(e.codes||[])}catch(e){console.error("Error fetching activation codes:",e),x.i.error("获取激活码列表失败"),t([{code:"VIP2024001",packageId:"pkg_vip_monthly_30d_a1b2",packageName:"VIP月卡",status:"unused",maxRedemptions:1,currentRedemptions:0,expireDate:"2024-12-31T23:59:59Z",source:"双十一活动",batchId:"batch_20241101_001",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{code:"UNLOCK50",packageId:"pkg_unlock_levels_50",packageName:"解锁50关卡",status:"used",maxRedemptions:3,currentRedemptions:2,redemptionHistory:["user123","user456"],usedBy:"12345678",usedAt:"2024-06-15T10:30:00Z",expireDate:"2024-06-30T23:59:59Z",source:"新用户福利",batchId:"batch_20240601_002",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-06-15T10:30:00Z"}])}finally{j(!1)}},R=e=>{navigator.clipboard.writeText(e).then(()=>{x.i.success("激活码已复制到剪贴板")})},T=async e=>{try{await C.L8.disable(e),x.i.success("激活码已禁用"),M()}catch(e){x.i.error("禁用激活码失败")}},L=async e=>{try{await C.L8.enable(e),x.i.success("激活码已启用"),M()}catch(e){x.i.error("启用激活码失败")}},B=e=>{try{x.a.confirm({title:"确认删除",content:`确定要删除激活码 ${e} 吗？此操作不可撤销。`,okText:"确认删除",okType:"danger",cancelText:"取消",onOk:async()=>{try{await C.L8.delete(e,{reason:"管理员手动删除"}),M()}catch(e){console.error("删除激活码失败:",e)}}})}catch(t){console.warn("Modal.confirm compatibility issue, using native confirm"),window.confirm(`确定要删除激活码 ${e} 吗？此操作不可撤销。`)&&F(e)}},F=async e=>{try{await C.L8.delete(e,{reason:"管理员手动删除"}),M()}catch(e){console.error("删除激活码失败:",e)}},N=async()=>{try{let e=await O.validateFields();z(!0);let t={packageId:e.packageId,customCode:e.customCode||void 0,expireDate:e.expireDate?I()(e.expireDate).format("YYYY-MM-DD"):void 0,source:e.source||"manual",maxRedemptions:e.maxRedemptions||1};await C.L8.create(t),P(!1),O.resetFields(),M()}catch(e){console.error("创建激活码失败:",e)}finally{z(!1)}},V=async()=>{try{let e=await q.validateFields();_(!0);let t={packageId:e.packageId,count:e.count,expireDate:e.expireDate?I()(e.expireDate).format("YYYY-MM-DD"):void 0,source:e.source||"batch",prefix:e.prefix||void 0,maxRedemptions:e.maxRedemptions||1},r=await C.L8.batchGenerate(t);x.i.success(`批量生成成功！成功生成 ${r.successCount} 个激活码${r.failedCount>0?`，失败 ${r.failedCount} 个`:""}`),E(!1),q.resetFields(),M()}catch(e){console.error("批量生成激活码失败:",e)}finally{_(!1)}},Y=[{title:"激活码",dataIndex:"code",key:"code",render:e=>(0,a.jsxs)(l.A,{children:[(0,a.jsx)("code",{style:{backgroundColor:"#f5f5f5",padding:"2px 6px",borderRadius:"4px"},children:e}),(0,a.jsx)(i.Ay,{type:"link",size:"small",icon:(0,a.jsx)(b.A,{}),onClick:()=>R(e)})]})},{title:"套餐",dataIndex:"packageName",key:"packageName",render:e=>(0,a.jsx)(c.A,{color:"blue",children:e})},{title:"状态",dataIndex:"status",key:"status",render:e=>{let t={unused:{text:"未使用",color:"green"},used:{text:"已使用",color:"blue"},expired:{text:"已过期",color:"red"},disabled:{text:"已禁用",color:"default"}}[e]||{text:e,color:"default"};return(0,a.jsx)(c.A,{color:t.color,children:t.text})}},{title:"兑换次数",key:"redemptions",render:(e,t)=>{let{maxRedemptions:r,currentRedemptions:o}=t,n=-1===r;return(0,a.jsxs)("span",{children:[o," / ",n?"∞":r,!n&&o>=r&&(0,a.jsx)(c.A,{color:"red",style:{marginLeft:4,fontSize:"12px"},children:"已满"})]})}},{title:"使用者",dataIndex:"usedBy",key:"usedBy",render:e=>e||"-"},{title:"过期时间",dataIndex:"expireDate",key:"expireDate",render:e=>new Date(e).toLocaleDateString()},{title:"操作",key:"action",render:(e,t)=>(0,a.jsxs)(l.A,{children:["unused"===t.status?(0,a.jsx)(i.Ay,{type:"link",size:"small",onClick:()=>T(t.code),children:"禁用"}):"disabled"===t.status?(0,a.jsx)(i.Ay,{type:"link",size:"small",onClick:()=>L(t.code),children:"启用"}):null,(0,a.jsx)(i.Ay,{type:"link",size:"small",icon:(0,a.jsx)(b.A,{}),onClick:()=>R(t.code),children:"复制"}),(0,a.jsx)(i.Ay,{type:"link",size:"small",danger:!0,icon:(0,a.jsx)(g.A,{}),onClick:()=>B(t.code),children:"删除"})]})}];return(0,a.jsxs)("div",{children:[(0,a.jsxs)(d.A,{children:[(0,a.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsx)("h2",{children:"激活码管理"}),(0,a.jsxs)(l.A,{children:[(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(y.A,{}),onClick:()=>P(!0),children:"手动创建"}),(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(A,{}),onClick:()=>E(!0),children:"批量生成"}),(0,a.jsx)(i.Ay,{icon:(0,a.jsx)(k.A,{}),onClick:()=>{M()},children:"刷新"})]})]}),(0,a.jsx)(u.A,{columns:Y,dataSource:e,rowKey:"id",loading:v,pagination:{showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}})]}),(0,a.jsx)(x.a,{title:"手动创建激活码",visible:w,onOk:N,onCancel:()=>{P(!1),O.resetFields()},confirmLoading:S,width:600,children:(0,a.jsxs)(s.A,{form:O,layout:"vertical",initialValues:{source:"manual"},children:[(0,a.jsx)(s.A.Item,{label:"套餐",name:"packageId",rules:[{required:!0,message:"请选择套餐"}],children:(0,a.jsx)(n.A,{placeholder:"选择套餐",children:r.map(e=>(0,a.jsx)($,{value:e.id,children:e.name},e.id))})}),(0,a.jsx)(s.A.Item,{label:"自定义激活码",name:"customCode",help:"留空则自动生成",children:(0,a.jsx)(p.A,{placeholder:"输入自定义激活码（可选）"})}),(0,a.jsx)(s.A.Item,{label:"过期时间",name:"expireDate",help:"留空则使用套餐默认过期时间",children:(0,a.jsx)(m.A,{style:{width:"100%"},placeholder:"选择过期时间（可选）",disabledDate:e=>e&&e<I()().endOf("day")})}),(0,a.jsx)(s.A.Item,{label:"兑换次数限制",name:"maxRedemptions",help:"设置激活码最大兑换次数，-1表示无限次，默认为1次",initialValue:1,children:(0,a.jsx)(h.A,{min:-1,max:999,style:{width:"100%"},placeholder:"输入最大兑换次数",formatter:e=>-1===e?"无限次":`${e}`,parser:e=>{if("无限次"===e)return -1;let t=parseInt(e||"1");return isNaN(t)?1:Math.max(-1,Math.min(999,t))}})}),(0,a.jsx)(s.A.Item,{label:"来源标识",name:"source",children:(0,a.jsx)(p.A,{placeholder:"如：手动创建、客服处理等"})})]})}),(0,a.jsx)(x.a,{title:"批量生成激活码",visible:D,onOk:V,onCancel:()=>{E(!1),q.resetFields()},confirmLoading:H,width:600,children:(0,a.jsxs)(s.A,{form:q,layout:"vertical",initialValues:{count:10,source:"batch"},children:[(0,a.jsx)(s.A.Item,{label:"套餐",name:"packageId",rules:[{required:!0,message:"请选择套餐"}],children:(0,a.jsx)(n.A,{placeholder:"选择套餐",children:r.map(e=>(0,a.jsx)($,{value:e.id,children:e.name},e.id))})}),(0,a.jsx)(s.A.Item,{label:"生成数量",name:"count",rules:[{required:!0,message:"请输入生成数量"},{type:"number",min:1,max:1e3,message:"数量必须在1-1000之间"}],children:(0,a.jsx)(h.A,{min:1,max:1e3,style:{width:"100%"},placeholder:"输入生成数量"})}),(0,a.jsx)(s.A.Item,{label:"激活码前缀",name:"prefix",help:"可选，为生成的激活码添加统一前缀",children:(0,a.jsx)(p.A,{placeholder:"如：VIP2024、SALE等（可选）"})}),(0,a.jsx)(s.A.Item,{label:"过期时间",name:"expireDate",help:"留空则使用套餐默认过期时间",children:(0,a.jsx)(m.A,{style:{width:"100%"},placeholder:"选择过期时间（可选）",disabledDate:e=>e&&e<I()().endOf("day")})}),(0,a.jsx)(s.A.Item,{label:"兑换次数限制",name:"maxRedemptions",help:"设置激活码最大兑换次数，-1表示无限次，默认为1次",initialValue:1,children:(0,a.jsx)(h.A,{min:-1,max:999,style:{width:"100%"},placeholder:"输入最大兑换次数",formatter:e=>-1===e?"无限次":`${e}`,parser:e=>{if("无限次"===e)return -1;let t=parseInt(e||"1");return isNaN(t)?1:Math.max(-1,Math.min(999,t))}})}),(0,a.jsx)(s.A.Item,{label:"来源标识",name:"source",children:(0,a.jsx)(p.A,{placeholder:"如：双十一活动、新用户福利等"})})]})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86722:(e,t,r)=>{"use strict";r.d(t,{L8:()=>o});var a=r(49895);let o={getAll:async e=>(await a.FH.get("/activation-codes",{params:e})).data,getByCode:async e=>(await a.FH.get(`/activation-codes/${e}`)).data,generate:async e=>(await a.KY.post("/activation-codes/generate",e,"激活码生成成功")).data,create:async e=>(await a.KY.post("/activation-codes/create",e,"激活码创建成功")).data,batchGenerate:async e=>(await a.KY.post("/activation-codes/generate",e,"批量生成激活码成功")).data,disable:async e=>{await a.KY.put(`/activation-codes/${e}/disable`,{},"激活码已禁用")},enable:async e=>{await a.KY.put(`/activation-codes/${e}/enable`,{},"激活码已启用")},getAllPackages:async()=>(await a.FH.get("/payment/packages")).data||[],getPopularPackages:async e=>(await a.FH.get("/payment/packages/popular",{params:{limit:e}})).data,delete:async(e,t)=>(await a.KY.delete(`/activation-codes/${e}`,"激活码删除成功",{data:t||{}})).data};o.getAll,o.generate,o.create,o.batchGenerate,o.disable,o.enable},91039:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(80828),o=r(43210);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"};var s=r(21898);let l=o.forwardRef(function(e,t){return o.createElement(s.A,(0,a.A)({},e,{ref:t,icon:n}))})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,6267,1658,8161,675,5336,9196,8331,553,84,7783,411,2535,7436,976],()=>r(4313));module.exports=a})();