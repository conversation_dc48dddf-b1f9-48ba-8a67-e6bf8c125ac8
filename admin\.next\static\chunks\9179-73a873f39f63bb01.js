"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9179],{11407:(a,t,e)=>{e.d(t,{m9:()=>n});var s=e(83899);let c=a=>({id:a.id||"",name:a.name||"",description:a.description||"",color:a.color||"#1890ff",isVip:!!a.isVip,status:a.status||"active",icon:a.icon,createdAt:a.createdAt||new Date().toISOString(),updatedAt:a.updatedAt||new Date().toISOString()}),n={getAll:async()=>((await s.Ay.get("/tags")).data||[]).map(c),getById:async a=>c((await s.Ay.get("/tags/".concat(a))).data),create:async a=>c((await s.Ay.post("/tags",a)).data),update:async(a,t)=>c((await s.Ay.put("/tags/".concat(a),t)).data),delete:async a=>{await s.Ay.delete("/tags/".concat(a))}};n.getAll,n.create,n.update,n.delete},21073:(a,t,e)=>{e.d(t,{Au:()=>c,Io:()=>i,LQ:()=>n,zN:()=>o});var s=e(83899);let c={getAll:async()=>(await s.Ay.get("/thesauruses")).data,getById:async a=>(await s.Ay.get("/thesauruses/".concat(a))).data,create:async a=>(await s.Ay.post("/thesauruses",a)).data,update:async(a,t)=>(await s.Ay.patch("/thesauruses/".concat(a),t)).data,delete:async a=>{await s.Ay.delete("/thesauruses/".concat(a))},addPhrase:async(a,t)=>(await s.Ay.post("/thesauruses/".concat(a,"/phrases"),t)).data,removePhrase:async(a,t)=>(await s.Ay.delete("/thesauruses/".concat(a,"/phrases/").concat(t))).data},n=c.create;c.update,c.delete;let i=c.delete;c.getById,c.getAll;let o=c.getAll},22918:(a,t,e)=>{e.d(t,{f:()=>c});var s=e(83899);let c={getAll:async()=>(await s.Ay.get("/settings")).data,getById:async a=>(await s.Ay.get("/settings/".concat(a))).data,getByKey:async a=>(await s.Ay.get("/settings/key/".concat(a))).data,update:async(a,t)=>(await s.Ay.patch("/settings/".concat(a),t)).data,updateByKey:async(a,t)=>(await s.Ay.patch("/settings/key/".concat(a),{value:t})).data,async initializeDefaults(){await s.Ay.post("/settings/initialize")},async updateAppConfig(a){let t=[];void 0!==a.helpUrl&&t.push(this.updateByKey("help_url",a.helpUrl)),void 0!==a.backgroundMusicUrl&&t.push(this.updateByKey("background_music_url",a.backgroundMusicUrl)),await Promise.all(t)},async getAppConfig(){try{let[a,t]=await Promise.all([this.getByKey("help_url").catch(()=>null),this.getByKey("background_music_url").catch(()=>null)]);return{helpUrl:(null==a?void 0:a.value)||"",backgroundMusicUrl:(null==t?void 0:t.value)||""}}catch(a){return console.error("获取小程序配置失败:",a),{helpUrl:"",backgroundMusicUrl:""}}},async testWeixinAppSettings(){try{return(await s.Ay.get("/weixin/app-settings")).data}catch(a){throw console.error("测试微信小程序设置接口失败:",a),a}},async testWeixinGlobalConfig(){try{return(await s.Ay.get("/weixin/global-config")).data}catch(a){throw console.error("测试微信小程序全局配置接口失败:",a),a}}}},48526:(a,t,e)=>{e.d(t,{L8:()=>c});var s=e(83899);let c={getAll:async a=>(await s.FH.get("/activation-codes",{params:a})).data,getByCode:async a=>(await s.FH.get("/activation-codes/".concat(a))).data,generate:async a=>(await s.KY.post("/activation-codes/generate",a,"激活码生成成功")).data,create:async a=>(await s.KY.post("/activation-codes/create",a,"激活码创建成功")).data,batchGenerate:async a=>(await s.KY.post("/activation-codes/generate",a,"批量生成激活码成功")).data,disable:async a=>{await s.KY.put("/activation-codes/".concat(a,"/disable"),{},"激活码已禁用")},enable:async a=>{await s.KY.put("/activation-codes/".concat(a,"/enable"),{},"激活码已启用")},getAllPackages:async()=>(await s.FH.get("/payment/packages")).data||[],getPopularPackages:async a=>(await s.FH.get("/payment/packages/popular",{params:{limit:a}})).data,delete:async(a,t)=>(await s.KY.delete("/activation-codes/".concat(a),"激活码删除成功",{data:t||{}})).data};c.getAll,c.generate,c.create,c.batchGenerate,c.disable,c.enable},49179:(a,t,e)=>{e.d(t,{k3:()=>o,m9:()=>p.m9,Yi:()=>r,LY:()=>n,Dv:()=>l,HK:()=>d});var s=e(83899),c=e(59959);let n={getAll:async()=>(await s.Ay.get("/phrases")).data,getById:async a=>(await s.Ay.get("/phrases/".concat(a))).data,create:async a=>(await s.Ay.post("/phrases",a)).data,update:async(a,t)=>(await s.Ay.patch("/phrases/".concat(a),t)).data,delete:async a=>{await s.Ay.delete("/phrases/".concat(a))}};var i=e(21073);let o={getAll:async a=>(await s.Ay.get("/levels",{params:a})).data,getAllSimple:async()=>(await s.Ay.get("/levels")).data,getById:async a=>(await s.Ay.get("/levels/".concat(a))).data,create:async a=>(await s.Ay.post("/levels",a)).data,update:async(a,t)=>(await s.Ay.patch("/levels/".concat(a),t)).data,delete:async a=>{await s.Ay.delete("/levels/".concat(a))},addPhrase:async(a,t)=>(await s.Ay.post("/levels/".concat(a,"/phrases"),t)).data,removePhrase:async(a,t)=>(await s.Ay.delete("/levels/".concat(a,"/phrases/").concat(t))).data,getCount:async()=>(await s.Ay.get("/levels/count")).data,getByDifficulty:async a=>(await s.Ay.get("/levels/difficulty/".concat(a))).data,getWithPhrases:async a=>(await s.Ay.get("/levels/".concat(a))).data,getLevelStarAnalytics:async a=>(await s.Ay.get("/levels/".concat(a,"/star-analytics"))).data};o.getAll;let l={getAll:async()=>(await s.FH.get("/users")).data,getById:async a=>(await s.FH.get("/users/".concat(a))).data,getByOpenid:async a=>(await s.FH.get("/users/by-openid?openid=".concat(a))).data,getByPhone:async a=>(await s.FH.get("/users/by-phone?phone=".concat(a))).data,create:async a=>(await s.KY.post("/users",a,"用户创建成功")).data,update:async(a,t)=>(await s.KY.patch("/users/".concat(a),t,"用户更新成功")).data,delete:async a=>{await s.KY.delete("/users/".concat(a),"用户删除成功")},completeLevel:async(a,t)=>(await s.FH.post("/users/".concat(a,"/complete-level"),t)).data,startGame:async a=>(await s.FH.post("/users/".concat(a,"/start-game"))).data,getStats:async a=>(await s.FH.get("/users/".concat(a,"/stats"))).data,resetProgress:async a=>(await s.KY.post("/users/".concat(a,"/reset-progress"),{},"用户进度重置成功")).data,setVipStatus:async(a,t)=>(await s.KY.post("/users/".concat(a,"/set-vip-package"),{packageId:t.packageId,reason:t.reason||"管理员手动设置"},"设置VIP成功")).data,cancelVipStatus:async(a,t)=>(await s.KY.post("/users/".concat(a,"/cancel-vip"),t,"取消VIP成功")).data,batchVipOperation:async a=>{await s.FH.post("/users/batch-vip-package",a)},batchSetVipStatus:async(a,t)=>{await l.batchVipOperation({userIds:a,packageId:t.packageId,reason:t.reason||"管理员批量设置"})},batchCancelVipStatus:async(a,t)=>{let e=a.map(a=>l.cancelVipStatus(a,t));await Promise.all(e)}};var y=e(73884);let d={getList:async a=>(await s.Ay.get("/payment/packages",{params:a})).data,getById:async a=>(await s.Ay.get("/payment/packages/".concat(a))).data,create:async a=>(await s.Ay.post("/payment/packages",a)).data,update:async(a,t)=>(await s.Ay.put("/payment/packages/".concat(a),t)).data,async delete(a){await s.Ay.delete("/payment/packages/".concat(a))},toggleStatus:async(a,t)=>(await s.Ay.put("/payment/packages/".concat(a),{isActive:t})).data},r={getList:async a=>(await s.Ay.get("/payment/orders",{params:a})).data,getById:async a=>(await s.Ay.get("/payment/orders/".concat(a))).data,refund:async(a,t)=>(await s.Ay.post("/payment/orders/".concat(a,"/refund"),{reason:t})).data,async cancel(a,t){await s.Ay.post("/payment/cancel/".concat(a),{userId:t})}};var g=e(22918),p=e(11407),u=e(93156),w=e(82112),A=e(48526);c.y,i.Au,y.Dw,g.f,p.m9,u.f,w.r,A.L8},59959:(a,t,e)=>{e.d(t,{y:()=>c});var s=e(83899);let c={login:async a=>(await s.Ay.post("/auth/login",a)).data,logout:()=>{localStorage.removeItem("admin_token"),window.location.href="/login"},getToken:()=>localStorage.getItem("admin_token"),setToken:a=>{localStorage.setItem("admin_token",a)},isLoggedIn:()=>!!localStorage.getItem("admin_token")};c.login},82112:(a,t,e)=>{e.d(t,{r:()=>c});var s=e(83899);let c={getAll:async a=>(await s.Ay.get("/user-favorites",{params:a})).data,exportData:async a=>(await s.Ay.get("/user-favorites/export",{params:a,responseType:"blob"})).data};c.getAll},93156:(a,t,e)=>{e.d(t,{f:()=>c});var s=e(83899);let c={getAll:async a=>(await s.Ay.get("/user-stars",{params:a})).data,exportData:async a=>(await s.Ay.get("/user-stars/export",{params:a,responseType:"blob"})).data};c.getAll}}]);