"use strict";exports.id=513,exports.ids=[513],exports.modules={513:(e,t,l)=>{l.d(t,{A:()=>A});var n=l(43210),a=l.n(n),o=l(69662),i=l.n(o),s=l(57266),r=l(71802),c=l(40908),d=l(54908);let b={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},m=a().createContext({});var g=l(26851),p=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let u=e=>(0,g.A)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var O=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let y=(e,t)=>{let[l,a]=(0,n.useMemo)(()=>(function(e,t){let l=[],n=[],a=!1,o=0;return e.filter(e=>e).forEach(e=>{let{filled:i}=e,s=O(e,["filled"]);if(i){n.push(s),l.push(n),n=[],o=0;return}let r=t-o;(o+=e.span||1)>=t?(o>t?(a=!0,n.push(Object.assign(Object.assign({},s),{span:r}))):n.push(s),l.push(n),n=[],o=0):n.push(s)}),n.length>0&&l.push(n),[l=l.map(e=>{let l=e.reduce((e,t)=>e+(t.span||1),0);if(l<t){let n=e[e.length-1];n.span=t-(l-(n.span||1))}return e}),a]})(t,e),[t,e]);return l},f=e=>{let{itemPrefixCls:t,component:l,span:a,className:o,style:s,labelStyle:r,contentStyle:c,bordered:d,label:b,content:g,colon:p,type:u,styles:O}=e,{classNames:y}=n.useContext(m);if(d)return n.createElement(l,{className:i()({[`${t}-item-label`]:"label"===u,[`${t}-item-content`]:"content"===u,[`${null==y?void 0:y.label}`]:"label"===u,[`${null==y?void 0:y.content}`]:"content"===u},o),style:s,colSpan:a},null!=b&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==O?void 0:O.label)},b),null!=g&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==O?void 0:O.content)},g));return n.createElement(l,{className:i()(`${t}-item`,o),style:s,colSpan:a},n.createElement("div",{className:`${t}-item-container`},(b||0===b)&&n.createElement("span",{className:i()(`${t}-item-label`,null==y?void 0:y.label,{[`${t}-item-no-colon`]:!p}),style:Object.assign(Object.assign({},r),null==O?void 0:O.label)},b),(g||0===g)&&n.createElement("span",{className:i()(`${t}-item-content`,null==y?void 0:y.content),style:Object.assign(Object.assign({},c),null==O?void 0:O.content)},g)))};function j(e,{colon:t,prefixCls:l,bordered:a},{component:o,type:i,showLabel:s,showContent:r,labelStyle:c,contentStyle:d,styles:b}){return e.map(({label:e,children:m,prefixCls:g=l,className:p,style:u,labelStyle:O,contentStyle:y,span:j=1,key:$,styles:h},v)=>"string"==typeof o?n.createElement(f,{key:`${i}-${$||v}`,className:p,style:u,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},c),null==b?void 0:b.label),O),null==h?void 0:h.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},d),null==b?void 0:b.content),y),null==h?void 0:h.content)},span:j,colon:t,component:o,itemPrefixCls:g,bordered:a,label:s?e:null,content:r?m:null,type:i}):[n.createElement(f,{key:`label-${$||v}`,className:p,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},c),null==b?void 0:b.label),u),O),null==h?void 0:h.label),span:1,colon:t,component:o[0],itemPrefixCls:g,bordered:a,label:e,type:"label"}),n.createElement(f,{key:`content-${$||v}`,className:p,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},d),null==b?void 0:b.content),u),y),null==h?void 0:h.content),span:2*j-1,component:o[1],itemPrefixCls:g,bordered:a,content:m,type:"content"})])}let $=e=>{let t=n.useContext(m),{prefixCls:l,vertical:a,row:o,index:i,bordered:s}=e;return a?n.createElement(n.Fragment,null,n.createElement("tr",{key:`label-${i}`,className:`${l}-row`},j(o,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),n.createElement("tr",{key:`content-${i}`,className:`${l}-row`},j(o,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):n.createElement("tr",{key:i,className:`${l}-row`},j(o,e,Object.assign({component:s?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var h=l(42411),v=l(32476),x=l(13581),S=l(60254);let E=e=>{let{componentCls:t,labelBg:l}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,h.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,h.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,h.zA)(e.padding)} ${(0,h.zA)(e.paddingLG)}`,borderInlineEnd:`${(0,h.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:l,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,h.zA)(e.paddingSM)} ${(0,h.zA)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,h.zA)(e.paddingXS)} ${(0,h.zA)(e.padding)}`}}}}}},w=e=>{let{componentCls:t,extraColor:l,itemPaddingBottom:n,itemPaddingEnd:a,colonMarginRight:o,colonMarginLeft:i,titleMarginBottom:s}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,v.dF)(e)),E(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:s},[`${t}-title`]:Object.assign(Object.assign({},v.L9),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:n,paddingInlineEnd:a},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,h.zA)(i)} ${(0,h.zA)(o)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},N=(0,x.OF)("Descriptions",e=>w((0,S.oX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}));var z=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let C=e=>{let{prefixCls:t,title:l,extra:a,column:o,colon:g=!0,bordered:O,layout:f,children:j,className:h,rootClassName:v,style:x,size:S,labelStyle:E,contentStyle:w,styles:C,items:A,classNames:P}=e,k=z(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:B,direction:I,className:L,style:M,classNames:T,styles:W}=(0,r.TP)("descriptions"),G=B("descriptions",t),H=(0,d.A)(),R=n.useMemo(()=>{var e;return"number"==typeof o?o:null!=(e=(0,s.ko)(H,Object.assign(Object.assign({},b),o)))?e:3},[H,o]),X=function(e,t,l){let a=n.useMemo(()=>t||u(l),[t,l]);return n.useMemo(()=>a.map(t=>{var{span:l}=t,n=p(t,["span"]);return"filled"===l?Object.assign(Object.assign({},n),{filled:!0}):Object.assign(Object.assign({},n),{span:"number"==typeof l?l:(0,s.ko)(e,l)})}),[a,e])}(H,A,j),F=(0,c.A)(S),D=y(R,X),[q,J,K]=N(G),Q=n.useMemo(()=>({labelStyle:E,contentStyle:w,styles:{content:Object.assign(Object.assign({},W.content),null==C?void 0:C.content),label:Object.assign(Object.assign({},W.label),null==C?void 0:C.label)},classNames:{label:i()(T.label,null==P?void 0:P.label),content:i()(T.content,null==P?void 0:P.content)}}),[E,w,C,P,T,W]);return q(n.createElement(m.Provider,{value:Q},n.createElement("div",Object.assign({className:i()(G,L,T.root,null==P?void 0:P.root,{[`${G}-${F}`]:F&&"default"!==F,[`${G}-bordered`]:!!O,[`${G}-rtl`]:"rtl"===I},h,v,J,K),style:Object.assign(Object.assign(Object.assign(Object.assign({},M),W.root),null==C?void 0:C.root),x)},k),(l||a)&&n.createElement("div",{className:i()(`${G}-header`,T.header,null==P?void 0:P.header),style:Object.assign(Object.assign({},W.header),null==C?void 0:C.header)},l&&n.createElement("div",{className:i()(`${G}-title`,T.title,null==P?void 0:P.title),style:Object.assign(Object.assign({},W.title),null==C?void 0:C.title)},l),a&&n.createElement("div",{className:i()(`${G}-extra`,T.extra,null==P?void 0:P.extra),style:Object.assign(Object.assign({},W.extra),null==C?void 0:C.extra)},a)),n.createElement("div",{className:`${G}-view`},n.createElement("table",null,n.createElement("tbody",null,D.map((e,t)=>n.createElement($,{key:t,index:t,colon:g,prefixCls:G,vertical:"vertical"===f,bordered:O,row:e}))))))))};C.Item=({children:e})=>e;let A=C}};