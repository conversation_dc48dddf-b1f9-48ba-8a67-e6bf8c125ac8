'use client'; // 标记为客户端组件以使用 hooks

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation'; // 使用 next/navigation

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      // 如果有 token，可以进一步验证 token 有效性或直接跳转到 dashboard
      // 为简化，这里直接跳转
      router.replace('/dashboard'); // 假设 dashboard 路径是 /dashboard
    } else {
      router.replace('/login');
    }
  }, [router]);

  // 可以在这里放一个加载指示器
  return (
    <div>
      <p>正在加载...</p>
    </div>
  );
}