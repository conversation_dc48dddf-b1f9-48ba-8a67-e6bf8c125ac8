import React from 'react';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import '@/styles/globals.css'; // 导入全局样式

const RootLayout = ({ children }: React.PropsWithChildren) => (
  <html lang="en">
    <head>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="description" content="游戏后台管理系统" />
      <link rel="icon" href="/favicon.ico" />
      <title>游戏管理后台</title>
    </head>
    <body>
      <AntdRegistry>{children}</AntdRegistry>
    </body>
  </html>
);
export default RootLayout;