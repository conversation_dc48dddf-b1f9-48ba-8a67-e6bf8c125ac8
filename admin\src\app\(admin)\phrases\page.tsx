'use client';

import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, Tag, message, Popconfirm, Card } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { phraseService, Phrase, CreatePhraseParams, UpdatePhraseParams } from '@/services';

const { TextArea } = Input;

export default function PhrasesPage() {
  const [phrases, setPhrases] = useState<Phrase[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPhrase, setEditingPhrase] = useState<Phrase | null>(null);
  const [form] = Form.useForm();

  // 获取词组列表
  const fetchPhrases = async () => {
    setLoading(true);
    try {
      const data = await phraseService.getAll();
      setPhrases(data);
    } catch (error) {
      message.error('获取词组列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPhrases();
  }, []);

  // 处理创建/编辑词组
  const handleSubmit = async (values: any) => {
    try {
      const tagsArray = values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [];
      const params = {
        ...values,
        tags: tagsArray,
      };

      if (editingPhrase) {
        await phraseService.update(editingPhrase.id, params as UpdatePhraseParams);
        message.success('词组更新成功');
      } else {
        await phraseService.create(params as CreatePhraseParams);
        message.success('词组创建成功');
      }
      
      setModalVisible(false);
      setEditingPhrase(null);
      form.resetFields();
      fetchPhrases();
    } catch (error) {
      message.error(editingPhrase ? '更新词组失败' : '创建词组失败');
    }
  };

  // 处理删除词组
  const handleDelete = async (id: string) => {
    try {
      await phraseService.delete(id);
      message.success('词组删除成功');
      fetchPhrases();
    } catch (error) {
      message.error('删除词组失败');
    }
  };

  // 打开编辑模态框
  const handleEdit = (phrase: Phrase) => {
    setEditingPhrase(phrase);
    form.setFieldsValue({
      ...phrase,
      tags: phrase.tags?.join(', ') || '',
    });
    setModalVisible(true);
  };

  // 打开创建模态框
  const handleCreate = () => {
    setEditingPhrase(null);
    form.resetFields();
    setModalVisible(true);
  };

  const columns: ColumnsType<Phrase> = [
    {
      title: '词组',
      dataIndex: 'text',
      key: 'text',
      width: 150,
    },
    {
      title: '含义',
      dataIndex: 'meaning',
      key: 'meaning',
      width: 200,
    },
    {
      title: '示例',
      dataIndex: 'exampleSentence',
      key: 'exampleSentence',
      width: 250,
      render: (text) => text || '-',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <>
          {tags?.map((tag) => (
            <Tag key={tag} color="blue">
              {tag}
            </Tag>
          ))}
        </>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个词组吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2>词组管理</h2>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            创建词组
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={phrases}
          rowKey="id"
          loading={loading}
          pagination={{
            total: phrases.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingPhrase ? '编辑词组' : '创建词组'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingPhrase(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="text"
            label="词组"
            rules={[{ required: true, message: '请输入词组' }]}
          >
            <Input placeholder="请输入词组" />
          </Form.Item>

          <Form.Item
            name="meaning"
            label="含义"
            rules={[{ required: true, message: '请输入词组含义' }]}
          >
            <TextArea rows={3} placeholder="请输入词组含义" />
          </Form.Item>

          <Form.Item
            name="exampleSentence"
            label="示例"
          >
            <TextArea rows={3} placeholder="请输入使用示例（可选）" />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
            help="多个标签用逗号分隔"
          >
            <Input placeholder="例如：动物,植物,食物" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingPhrase ? '更新' : '创建'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingPhrase(null);
                form.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
