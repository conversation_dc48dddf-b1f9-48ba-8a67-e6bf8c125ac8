{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/levels/[id]/edit", "regex": "^/levels/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/levels/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/shares/[id]", "regex": "^/shares/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/shares/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/shares/[id]/edit", "regex": "^/shares/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/shares/(?<nxtPid>[^/]+?)/edit(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/activation-codes", "regex": "^/activation\\-codes(?:/)?$", "routeKeys": {}, "namedRegex": "^/activation\\-codes(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/level-tags", "regex": "^/level\\-tags(?:/)?$", "routeKeys": {}, "namedRegex": "^/level\\-tags(?:/)?$"}, {"page": "/levels", "regex": "^/levels(?:/)?$", "routeKeys": {}, "namedRegex": "^/levels(?:/)?$"}, {"page": "/levels/create", "regex": "^/levels/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/levels/create(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/payment-orders", "regex": "^/payment\\-orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/payment\\-orders(?:/)?$"}, {"page": "/phrases", "regex": "^/phrases(?:/)?$", "routeKeys": {}, "namedRegex": "^/phrases(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/shares", "regex": "^/shares(?:/)?$", "routeKeys": {}, "namedRegex": "^/shares(?:/)?$"}, {"page": "/thesauruses", "regex": "^/thesauruses(?:/)?$", "routeKeys": {}, "namedRegex": "^/thesauruses(?:/)?$"}, {"page": "/thesauruses/create", "regex": "^/thesauruses/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/thesauruses/create(?:/)?$"}, {"page": "/user-favorites", "regex": "^/user\\-favorites(?:/)?$", "routeKeys": {}, "namedRegex": "^/user\\-favorites(?:/)?$"}, {"page": "/user-stars", "regex": "^/user\\-stars(?:/)?$", "routeKeys": {}, "namedRegex": "^/user\\-stars(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/vip-packages", "regex": "^/vip\\-packages(?:/)?$", "routeKeys": {}, "namedRegex": "^/vip\\-packages(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}