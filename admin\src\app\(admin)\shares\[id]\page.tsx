'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Image,
  Typography,
  Spin,
  message,
  Row,
  Col,
  Divider,
  Alert,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  ShareAltOutlined,
  CheckCircleOutlined,
  StopOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { ShareService, SHARE_TYPE_OPTIONS } from '../../../../services/shareService';
import type { ShareConfig } from '../../../../types/share';

const { Title, Text, Paragraph } = Typography;

export default function ShareDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [share, setShare] = useState<ShareConfig | null>(null);
  const [loading, setLoading] = useState(true);

  const shareId = params.id as string;

  // 获取分享配置详情
  const fetchShareDetail = async () => {
    if (!shareId) return;
    
    setLoading(true);
    try {
      const data = await ShareService.getShareConfigById(shareId);
      setShare(data);
    } catch (error) {
      message.error('获取分享配置详情失败');
      console.error('获取分享配置详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchShareDetail();
  }, [shareId]);

  // 返回列表页
  const goBack = () => {
    router.push('/shares');
  };

  // 编辑分享配置
  const editShare = () => {
    router.push(`/shares/${shareId}/edit`);
  };

  // 切换启用状态
  const toggleActive = async () => {
    if (!share) return;
    
    try {
      await ShareService.toggleShareConfig(share.id);
      message.success(`${share.isActive ? '禁用' : '启用'}成功`);
      fetchShareDetail();
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'message' in error
        ? (error as { message: string }).message
        : '操作失败';
      message.error(errorMessage);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 获取类型标签颜色
  const getTypeTagColor = (type: string) => {
    const colors: Record<string, string> = {
      default: 'blue',
      result: 'green',
      level: 'orange',
      achievement: 'purple',
      custom: 'gray',
    };
    return colors[type] || 'gray';
  };

  // 获取类型标签文本
  const getTypeTagText = (type: string) => {
    const option = SHARE_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!share) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="分享配置不存在"
          description="请检查URL是否正确，或者该配置已被删除。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={goBack}>
              返回列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        {/* 页面头部 */}
        <div style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Button icon={<ArrowLeftOutlined />} onClick={goBack}>
                  返回
                </Button>
                <Title level={3} style={{ margin: 0 }}>
                  <ShareAltOutlined style={{ marginRight: '8px' }} />
                  分享配置详情
                </Title>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type={share.isActive ? 'default' : 'primary'}
                  icon={share.isActive ? <StopOutlined /> : <CheckCircleOutlined />}
                  onClick={toggleActive}
                >
                  {share.isActive ? '禁用' : '启用'}
                </Button>
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={editShare}
                >
                  编辑
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 基本信息 */}
        <Descriptions
          title="基本信息"
          bordered
          column={2}
          size="middle"
        >
          <Descriptions.Item label="配置ID" span={2}>
            <Space>
              <Text code>{share.id}</Text>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(share.id)}
              />
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item label="配置名称">
            {share.name}
          </Descriptions.Item>
          
          <Descriptions.Item label="分享类型">
            <Tag color={getTypeTagColor(share.type)}>
              {getTypeTagText(share.type)}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label="启用状态">
            <Tag 
              color={share.isActive ? 'success' : 'default'} 
              icon={share.isActive ? <CheckCircleOutlined /> : <StopOutlined />}
            >
              {share.isActive ? '启用' : '禁用'}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label="排序权重">
            {share.sortOrder}
          </Descriptions.Item>
          
          <Descriptions.Item label="创建时间">
            {new Date(share.createdAt).toLocaleString()}
          </Descriptions.Item>
          
          <Descriptions.Item label="更新时间">
            {new Date(share.updatedAt).toLocaleString()}
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        {/* 分享内容 */}
        <Title level={4}>分享内容</Title>
        
        <Descriptions bordered column={1} size="middle">
          <Descriptions.Item label="分享标题">
            <Space>
              <Text strong>{share.title}</Text>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(share.title)}
              />
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item label="分享路径">
            <Space>
              <Text code>{share.path}</Text>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(share.path)}
              />
            </Space>
          </Descriptions.Item>
          
          {share.description && (
            <Descriptions.Item label="分享描述">
              <Paragraph>{share.description}</Paragraph>
            </Descriptions.Item>
          )}
          
          {share.imageUrl && (
            <Descriptions.Item label="分享图片">
              <Space direction="vertical">
                <Space>
                  <Text code>{share.imageUrl}</Text>
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => copyToClipboard(share.imageUrl!)}
                  />
                </Space>
                <Image
                  src={share.imageUrl}
                  alt="分享图片"
                  style={{ maxWidth: '300px', maxHeight: '200px' }}
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
              </Space>
            </Descriptions.Item>
          )}
        </Descriptions>

        {/* 使用说明 */}
        <Divider />
        
        <Title level={4}>使用说明</Title>
        
        <Alert
          message="微信小程序分享配置"
          description={
            <div>
              <p>该配置可用于微信小程序的分享功能，小程序端可通过以下API获取：</p>
              <ul>
                <li><Text code>GET /api/v1/weixin/share-config</Text> - 获取所有分享配置</li>
                <li><Text code>GET /api/v1/weixin/share-config/{share.type}</Text> - 获取指定类型的分享配置</li>
              </ul>
              <p>在小程序中使用时，可以在页面的 <Text code>onShareAppMessage</Text> 方法中返回这些配置。</p>
            </div>
          }
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
}
