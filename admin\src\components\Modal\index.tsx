import React, { useState, useEffect, ReactNode } from 'react';
import { createRoot } from 'react-dom/client';
import './modal.css';

export interface ModalProps {
  title?: ReactNode;
  content?: ReactNode;
  children?: ReactNode;
  visible?: boolean;
  width?: number | string;
  centered?: boolean;
  closable?: boolean;
  maskClosable?: boolean;
  footer?: ReactNode;
  okText?: string;
  cancelText?: string;
  okType?: 'primary' | 'danger';
  confirmLoading?: boolean;
  onOk?: () => void | Promise<void>;
  onCancel?: () => void;
  afterClose?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

// Modal 组件
const Modal: React.FC<ModalProps> = ({
  title,
  content,
  children,
  visible = false,
  width = 520,
  centered = false,
  closable = true,
  maskClosable = true,
  footer,
  okText = '确定',
  cancelText = '取消',
  okType = 'primary',
  confirmLoading = false,
  onOk,
  onCancel,
  afterClose,
  className = '',
  style = {},
}) => {
  const [isVisible, setIsVisible] = useState(visible);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (visible) {
      setIsVisible(true);
      setIsAnimating(true);
      document.body.style.overflow = 'hidden';
    } else {
      setIsAnimating(false);
      setTimeout(() => {
        setIsVisible(false);
        document.body.style.overflow = '';
        afterClose?.();
      }, 300);
    }
  }, [visible, afterClose]);

  const handleMaskClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && maskClosable) {
      onCancel?.();
    }
  };

  const handleOk = async () => {
    if (onOk) {
      try {
        await onOk();
      } catch (error) {
        console.error('Modal onOk error:', error);
      }
    }
  };

  const handleCancel = () => {
    onCancel?.();
  };

  const renderFooter = () => {
    if (footer === null) return null;

    if (footer) return footer;

    return (
      <div className="custom-modal-footer">
        {cancelText && (
          <button
            className="custom-modal-btn custom-modal-btn-default"
            onClick={handleCancel}
          >
            {cancelText}
          </button>
        )}
        <button
          className={`custom-modal-btn custom-modal-btn-${okType}`}
          onClick={handleOk}
          disabled={confirmLoading}
        >
          {confirmLoading && <span className="custom-modal-loading">⟳</span>}
          {okText}
        </button>
      </div>
    );
  };

  if (!isVisible) return null;

  return (
    <div
      className={`custom-modal-mask ${isAnimating ? 'custom-modal-mask-show' : 'custom-modal-mask-hide'}`}
      onClick={handleMaskClick}
    >
      <div className={`custom-modal-wrap ${centered ? 'custom-modal-centered' : ''}`}>
        <div
          className={`custom-modal ${className} ${isAnimating ? 'custom-modal-show' : 'custom-modal-hide'}`}
          style={{ width, ...style }}
        >
          {(title || closable) && (
            <div className="custom-modal-header">
              {title && <div className="custom-modal-title">{title}</div>}
              {closable && (
                <button
                  className="custom-modal-close"
                  onClick={handleCancel}
                  aria-label="Close"
                >
                  ×
                </button>
              )}
            </div>
          )}
          
          <div className="custom-modal-body">
            {content || children}
          </div>
          
          {renderFooter()}
        </div>
      </div>
    </div>
  );
};

// 确认对话框配置
export interface ConfirmConfig {
  title?: ReactNode;
  content?: ReactNode;
  okText?: string;
  cancelText?: string;
  okType?: 'primary' | 'danger';
  onOk?: () => void | Promise<void>;
  onCancel?: () => void;
  width?: number | string;
  centered?: boolean;
  maskClosable?: boolean;
}

// 确认对话框管理器
class ConfirmManager {
  private container: HTMLDivElement | null = null;
  private root: any = null;

  private getContainer() {
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.className = 'custom-modal-container';
      document.body.appendChild(this.container);
      this.root = createRoot(this.container);
    }
    return this.container;
  }

  confirm(config: ConfirmConfig) {
    return new Promise<void>((resolve, reject) => {
      let isResolved = false;

      const handleOk = async () => {
        if (isResolved) return;
        
        try {
          if (config.onOk) {
            await config.onOk();
          }
          isResolved = true;
          this.destroy();
          resolve();
        } catch (error) {
          reject(error);
        }
      };

      const handleCancel = () => {
        if (isResolved) return;
        
        isResolved = true;
        config.onCancel?.();
        this.destroy();
        reject(new Error('User cancelled'));
      };

      this.getContainer();
      this.root.render(
        <Modal
          visible={true}
          title={config.title}
          content={config.content}
          okText={config.okText}
          cancelText={config.cancelText}
          okType={config.okType}
          width={config.width}
          centered={config.centered}
          maskClosable={config.maskClosable}
          onOk={handleOk}
          onCancel={handleCancel}
          afterClose={() => this.destroy()}
        />
      );
    });
  }

  destroy() {
    if (this.container && document.body.contains(this.container)) {
      document.body.removeChild(this.container);
      this.container = null;
      this.root = null;
    }
  }
}

// 创建带有静态方法的Modal组件
const ModalWithStatic = Modal as typeof Modal & {
  confirm: (config: ConfirmConfig) => Promise<void>;
  info: (config: ConfirmConfig) => Promise<void>;
  success: (config: ConfirmConfig) => Promise<void>;
  error: (config: ConfirmConfig) => Promise<void>;
  warning: (config: ConfirmConfig) => Promise<void>;
};

// 静态方法
ModalWithStatic.confirm = (config: ConfirmConfig) => {
  const manager = new ConfirmManager();
  return manager.confirm({
    ...config,
    okType: config.okType || 'primary'
  });
};

ModalWithStatic.info = (config: ConfirmConfig) => {
  const manager = new ConfirmManager();
  return manager.confirm({
    ...config,
    okType: 'primary',
    cancelText: undefined, // info模式通常只有确定按钮
  });
};

ModalWithStatic.success = (config: ConfirmConfig) => {
  const manager = new ConfirmManager();
  return manager.confirm({
    ...config,
    okType: 'primary',
    cancelText: undefined,
  });
};

ModalWithStatic.error = (config: ConfirmConfig) => {
  const manager = new ConfirmManager();
  return manager.confirm({
    ...config,
    okType: 'danger',
    cancelText: undefined,
  });
};

ModalWithStatic.warning = (config: ConfirmConfig) => {
  const manager = new ConfirmManager();
  return manager.confirm({
    ...config,
    okType: 'primary',
    cancelText: undefined,
  });
};

export default ModalWithStatic;
