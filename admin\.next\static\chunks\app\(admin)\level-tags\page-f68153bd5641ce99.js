(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8196],{11407:(e,t,s)=>{"use strict";s.d(t,{m9:()=>o});var a=s(83899);let n=e=>({id:e.id||"",name:e.name||"",description:e.description||"",color:e.color||"#1890ff",isVip:!!e.isVip,status:e.status||"active",icon:e.icon,createdAt:e.createdAt||new Date().toISOString(),updatedAt:e.updatedAt||new Date().toISOString()}),o={getAll:async()=>((await a.Ay.get("/tags")).data||[]).map(n),getById:async e=>n((await a.Ay.get("/tags/".concat(e))).data),create:async e=>n((await a.Ay.post("/tags",e)).data),update:async(e,t)=>n((await a.Ay.put("/tags/".concat(e),t)).data),delete:async e=>{await a.Ay.delete("/tags/".concat(e))}};o.getAll,o.create,o.update,o.delete},36449:()=>{},41008:(e,t,s)=>{"use strict";s.d(t,{i:()=>a});let a={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},52702:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(95155),n=s(12115),o=s(20778),i=s(86615),r=s(19868),c=s(12320),l=s(37974),d=s(13324),u=s(77325),m=s(27212),h=s(19361),p=s(74947),y=s(6124),g=s(44297),x=s(51087),A=s(46002),v=s(56020),j=s(46143),w=s(79659),f=s(56170),k=s(40670),b=s(46996),T=s(11407);let{Option:C}=o.A;function I(){let[e,t]=(0,n.useState)([]),[s,I]=(0,n.useState)(!1),[S,N]=(0,n.useState)(!1),[U,E]=(0,n.useState)(null),[L]=i.A.useForm(),R=async()=>{I(!0);try{let e=await T.m9.getAll();t(e)}catch(e){r.Ay.error("获取标签列表失败"),console.error("Error fetching tags:",e),t([{id:"1",name:"基础词汇",description:"适合初学者的基础英语词汇",color:"#1890ff",isVip:!1,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"2",name:"商务英语",description:"商务场景常用词汇和表达",color:"#52c41a",isVip:!0,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"3",name:"日常对话",description:"日常生活中的常用对话",color:"#faad14",isVip:!1,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}])}finally{I(!1)}};(0,n.useEffect)(()=>{R()},[]);let _=async e=>{try{var t,s;let a={...e,color:"string"==typeof e.color?e.color:null==(s=e.color)||null==(t=s.toHexString)?void 0:t.call(s)};U?(await T.m9.update(U.id,a),r.Ay.success("标签更新成功")):(await T.m9.create(a),r.Ay.success("标签创建成功")),N(!1),E(null),L.resetFields(),R()}catch(e){r.Ay.error(U?"标签更新失败":"标签创建失败")}},P=async e=>{try{await T.m9.delete(e),r.Ay.success("标签删除成功"),R()}catch(e){r.Ay.error("标签删除失败")}},V=async e=>{try{let t="active"===e.status?"inactive":"active";await T.m9.update(e.id,{status:t}),r.Ay.success("标签状态更新成功"),R()}catch(e){r.Ay.error("标签状态更新失败")}},F=[{title:"标签名称",dataIndex:"name",key:"name",render:(e,t)=>(0,a.jsxs)(c.A,{children:[(0,a.jsx)(l.A,{color:t.color,style:{marginRight:8},children:e}),t.isVip&&(0,a.jsx)(l.A,{color:"gold",children:"VIP"})]})},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"状态",dataIndex:"status",key:"status",render:(e,t)=>(0,a.jsx)(d.A,{checked:"active"===e,onChange:()=>V(t),checkedChildren:"启用",unCheckedChildren:"禁用"})},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleDateString()},{title:"操作",key:"action",render:(e,t)=>(0,a.jsxs)(c.A,{children:[(0,a.jsx)(u.Ay,{type:"link",size:"small",icon:(0,a.jsx)(w.A,{}),onClick:()=>{E(t),L.setFieldsValue(t),N(!0)},children:"编辑"}),(0,a.jsx)(m.A,{title:"确定要删除这个标签吗？",onConfirm:()=>P(t.id),okText:"确定",cancelText:"取消",children:(0,a.jsx)(u.Ay,{type:"link",danger:!0,size:"small",icon:(0,a.jsx)(f.A,{}),children:"删除"})})]})}],M=e.length,O=e.filter(e=>"active"===e.status).length,B=e.filter(e=>e.isVip).length;return(0,a.jsxs)("div",{children:[(0,a.jsxs)(h.A,{gutter:16,style:{marginBottom:16},children:[(0,a.jsx)(p.A,{span:8,children:(0,a.jsx)(y.A,{children:(0,a.jsx)(g.A,{title:"总标签数",value:M,prefix:(0,a.jsx)(k.A,{})})})}),(0,a.jsx)(p.A,{span:8,children:(0,a.jsx)(y.A,{children:(0,a.jsx)(g.A,{title:"启用标签",value:O,valueStyle:{color:"#52c41a"}})})}),(0,a.jsx)(p.A,{span:8,children:(0,a.jsx)(y.A,{children:(0,a.jsx)(g.A,{title:"VIP标签",value:B,valueStyle:{color:"#faad14"}})})})]}),(0,a.jsxs)(y.A,{children:[(0,a.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsx)("h2",{children:"标签管理"}),(0,a.jsx)(u.Ay,{type:"primary",icon:(0,a.jsx)(b.A,{}),onClick:()=>{E(null),L.resetFields(),N(!0)},children:"创建标签"})]}),(0,a.jsx)(x.A,{columns:F,dataSource:e,rowKey:"id",loading:s,pagination:{showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")}})]}),(0,a.jsx)(A.A,{title:U?"编辑标签":"创建标签",open:S,onCancel:()=>{N(!1),E(null),L.resetFields()},footer:null,width:600,children:(0,a.jsxs)(i.A,{form:L,layout:"vertical",onFinish:_,children:[(0,a.jsx)(i.A.Item,{name:"name",label:"标签名称",rules:[{required:!0,message:"请输入标签名称"}],children:(0,a.jsx)(v.A,{placeholder:"标签名称"})}),(0,a.jsx)(i.A.Item,{name:"description",label:"描述",rules:[{required:!0,message:"请输入标签描述"}],children:(0,a.jsx)(v.A.TextArea,{placeholder:"标签描述",rows:3})}),(0,a.jsx)(i.A.Item,{name:"color",label:"标签颜色",rules:[{required:!0,message:"请选择标签颜色"}],children:(0,a.jsx)(j.A,{showText:!0})}),(0,a.jsx)(i.A.Item,{name:"isVip",label:"VIP标签",valuePropName:"checked",children:(0,a.jsx)(d.A,{checkedChildren:"是",unCheckedChildren:"否"})}),(0,a.jsx)(i.A.Item,{name:"status",label:"启用状态",initialValue:"active",children:(0,a.jsxs)(o.A,{children:[(0,a.jsx)(C,{value:"active",children:"启用"}),(0,a.jsx)(C,{value:"inactive",children:"禁用"})]})}),(0,a.jsx)(i.A.Item,{children:(0,a.jsxs)(c.A,{children:[(0,a.jsx)(u.Ay,{type:"primary",htmlType:"submit",children:U?"更新":"创建"}),(0,a.jsx)(u.Ay,{onClick:()=>{N(!1),E(null),L.resetFields()},children:"取消"})]})})]})})]})}},71047:(e,t,s)=>{Promise.resolve().then(s.bind(s,52702))},73629:()=>{},83899:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,FH:()=>r,KY:()=>c});var a=s(23464),n=s(90285),o=s(41008);let i=a.A.create({baseURL:o.i.FULL_BASE_URL,timeout:o.i.TIMEOUT,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{var t;let s=localStorage.getItem("admin_token");return s&&(e.headers.Authorization="Bearer ".concat(s)),console.log("\uD83D\uDE80 发送请求:",{method:null==(t=e.method)?void 0:t.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),i.interceptors.response.use(e=>{var t;let s=e.config;return console.log("✅ 请求成功:",{method:null==(t=s.method)?void 0:t.toUpperCase(),url:s.url,status:e.status,statusText:e.statusText,data:e.data}),s.showSuccess&&s.successMessage&&n.i.success(s.successMessage),e},e=>{var t,s,a,o,i,r,c,l,d;console.error("❌ 请求失败:",{method:null==(s=e.config)||null==(t=s.method)?void 0:t.toUpperCase(),url:null==(a=e.config)?void 0:a.url,baseURL:null==(o=e.config)?void 0:o.baseURL,fullURL:"".concat(null==(i=e.config)?void 0:i.baseURL).concat(null==(r=e.config)?void 0:r.url),status:null==(c=e.response)?void 0:c.status,statusText:null==(l=e.response)?void 0:l.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:t,data:s}=e.response,a="";switch(t){case 401:a="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a=(null==s?void 0:s.message)||"没有权限访问该资源";break;case 404:a=(null==s?void 0:s.message)||"请求的资源不存在";break;case 422:a=(null==s?void 0:s.message)||"请求参数验证失败";break;case 500:a=(null==s?void 0:s.message)||"服务器内部错误";break;default:a=(null==s?void 0:s.message)||"请求失败 (".concat(t,")")}n.i.error(a)}else e.request?n.i.error("网络连接失败，请检查网络"):n.i.error("请求配置错误");return Promise.reject(e)});let r={get:(e,t)=>i.get(e,t),post:(e,t,s)=>i.post(e,t,s),put:(e,t,s)=>i.put(e,t,s),patch:(e,t,s)=>i.patch(e,t,s),delete:(e,t)=>i.delete(e,t)},c={post:(e,t,s,a)=>r.post(e,t,{...a,showSuccess:!0,successMessage:s||"操作成功"}),put:(e,t,s,a)=>r.put(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),patch:(e,t,s,a)=>r.patch(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),delete:(e,t,s)=>r.delete(e,{...s,showSuccess:!0,successMessage:t||"删除成功"})},l=r},90285:(e,t,s)=>{"use strict";s.d(t,{a:()=>c,i:()=>m});var a=s(95155),n=s(12115),o=s(12669);s(36449);let i=e=>{let{title:t,content:s,children:o,visible:i=!1,width:r=520,centered:c=!1,closable:l=!0,maskClosable:d=!0,footer:u,okText:m="确定",cancelText:h="取消",okType:p="primary",confirmLoading:y=!1,onOk:g,onCancel:x,afterClose:A,className:v="",style:j={}}=e,[w,f]=(0,n.useState)(i),[k,b]=(0,n.useState)(!1);(0,n.useEffect)(()=>{i?(f(!0),b(!0),document.body.style.overflow="hidden"):(b(!1),setTimeout(()=>{f(!1),document.body.style.overflow="",null==A||A()},300))},[i,A]);let T=async()=>{if(g)try{await g()}catch(e){console.error("Modal onOk error:",e)}},C=()=>{null==x||x()};return w?(0,a.jsx)("div",{className:"custom-modal-mask ".concat(k?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==x||x())},children:(0,a.jsx)("div",{className:"custom-modal-wrap ".concat(c?"custom-modal-centered":""),children:(0,a.jsxs)("div",{className:"custom-modal ".concat(v," ").concat(k?"custom-modal-show":"custom-modal-hide"),style:{width:r,...j},children:[(t||l)&&(0,a.jsxs)("div",{className:"custom-modal-header",children:[t&&(0,a.jsx)("div",{className:"custom-modal-title",children:t}),l&&(0,a.jsx)("button",{className:"custom-modal-close",onClick:C,"aria-label":"Close",children:"\xd7"})]}),(0,a.jsx)("div",{className:"custom-modal-body",children:s||o}),null===u?null:u||(0,a.jsxs)("div",{className:"custom-modal-footer",children:[h&&(0,a.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:C,children:h}),(0,a.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(p),onClick:T,disabled:y,children:[y&&(0,a.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),m]})]})]})})}):null};class r{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,o.createRoot)(this.container)),this.container}confirm(e){return new Promise((t,s)=>{let n=!1,o=async()=>{if(!n)try{e.onOk&&await e.onOk(),n=!0,this.destroy(),t()}catch(e){s(e)}};this.getContainer(),this.root.render((0,a.jsx)(i,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:o,onCancel:()=>{var t;n||(n=!0,null==(t=e.onCancel)||t.call(e),this.destroy(),s(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}i.confirm=e=>new r().confirm({...e,okType:e.okType||"primary"}),i.info=e=>new r().confirm({...e,okType:"primary",cancelText:void 0}),i.success=e=>new r().confirm({...e,okType:"primary",cancelText:void 0}),i.error=e=>new r().confirm({...e,okType:"danger",cancelText:void 0}),i.warning=e=>new r().confirm({...e,okType:"primary",cancelText:void 0});let c=i;s(73629);let l=e=>{let{messages:t}=e;return(0,a.jsx)("div",{className:"custom-message-container",children:t.map(e=>(0,a.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,a.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,a.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,o.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,a.jsx)(l,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var t;let s=e.key||this.generateId(),a=null!=(t=e.duration)?t:3e3;e.key&&(this.messages=this.messages.filter(t=>t.id!==e.key));let n={...e,id:s,visible:!0};return this.messages.push(n),this.getContainer(),this.render(),a>0&&setTimeout(()=>{this.hide(s)},a),s}hide(e){let t=this.messages.findIndex(t=>t.id===e);t>-1&&(this.messages[t].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(t=>t.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,m={success:(e,t)=>u.show({content:e,type:"success",duration:t}),error:(e,t)=>u.show({content:e,type:"error",duration:t}),warning:(e,t)=>u.show({content:e,type:"warning",duration:t}),info:(e,t)=>u.show({content:e,type:"info",duration:t}),destroy:()=>u.destroy()}}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,2343,1087,6615,404,6002,642,7238,5634,2024,8441,1684,7358],()=>t(71047)),_N_E=e.O()}]);