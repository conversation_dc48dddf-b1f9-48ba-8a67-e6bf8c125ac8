'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Form, Input, Button, Select, message, Card, Typography, Space, Divider, Alert, Tag, Table, Modal, Spin } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { levelService, levelTagService, UpdateLevelParams, Level } from '@/services';
import { LevelTag } from '@/services/levelTagService';

import { API_CONFIG } from '@/config/api';

const { Title } = Typography;
const { TextArea } = Input;

interface PhraseItem {
  id: string;
  text: string;
  meaning: string;
}

export default function EditLevelPage() {
  const [form] = Form.useForm();
  const router = useRouter();
  const params = useParams();

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [phrases, setPhrases] = useState<PhraseItem[]>([]);
  const [tags, setTags] = useState<LevelTag[]>([]);
  const [levelStats, setLevelStats] = useState<{ total: number; maxLevels: number; remaining: number } | null>(null);
  const [currentLevel, setCurrentLevel] = useState<Level | null>(null);

  // 添加词组的表单状态
  const [phraseForm] = Form.useForm();
  const [showAddPhrase, setShowAddPhrase] = useState(false);

  const levelId = params.id as string;


  // 添加词组到关卡
  const handleAddPhrase = (values: { text: string; meaning: string }) => {
    // 检查是否已存在相同的英文词组
    const isDuplicate = phrases.some(phrase =>
      phrase.text.toLowerCase().trim() === values.text.toLowerCase().trim()
    );

    if (isDuplicate) {
      message.error('该英文词组已存在，请勿重复添加');
      return;
    }

    const newPhrase: PhraseItem = {
      id: `temp_${Date.now()}`, // 临时ID
      text: values.text.trim(),
      meaning: values.meaning.trim(),
    };

    setPhrases(prev => [...prev, newPhrase]);
    phraseForm.resetFields();
    setShowAddPhrase(false);
    message.success('词组添加成功');
  };

  // 删除词组
  const handleDeletePhrase = (id: string) => {
    setPhrases(prev => prev.filter(phrase => phrase.id !== id));
    message.success('词组删除成功');
  };

  // 获取关卡详情
  const fetchLevelDetail = async () => {
    if (!levelId) return;

    setInitialLoading(true);
    try {
      const levelWithPhrases = await levelService.getWithPhrases(levelId);
      setCurrentLevel(levelWithPhrases);

      // 回显表单数据
      form.setFieldsValue({
        name: levelWithPhrases.name,
        difficulty: levelWithPhrases.difficulty,
        description: levelWithPhrases.description,
        tagIds: levelWithPhrases.tagIds || [],
      });

      // 回显词组数据
      if (levelWithPhrases.phrases && levelWithPhrases.phrases.length > 0) {
        const phrasesData = levelWithPhrases.phrases.map((phrase: any) => ({
          id: phrase.id,
          text: phrase.text,
          meaning: phrase.meaning,
        }));
        setPhrases(phrasesData);
      }
    } catch (error) {
      message.error('获取关卡详情失败');
      console.error('获取关卡详情失败:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  // 获取标签列表
  const fetchTags = async () => {
    try {
      const data = await levelTagService.getAll();
      setTags(data.filter(tag => tag.status === 'active')); // 只显示激活的标签
    } catch (error) {
      message.error('获取标签列表失败');
    }
  };

  // 获取关卡统计
  const fetchLevelStats = async () => {
    try {
      const stats = await levelService.getCount();
      setLevelStats(stats);
    } catch (error) {
      message.error('获取关卡统计失败');
    }
  };

  useEffect(() => {
    fetchLevelDetail();
    fetchTags();
    fetchLevelStats();
  }, [levelId]);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!levelId) {
      message.error('关卡ID不存在');
      return;
    }

    // 验证添加了词组
    if (phrases.length === 0) {
      message.error('请添加词组');
      return;
    }

    setLoading(true);
    try {
      // 获取现有词组ID（排除临时ID）
      const existingPhraseIds = phrases
        .filter(phrase => !phrase.id.startsWith('temp_'))
        .map(phrase => phrase.id);

      // 获取新词组（临时ID）
      const newPhrases = phrases.filter(phrase => phrase.id.startsWith('temp_'));

      console.log('现有词组ID:', existingPhraseIds);
      console.log('新词组:', newPhrases);

      // 准备新词组数据（直接传递给后端）
      const newPhrasesData = newPhrases.map(phrase => ({
        text: phrase.text,
        meaning: phrase.meaning,
      }));

      const updateParams: UpdateLevelParams = {
        name: values.name,
        difficulty: values.difficulty,
        description: values.description,
        tagIds: values.tagIds || [],
        phraseIds: existingPhraseIds, // 传递现有词组ID
        phrases: newPhrasesData.length > 0 ? newPhrasesData : undefined, // 传递新词组数据
        thesaurusIds: currentLevel?.thesaurusIds || [], // 保持现有的词库ID
      };

      console.log('更新参数:', updateParams);
      console.log('请求URL:', `${API_CONFIG.FULL_BASE_URL}/levels/${levelId}`);
      console.log('请求方法: PATCH');

      const result = await levelService.update(levelId, updateParams);
      console.log('更新结果:', result);

      // 成功提示
      if (newPhrases.length > 0) {
        message.success(`关卡更新成功！已创建 ${newPhrases.length} 个新词组并添加到关卡中`);
      } else {
        message.success('关卡更新成功');
      }

      router.push('/levels');
    } catch (error: any) {
      console.error('更新关卡失败:', error);
      const errorMessage = error.response?.data?.message || error.message || '更新关卡失败';
      message.error(`更新关卡失败: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };



  if (initialLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载关卡详情...</div>
      </div>
    );
  }

  return (
    <div>
      {currentLevel && (
        <Alert
          message={`正在编辑关卡：${currentLevel.name} (难度: ${currentLevel.difficulty})`}
          type="info"
          style={{ marginBottom: 16 }}
          showIcon
        />
      )}

      <Card>
        <Title level={2}>编辑关卡</Title>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ difficulty: 3 }}
        >
          <Form.Item
            name="name"
            label="关卡名称"
            rules={[{ required: true, message: '请输入关卡名称' }]}
          >
            <Input placeholder="请输入关卡名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="关卡描述"
          >
            <TextArea rows={3} placeholder="请输入关卡描述（可选）" />
          </Form.Item>

          <Divider>添加关卡词组</Divider>

          <Alert
            message="提示：请添加要包含在关卡中的词组。"
            type="info"
            style={{ marginBottom: 16 }}
          />

          <div style={{ marginBottom: 16 }}>
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={() => setShowAddPhrase(true)}
              style={{ width: '100%' }}
            >
              添加词组
            </Button>
          </div>

          <div style={{ marginBottom: 16 }}>
            <Table
              dataSource={phrases}
              rowKey="id"
              pagination={false}
              size="small"
              columns={[
                {
                  title: '英文',
                  dataIndex: 'text',
                  key: 'text',
                  render: (text) => text || '未设置',
                },
                {
                  title: '中文',
                  dataIndex: 'meaning',
                  key: 'meaning',
                  render: (meaning) => meaning || '未设置',
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 80,
                  render: (_, record) => (
                    <Button
                      type="link"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeletePhrase(record.id)}
                    />
                  ),
                },
              ]}
              locale={{ emptyText: '暂无词组，请点击上方按钮添加' }}
            />
          </div>

          <Divider>关卡标签</Divider>

          <Form.Item
            name="tagIds"
            label="选择标签"
            help="为关卡添加标签，便于分类和筛选"
          >
            <Select
              mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择关卡标签"
              filterOption={(input, option) =>
                (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
              }
              optionRender={(option) => (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Tag color={tags.find(tag => tag.id === option.value)?.color}>
                    {option.label}
                  </Tag>
                  {tags.find(tag => tag.id === option.value)?.isVip && (
                    <Tag color="gold" style={{ fontSize: '10px', padding: '0 4px' }}>VIP</Tag>
                  )}
                </div>
              )}
              options={tags.map(tag => ({
                label: tag.name,
                value: tag.id,
              }))}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                更新关卡
              </Button>
              <Button onClick={() => router.back()}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 添加词组的模态框 */}
      <Modal
        title="添加词组"
        open={showAddPhrase}
        onCancel={() => setShowAddPhrase(false)}
        footer={null}
        width={500}
      >
        <Form
          form={phraseForm}
          layout="vertical"
          onFinish={handleAddPhrase}
        >
          <Form.Item
            name="text"
            label="英文"
            rules={[
              { required: true, message: '请输入英文' },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  const isDuplicate = phrases.some(phrase =>
                    phrase.text.toLowerCase().trim() === value.toLowerCase().trim()
                  );
                  if (isDuplicate) {
                    return Promise.reject(new Error('该英文词组已存在'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="请输入英文词组" />
          </Form.Item>
          <Form.Item
            name="meaning"
            label="中文"
            rules={[{ required: true, message: '请输入中文' }]}
          >
            <Input placeholder="请输入中文意思" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                添加词组
              </Button>
              <Button onClick={() => setShowAddPhrase(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
