(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4572],{5983:(e,l,i)=>{Promise.resolve().then(i.bind(i,45742))},45742:(e,l,i)=>{"use strict";i.r(l),i.d(l,{default:()=>q});var s=i(95155),t=i(12115),n=i(20778),r=i(24971),a=i(86615),c=i(12320),d=i(28562),o=i(37974),h=i(77325),x=i(26922),j=i(27212),p=i(19361),A=i(74947),u=i(6124),m=i(44297),y=i(56020),v=i(51087),g=i(10642),f=i(13324),k=i(85189),I=i(35125),S=i(89631),V=i(85),w=i(59474),z=i(66786),C=i(90285),b=i(50274),P=i(97550),D=i(52092),U=i(81094),L=i(18517),F=i(79659),T=i(34140),R=i(56170),O=i(34095),B=i(46996),Y=i(88870),_=i(83899),E=i(49179);let{Option:M}=n.A,{RangePicker:N}=r.A;function q(){let[e,l]=(0,t.useState)([]),[i,r]=(0,t.useState)(!1),[q,G]=(0,t.useState)(!1),[K,W]=(0,t.useState)(!1),[J,$]=(0,t.useState)(null),[H,Q]=(0,t.useState)(null),[X]=a.A.useForm(),[Z,ee]=(0,t.useState)([]),[el,ei]=(0,t.useState)(""),[es,et]=(0,t.useState)(void 0),[en,er]=(0,t.useState)(null),[ea,ec]=(0,t.useState)(!1),[ed,eo]=(0,t.useState)(null),[eh,ex]=(0,t.useState)(!1),[ej,ep]=(0,t.useState)([]),[eA,eu]=(0,t.useState)({current:1,pageSize:20,total:0}),[em,ey]=(0,t.useState)({totalUsers:0,vipUsers:0,normalUsers:0,vipRate:0,totalRevenue:0,avgDailyUnlocks:0}),[ev,eg]=(0,t.useState)(!1),[ef,ek]=(0,t.useState)(null),[eI,eS]=(0,t.useState)([]),[eV]=a.A.useForm(),ew=async()=>{try{let e=await E.HK.getList({isActive:!0})||[];eS(Array.isArray(e)?e:[])}catch(e){console.error("获取VIP套餐失败:",e),eS([])}},ez=async e=>{r(!0);try{let i={...(null==e?void 0:e.search)&&{search:e.search},...(null==e?void 0:e.isVip)!==void 0&&{isVip:e.isVip.toString()},...(null==e?void 0:e.startDate)&&{startDate:e.startDate},...(null==e?void 0:e.endDate)&&{endDate:e.endDate},page:((null==e?void 0:e.page)||eA.current).toString(),pageSize:((null==e?void 0:e.pageSize)||eA.pageSize).toString()},s=(await _.Ay.get("/users",{params:i})).data;l(s.users||[]),eu(l=>({...l,total:s.total||0,current:(null==e?void 0:e.page)||l.current}));let t=eC(s.users||[]);ey(t)}catch(e){C.i.error("获取用户列表失败"),console.error("Error fetching users:",e)}finally{r(!1)}},eC=e=>{let l=e.length,i=e.filter(e=>e.isVip).length,s=l>0?e.reduce((e,l)=>e+l.dailyUnlockCount,0)/l:0;return{totalUsers:l,vipUsers:i,normalUsers:l-i,vipRate:l>0?i/l*100:0,totalRevenue:0,avgDailyUnlocks:s}};(0,t.useEffect)(()=>{ez(),ew()},[]);let eb=(e,l)=>{let i={page:e,pageSize:l};el&&(i.search=el),void 0!==es&&(i.isVip=es),en&&(i.startDate=en[0],i.endDate=en[1]),eu(i=>({...i,current:e,pageSize:l||i.pageSize})),ez(i)},eP=async e=>{eo(e),ec(!0)},eD=async e=>{try{let l=await _.Ay.get("/users/".concat(e.id,"/activity-log"));ep(l.data.logs||[]),eo(e),ex(!0)}catch(e){C.i.error("获取用户活动日志失败")}},eU=async e=>{if(0===Z.length)return void C.i.warning("请选择要操作的用户");try{if(e){let e=eI.filter(e=>e.isActive);if(0===e.length)return void C.i.error("没有可用的VIP套餐");let l={packageId:e[0].id,reason:"管理员批量设置"};await E.Dv.batchSetVipStatus(Z,l)}else await E.Dv.batchCancelVipStatus(Z,{reason:"管理员批量设置",immediate:!0});C.i.success("批量".concat(e?"设置":"取消","VIP成功")),ee([]),ez()}catch(e){console.error("批量更新VIP状态失败:",e)}},eL=async()=>{if(0===Z.length)return void C.i.warning("请选择要操作的用户");C.a.confirm({title:"确认重置用户进度",content:"确定要重置选中的 ".concat(Z.length," 个用户的游戏进度吗？此操作不可恢复。"),onOk:async()=>{try{let e=Z.map(e=>_.Ay.post("/users/".concat(e,"/reset-progress")));await Promise.all(e),C.i.success("批量重置进度成功"),ee([]),ez()}catch(e){C.i.error("批量重置进度失败")}}})},eF=async e=>{try{J?(await E.Dv.update(J.id,e),C.i.success("用户更新成功")):(await E.Dv.create(e),C.i.success("用户创建成功")),G(!1),$(null),X.resetFields(),ez()}catch(e){C.i.error(J?"更新用户失败":"创建用户失败")}},eT=async e=>{try{await E.Dv.delete(e),C.i.success("用户删除成功"),ez()}catch(e){C.i.error("删除用户失败")}},eR=async e=>{try{await E.Dv.resetProgress(e),C.i.success("用户进度重置成功"),ez()}catch(e){C.i.error("重置用户进度失败")}},eO=async e=>{try{let l=await E.Dv.getStats(e.id);Q(l),W(!0)}catch(e){C.i.error("获取用户统计失败")}},eB=e=>{e.isVip?eY(e):(ek(e),eg(!0),eV.resetFields())},eY=async e=>{try{await E.Dv.cancelVipStatus(e.id,{reason:"管理员手动取消",immediate:!0}),ez()}catch(e){console.error("取消VIP失败:",e)}},e_=async e=>{if(ef)try{if(!e.packageId)return void C.i.error("请选择VIP套餐");let l={packageId:e.packageId,reason:e.reason||"管理员手动设置"};await E.Dv.setVipStatus(ef.id,l),eg(!1),ek(null),eV.resetFields(),ez()}catch(e){console.error("设置VIP失败:",e)}},eE=e=>{$(e),X.setFieldsValue({phone:e.phone,openid:e.openid,nickname:e.nickname,avatarUrl:e.avatarUrl,unlockedLevels:e.unlockedLevels,isVip:e.isVip,dailyUnlockLimit:e.dailyUnlockLimit}),G(!0)},eM=[{title:"用户信息",key:"user",width:250,render:(e,l)=>(0,s.jsxs)(c.A,{children:[(0,s.jsx)(d.A,{src:l.avatarUrl,icon:(0,s.jsx)(b.A,{})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:l.nickname||"未设置昵称"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",l.id]}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["手机: ",l.phone]}),l.openid&&(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:["OpenID: ",l.openid.substring(0,8),"..."]}),l.isVip&&(0,s.jsx)(o.A,{color:"gold",icon:(0,s.jsx)(P.A,{}),style:{marginTop:4},children:"VIP"})]})]})},{title:"游戏进度",key:"progress",width:150,render:(e,l)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:["已开启: ",l.unlockedLevels," 关"]}),(0,s.jsxs)("div",{children:["已通关: ",l.completedLevelIds.length," 关"]})]})},{title:"游戏统计",key:"stats",width:150,render:(e,l)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:["总游戏: ",l.totalGames," 次"]}),(0,s.jsxs)("div",{children:["总通关: ",l.totalCompletions," 次"]})]})},{title:"每日解锁",key:"dailyUnlock",width:150,render:(e,l)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:["今日: ",l.dailyUnlockCount||0,"/",l.dailyUnlockLimit||15,l.isVip&&(0,s.jsx)(o.A,{color:"gold",style:{marginLeft:4,fontSize:"12px"},children:"无限制"})]}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["分享: ",l.totalShares||0," 次"]}),l.dailyShared&&(0,s.jsx)(o.A,{color:"green",style:{fontSize:"12px"},children:"今日已分享"})]})},{title:"最后游戏",dataIndex:"lastPlayTime",key:"lastPlayTime",width:180},{title:"注册时间",dataIndex:"createdAt",key:"createdAt",width:180},{title:"操作",key:"action",width:280,render:(e,l)=>(0,s.jsxs)(c.A,{size:"small",wrap:!0,children:[(0,s.jsx)(h.Ay,{type:"link",size:"small",icon:(0,s.jsx)(D.A,{}),onClick:()=>eP(l),children:"详情"}),(0,s.jsx)(h.Ay,{type:"link",size:"small",icon:(0,s.jsx)(U.A,{}),onClick:()=>eD(l),children:"日志"}),(0,s.jsx)(h.Ay,{type:"link",size:"small",icon:(0,s.jsx)(L.A,{}),onClick:()=>eO(l),children:"统计"}),(0,s.jsx)(x.A,{title:l.isVip?"取消VIP":"设为VIP",children:(0,s.jsx)(h.Ay,{type:"link",size:"small",icon:(0,s.jsx)(P.A,{}),style:{color:l.isVip?"#faad14":"#d9d9d9"},onClick:()=>eB(l),children:"VIP"})}),(0,s.jsx)(h.Ay,{type:"link",size:"small",icon:(0,s.jsx)(F.A,{}),onClick:()=>eE(l),children:"编辑"}),(0,s.jsx)(j.A,{title:"确定要重置用户进度吗？",onConfirm:()=>eR(l.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(h.Ay,{type:"link",size:"small",icon:(0,s.jsx)(T.A,{}),children:"重置"})}),(0,s.jsx)(j.A,{title:"确定要删除这个用户吗？",onConfirm:()=>eT(l.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(h.Ay,{type:"link",danger:!0,size:"small",icon:(0,s.jsx)(R.A,{}),children:"删除"})})]})}];return(0,s.jsxs)("div",{children:[(0,s.jsxs)(p.A,{gutter:16,style:{marginBottom:16},children:[(0,s.jsx)(A.A,{span:6,children:(0,s.jsx)(u.A,{children:(0,s.jsx)(m.A,{title:"总用户数",value:em.totalUsers,prefix:(0,s.jsx)(b.A,{})})})}),(0,s.jsx)(A.A,{span:6,children:(0,s.jsx)(u.A,{children:(0,s.jsx)(m.A,{title:"VIP用户",value:em.vipUsers,valueStyle:{color:"#faad14"},prefix:(0,s.jsx)(P.A,{})})})}),(0,s.jsx)(A.A,{span:6,children:(0,s.jsx)(u.A,{children:(0,s.jsx)(m.A,{title:"VIP转化率",value:em.vipRate,precision:1,suffix:"%",valueStyle:{color:em.vipRate>=10?"#3f8600":"#cf1322"}})})}),(0,s.jsx)(A.A,{span:6,children:(0,s.jsx)(u.A,{children:(0,s.jsx)(m.A,{title:"平均解锁数",value:em.avgDailyUnlocks,precision:1,suffix:"次/日",prefix:(0,s.jsx)(O.A,{})})})})]}),(0,s.jsxs)(u.A,{children:[(0,s.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,s.jsx)("h2",{children:"用户管理"}),(0,s.jsx)(c.A,{children:(0,s.jsx)(h.Ay,{type:"primary",icon:(0,s.jsx)(B.A,{}),onClick:()=>{$(null),X.resetFields(),G(!0)},children:"创建用户"})})]}),(0,s.jsx)(u.A,{size:"small",style:{marginBottom:16},children:(0,s.jsxs)(p.A,{gutter:16,align:"middle",children:[(0,s.jsx)(A.A,{span:6,children:(0,s.jsx)(y.A,{placeholder:"搜索用户昵称、手机号",value:el,onChange:e=>ei(e.target.value),prefix:(0,s.jsx)(Y.A,{}),allowClear:!0})}),(0,s.jsx)(A.A,{span:4,children:(0,s.jsxs)(n.A,{placeholder:"筛选VIP状态",value:es,onChange:et,allowClear:!0,style:{width:"100%"},children:[(0,s.jsxs)(M,{value:!0,children:[(0,s.jsx)(P.A,{style:{color:"#faad14",marginRight:4}}),"VIP用户"]}),(0,s.jsxs)(M,{value:!1,children:[(0,s.jsx)(b.A,{style:{color:"#d9d9d9",marginRight:4}}),"普通用户"]})]})}),(0,s.jsx)(A.A,{span:6,children:(0,s.jsx)(N,{placeholder:["开始日期","结束日期"],value:en,onChange:e=>{e?er([e[0].format("YYYY-MM-DD"),e[1].format("YYYY-MM-DD")]):er(null)},style:{width:"100%"}})}),(0,s.jsx)(A.A,{span:8,children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(h.Ay,{type:"primary",icon:(0,s.jsx)(Y.A,{}),onClick:()=>{let e={};el&&(e.search=el),void 0!==es&&(e.isVip=es),en&&(e.startDate=en[0],e.endDate=en[1]),e.page=1,eu(e=>({...e,current:1})),ez(e)},children:"搜索"}),(0,s.jsx)(h.Ay,{icon:(0,s.jsx)(T.A,{}),onClick:()=>{ei(""),et(void 0),er(null),eu(e=>({...e,current:1})),ez({page:1})},children:"重置"})]})})]})}),Z.length>0&&(0,s.jsx)(u.A,{size:"small",style:{marginBottom:16,backgroundColor:"#f6ffed"},children:(0,s.jsxs)(p.A,{justify:"space-between",align:"middle",children:[(0,s.jsx)(A.A,{children:(0,s.jsxs)("span",{children:["已选择 ",Z.length," 个用户"]})}),(0,s.jsx)(A.A,{children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(h.Ay,{type:"primary",size:"small",icon:(0,s.jsx)(P.A,{}),onClick:()=>eU(!0),children:"批量设为VIP"}),(0,s.jsx)(h.Ay,{size:"small",onClick:()=>eU(!1),children:"批量取消VIP"}),(0,s.jsx)(h.Ay,{danger:!0,size:"small",icon:(0,s.jsx)(T.A,{}),onClick:eL,children:"批量重置进度"}),(0,s.jsx)(h.Ay,{size:"small",onClick:()=>ee([]),children:"取消选择"})]})})]})}),(0,s.jsx)(v.A,{columns:eM,dataSource:e,rowKey:"id",loading:i,rowSelection:{selectedRowKeys:Z,onChange:e=>{ee(e)}},pagination:{current:eA.current,pageSize:eA.pageSize,total:eA.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录"),onChange:eb,onShowSizeChange:eb}})]}),(0,s.jsx)(C.a,{title:J?"编辑用户":"创建用户",visible:q,onCancel:()=>{G(!1),$(null),X.resetFields()},footer:null,width:500,children:(0,s.jsxs)(a.A,{form:X,layout:"vertical",onFinish:eF,children:[!J&&(0,s.jsx)(a.A.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码"}],children:(0,s.jsx)(y.A,{placeholder:"请输入手机号"})}),J&&(0,s.jsx)(a.A.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码"}],children:(0,s.jsx)(y.A,{placeholder:"请输入手机号"})}),(0,s.jsx)(a.A.Item,{name:"openid",label:"微信OpenID",children:(0,s.jsx)(y.A,{placeholder:"请输入微信用户OpenID（可选）"})}),(0,s.jsx)(a.A.Item,{name:"nickname",label:"昵称",children:(0,s.jsx)(y.A,{placeholder:"请输入用户昵称"})}),(0,s.jsx)(a.A.Item,{name:"avatarUrl",label:"头像URL",children:(0,s.jsx)(y.A,{placeholder:"请输入头像URL"})}),J&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A.Item,{name:"unlockedLevels",label:"已开启关卡数",children:(0,s.jsx)(g.A,{min:1,max:1e3,placeholder:"已开启关卡数",style:{width:"100%"}})}),(0,s.jsx)(a.A.Item,{name:"isVip",label:"VIP状态",valuePropName:"checked",children:(0,s.jsx)(f.A,{checkedChildren:(0,s.jsx)(P.A,{}),unCheckedChildren:"普通"})}),(0,s.jsx)(a.A.Item,{name:"dailyUnlockLimit",label:"每日解锁限制",tooltip:"VIP用户无限制，普通用户默认15次",children:(0,s.jsx)(g.A,{min:1,max:999,placeholder:"每日解锁限制次数",style:{width:"100%"},addonAfter:"次/天"})})]}),(0,s.jsx)(a.A.Item,{children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(h.Ay,{type:"primary",htmlType:"submit",children:J?"更新":"创建"}),(0,s.jsx)(h.Ay,{onClick:()=>{G(!1),$(null),X.resetFields()},children:"取消"})]})})]})}),(0,s.jsx)(C.a,{title:"用户游戏统计",visible:K,onCancel:()=>W(!1),footer:[(0,s.jsx)(h.Ay,{onClick:()=>W(!1),children:"关闭"},"close")],width:600,children:H&&(0,s.jsxs)("div",{children:[(0,s.jsxs)(p.A,{gutter:16,style:{marginBottom:24},children:[(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(m.A,{title:"总游戏次数",value:H.totalGames})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(m.A,{title:"总通关次数",value:H.totalCompletions})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(m.A,{title:"已解锁关卡",value:H.unlockedLevels})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(m.A,{title:"已完成关卡",value:H.completedLevels})}),(0,s.jsx)(A.A,{span:24,children:(0,s.jsx)(m.A,{title:"通关率",value:H.completionRate,precision:2,suffix:"%"})})]}),(0,s.jsx)(u.A,{title:"每日解锁统计",size:"small",children:(0,s.jsxs)(p.A,{gutter:16,children:[(0,s.jsx)(A.A,{span:8,children:(0,s.jsx)(m.A,{title:"今日解锁",value:H.dailyUnlockCount,suffix:"/ ".concat(H.isVip?"∞":H.dailyUnlockLimit)})}),(0,s.jsx)(A.A,{span:8,children:(0,s.jsx)(m.A,{title:"剩余次数",value:H.isVip?"∞":H.remainingUnlocks})}),(0,s.jsx)(A.A,{span:8,children:(0,s.jsx)(m.A,{title:"总分享次数",value:H.totalShares})}),(0,s.jsx)(A.A,{span:24,style:{marginTop:16},children:H.isVip?(0,s.jsx)(o.A,{color:"gold",icon:(0,s.jsx)(P.A,{}),style:{fontSize:"14px",padding:"4px 8px"},children:"VIP用户 - 无限制解锁"}):(0,s.jsxs)(o.A,{color:"blue",icon:(0,s.jsx)(O.A,{}),style:{fontSize:"14px",padding:"4px 8px"},children:["普通用户 - 每日",H.dailyUnlockLimit,"次解锁"]})})]})})]})}),(0,s.jsx)(C.a,{title:"设置VIP - ".concat((null==ef?void 0:ef.nickname)||"未设置昵称"),visible:ev,onCancel:()=>{eg(!1),ek(null),eV.resetFields()},footer:null,width:500,children:(0,s.jsxs)(a.A,{form:eV,layout:"vertical",onFinish:e_,children:[(0,s.jsx)(a.A.Item,{name:"packageId",label:"选择VIP套餐",rules:[{required:!0,message:"请选择VIP套餐"}],children:(0,s.jsx)(n.A,{placeholder:"请选择VIP套餐",size:"large",children:Array.isArray(eI)&&eI.map(e=>(0,s.jsx)(n.A.Option,{value:e.id,children:(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:e.name}),(0,s.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:e.description})]}),(0,s.jsxs)("div",{style:{textAlign:"right"},children:[(0,s.jsxs)("div",{style:{color:"#f50",fontWeight:"bold"},children:["\xa5",(e.price/100).toFixed(2)]}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:[e.duration,"天"]})]})]})},e.id))})}),(0,s.jsx)(a.A.Item,{name:"reason",label:"操作原因",initialValue:"管理员手动设置",children:(0,s.jsx)(y.A.TextArea,{rows:3,placeholder:"请输入设置VIP的原因"})}),(0,s.jsx)(a.A.Item,{style:{marginBottom:0,textAlign:"right"},children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(h.Ay,{onClick:()=>{eg(!1),ek(null),eV.resetFields()},children:"取消"}),(0,s.jsx)(h.Ay,{type:"primary",htmlType:"submit",children:"确认设置"})]})})]})}),(0,s.jsx)(k.A,{title:"用户详情",placement:"right",width:600,open:ea,onClose:()=>ec(!1),children:ed&&(0,s.jsx)(I.A,{defaultActiveKey:"basic",items:[{key:"basic",label:"基本信息",children:(0,s.jsxs)(S.A,{column:1,bordered:!0,children:[(0,s.jsx)(S.A.Item,{label:"用户ID",children:ed.id}),(0,s.jsx)(S.A.Item,{label:"昵称",children:ed.nickname||"未设置"}),(0,s.jsx)(S.A.Item,{label:"手机号",children:ed.phone}),(0,s.jsx)(S.A.Item,{label:"OpenID",children:ed.openid}),(0,s.jsx)(S.A.Item,{label:"头像",children:(0,s.jsx)(d.A,{src:ed.avatarUrl,size:64,icon:(0,s.jsx)(b.A,{})})}),(0,s.jsx)(S.A.Item,{label:"VIP状态",children:(0,s.jsx)(V.A,{status:ed.isVip?"success":"default",text:ed.isVip?"VIP用户":"普通用户"})}),(0,s.jsx)(S.A.Item,{label:"注册时间",children:new Date(ed.createdAt).toLocaleString()}),(0,s.jsx)(S.A.Item,{label:"最后登录",children:ed.lastPlayTime?new Date(ed.lastPlayTime).toLocaleString():"从未登录"})]})},{key:"progress",label:"游戏进度",children:(0,s.jsxs)(c.A,{direction:"vertical",style:{width:"100%"},children:[(0,s.jsx)(u.A,{size:"small",children:(0,s.jsx)(m.A,{title:"已解锁关卡",value:ed.unlockedLevels,suffix:"关"})}),(0,s.jsx)(u.A,{size:"small",children:(0,s.jsx)(m.A,{title:"已完成关卡",value:ed.completedLevelIds.length,suffix:"关"})}),(0,s.jsx)(u.A,{size:"small",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{marginBottom:8},children:"游戏进度"}),(0,s.jsx)(w.A,{percent:ed.unlockedLevels>0?Math.round(ed.completedLevelIds.length/ed.unlockedLevels*100):0,status:"active"})]})})]})},{key:"stats",label:"使用统计",children:(0,s.jsxs)(p.A,{gutter:16,children:[(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(u.A,{size:"small",children:(0,s.jsx)(m.A,{title:"总游戏次数",value:ed.totalGames})})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(u.A,{size:"small",children:(0,s.jsx)(m.A,{title:"总通关次数",value:ed.totalCompletions})})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(u.A,{size:"small",children:(0,s.jsx)(m.A,{title:"分享次数",value:ed.totalShares||0})})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(u.A,{size:"small",children:(0,s.jsx)(m.A,{title:"每日解锁",value:ed.dailyUnlockCount||0,suffix:"/ ".concat(ed.dailyUnlockLimit||15)})})})]})}]})}),(0,s.jsx)(k.A,{title:"用户活动日志",placement:"right",width:800,open:eh,onClose:()=>ex(!1),children:ed&&(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{marginBottom:16},children:(0,s.jsxs)(c.A,{children:[(0,s.jsx)(d.A,{src:ed.avatarUrl,icon:(0,s.jsx)(b.A,{})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:ed.nickname||"未设置昵称"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",ed.id]})]})]})}),(0,s.jsx)(z.A,{children:ej.map((e,l)=>(0,s.jsx)(z.A.Item,{color:"login"===e.action?"green":"blue",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:e.description}),(0,s.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:new Date(e.timestamp).toLocaleString()}),e.ip&&(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:["IP: ",e.ip]}),e.details&&(0,s.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:4},children:JSON.stringify(e.details)})]})},l))}),0===ej.length&&(0,s.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂无活动记录"})]})})]})}}},e=>{var l=l=>e(e.s=l);e.O(0,[1291,5573,4492,5669,3464,9484,9301,6312,778,2343,1087,6615,642,7238,6226,5634,9474,9631,7577,3884,9179,8441,1684,7358],()=>l(5983)),_N_E=e.O()}]);