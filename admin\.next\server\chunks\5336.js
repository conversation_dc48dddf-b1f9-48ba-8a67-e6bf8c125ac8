"use strict";exports.id=5336,exports.ids=[5336],exports.modules={2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],l=n[0];if(Array.isArray(r)&&Array.isArray(l)){if(r[0]!==l[0]||r[2]!==l[2])return!0}else if(r!==l)return!0;if(t[4])return!n[4];if(n[4])return!0;let a=Object.values(t[1])[0],o=Object.values(n[1])[0];return!a||!o||e(a,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return l}});let r=n(19169);function l(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},3788:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M752 664c-28.5 0-54.8 10-75.4 26.7L469.4 540.8a160.68 160.68 0 000-57.6l207.2-149.9C697.2 350 723.5 360 752 360c66.2 0 120-53.8 120-120s-53.8-120-120-120-120 53.8-120 120c0 11.6 1.6 22.7 4.7 33.3L439.9 415.8C410.7 377.1 364.3 352 312 352c-88.4 0-160 71.6-160 160s71.6 160 160 160c52.3 0 98.7-25.1 127.9-63.8l196.8 142.5c-3.1 10.6-4.7 21.8-4.7 33.3 0 66.2 53.8 120 120 120s120-53.8 120-120-53.8-120-120-120zm0-476c28.7 0 52 23.3 52 52s-23.3 52-52 52-52-23.3-52-52 23.3-52 52-52zM312 600c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88zm440 236c-28.7 0-52-23.3-52-52s23.3-52 52-52 52 23.3 52 52-23.3 52-52 52z"}}]},name:"share-alt",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let r=n(51550),l=n(59656);var a=l._("_maxConcurrency"),o=l._("_runningCount"),i=l._("_queue"),u=l._("_processNext");class c{enqueue(e){let t,n,l=new Promise((e,r)=>{t=e,n=r}),a=async()=>{try{r._(this,o)[o]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,o)[o]--,r._(this,u)[u]()}};return r._(this,i)[i].push({promiseFn:l,task:a}),r._(this,u)[u](),l}bump(e){let t=r._(this,i)[i].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,i)[i].splice(t,1)[0];r._(this,i)[i].unshift(e),r._(this,u)[u](!0)}}constructor(e=5){Object.defineProperty(this,u,{value:s}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),r._(this,a)[a]=e,r._(this,o)[o]=0,r._(this,i)[i]=[]}}function s(e){if(void 0===e&&(e=!1),(r._(this,o)[o]<r._(this,a)[a]||e)&&r._(this,i)[i].length>0){var t;null==(t=r._(this,i)[i].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return c},getOrCreatePrefetchCacheEntry:function(){return u},prunePrefetchCache:function(){return d}});let r=n(59008),l=n(59154),a=n(75076);function o(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function i(e,t,n){return o(e,t===l.PrefetchKind.FULL,n)}function u(e){let{url:t,nextUrl:n,tree:r,prefetchCache:a,kind:i,allowAliasing:u=!0}=e,c=function(e,t,n,r,a){for(let i of(void 0===t&&(t=l.PrefetchKind.TEMPORARY),[n,null])){let n=o(e,!0,i),u=o(e,!1,i),c=e.search?n:u,s=r.get(c);if(s&&a){if(s.url.pathname===e.pathname&&s.url.search!==e.search)return{...s,aliased:!0};return s}let d=r.get(u);if(a&&e.search&&t!==l.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==l.PrefetchKind.FULL&&a){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,i,n,a,u);return c?(c.status=m(c),c.kind!==l.PrefetchKind.FULL&&i===l.PrefetchKind.FULL&&c.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return s({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:null!=i?i:l.PrefetchKind.TEMPORARY})}),i&&c.kind===l.PrefetchKind.TEMPORARY&&(c.kind=i),c):s({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:i||l.PrefetchKind.TEMPORARY})}function c(e){let{nextUrl:t,tree:n,prefetchCache:r,url:a,data:o,kind:u}=e,c=o.couldBeIntercepted?i(a,u,t):i(a,u),s={treeAtTimeOfPrefetch:n,data:Promise.resolve(o),kind:u,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:c,status:l.PrefetchCacheEntryStatus.fresh,url:a};return r.set(c,s),s}function s(e){let{url:t,kind:n,tree:o,nextUrl:u,prefetchCache:c}=e,s=i(t,n),d=a.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:o,nextUrl:u,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:l}=e,a=r.get(l);if(!a)return;let o=i(t,a.kind,n);return r.set(o,{...a,key:o}),r.delete(l),o}({url:t,existingCacheKey:s,nextUrl:u,prefetchCache:c})),e.prerendered){let t=c.get(null!=n?n:s);t&&(t.kind=l.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:o,data:d,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:s,status:l.PrefetchCacheEntryStatus.fresh,url:t};return c.set(s,f),f}function d(e){for(let[t,n]of e)m(n)===l.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function m(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:a}=e;return -1!==a?Date.now()<n+a?l.PrefetchCacheEntryStatus.fresh:l.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+f?r?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.fresh:t===l.PrefetchKind.AUTO&&Date.now()<n+p?l.PrefetchCacheEntryStatus.stale:t===l.PrefetchKind.FULL&&Date.now()<n+p?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return l}});let r=n(96127);function l(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6491:(e,t,n)=>{n.d(t,{A:()=>u,h:()=>c});var r=n(43210),l=n(7224),a=n(62028),o=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let i=r.createContext(null),u=r.forwardRef((e,t)=>{let{children:n}=e,u=o(e,["children"]),c=r.useContext(i),s=r.useMemo(()=>Object.assign(Object.assign({},c),u),[c,u.prefixCls,u.mode,u.selectable,u.rootClassName]),d=(0,l.H3)(n),f=(0,l.xK)(t,d?(0,l.A9)(n):null);return r.createElement(i.Provider,{value:s},r.createElement(a.A,{space:!0},d?r.cloneElement(n,{ref:f}):n))}),c=i},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9242:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return s}});let r=n(83913),l=n(89752),a=n(86770),o=n(57391),i=n(33123),u=n(33898),c=n(59435);function s(e,t,n,s,f){let p,m=t.tree,h=t.cache,g=(0,o.createHrefFromUrl)(s);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=d(n,Object.fromEntries(s.searchParams));let{seedData:o,isRootRender:c,pathToSegment:f}=t,b=["",...f];n=d(n,Object.fromEntries(s.searchParams));let v=(0,a.applyRouterStatePatchToTree)(b,m,n,g),y=(0,l.createEmptyCacheNode)();if(c&&o){let t=o[1];y.loading=o[3],y.rsc=t,function e(t,n,l,a,o){if(0!==Object.keys(a[1]).length)for(let u in a[1]){let c,s=a[1][u],d=s[0],f=(0,i.createRouterCacheKey)(d),p=null!==o&&void 0!==o[2][u]?o[2][u]:null;if(null!==p){let e=p[1],n=p[3];c={lazyData:null,rsc:d.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let m=n.parallelRoutes.get(u);m?m.set(f,c):n.parallelRoutes.set(u,new Map([[f,c]])),e(t,c,l,s,p)}}(e,y,h,n,o)}else y.rsc=h.rsc,y.prefetchRsc=h.prefetchRsc,y.loading=h.loading,y.parallelRoutes=new Map(h.parallelRoutes),(0,u.fillCacheWithNewSubTreeDataButOnlyLoading)(e,y,h,t);v&&(m=v,h=y,p=!0)}return!!p&&(f.patchedTree=m,f.cache=h,f.canonicalUrl=g,f.hashFragment=s.hash,(0,c.handleMutable)(t,f))}function d(e,t){let[n,l,...a]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),l,...a];let o={};for(let[e,n]of Object.entries(l))o[e]=d(n,t);return[n,o,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},14723:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},18468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let o=a.length<=2,[i,u]=a,c=(0,r.createRouterCacheKey)(u),s=n.parallelRoutes.get(i);if(!s)return;let d=t.parallelRoutes.get(i);if(d&&d!==s||(d=new Map(s),t.parallelRoutes.set(i,d)),o)return void d.delete(c);let f=s.get(c),p=d.get(c);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(c,p)),e(p,f,(0,l.getNextFlightSegmentPath)(a)))}}});let r=n(33123),l=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},22308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,l,,o]=t;for(let i in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=n,t[3]="refresh"),l)e(l[i],n)}},refreshInactiveParallelSegments:function(){return o}});let r=n(56928),l=n(59008),a=n(83913);async function o(e){let t=new Set;await i({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function i(e){let{navigatedAt:t,state:n,updatedTree:a,updatedCache:o,includeNextUrl:u,fetchedSegments:c,rootTree:s=a,canonicalUrl:d}=e,[,f,p,m]=a,h=[];if(p&&p!==d&&"refresh"===m&&!c.has(p)){c.add(p);let e=(0,l.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[s[0],s[1],s[2],"refetch"],nextUrl:u?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,o,o,e)});h.push(e)}for(let e in f){let r=i({navigatedAt:t,state:n,updatedTree:f[e],updatedCache:o,includeNextUrl:u,fetchedSegments:c,rootTree:s,canonicalUrl:d});h.push(r)}await Promise.all(h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24012:(e,t,n)=>{n.d(t,{Ay:()=>p,hJ:()=>d});var r=n(43210),l=n(69662),a=n.n(l),o=n(21854),i=n(72100),u=n(71802),c=n(84034),s=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let d=({title:e,content:t,prefixCls:n})=>e||t?r.createElement(r.Fragment,null,e&&r.createElement("div",{className:`${n}-title`},e),t&&r.createElement("div",{className:`${n}-inner-content`},t)):null,f=e=>{let{hashId:t,prefixCls:n,className:l,style:u,placement:c="top",title:s,content:f,children:p}=e,m=(0,i.b)(s),h=(0,i.b)(f),g=a()(t,n,`${n}-pure`,`${n}-placement-${c}`,l);return r.createElement("div",{className:g,style:u},r.createElement("div",{className:`${n}-arrow`}),r.createElement(o.z,Object.assign({},e,{className:t,prefixCls:n}),p||r.createElement(d,{prefixCls:n,title:m,content:h})))},p=e=>{let{prefixCls:t,className:n}=e,l=s(e,["prefixCls","className"]),{getPrefixCls:o}=r.useContext(u.QO),i=o("popover",t),[d,p,m]=(0,c.A)(i);return d(r.createElement(f,Object.assign({},l,{prefixCls:i,hashId:p,className:a()(n,m)})))}},24642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return y},navigateReducer:function(){return function e(t,n){let{url:$,isExternalUrl:_,navigateType:P,shouldScroll:w,allowAliasing:R}=n,E={},{hash:j}=$,x=(0,l.createHrefFromUrl)($),S="push"===P;if((0,g.prunePrefetchCache)(t.prefetchCache),E.preserveCustomHistoryState=!1,E.pendingPush=S,_)return y(t,E,$.toString(),S);if(document.getElementById("__next-page-redirect"))return y(t,E,x,S);let C=(0,g.getOrCreatePrefetchCacheEntry)({url:$,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:R}),{treeAtTimeOfPrefetch:A,data:T}=C;return f.prefetchQueue.bump(T),T.then(f=>{let{flightData:g,canonicalUrl:_,postponed:P}=f,R=Date.now(),T=!1;if(C.lastUsedTime||(C.lastUsedTime=R,T=!0),C.aliased){let r=(0,v.handleAliasedPrefetchEntry)(R,t,g,$,E);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return y(t,E,g,S);let M=_?(0,l.createHrefFromUrl)(_):x;if(j&&t.canonicalUrl.split("#",1)[0]===M.split("#",1)[0])return E.onlyHashChange=!0,E.canonicalUrl=M,E.shouldScroll=w,E.hashFragment=j,E.scrollableSegments=[],(0,s.handleMutable)(t,E);let I=t.tree,N=t.cache,z=[];for(let e of g){let{pathToSegment:n,seedData:l,head:s,isHeadPartial:f,isRootRender:g}=e,v=e.tree,_=["",...n],w=(0,o.applyRouterStatePatchToTree)(_,I,v,x);if(null===w&&(w=(0,o.applyRouterStatePatchToTree)(_,A,v,x)),null!==w){if(l&&g&&P){let e=(0,h.startPPRNavigation)(R,N,I,v,l,s,f,!1,z);if(null!==e){if(null===e.route)return y(t,E,x,S);w=e.route;let n=e.node;null!==n&&(E.cache=n);let l=e.dynamicRequestTree;if(null!==l){let n=(0,r.fetchServerResponse)($,{flightRouterState:l,nextUrl:t.nextUrl});(0,h.listenForDynamicRequest)(e,n)}}else w=v}else{if((0,u.isNavigatingToNewRootLayout)(I,w))return y(t,E,x,S);let r=(0,p.createEmptyCacheNode)(),l=!1;for(let t of(C.status!==c.PrefetchCacheEntryStatus.stale||T?l=(0,d.applyFlightData)(R,N,r,e,C):(l=function(e,t,n,r){let l=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),O(r).map(e=>[...n,...e])))(0,b.clearCacheNodeDataForSegmentPath)(e,t,a),l=!0;return l}(r,N,n,v),C.lastUsedTime=R),(0,i.shouldHardNavigate)(_,I)?(r.rsc=N.rsc,r.prefetchRsc=N.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(r,N,n),E.cache=r):l&&(E.cache=r,N=r),O(v))){let e=[...n,...t];e[e.length-1]!==m.DEFAULT_SEGMENT_KEY&&z.push(e)}}I=w}}return E.patchedTree=I,E.canonicalUrl=M,E.scrollableSegments=z,E.hashFragment=j,E.shouldScroll=w,(0,s.handleMutable)(t,E)},()=>t)}}});let r=n(59008),l=n(57391),a=n(18468),o=n(86770),i=n(65951),u=n(2030),c=n(59154),s=n(59435),d=n(56928),f=n(75076),p=n(89752),m=n(83913),h=n(65956),g=n(5334),b=n(97464),v=n(9707);function y(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,s.handleMutable)(e,t)}function O(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,l]of Object.entries(r))for(let r of O(l))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return l}});let r=n(2255);function l(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=n(57391),l=n(70642);function a(e,t){var n;let{url:a,tree:o}=t,i=(0,r.createHrefFromUrl)(a),u=o||e.tree,c=e.cache;return{canonicalUrl:i,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:u,nextUrl:null!=(n=(0,l.extractPathFromFlightRouterState)(u))?n:a.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return s}});let r=n(57391),l=n(86770),a=n(2030),o=n(25232),i=n(56928),u=n(59435),c=n(89752);function s(e,t){let{serverResponse:{flightData:n,canonicalUrl:s},navigatedAt:d}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof n)return(0,o.handleExternalUrl)(e,f,n,e.pushRef.pendingPush);let p=e.tree,m=e.cache;for(let t of n){let{segmentPath:n,tree:u}=t,h=(0,l.applyRouterStatePatchToTree)(["",...n],p,u,e.canonicalUrl);if(null===h)return e;if((0,a.isNavigatingToNewRootLayout)(p,h))return(0,o.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let g=s?(0,r.createHrefFromUrl)(s):void 0;g&&(f.canonicalUrl=g);let b=(0,c.createEmptyCacheNode)();(0,i.applyFlightData)(d,m,b,t),f.patchedTree=h,f.cache=b,m=b,p=h}return(0,u.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return o}});let r=n(40740)._(n(76715)),l=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",o=e.pathname||"",i=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||l.test(a))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),s&&"?"!==s[0]&&(s="?"+s),""+a+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+i}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},31189:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M516 673c0 4.4 3.4 8 7.5 8h185c4.1 0 7.5-3.6 7.5-8v-48c0-4.4-3.4-8-7.5-8h-185c-4.1 0-7.5 3.6-7.5 8v48zm-194.9 6.1l192-161c3.8-3.2 3.8-9.1 0-12.3l-192-160.9A7.95 7.95 0 00308 351v62.7c0 2.4 1 4.6 2.9 6.1L420.7 512l-109.8 92.2a8.1 8.1 0 00-2.9 6.1V673c0 6.8 7.9 10.5 13.1 6.1zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"code",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},32708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return u},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let r=n(34400),l=n(41500),a=n(33123),o=n(83913);function i(e,t,n,i,u,c){let{segmentPath:s,seedData:d,tree:f,head:p}=i,m=t,h=n;for(let t=0;t<s.length;t+=2){let n=s[t],i=s[t+1],g=t===s.length-2,b=(0,a.createRouterCacheKey)(i),v=h.parallelRoutes.get(n);if(!v)continue;let y=m.parallelRoutes.get(n);y&&y!==v||(y=new Map(v),m.parallelRoutes.set(n,y));let O=v.get(b),$=y.get(b);if(g){if(d&&(!$||!$.lazyData||$===O)){let t=d[0],n=d[1],a=d[3];$={lazyData:null,rsc:c||t!==o.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:c&&O?new Map(O.parallelRoutes):new Map,navigatedAt:e},O&&c&&(0,r.invalidateCacheByRouterState)($,O,f),c&&(0,l.fillLazyItemsTillLeafWithHead)(e,$,O,f,d,p,u),y.set(b,$)}continue}$&&O&&($===O&&($={lazyData:$.lazyData,rsc:$.rsc,prefetchRsc:$.prefetchRsc,head:$.head,prefetchHead:$.prefetchHead,parallelRoutes:new Map($.parallelRoutes),loading:$.loading},y.set(b,$)),m=$,h=O)}}function u(e,t,n,r,l){i(e,t,n,r,l,!0)}function c(e,t,n,r,l){i(e,t,n,r,l,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return l}});let r=n(33123);function l(e,t,n){for(let l in n[1]){let a=n[1][l][0],o=(0,r.createRouterCacheKey)(a),i=t.parallelRoutes.get(l);if(i){let t=new Map(i);t.delete(o),e.parallelRoutes.set(l,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return u},isBot:function(){return i}});let r=n(95796),l=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=r.HTML_LIMITED_BOT_UA_RE.source;function o(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function i(e){return l.test(e)||o(e)}function u(e){return l.test(e)?"dom":o(e)?"html":void 0}},35429:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return S}});let r=n(11264),l=n(11448),a=n(91563),o=n(59154),i=n(6361),u=n(57391),c=n(25232),s=n(86770),d=n(2030),f=n(59435),p=n(41500),m=n(89752),h=n(68214),g=n(96493),b=n(22308),v=n(74007),y=n(36875),O=n(97860),$=n(5334),_=n(25942),P=n(26736),w=n(24642);n(50593);let{createFromFetch:R,createTemporaryReferenceSet:E,encodeReply:j}=n(19357);async function x(e,t,n){let o,u,{actionId:c,actionArgs:s}=n,d=E(),f=(0,w.extractInfoFromServerReferenceId)(c),p="use-cache"===f.type?(0,w.omitUnusedArgs)(s,f):s,m=await j(p,{temporaryReferences:d}),h=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:c,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[a.NEXT_URL]:t}:{}},body:m}),g=h.headers.get("x-action-redirect"),[b,y]=(null==g?void 0:g.split(";"))||[];switch(y){case"push":o=O.RedirectType.push;break;case"replace":o=O.RedirectType.replace;break;default:o=void 0}let $=!!h.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(h.headers.get("x-action-revalidated")||"[[],0,0]");u={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){u={paths:[],tag:!1,cookie:!1}}let _=b?(0,i.assignLocation)(b,new URL(e.canonicalUrl,window.location.href)):void 0,P=h.headers.get("content-type");if(null==P?void 0:P.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await R(Promise.resolve(h),{callServer:r.callServer,findSourceMapURL:l.findSourceMapURL,temporaryReferences:d});return b?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:_,redirectType:o,revalidatedParts:u,isPrerender:$}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:_,redirectType:o,revalidatedParts:u,isPrerender:$}}if(h.status>=400)throw Object.defineProperty(Error("text/plain"===P?await h.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:_,redirectType:o,revalidatedParts:u,isPrerender:$}}function S(e,t){let{resolve:n,reject:r}=t,l={},a=e.tree;l.preserveCustomHistoryState=!1;let i=e.nextUrl&&(0,h.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return x(e,i,t).then(async h=>{let w,{actionResult:R,actionFlightData:E,redirectLocation:j,redirectType:x,isPrerender:S,revalidatedParts:C}=h;if(j&&(x===O.RedirectType.replace?(e.pushRef.pendingPush=!1,l.pendingPush=!1):(e.pushRef.pendingPush=!0,l.pendingPush=!0),l.canonicalUrl=w=(0,u.createHrefFromUrl)(j,!1)),!E)return(n(R),j)?(0,c.handleExternalUrl)(e,l,j.href,e.pushRef.pendingPush):e;if("string"==typeof E)return n(R),(0,c.handleExternalUrl)(e,l,E,e.pushRef.pendingPush);let A=C.paths.length>0||C.tag||C.cookie;for(let r of E){let{tree:o,seedData:u,head:f,isRootRender:h}=r;if(!h)return console.log("SERVER ACTION APPLY FAILED"),n(R),e;let y=(0,s.applyRouterStatePatchToTree)([""],a,o,w||e.canonicalUrl);if(null===y)return n(R),(0,g.handleSegmentMismatch)(e,t,o);if((0,d.isNavigatingToNewRootLayout)(a,y))return n(R),(0,c.handleExternalUrl)(e,l,w||e.canonicalUrl,e.pushRef.pendingPush);if(null!==u){let t=u[1],n=(0,m.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=u[3],(0,p.fillLazyItemsTillLeafWithHead)(v,n,void 0,o,u,f,void 0),l.cache=n,l.prefetchCache=new Map,A&&await (0,b.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:y,updatedCache:n,includeNextUrl:!!i,canonicalUrl:l.canonicalUrl||e.canonicalUrl})}l.patchedTree=y,a=y}return j&&w?(A||((0,$.createSeededPrefetchCacheEntry)({url:j,data:{flightData:E,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:S?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),l.prefetchCache=e.prefetchCache),r((0,y.getRedirectError)((0,P.hasBasePath)(w)?(0,_.removeBasePath)(w):w,x||O.RedirectType.push))):n(R),(0,f.handleMutable)(e,l)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,a,o,i,u,c){if(0===Object.keys(o[1]).length){n.head=u;return}for(let s in o[1]){let d,f=o[1][s],p=f[0],m=(0,r.createRouterCacheKey)(p),h=null!==i&&void 0!==i[2][s]?i[2][s]:null;if(a){let r=a.parallelRoutes.get(s);if(r){let a,o=(null==c?void 0:c.kind)==="auto"&&c.status===l.PrefetchCacheEntryStatus.reusable,i=new Map(r),d=i.get(m);a=null!==h?{lazyData:null,rsc:h[1],prefetchRsc:null,head:null,prefetchHead:null,loading:h[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:o&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},i.set(m,a),e(t,a,d,f,h||null,u,c),n.parallelRoutes.set(s,i);continue}}if(null!==h){let e=h[1],n=h[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=n.parallelRoutes.get(s);g?g.set(m,d):n.parallelRoutes.set(s,new Map([[m,d]])),e(t,d,void 0,f,h,u,c)}}}});let r=n(33123),l=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return l}});let r=n(33123);function l(e,t){return function e(t,n,l){if(0===Object.keys(n).length)return[t,l];if(n.children){let[a,o]=n.children,i=t.parallelRoutes.get("children");if(i){let t=(0,r.createRouterCacheKey)(a),n=i.get(t);if(n){let r=e(n,o,l+"/"+t);if(r)return r}}}for(let a in n){if("children"===a)continue;let[o,i]=n[a],u=t.parallelRoutes.get(a);if(!u)continue;let c=(0,r.createRouterCacheKey)(o),s=u.get(c);if(!s)continue;let d=e(s,i,l+"/"+c);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45032:(e,t,n)=>{n.d(t,{A:()=>u,U:()=>i});var r=n(43210),l=n(28344),a=n(6666),o=n(71802);function i(e){return t=>r.createElement(a.Ay,{theme:{token:{motion:!1,zIndexPopupBase:0}}},r.createElement(e,Object.assign({},t)))}let u=(e,t,n,a,u)=>i(i=>{let{prefixCls:c,style:s}=i,d=r.useRef(null),[f,p]=r.useState(0),[m,h]=r.useState(0),[g,b]=(0,l.A)(!1,{value:i.open}),{getPrefixCls:v}=r.useContext(o.QO),y=v(a||"select",c);r.useEffect(()=>{if(b(!0),"undefined"!=typeof ResizeObserver){let e=new ResizeObserver(e=>{let t=e[0].target;p(t.offsetHeight+8),h(t.offsetWidth)}),t=setInterval(()=>{var n;let r=u?`.${u(y)}`:`.${y}-dropdown`,l=null==(n=d.current)?void 0:n.querySelector(r);l&&(clearInterval(t),e.observe(l))},10);return()=>{clearInterval(t),e.disconnect()}}},[]);let O=Object.assign(Object.assign({},i),{style:Object.assign(Object.assign({},s),{margin:0}),open:g,visible:g,getPopupContainer:()=>d.current});return n&&(O=n(O)),t&&Object.assign(O,{[t]:{overflow:{adjustX:!1,adjustY:!1}}}),r.createElement("div",{ref:d,style:{paddingBottom:f,position:"relative",minWidth:m}},r.createElement(e,Object.assign({},O)))})},46438:(e,t,n)=>{n.d(t,{Mh:()=>f});var r=n(42411),l=n(55385);let a=new r.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),o=new r.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),i=new r.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),u=new r.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),c=new r.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),s=new r.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),d={"move-up":{inKeyframes:new r.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new r.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:a,outKeyframes:o},"move-left":{inKeyframes:i,outKeyframes:u},"move-right":{inKeyframes:c,outKeyframes:s}},f=(e,t)=>{let{antCls:n}=e,r=`${n}-${t}`,{inKeyframes:a,outKeyframes:o}=d[t];return[(0,l.b)(r,a,o,e.motionDurationMid),{[`
        ${r}-enter,
        ${r}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${r}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]}},46450:(e,t,n)=>{n.d(t,{M:()=>r});let r=n(43210).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},47453:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},48111:(e,t,n)=>{n.d(t,{A:()=>v});var r=n(43210),l=n.n(r),a=n(69662),o=n.n(a),i=n(26851);function u(e){return["small","middle","large"].includes(e)}function c(e){return!!e&&"number"==typeof e&&!Number.isNaN(e)}var s=n(71802),d=n(72202);let f=l().createContext({latestIndex:0}),p=f.Provider,m=({className:e,index:t,children:n,split:l,style:a})=>{let{latestIndex:o}=r.useContext(f);return null==n?null:r.createElement(r.Fragment,null,r.createElement("div",{className:e,style:a},n),t<o&&l&&r.createElement("span",{className:`${e}-split`},l))};var h=n(88112),g=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let b=r.forwardRef((e,t)=>{var n;let{getPrefixCls:l,direction:a,size:d,className:f,style:b,classNames:v,styles:y}=(0,s.TP)("space"),{size:O=null!=d?d:"small",align:$,className:_,rootClassName:P,children:w,direction:R="horizontal",prefixCls:E,split:j,style:x,wrap:S=!1,classNames:C,styles:A}=e,T=g(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[M,I]=Array.isArray(O)?O:[O,O],N=u(I),z=u(M),k=c(I),B=c(M),L=(0,i.A)(w,{keepEmpty:!0}),H=void 0===$&&"horizontal"===R?"center":$,U=l("space",E),[D,F,K]=(0,h.A)(U),W=o()(U,f,F,`${U}-${R}`,{[`${U}-rtl`]:"rtl"===a,[`${U}-align-${H}`]:H,[`${U}-gap-row-${I}`]:N,[`${U}-gap-col-${M}`]:z},_,P,K),V=o()(`${U}-item`,null!=(n=null==C?void 0:C.item)?n:v.item),q=0,G=L.map((e,t)=>{var n;null!=e&&(q=t);let l=(null==e?void 0:e.key)||`${V}-${t}`;return r.createElement(m,{className:V,key:l,index:t,split:j,style:null!=(n=null==A?void 0:A.item)?n:y.item},e)}),X=r.useMemo(()=>({latestIndex:q}),[q]);if(0===L.length)return null;let Y={};return S&&(Y.flexWrap="wrap"),!z&&B&&(Y.columnGap=M),!N&&k&&(Y.rowGap=I),D(r.createElement("div",Object.assign({ref:t,className:W,style:Object.assign(Object.assign(Object.assign({},Y),b),x)},T),r.createElement(p,{value:X},G)))});b.Compact=d.Ay;let v=b},50593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return u},createCacheKey:function(){return s},getCurrentCacheVersion:function(){return o},navigate:function(){return l},prefetch:function(){return r},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return i}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,l=n,a=n,o=n,i=n,u=n,c=n,s=n;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53038:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let r=n(43210);function l(e,t){let n=(0,r.useRef)(null),l=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(n.current=a(e,r)),t&&(l.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53788:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"unordered-list",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},54674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let r=n(84949),l=n(19169),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:a}=(0,l.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56072:(e,t,n)=>{n.d(t,{A:()=>K});var r=n(43210),l=n(92799),a=n(57314),o=n(69662),i=n.n(o),u=n(30305),c=n(26165),s=n(28344),d=n(11056),f=n(18130);let p=e=>"object"!=typeof e&&"function"!=typeof e||null===e;var m=n(84230),h=n(45032),g=n(56883),b=n(67716),v=n(22765),y=n(71802),O=n(59897),$=n(63736),_=n(6491),P=n(56571),w=n(42411),R=n(32476),E=n(48222),j=n(46438),x=n(11908),S=n(50410),C=n(53160),A=n(13581),T=n(60254);let M=e=>{let{componentCls:t,menuCls:n,colorError:r,colorTextLightSolid:l}=e,a=`${n}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${n} ${a}`]:{[`&${a}-danger:not(${a}-disabled)`]:{color:r,"&:hover":{color:l,backgroundColor:r}}}}}},I=e=>{let{componentCls:t,menuCls:n,zIndexPopup:r,dropdownArrowDistance:l,sizePopupArrow:a,antCls:o,iconCls:i,motionDurationMid:u,paddingBlock:c,fontSize:s,dropdownEdgeChildPadding:d,colorTextDisabled:f,fontSizeIcon:p,controlPaddingHorizontal:m,colorBgElevated:h}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:r,display:"block","&::before":{position:"absolute",insetBlock:e.calc(a).div(2).sub(l).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${o}-btn`]:{[`& > ${i}-down, & > ${o}-btn-icon > ${i}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${o}-btn > ${i}-down`]:{fontSize:p},[`${i}-down::before`]:{transition:`transform ${u}`}},[`${t}-wrap-open`]:{[`${i}-down::before`]:{transform:"rotate(180deg)"}},[`
        &-hidden,
        &-menu-hidden,
        &-menu-submenu-hidden
      `]:{display:"none"},[`&${o}-slide-down-enter${o}-slide-down-enter-active${t}-placement-bottomLeft,
          &${o}-slide-down-appear${o}-slide-down-appear-active${t}-placement-bottomLeft,
          &${o}-slide-down-enter${o}-slide-down-enter-active${t}-placement-bottom,
          &${o}-slide-down-appear${o}-slide-down-appear-active${t}-placement-bottom,
          &${o}-slide-down-enter${o}-slide-down-enter-active${t}-placement-bottomRight,
          &${o}-slide-down-appear${o}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:E.ox},[`&${o}-slide-up-enter${o}-slide-up-enter-active${t}-placement-topLeft,
          &${o}-slide-up-appear${o}-slide-up-appear-active${t}-placement-topLeft,
          &${o}-slide-up-enter${o}-slide-up-enter-active${t}-placement-top,
          &${o}-slide-up-appear${o}-slide-up-appear-active${t}-placement-top,
          &${o}-slide-up-enter${o}-slide-up-enter-active${t}-placement-topRight,
          &${o}-slide-up-appear${o}-slide-up-appear-active${t}-placement-topRight`]:{animationName:E.nP},[`&${o}-slide-down-leave${o}-slide-down-leave-active${t}-placement-bottomLeft,
          &${o}-slide-down-leave${o}-slide-down-leave-active${t}-placement-bottom,
          &${o}-slide-down-leave${o}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:E.vR},[`&${o}-slide-up-leave${o}-slide-up-leave-active${t}-placement-topLeft,
          &${o}-slide-up-leave${o}-slide-up-leave-active${t}-placement-top,
          &${o}-slide-up-leave${o}-slide-up-leave-active${t}-placement-topRight`]:{animationName:E.YU}}},(0,S.Ay)(e,h,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:r,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},(0,R.dF)(e)),{[n]:Object.assign(Object.assign({padding:d,listStyleType:"none",backgroundColor:h,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,R.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${n}-item-group-title`]:{padding:`${(0,w.zA)(c)} ${(0,w.zA)(m)}`,color:e.colorTextDescription,transition:`all ${u}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${n}-item-icon`]:{minWidth:s,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${u}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${n}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${n}-item, ${n}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${(0,w.zA)(c)} ${(0,w.zA)(m)}`,color:e.colorText,fontWeight:"normal",fontSize:s,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${u}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,R.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:f,cursor:"not-allowed","&:hover":{color:f,backgroundColor:h,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${(0,w.zA)(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:p,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${(0,w.zA)(e.marginXS)}`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:e.calc(m).add(e.fontSizeSM).equal()},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:f,backgroundColor:h,cursor:"not-allowed"}},[`${n}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[(0,E._j)(e,"slide-up"),(0,E._j)(e,"slide-down"),(0,j.Mh)(e,"move-up"),(0,j.Mh)(e,"move-down"),(0,x.aB)(e,"zoom-big")]]},N=(0,A.OF)("Dropdown",e=>{let{marginXXS:t,sizePopupArrow:n,paddingXXS:r,componentCls:l}=e,a=(0,T.oX)(e,{menuCls:`${l}-menu`,dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:r});return[I(a),M(a)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,S.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,C.n)(e)),{resetStyle:!1}),z=e=>{var t;let{menu:n,arrow:o,prefixCls:h,children:w,trigger:R,disabled:E,dropdownRender:j,popupRender:x,getPopupContainer:S,overlayClassName:C,rootClassName:A,overlayStyle:T,open:M,onOpenChange:I,visible:z,onVisibleChange:k,mouseEnterDelay:B=.15,mouseLeaveDelay:L=.1,autoAdjustOverflow:H=!0,placement:U="",overlay:D,transitionName:F,destroyOnHidden:K,destroyPopupOnHide:W}=e,{getPopupContainer:V,getPrefixCls:q,direction:G,dropdown:X}=r.useContext(y.QO),Y=x||j;(0,b.rJ)("Dropdown");let Q=r.useMemo(()=>{let e=q();return void 0!==F?F:U.includes("top")?`${e}-slide-down`:`${e}-slide-up`},[q,U,F]),J=r.useMemo(()=>U?U.includes("Center")?U.slice(0,U.indexOf("Center")):U:"rtl"===G?"bottomRight":"bottomLeft",[U,G]),Z=q("dropdown",h),ee=(0,O.A)(Z),[et,en,er]=N(Z,ee),[,el]=(0,P.Ay)(),ea=r.Children.only(p(w)?r.createElement("span",null,w):w),eo=(0,g.Ob)(ea,{className:i()(`${Z}-trigger`,{[`${Z}-rtl`]:"rtl"===G},ea.props.className),disabled:null!=(t=ea.props.disabled)?t:E}),ei=E?[]:R,eu=!!(null==ei?void 0:ei.includes("contextMenu")),[ec,es]=(0,s.A)(!1,{value:null!=M?M:z}),ed=(0,c.A)(e=>{null==I||I(e,{source:"trigger"}),null==k||k(e),es(e)}),ef=i()(C,A,en,er,ee,null==X?void 0:X.className,{[`${Z}-rtl`]:"rtl"===G}),ep=(0,m.A)({arrowPointAtCenter:"object"==typeof o&&o.pointAtCenter,autoAdjustOverflow:H,offset:el.marginXXS,arrowWidth:o?el.sizePopupArrow:0,borderRadius:el.borderRadius}),em=r.useCallback(()=>{null!=n&&n.selectable&&null!=n&&n.multiple||(null==I||I(!1,{source:"menu"}),es(!1))},[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),[eh,eg]=(0,f.YK)("Dropdown",null==T?void 0:T.zIndex),eb=r.createElement(u.A,Object.assign({alignPoint:eu},(0,d.A)(e,["rootClassName"]),{mouseEnterDelay:B,mouseLeaveDelay:L,visible:ec,builtinPlacements:ep,arrow:!!o,overlayClassName:ef,prefixCls:Z,getPopupContainer:S||V,transitionName:Q,trigger:ei,overlay:()=>{let e;return e=(null==n?void 0:n.items)?r.createElement($.A,Object.assign({},n)):"function"==typeof D?D():D,Y&&(e=Y(e)),e=r.Children.only("string"==typeof e?r.createElement("span",null,e):e),r.createElement(_.A,{prefixCls:`${Z}-menu`,rootClassName:i()(er,ee),expandIcon:r.createElement("span",{className:`${Z}-menu-submenu-arrow`},"rtl"===G?r.createElement(l.A,{className:`${Z}-menu-submenu-arrow-icon`}):r.createElement(a.A,{className:`${Z}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:em,validator:({mode:e})=>{}},e)},placement:J,onVisibleChange:ed,overlayStyle:Object.assign(Object.assign(Object.assign({},null==X?void 0:X.style),T),{zIndex:eh}),autoDestroy:null!=K?K:W}),eo);return eh&&(eb=r.createElement(v.A.Provider,{value:eg},eb)),et(eb)},k=(0,h.A)(z,"align",void 0,"dropdown",e=>e);z._InternalPanelDoNotUseOrYouWillBeFired=e=>r.createElement(k,Object.assign({},e),r.createElement("span",null));var B=n(72519),L=n(77833),H=n(48111),U=n(72202),D=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let F=e=>{let{getPopupContainer:t,getPrefixCls:n,direction:l}=r.useContext(y.QO),{prefixCls:a,type:o="default",danger:u,disabled:c,loading:s,onClick:d,htmlType:f,children:p,className:m,menu:h,arrow:g,autoFocus:b,overlay:v,trigger:O,align:$,open:_,onOpenChange:P,placement:w,getPopupContainer:R,href:E,icon:j=r.createElement(B.A,null),title:x,buttonsRender:S=e=>e,mouseEnterDelay:C,mouseLeaveDelay:A,overlayClassName:T,overlayStyle:M,destroyOnHidden:I,destroyPopupOnHide:N,dropdownRender:k,popupRender:F}=e,K=D(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),W=n("dropdown",a),V=`${W}-button`,q={menu:h,arrow:g,autoFocus:b,align:$,disabled:c,trigger:c?[]:O,onOpenChange:P,getPopupContainer:R||t,mouseEnterDelay:C,mouseLeaveDelay:A,overlayClassName:T,overlayStyle:M,destroyOnHidden:I,popupRender:F||k},{compactSize:G,compactItemClassnames:X}=(0,U.RQ)(W,l),Y=i()(V,X,m);"destroyPopupOnHide"in e&&(q.destroyPopupOnHide=N),"overlay"in e&&(q.overlay=v),"open"in e&&(q.open=_),"placement"in e?q.placement=w:q.placement="rtl"===l?"bottomLeft":"bottomRight";let[Q,J]=S([r.createElement(L.Ay,{type:o,danger:u,disabled:c,loading:s,onClick:d,htmlType:f,href:E,title:x},p),r.createElement(L.Ay,{type:o,danger:u,icon:j})]);return r.createElement(H.A.Compact,Object.assign({className:Y,size:G,block:!0},K),Q,r.createElement(z,Object.assign({},q),J))};F.__ANT_BUTTON=!0,z.Button=F;let K=z},56928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let r=n(41500),l=n(33898);function a(e,t,n,a,o){let{tree:i,seedData:u,head:c,isRootRender:s}=a;if(null===u)return!1;if(s){let l=u[1];n.loading=u[3],n.rsc=l,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,i,u,c,o)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,l.fillCacheWithNewSubTreeData)(e,n,t,a,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let r=n(70642);function l(e){return void 0!==e}function a(e,t){var n,a;let o=null==(n=t.shouldScroll)||n,i=e.nextUrl;if(l(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?i=n:i||(i=e.canonicalUrl)}return{canonicalUrl:l(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:l(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:l(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:l(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!l(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:l(t.patchedTree)?t.patchedTree:e.tree,nextUrl:i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,n)=>{n.r(t),n.d(t,{_:()=>l});var r=0;function l(e){return"__private_"+r+++"_"+e}},60203:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 01140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276zM623.5 421.5a8.03 8.03 0 00-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 000 79.2 55.95 55.95 0 0079.2 0 55.87 55.87 0 0014.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8zm260 218v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8zm12.7-197.2l-31.1-31.1a8.03 8.03 0 00-11.3 0l-56.6 56.6a8.03 8.03 0 000 11.3l31.1 31.1c3.1 3.1 8.2 3.1 11.3 0l56.6-56.6c3.1-3.1 3.1-8.2 0-11.3zm-458.6-31.1a8.03 8.03 0 00-11.3 0l-31.1 31.1a8.03 8.03 0 000 11.3l56.6 56.6c3.1 3.1 8.2 3.1 11.3 0l31.1-31.1c3.1-3.1 3.1-8.2 0-11.3l-56.6-56.6zM262 530h-80c-4.4 0-8 3.6-8 8v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8z"}}]},name:"dashboard",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},61794:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(79289),l=n(26736);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,l.hasBasePath)(n.pathname)}catch(e){return!1}}},62727:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},63690:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return m},dispatchNavigateAction:function(){return b},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return h},publicAppRouterInstance:function(){return y}});let r=n(59154),l=n(8830),a=n(43210),o=n(91992);n(50593);let i=n(19129),u=n(96127),c=n(89752),s=n(75076),d=n(73406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:n,setState:r}=e,l=t.state;t.pending=n;let a=n.payload,i=t.action(l,a);function u(e){n.discarded||(t.state=e,f(t,r),n.resolve(e))}(0,o.isThenable)(i)?i.then(u,e=>{f(t,r),n.reject(e)}):u(i)}function m(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let l={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{l={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let o={payload:t,next:null,resolve:l.resolve,reject:l.reject};null===e.pending?(e.last=o,p({actionQueue:e,action:o,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:o,setState:n})):(null!==e.last&&(e.last.next=o),e.last=o)})(n,e,t),action:async(e,t)=>(0,l.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function h(){return null}function g(){return null}function b(e,t,n,l){let a=new URL((0,u.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(l);(0,i.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,c.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,i.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let y={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),l=(0,c.createPrefetchURL)(e);if(null!==l){var a;(0,s.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:l,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;b(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;b(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,i.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63736:(e,t,n)=>{n.d(t,{A:()=>G});var r=n(43210),l=n(16561),a=n(93004),o=n(72519),i=n(69662),u=n.n(i),c=n(26165),s=n(11056),d=n(50604),f=n(56883),p=n(71802),m=n(59897);let h=(0,r.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var g=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let b=e=>{let{prefixCls:t,className:n,dashed:a}=e,o=g(e,["prefixCls","className","dashed"]),{getPrefixCls:i}=r.useContext(p.QO),c=i("menu",t),s=u()({[`${c}-item-divider-dashed`]:!!a},n);return r.createElement(l.cG,Object.assign({className:s},o))};var v=n(26851),y=n(33519);let O=e=>{var t;let{className:n,children:o,icon:i,title:c,danger:d,extra:p}=e,{prefixCls:m,firstLevel:g,direction:b,disableMenuItemTitleTooltip:O,inlineCollapsed:$}=r.useContext(h),{siderCollapsed:_}=r.useContext(a.P),P=c;void 0===c?P=g?o:"":!1===c&&(P="");let w={title:P};_||$||(w.title=null,w.open=!1);let R=(0,v.A)(o).length,E=r.createElement(l.q7,Object.assign({},(0,s.A)(e,["title","icon","danger"]),{className:u()({[`${m}-item-danger`]:d,[`${m}-item-only-child`]:(i?R+1:R)===1},n),title:"string"==typeof c?c:void 0}),(0,f.Ob)(i,{className:u()(r.isValidElement(i)?null==(t=i.props)?void 0:t.className:void 0,`${m}-item-icon`)}),(e=>{let t=null==o?void 0:o[0],n=r.createElement("span",{className:u()(`${m}-title-content`,{[`${m}-title-content-with-extra`]:!!p||0===p})},o);return(!i||r.isValidElement(o)&&"span"===o.type)&&o&&e&&g&&"string"==typeof t?r.createElement("div",{className:`${m}-inline-collapsed-noicon`},t.charAt(0)):n})($));return O||(E=r.createElement(y.A,Object.assign({},w,{placement:"rtl"===b?"left":"right",classNames:{root:`${m}-inline-collapsed-tooltip`}}),E)),E};var $=n(6491),_=n(42411),P=n(73117),w=n(32476),R=n(98e3),E=n(48222),j=n(11908),x=n(13581),S=n(60254);let C=e=>{let{componentCls:t,motionDurationSlow:n,horizontalLineHeight:r,colorSplit:l,lineWidth:a,lineType:o,itemPaddingInline:i}=e;return{[`${t}-horizontal`]:{lineHeight:r,border:0,borderBottom:`${(0,_.zA)(a)} ${o} ${l}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:i},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:`border-color ${n},background ${n}`},[`${t}-submenu-arrow`]:{display:"none"}}}},A=({componentCls:e,menuArrowOffset:t,calc:n})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,
    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,_.zA)(n(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,_.zA)(t)})`}}}}),T=e=>Object.assign({},(0,w.jk)(e)),M=(e,t)=>{let{componentCls:n,itemColor:r,itemSelectedColor:l,subMenuItemSelectedColor:a,groupTitleColor:o,itemBg:i,subMenuItemBg:u,itemSelectedBg:c,activeBarHeight:s,activeBarWidth:d,activeBarBorderWidth:f,motionDurationSlow:p,motionEaseInOut:m,motionEaseOut:h,itemPaddingInline:g,motionDurationMid:b,itemHoverColor:v,lineType:y,colorSplit:O,itemDisabledColor:$,dangerItemColor:P,dangerItemHoverColor:w,dangerItemSelectedColor:R,dangerItemActiveBg:E,dangerItemSelectedBg:j,popupBg:x,itemHoverBg:S,itemActiveBg:C,menuSubMenuBg:A,horizontalItemSelectedColor:M,horizontalItemSelectedBg:I,horizontalItemBorderRadius:N,horizontalItemHoverBg:z}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:r,background:i,[`&${n}-root:focus-visible`]:Object.assign({},T(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:o}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:a},[`${n}-item, ${n}-submenu-title`]:{color:r,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},T(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${$} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:v}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:S},"&:active":{backgroundColor:C}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:S},"&:active":{backgroundColor:C}}},[`${n}-item-danger`]:{color:P,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:w}},[`&${n}-item:active`]:{background:E}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:l,[`&${n}-item-danger`]:{color:R},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:c,[`&${n}-item-danger`]:{backgroundColor:j}},[`&${n}-submenu > ${n}`]:{backgroundColor:A},[`&${n}-popup > ${n}`]:{backgroundColor:x},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:x},[`&${n}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:f,marginTop:e.calc(f).mul(-1).equal(),marginBottom:0,borderRadius:N,"&::after":{position:"absolute",insetInline:g,bottom:0,borderBottom:`${(0,_.zA)(s)} solid transparent`,transition:`border-color ${p} ${m}`,content:'""'},"&:hover, &-active, &-open":{background:z,"&::after":{borderBottomWidth:s,borderBottomColor:M}},"&-selected":{color:M,backgroundColor:I,"&:hover":{backgroundColor:I},"&::after":{borderBottomWidth:s,borderBottomColor:M}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${(0,_.zA)(f)} ${y} ${O}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:u},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,_.zA)(d)} solid ${l}`,transform:"scaleY(0.0001)",opacity:0,transition:`transform ${b} ${h},opacity ${b} ${h}`,content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:R}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:`transform ${b} ${m},opacity ${b} ${m}`}}}}}},I=e=>{let{componentCls:t,itemHeight:n,itemMarginInline:r,padding:l,menuArrowSize:a,marginXS:o,itemMarginBlock:i,itemWidth:u,itemPaddingInline:c}=e,s=e.calc(a).add(l).add(o).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:(0,_.zA)(n),paddingInline:c,overflow:"hidden",textOverflow:"ellipsis",marginInline:r,marginBlock:i,width:u},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:(0,_.zA)(n)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:s}}},N=e=>{let{componentCls:t,iconCls:n,itemHeight:r,colorTextLightSolid:l,dropdownWidth:a,controlHeightLG:o,motionEaseOut:i,paddingXL:u,itemMarginInline:c,fontSizeLG:s,motionDurationFast:d,motionDurationSlow:f,paddingXS:p,boxShadowSecondary:m,collapsedWidth:h,collapsedIconSize:g}=e,b={height:r,lineHeight:(0,_.zA)(r),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},I(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},I(e)),{boxShadow:m})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:a,maxHeight:`calc(100vh - ${(0,_.zA)(e.calc(o).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:`border-color ${f},background ${f},padding ${d} ${i}`,[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:b,[`& ${t}-item-group-title`]:{paddingInlineStart:u}},[`${t}-item`]:b}},{[`${t}-inline-collapsed`]:{width:h,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:s,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,_.zA)(e.calc(g).div(2).equal())} - ${(0,_.zA)(c)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:g,lineHeight:(0,_.zA)(r),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:l}},[`${t}-item-group-title`]:Object.assign(Object.assign({},w.L9),{paddingInline:p})}}]},z=e=>{let{componentCls:t,motionDurationSlow:n,motionDurationMid:r,motionEaseInOut:l,motionEaseOut:a,iconCls:o,iconSize:i,iconMarginInlineEnd:u}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:`border-color ${n},background ${n},padding calc(${n} + 0.1s) ${l}`,[`${t}-item-icon, ${o}`]:{minWidth:i,fontSize:i,transition:`font-size ${r} ${a},margin ${n} ${l},color ${n}`,"+ span":{marginInlineStart:u,opacity:1,transition:`opacity ${n} ${l},margin ${n},color ${n}`}},[`${t}-item-icon`]:Object.assign({},(0,w.Nk)()),[`&${t}-item-only-child`]:{[`> ${o}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},k=e=>{let{componentCls:t,motionDurationSlow:n,motionEaseInOut:r,borderRadius:l,menuArrowSize:a,menuArrowOffset:o}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:a,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${r}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(a).mul(.6).equal(),height:e.calc(a).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:l,transition:`background ${n} ${r},transform ${n} ${r},top ${n} ${r},color ${n} ${r}`,content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,_.zA)(e.calc(o).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,_.zA)(o)})`}}}}},B=e=>{let{antCls:t,componentCls:n,fontSize:r,motionDurationSlow:l,motionDurationMid:a,motionEaseInOut:o,paddingXS:i,padding:u,colorSplit:c,lineWidth:s,zIndexPopup:d,borderRadiusLG:f,subMenuItemBorderRadius:p,menuArrowSize:m,menuArrowOffset:h,lineType:g,groupTitleLineHeight:b,groupTitleFontSize:v}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,w.t6)()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,w.dF)(e)),(0,w.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:r,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${l} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${(0,_.zA)(i)} ${(0,_.zA)(u)}`,fontSize:v,lineHeight:b,transition:`all ${l}`},[`&-horizontal ${n}-submenu`]:{transition:`border-color ${l} ${o},background ${l} ${o}`},[`${n}-submenu, ${n}-submenu-inline`]:{transition:`border-color ${l} ${o},background ${l} ${o},padding ${a} ${o}`},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:`background ${l} ${o},padding ${l} ${o}`},[`${n}-title-content`]:{transition:`color ${l}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:c,borderStyle:g,borderWidth:0,borderTopWidth:s,marginBlock:s,padding:0,"&-dashed":{borderStyle:"dashed"}}}),z(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${(0,_.zA)(e.calc(r).mul(2).equal())} ${(0,_.zA)(u)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:d,borderRadius:f,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:f},z(e)),k(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:p},[`${n}-submenu-title::after`]:{transition:`transform ${l} ${o}`}})},[`
          &-placement-leftTop,
          &-placement-bottomRight,
          `]:{transformOrigin:"100% 0"},[`
          &-placement-leftBottom,
          &-placement-topRight,
          `]:{transformOrigin:"100% 100%"},[`
          &-placement-rightBottom,
          &-placement-topLeft,
          `]:{transformOrigin:"0 100%"},[`
          &-placement-bottomLeft,
          &-placement-rightTop,
          `]:{transformOrigin:"0 0"},[`
          &-placement-leftTop,
          &-placement-leftBottom
          `]:{paddingInlineEnd:e.paddingXS},[`
          &-placement-rightTop,
          &-placement-rightBottom
          `]:{paddingInlineStart:e.paddingXS},[`
          &-placement-topRight,
          &-placement-topLeft
          `]:{paddingBottom:e.paddingXS},[`
          &-placement-bottomRight,
          &-placement-bottomLeft
          `]:{paddingTop:e.paddingXS}}}),k(e)),{[`&-inline-collapsed ${n}-submenu-arrow,
        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,_.zA)(h)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,_.zA)(e.calc(h).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${(0,_.zA)(e.calc(m).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,_.zA)(e.calc(h).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,_.zA)(h)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},L=e=>{var t,n,r;let{colorPrimary:l,colorError:a,colorTextDisabled:o,colorErrorBg:i,colorText:u,colorTextDescription:c,colorBgContainer:s,colorFillAlter:d,colorFillContent:f,lineWidth:p,lineWidthBold:m,controlItemBgActive:h,colorBgTextHover:g,controlHeightLG:b,lineHeight:v,colorBgElevated:y,marginXXS:O,padding:$,fontSize:_,controlHeightSM:w,fontSizeLG:R,colorTextLightSolid:E,colorErrorHover:j}=e,x=null!=(t=e.activeBarWidth)?t:0,S=null!=(n=e.activeBarBorderWidth)?n:p,C=null!=(r=e.itemMarginInline)?r:e.marginXXS,A=new P.Y(E).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:u,itemColor:u,colorItemTextHover:u,itemHoverColor:u,colorItemTextHoverHorizontal:l,horizontalItemHoverColor:l,colorGroupTitle:c,groupTitleColor:c,colorItemTextSelected:l,itemSelectedColor:l,subMenuItemSelectedColor:l,colorItemTextSelectedHorizontal:l,horizontalItemSelectedColor:l,colorItemBg:s,itemBg:s,colorItemBgHover:g,itemHoverBg:g,colorItemBgActive:f,itemActiveBg:h,colorSubItemBg:d,subMenuItemBg:d,colorItemBgSelected:h,itemSelectedBg:h,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:x,colorActiveBarHeight:m,activeBarHeight:m,colorActiveBarBorderSize:p,activeBarBorderWidth:S,colorItemTextDisabled:o,itemDisabledColor:o,colorDangerItemText:a,dangerItemColor:a,colorDangerItemTextHover:a,dangerItemHoverColor:a,colorDangerItemTextSelected:a,dangerItemSelectedColor:a,colorDangerItemBgActive:i,dangerItemActiveBg:i,colorDangerItemBgSelected:i,dangerItemSelectedBg:i,itemMarginInline:C,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:b,groupTitleLineHeight:v,collapsedWidth:2*b,popupBg:y,itemMarginBlock:O,itemPaddingInline:$,horizontalLineHeight:`${1.15*b}px`,iconSize:_,iconMarginInlineEnd:w-_,collapsedIconSize:R,groupTitleFontSize:_,darkItemDisabledColor:new P.Y(E).setA(.25).toRgbString(),darkItemColor:A,darkDangerItemColor:a,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:E,darkItemSelectedBg:l,darkDangerItemSelectedBg:a,darkItemHoverBg:"transparent",darkGroupTitleColor:A,darkItemHoverColor:E,darkDangerItemHoverColor:j,darkDangerItemSelectedColor:E,darkDangerItemActiveBg:a,itemWidth:x?`calc(100% + ${S}px)`:`calc(100% - ${2*C}px)`}},H=(e,t=e,n=!0)=>(0,x.OF)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:n,fontSize:r,darkItemColor:l,darkDangerItemColor:a,darkItemBg:o,darkSubMenuItemBg:i,darkItemSelectedColor:u,darkItemSelectedBg:c,darkDangerItemSelectedBg:s,darkItemHoverBg:d,darkGroupTitleColor:f,darkItemHoverColor:p,darkItemDisabledColor:m,darkDangerItemHoverColor:h,darkDangerItemSelectedColor:g,darkDangerItemActiveBg:b,popupBg:v,darkPopupBg:y}=e,O=e.calc(r).div(7).mul(5).equal(),$=(0,S.oX)(e,{menuArrowSize:O,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(O).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:v}),_=(0,S.oX)($,{itemColor:l,itemHoverColor:p,groupTitleColor:f,itemSelectedColor:u,subMenuItemSelectedColor:u,itemBg:o,popupBg:y,subMenuItemBg:i,itemActiveBg:"transparent",itemSelectedBg:c,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:d,itemDisabledColor:m,dangerItemColor:a,dangerItemHoverColor:h,dangerItemSelectedColor:g,dangerItemActiveBg:b,dangerItemSelectedBg:s,menuSubMenuBg:i,horizontalItemSelectedColor:u,horizontalItemSelectedBg:c});return[B($),C($),N($),M($,"light"),M(_,"dark"),A($),(0,R.A)($),(0,E._j)($,"slide-up"),(0,E._j)($,"slide-down"),(0,j.aB)($,"zoom-big")]},L,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t);var U=n(18130);let D=e=>{var t;let n,{popupClassName:a,icon:o,title:i,theme:c}=e,d=r.useContext(h),{prefixCls:p,inlineCollapsed:m,theme:g}=d,b=(0,l.Wj)();if(o){let e=r.isValidElement(i)&&"span"===i.type;n=r.createElement(r.Fragment,null,(0,f.Ob)(o,{className:u()(r.isValidElement(o)?null==(t=o.props)?void 0:t.className:void 0,`${p}-item-icon`)}),e?i:r.createElement("span",{className:`${p}-title-content`},i))}else n=m&&!b.length&&i&&"string"==typeof i?r.createElement("div",{className:`${p}-inline-collapsed-noicon`},i.charAt(0)):r.createElement("span",{className:`${p}-title-content`},i);let v=r.useMemo(()=>Object.assign(Object.assign({},d),{firstLevel:!1}),[d]),[y]=(0,U.YK)("Menu");return r.createElement(h.Provider,{value:v},r.createElement(l.g8,Object.assign({},(0,s.A)(e,["icon"]),{title:n,popupClassName:u()(p,a,`${p}-${c||g}`),popupStyle:Object.assign({zIndex:y},e.popupStyle)})))};var F=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};function K(e){return null===e||!1===e}let W={item:O,submenu:D,divider:b},V=(0,r.forwardRef)((e,t)=>{var n;let a=r.useContext($.h),i=a||{},{getPrefixCls:g,getPopupContainer:b,direction:v,menu:y}=r.useContext(p.QO),O=g(),{prefixCls:_,className:P,style:w,theme:R="light",expandIcon:E,_internalDisableMenuItemTitleTooltip:j,inlineCollapsed:x,siderCollapsed:S,rootClassName:C,mode:A,selectable:T,onClick:M,overflowedIndicatorPopupClassName:I}=e,N=F(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),z=(0,s.A)(N,["collapsedWidth"]);null==(n=i.validator)||n.call(i,{mode:A});let k=(0,c.A)((...e)=>{var t;null==M||M.apply(void 0,e),null==(t=i.onClick)||t.call(i)}),B=i.mode||A,L=null!=T?T:i.selectable,U=null!=x?x:S,D={horizontal:{motionName:`${O}-slide-up`},inline:(0,d.A)(O),other:{motionName:`${O}-zoom-big`}},V=g("menu",_||i.prefixCls),q=(0,m.A)(V),[G,X,Y]=H(V,q,!a),Q=u()(`${V}-${R}`,null==y?void 0:y.className,P),J=r.useMemo(()=>{var e,t;if("function"==typeof E||K(E))return E||null;if("function"==typeof i.expandIcon||K(i.expandIcon))return i.expandIcon||null;if("function"==typeof(null==y?void 0:y.expandIcon)||K(null==y?void 0:y.expandIcon))return(null==y?void 0:y.expandIcon)||null;let n=null!=(e=null!=E?E:null==i?void 0:i.expandIcon)?e:null==y?void 0:y.expandIcon;return(0,f.Ob)(n,{className:u()(`${V}-submenu-expand-icon`,r.isValidElement(n)?null==(t=n.props)?void 0:t.className:void 0)})},[E,null==i?void 0:i.expandIcon,null==y?void 0:y.expandIcon,V]),Z=r.useMemo(()=>({prefixCls:V,inlineCollapsed:U||!1,direction:v,firstLevel:!0,theme:R,mode:B,disableMenuItemTitleTooltip:j}),[V,U,v,j,R]);return G(r.createElement($.h.Provider,{value:null},r.createElement(h.Provider,{value:Z},r.createElement(l.Ay,Object.assign({getPopupContainer:b,overflowedIndicator:r.createElement(o.A,null),overflowedIndicatorPopupClassName:u()(V,`${V}-${R}`,I),mode:B,selectable:L,onClick:k},z,{inlineCollapsed:U,style:Object.assign(Object.assign({},null==y?void 0:y.style),w),className:Q,prefixCls:V,direction:v,defaultMotions:D,expandIcon:J,ref:t,rootClassName:u()(C,X,i.rootClassName,Y,q),_internalComponents:W})))))}),q=(0,r.forwardRef)((e,t)=>{let n=(0,r.useRef)(null),l=r.useContext(a.P);return(0,r.useImperativeHandle)(t,()=>({menu:n.current,focus:e=>{var t;null==(t=n.current)||t.focus(e)}})),r.createElement(V,Object.assign({ref:n},e,l))});q.Item=O,q.SubMenu=D,q.Divider=b,q.ItemGroup=l.te;let G=q},65951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[a,o]=n,[i,u]=t;return(0,l.matchSegment)(i,a)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),o[u]):!!Array.isArray(i)}}});let r=n(74007),l=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return m},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],l=t.parallelRoutes,o=new Map(l);for(let t in r){let n=r[t],i=n[0],u=(0,a.createRouterCacheKey)(i),c=l.get(t);if(void 0!==c){let r=c.get(u);if(void 0!==r){let l=e(r,n),a=new Map(c);a.set(u,l),o.set(t,a)}}}let i=t.rsc,u=b(i)&&"pending"===i.status;return{lazyData:null,rsc:i,head:t.head,prefetchHead:u?t.prefetchHead:[null,null],prefetchRsc:u?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o,navigatedAt:t.navigatedAt}}}});let r=n(83913),l=n(14077),a=n(33123),o=n(2030),i=n(5334),u={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,n,o,i,c,f,p,m){return function e(t,n,o,i,c,f,p,m,h,g,b){let v=o[1],y=i[1],O=null!==f?f[2]:null;c||!0===i[4]&&(c=!0);let $=n.parallelRoutes,_=new Map($),P={},w=null,R=!1,E={};for(let n in y){let o,i=y[n],d=v[n],f=$.get(n),j=null!==O?O[n]:null,x=i[0],S=g.concat([n,x]),C=(0,a.createRouterCacheKey)(x),A=void 0!==d?d[0]:void 0,T=void 0!==f?f.get(C):void 0;if(null!==(o=x===r.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:s(t,d,i,T,c,void 0!==j?j:null,p,m,S,b):h&&0===Object.keys(i[1]).length?s(t,d,i,T,c,void 0!==j?j:null,p,m,S,b):void 0!==d&&void 0!==A&&(0,l.matchSegment)(x,A)&&void 0!==T&&void 0!==d?e(t,T,d,i,c,j,p,m,h,S,b):s(t,d,i,T,c,void 0!==j?j:null,p,m,S,b))){if(null===o.route)return u;null===w&&(w=new Map),w.set(n,o);let e=o.node;if(null!==e){let t=new Map(f);t.set(C,e),_.set(n,t)}let t=o.route;P[n]=t;let r=o.dynamicRequestTree;null!==r?(R=!0,E[n]=r):E[n]=t}else P[n]=i,E[n]=i}if(null===w)return null;let j={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:_,navigatedAt:t};return{route:d(i,P),node:j,dynamicRequestTree:R?d(i,E):null,children:w}}(e,t,n,o,!1,i,c,f,p,[],m)}function s(e,t,n,r,l,c,s,p,m,h){return!l&&(void 0===t||(0,o.isNavigatingToNewRootLayout)(t,n))?u:function e(t,n,r,l,o,u,c,s){let p,m,h,g,b=n[1],v=0===Object.keys(b).length;if(void 0!==r&&r.navigatedAt+i.DYNAMIC_STALETIME_MS>t)p=r.rsc,m=r.loading,h=r.head,g=r.navigatedAt;else if(null===l)return f(t,n,null,o,u,c,s);else if(p=l[1],m=l[3],h=v?o:null,g=t,l[4]||u&&v)return f(t,n,l,o,u,c,s);let y=null!==l?l[2]:null,O=new Map,$=void 0!==r?r.parallelRoutes:null,_=new Map($),P={},w=!1;if(v)s.push(c);else for(let n in b){let r=b[n],l=null!==y?y[n]:null,i=null!==$?$.get(n):void 0,d=r[0],f=c.concat([n,d]),p=(0,a.createRouterCacheKey)(d),m=e(t,r,void 0!==i?i.get(p):void 0,l,o,u,f,s);O.set(n,m);let h=m.dynamicRequestTree;null!==h?(w=!0,P[n]=h):P[n]=r;let g=m.node;if(null!==g){let e=new Map;e.set(p,g),_.set(n,e)}}return{route:n,node:{lazyData:null,rsc:p,prefetchRsc:null,head:h,prefetchHead:null,loading:m,parallelRoutes:_,navigatedAt:g},dynamicRequestTree:w?d(n,P):null,children:O}}(e,n,r,c,s,p,m,h)}function d(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function f(e,t,n,r,l,o,i){let u=d(t,t[1]);return u[3]="refetch",{route:t,node:function e(t,n,r,l,o,i,u){let c=n[1],s=null!==r?r[2]:null,d=new Map;for(let n in c){let r=c[n],f=null!==s?s[n]:null,p=r[0],m=i.concat([n,p]),h=(0,a.createRouterCacheKey)(p),g=e(t,r,void 0===f?null:f,l,o,m,u),b=new Map;b.set(h,g),d.set(n,b)}let f=0===d.size;f&&u.push(i);let p=null!==r?r[1]:null,m=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==p?p:null,prefetchHead:f?l:[null,null],loading:void 0!==m?m:null,rsc:v(),head:f?v():null,navigatedAt:t}}(e,t,n,r,l,o,i),dynamicRequestTree:u,children:null}}function p(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:o,head:i}=t;o&&function(e,t,n,r,o){let i=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],a=i.children;if(null!==a){let e=a.get(n);if(void 0!==e){let t=e.route[0];if((0,l.matchSegment)(r,t)){i=e;continue}}}return}!function e(t,n,r,o){if(null===t.dynamicRequestTree)return;let i=t.children,u=t.node;if(null===i){null!==u&&(function e(t,n,r,o,i){let u=n[1],c=r[1],s=o[2],d=t.parallelRoutes;for(let t in u){let n=u[t],r=c[t],o=s[t],f=d.get(t),p=n[0],m=(0,a.createRouterCacheKey)(p),g=void 0!==f?f.get(m):void 0;void 0!==g&&(void 0!==r&&(0,l.matchSegment)(p,r[0])&&null!=o?e(g,n,r,o,i):h(n,g,null))}let f=t.rsc,p=o[1];null===f?t.rsc=p:b(f)&&f.resolve(p);let m=t.head;b(m)&&m.resolve(i)}(u,t.route,n,r,o),t.dynamicRequestTree=null);return}let c=n[1],s=r[2];for(let t in n){let n=c[t],r=s[t],a=i.get(t);if(void 0!==a){let t=a.route[0];if((0,l.matchSegment)(n[0],t)&&null!=r)return e(a,n,r,o)}}}(i,n,r,o)}(e,n,r,o,i)}m(e,null)}},t=>{m(e,t)})}function m(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)h(e.route,n,t);else for(let e of r.values())m(e,t);e.dynamicRequestTree=null}function h(e,t,n){let r=e[1],l=t.parallelRoutes;for(let e in r){let t=r[e],o=l.get(e);if(void 0===o)continue;let i=t[0],u=(0,a.createRouterCacheKey)(i),c=o.get(u);void 0!==c&&h(t,c,n)}let o=t.rsc;b(o)&&(null===n?o.resolve(null):o.reject(n));let i=t.head;b(i)&&i.resolve(null)}let g=Symbol();function b(e){return e&&e.tag===g}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=g,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return s},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],a=Array.isArray(t),o=a?t[1]:t;!o||o.startsWith(l.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):a&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),l=n(83913),a=n(14077),o=e=>"/"===e[0]?e.slice(1):e,i=e=>"string"==typeof e?"children"===e?"":e:e[1];function u(e){return e.reduce((e,t)=>""===(t=o(t))||(0,l.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===l.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(l.PAGE_SEGMENT_KEY))return"";let a=[i(n)],o=null!=(t=e[1])?t:{},s=o.children?c(o.children):void 0;if(void 0!==s)a.push(s);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let n=c(t);void 0!==n&&a.push(n)}return u(a)}function s(e,t){let n=function e(t,n){let[l,o]=t,[u,s]=n,d=i(l),f=i(u);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,a.matchSegment)(l,u)){var p;return null!=(p=c(n))?p:""}for(let t in o)if(s[t]){let n=e(o[t],s[t]);if(null!==n)return i(u)+"/"+n}return null}(e,t);return null==n||"/"===n?n:u(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72061:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},72100:(e,t,n)=>{n.d(t,{b:()=>r});let r=e=>e?"function"==typeof e?e():e:null},73237:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 310H732.4c13.6-21.4 21.6-46.8 21.6-74 0-76.1-61.9-138-138-138-41.4 0-78.7 18.4-104 47.4-25.3-29-62.6-47.4-104-47.4-76.1 0-138 61.9-138 138 0 27.2 7.9 52.6 21.6 74H144c-17.7 0-32 14.3-32 32v200c0 4.4 3.6 8 8 8h40v344c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V550h40c4.4 0 8-3.6 8-8V342c0-17.7-14.3-32-32-32zm-334-74c0-38.6 31.4-70 70-70s70 31.4 70 70-31.4 70-70 70h-70v-70zm-138-70c38.6 0 70 31.4 70 70v70h-70c-38.6 0-70-31.4-70-70s31.4-70 70-70zM180 482V378h298v104H180zm48 68h250v308H228V550zm568 308H546V550h250v308zm48-376H546V378h298v104z"}}]},name:"gift",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},73406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return u},mountFormInstance:function(){return v},mountLinkInstance:function(){return b},onLinkVisibilityChanged:function(){return O},onNavigationIntent:function(){return $},pingVisibleLinks:function(){return P},setLinkForCurrentNavigation:function(){return s},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return y}}),n(63690);let r=n(89752),l=n(59154),a=n(50593),o=n(43210),i=null,u={pending:!0},c={pending:!1};function s(e){(0,o.startTransition)(()=>{null==i||i.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(u),i=e})}function d(e){i===e&&(i=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,m="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;O(t.target,e)}},{rootMargin:"200px"}):null;function h(e,t){void 0!==f.get(e)&&y(e),f.set(e,t),null!==m&&m.observe(e)}function g(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function b(e,t,n,r,l,a){if(l){let l=g(t);if(null!==l){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:a};return h(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,n,r){let l=g(t);null!==l&&h(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:null})}function y(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==m&&m.unobserve(e)}function O(e,t){let n=f.get(e);void 0!==n&&(n.isVisible=t,t?p.add(n):p.delete(n),_(n))}function $(e,t){let n=f.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,_(n))}function _(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function P(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of p){let o=r.prefetchTask;if(null!==o&&r.cacheVersion===n&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,a.cancelPrefetchTask)(o);let i=(0,a.createCacheKey)(r.prefetchHref,e),u=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(i,t,r.kind===l.PrefetchKind.FULL,u),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return o}});let r=n(5144),l=n(5334),a=new r.PromiseQueue(5),o=function(e,t){(0,l.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,l.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let t=new URLSearchParams;for(let[n,l]of Object.entries(e))if(Array.isArray(l))for(let e of l)t.append(n,r(e));else t.set(n,r(l));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return l}})},77022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let r=n(43210),l=n(51215),a="next-route-announcer";function o(e){let{tree:t}=e,[n,o]=(0,r.useState)(null);(0,r.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,u]=(0,r.useState)(""),c=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&u(e),c.current=e},[t]),n?(0,l.createPortal)(i,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78620:(e,t,n)=>{n.d(t,{A:()=>j});var r=n(43210),l=n(69662),a=n.n(l),o=n(29769),i=n(7224),u=n(57266),c=n(71802),s=n(59897),d=n(40908),f=n(54908);let p=r.createContext({});var m=n(42411),h=n(32476),g=n(13581),b=n(60254);let v=e=>{let{antCls:t,componentCls:n,iconCls:r,avatarBg:l,avatarColor:a,containerSize:o,containerSizeLG:i,containerSizeSM:u,textFontSize:c,textFontSizeLG:s,textFontSizeSM:d,borderRadius:f,borderRadiusLG:p,borderRadiusSM:g,lineWidth:b,lineType:v}=e,y=(e,t,l)=>({width:e,height:e,borderRadius:"50%",[`&${n}-square`]:{borderRadius:l},[`&${n}-icon`]:{fontSize:t,[`> ${r}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,h.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:a,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:l,border:`${(0,m.zA)(b)} ${v} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),y(o,c,f)),{"&-lg":Object.assign({},y(i,s,p)),"&-sm":Object.assign({},y(u,d,g)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},y=e=>{let{componentCls:t,groupBorderColor:n,groupOverlapping:r,groupSpace:l}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:r}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:l}}}},O=(0,g.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:n}=e,r=(0,b.oX)(e,{avatarBg:n,avatarColor:t});return[v(r),y(r)]},e=>{let{controlHeight:t,controlHeightLG:n,controlHeightSM:r,fontSize:l,fontSizeLG:a,fontSizeXL:o,fontSizeHeading3:i,marginXS:u,marginXXS:c,colorBorderBg:s}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:r,textFontSize:Math.round((a+o)/2),textFontSizeLG:i,textFontSizeSM:l,groupSpace:c,groupOverlapping:-u,groupBorderColor:s}});var $=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let _=r.forwardRef((e,t)=>{let n,{prefixCls:l,shape:m,size:h,src:g,srcSet:b,icon:v,className:y,rootClassName:_,style:P,alt:w,draggable:R,children:E,crossOrigin:j,gap:x=4,onError:S}=e,C=$(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[A,T]=r.useState(1),[M,I]=r.useState(!1),[N,z]=r.useState(!0),k=r.useRef(null),B=r.useRef(null),L=(0,i.K4)(t,k),{getPrefixCls:H,avatar:U}=r.useContext(c.QO),D=r.useContext(p),F=()=>{if(!B.current||!k.current)return;let e=B.current.offsetWidth,t=k.current.offsetWidth;0!==e&&0!==t&&2*x<t&&T(t-2*x<e?(t-2*x)/e:1)};r.useEffect(()=>{I(!0)},[]),r.useEffect(()=>{z(!0),T(1)},[g]),r.useEffect(F,[x]);let K=(0,d.A)(e=>{var t,n;return null!=(n=null!=(t=null!=h?h:null==D?void 0:D.size)?t:e)?n:"default"}),W=Object.keys("object"==typeof K&&K||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),V=(0,f.A)(W),q=r.useMemo(()=>{if("object"!=typeof K)return{};let e=K[u.ye.find(e=>V[e])];return e?{width:e,height:e,fontSize:e&&(v||E)?e/2:18}:{}},[V,K]),G=H("avatar",l),X=(0,s.A)(G),[Y,Q,J]=O(G,X),Z=a()({[`${G}-lg`]:"large"===K,[`${G}-sm`]:"small"===K}),ee=r.isValidElement(g),et=m||(null==D?void 0:D.shape)||"circle",en=a()(G,Z,null==U?void 0:U.className,`${G}-${et}`,{[`${G}-image`]:ee||g&&N,[`${G}-icon`]:!!v},J,X,y,_,Q),er="number"==typeof K?{width:K,height:K,fontSize:v?K/2:18}:{};if("string"==typeof g&&N)n=r.createElement("img",{src:g,draggable:R,srcSet:b,onError:()=>{!1!==(null==S?void 0:S())&&z(!1)},alt:w,crossOrigin:j});else if(ee)n=g;else if(v)n=v;else if(M||1!==A){let e=`scale(${A})`;n=r.createElement(o.A,{onResize:F},r.createElement("span",{className:`${G}-string`,ref:B,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},E))}else n=r.createElement("span",{className:`${G}-string`,style:{opacity:0},ref:B},E);return Y(r.createElement("span",Object.assign({},C,{style:Object.assign(Object.assign(Object.assign(Object.assign({},er),q),null==U?void 0:U.style),P),className:en,ref:L}),n))});var P=n(26851),w=n(56883),R=n(80220);let E=e=>{let{size:t,shape:n}=r.useContext(p),l=r.useMemo(()=>({size:e.size||t,shape:e.shape||n}),[e.size,e.shape,t,n]);return r.createElement(p.Provider,{value:l},e.children)};_.Group=e=>{var t,n,l,o;let{getPrefixCls:i,direction:u}=r.useContext(c.QO),{prefixCls:d,className:f,rootClassName:p,style:m,maxCount:h,maxStyle:g,size:b,shape:v,maxPopoverPlacement:y,maxPopoverTrigger:$,children:j,max:x}=e,S=i("avatar",d),C=`${S}-group`,A=(0,s.A)(S),[T,M,I]=O(S,A),N=a()(C,{[`${C}-rtl`]:"rtl"===u},I,A,f,p,M),z=(0,P.A)(j).map((e,t)=>(0,w.Ob)(e,{key:`avatar-key-${t}`})),k=(null==x?void 0:x.count)||h,B=z.length;if(k&&k<B){let e=z.slice(0,k),i=z.slice(k,B),u=(null==x?void 0:x.style)||g,c=(null==(t=null==x?void 0:x.popover)?void 0:t.trigger)||$||"hover",s=(null==(n=null==x?void 0:x.popover)?void 0:n.placement)||y||"top",d=Object.assign(Object.assign({content:i},null==x?void 0:x.popover),{classNames:{root:a()(`${C}-popover`,null==(o=null==(l=null==x?void 0:x.popover)?void 0:l.classNames)?void 0:o.root)},placement:s,trigger:c});return e.push(r.createElement(R.A,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},d),r.createElement(_,{style:u},`+${B-k}`))),T(r.createElement(E,{shape:v,size:b},r.createElement("div",{className:N,style:m},e)))}return T(r.createElement(E,{shape:v,size:b},r.createElement("div",{className:N,style:m},z)))};let j=_},78866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return m}});let r=n(59008),l=n(57391),a=n(86770),o=n(2030),i=n(25232),u=n(59435),c=n(41500),s=n(89752),d=n(96493),f=n(68214),p=n(22308);function m(e,t){let{origin:n}=t,m={},h=e.canonicalUrl,g=e.tree;m.preserveCustomHistoryState=!1;let b=(0,s.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);b.lazyData=(0,r.fetchServerResponse)(new URL(h,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let y=Date.now();return b.lazyData.then(async n=>{let{flightData:r,canonicalUrl:s}=n;if("string"==typeof r)return(0,i.handleExternalUrl)(e,m,r,e.pushRef.pendingPush);for(let n of(b.lazyData=null,r)){let{tree:r,seedData:u,head:f,isRootRender:O}=n;if(!O)return console.log("REFRESH FAILED"),e;let $=(0,a.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===$)return(0,d.handleSegmentMismatch)(e,t,r);if((0,o.isNavigatingToNewRootLayout)(g,$))return(0,i.handleExternalUrl)(e,m,h,e.pushRef.pendingPush);let _=s?(0,l.createHrefFromUrl)(s):void 0;if(s&&(m.canonicalUrl=_),null!==u){let e=u[1],t=u[3];b.rsc=e,b.prefetchRsc=null,b.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(y,b,void 0,r,u,f,void 0),m.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:y,state:e,updatedTree:$,updatedCache:b,includeNextUrl:v,canonicalUrl:m.canonicalUrl||e.canonicalUrl}),m.cache=b,m.patchedTree=$,g=$}return(0,u.handleMutable)(e,m)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return b},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return o},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return y}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,l=Array(r),a=0;a<r;a++)l[a]=arguments[a];return n||(n=!0,t=e(...l)),t}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>l.test(e);function o(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=o();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},80220:(e,t,n)=>{n.d(t,{A:()=>b});var r=n(43210),l=n(69662),a=n.n(l),o=n(28344),i=n(2291),u=n(72100),c=n(50604),s=n(56883),d=n(71802),f=n(33519),p=n(24012),m=n(84034),h=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let g=r.forwardRef((e,t)=>{var n,l;let{prefixCls:g,title:b,content:v,overlayClassName:y,placement:O="top",trigger:$="hover",children:_,mouseEnterDelay:P=.1,mouseLeaveDelay:w=.1,onOpenChange:R,overlayStyle:E={},styles:j,classNames:x}=e,S=h(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:C,className:A,style:T,classNames:M,styles:I}=(0,d.TP)("popover"),N=C("popover",g),[z,k,B]=(0,m.A)(N),L=C(),H=a()(y,k,B,A,M.root,null==x?void 0:x.root),U=a()(M.body,null==x?void 0:x.body),[D,F]=(0,o.A)(!1,{value:null!=(n=e.open)?n:e.visible,defaultValue:null!=(l=e.defaultOpen)?l:e.defaultVisible}),K=(e,t)=>{F(e,!0),null==R||R(e,t)},W=e=>{e.keyCode===i.A.ESC&&K(!1,e)},V=(0,u.b)(b),q=(0,u.b)(v);return z(r.createElement(f.A,Object.assign({placement:O,trigger:$,mouseEnterDelay:P,mouseLeaveDelay:w},S,{prefixCls:N,classNames:{root:H,body:U},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},I.root),T),E),null==j?void 0:j.root),body:Object.assign(Object.assign({},I.body),null==j?void 0:j.body)},ref:t,open:D,onOpenChange:e=>{K(e)},overlay:V||q?r.createElement(p.hJ,{prefixCls:N,title:V,content:q}):null,transitionName:(0,c.b)(L,"zoom-big",S.transitionName),"data-popover-inject":!0}),(0,s.Ob)(_,{onKeyDown:e=>{var t,n;(0,r.isValidElement)(_)&&(null==(n=null==_?void 0:(t=_.props).onKeyDown)||n.call(t,e)),W(e)}})))});g._InternalPanelDoNotUseOrYouWillBeFired=p.Ay;let b=g},80461:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},81945:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},84034:(e,t,n)=>{n.d(t,{A:()=>f});var r=n(32476),l=n(11908),a=n(50410),o=n(53160),i=n(84509),u=n(13581),c=n(60254);let s=e=>{let{componentCls:t,popoverColor:n,titleMinWidth:l,fontWeightStrong:o,innerPadding:i,boxShadowSecondary:u,colorTextHeading:c,borderRadiusLG:s,zIndexPopup:d,titleMarginBottom:f,colorBgElevated:p,popoverBg:m,titleBorderBottom:h,innerContentPadding:g,titlePadding:b}=e;return[{[t]:Object.assign(Object.assign({},(0,r.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:d,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":p,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:s,boxShadow:u,padding:i},[`${t}-title`]:{minWidth:l,marginBottom:f,color:c,fontWeight:o,borderBottom:h,padding:b},[`${t}-inner-content`]:{color:n,padding:g}})},(0,a.Ay)(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},d=e=>{let{componentCls:t}=e;return{[t]:i.s.map(n=>{let r=e[`${n}6`];return{[`&${t}-${n}`]:{"--antd-arrow-background-color":r,[`${t}-inner`]:{backgroundColor:r},[`${t}-arrow`]:{background:"transparent"}}}})}},f=(0,u.OF)("Popover",e=>{let{colorBgElevated:t,colorText:n}=e,r=(0,c.oX)(e,{popoverBg:t,popoverColor:n});return[s(r),d(r),(0,l.aB)(r,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:n,fontHeight:r,padding:l,wireframe:i,zIndexPopupBase:u,borderRadiusLG:c,marginXS:s,lineType:d,colorSplit:f,paddingSM:p}=e,m=n-r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:u+30},(0,o.n)(e)),(0,a.Ke)({contentRadius:c,limitVerticalRadius:!0})),{innerPadding:12*!i,titleMarginBottom:i?0:s,titlePadding:i?`${m/2}px ${l}px ${m/2-t}px`:0,titleBorderBottom:i?`${t}px ${d} ${f}`:"none",innerContentPadding:i?`${p}px ${l}px`:0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},84949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(40740),l=n(60687),a=r._(n(43210)),o=n(30195),i=n(22142),u=n(59154),c=n(53038),s=n(79289),d=n(96127);n(50148);let f=n(73406),p=n(61794),m=n(63690);function h(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function g(e){let t,n,r,[o,g]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:y,as:O,children:$,prefetch:_=null,passHref:P,replace:w,shallow:R,scroll:E,onClick:j,onMouseEnter:x,onTouchStart:S,legacyBehavior:C=!1,onNavigate:A,ref:T,unstable_dynamicOnHover:M,...I}=e;t=$,C&&("string"==typeof t||"number"==typeof t)&&(t=(0,l.jsx)("a",{children:t}));let N=a.default.useContext(i.AppRouterContext),z=!1!==_,k=null===_?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:B,as:L}=a.default.useMemo(()=>{let e=h(y);return{href:e,as:O?h(O):e}},[y,O]);C&&(n=a.default.Children.only(t));let H=C?n&&"object"==typeof n&&n.ref:T,U=a.default.useCallback(e=>(null!==N&&(v.current=(0,f.mountLinkInstance)(e,B,N,k,z,g)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(e)}),[z,B,N,k,g]),D={ref:(0,c.useMergedRef)(U,H),onClick(e){C||"function"!=typeof j||j(e),C&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),N&&(e.defaultPrevented||function(e,t,n,r,l,o,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){l&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(n||t,l?"replace":"push",null==o||o,r.current)})}}(e,B,L,v,w,E,A))},onMouseEnter(e){C||"function"!=typeof x||x(e),C&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),N&&z&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){C||"function"!=typeof S||S(e),C&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),N&&z&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,s.isAbsoluteUrl)(L)?D.href=L:C&&!P&&("a"!==n.type||"href"in n.props)||(D.href=(0,d.addBasePath)(L)),r=C?a.default.cloneElement(n,D):(0,l.jsx)("a",{...I,...D,children:t}),(0,l.jsx)(b.Provider,{value:o,children:r})}n(32708);let b=(0,a.createContext)(f.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(b);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86591:(e,t,n)=>{n.d(t,{Ay:()=>u,cH:()=>o,lB:()=>i});var r=n(42411),l=n(13581);let a=e=>{let{antCls:t,componentCls:n,colorText:l,footerBg:a,headerHeight:o,headerPadding:i,headerColor:u,footerPadding:c,fontSize:s,bodyBg:d,headerBg:f}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:d,"&, *":{boxSizing:"border-box"},[`&${n}-has-sider`]:{flexDirection:"row",[`> ${n}, > ${n}-content`]:{width:0}},[`${n}-header, &${n}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${n}-header`]:{height:o,padding:i,color:u,lineHeight:(0,r.zA)(o),background:f,[`${t}-menu`]:{lineHeight:"inherit"}},[`${n}-footer`]:{padding:c,color:l,fontSize:s,background:a},[`${n}-content`]:{flex:"auto",color:l,minHeight:0}}},o=e=>{let{colorBgLayout:t,controlHeight:n,controlHeightLG:r,colorText:l,controlHeightSM:a,marginXXS:o,colorTextLightSolid:i,colorBgContainer:u}=e,c=1.25*r;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:`0 ${c}px`,headerColor:l,footerPadding:`${a}px ${c}px`,footerBg:t,siderBg:"#001529",triggerHeight:r+2*o,triggerBg:"#002140",triggerColor:i,zeroTriggerWidth:r,zeroTriggerHeight:r,lightSiderBg:u,lightTriggerBg:u,lightTriggerColor:l}},i=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],u=(0,l.OF)("Layout",e=>[a(e)],o,{deprecatedTokens:i})},86770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,u){let c,[s,d,f,p,m]=n;if(1===t.length){let e=i(n,r);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,u),e}let[h,g]=t;if(!(0,a.matchSegment)(h,s))return null;if(2===t.length)c=i(d[g],r);else if(null===(c=e((0,l.getNextFlightSegmentPath)(t),d[g],r,u)))return null;let b=[t[0],{...d,[g]:c},f,p];return m&&(b[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(b,u),b}}});let r=n(83913),l=n(74007),a=n(14077),o=n(22308);function i(e,t){let[n,l]=e,[o,u]=t;if(o===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(n,o)){let t={};for(let e in l)void 0!==u[e]?t[e]=i(l[e],u[e]):t[e]=l[e];for(let e in u)t[e]||(t[e]=u[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return j},default:function(){return M},isExternalURL:function(){return E}});let r=n(40740),l=n(60687),a=r._(n(43210)),o=n(22142),i=n(59154),u=n(57391),c=n(10449),s=n(19129),d=r._(n(35656)),f=n(35416),p=n(96127),m=n(77022),h=n(67086),g=n(44397),b=n(89330),v=n(25942),y=n(26736),O=n(70642),$=n(12776),_=n(63690),P=n(36875),w=n(97860);n(73406);let R={};function E(e){return e.origin!==window.location.origin}function j(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return E(t)?null:t}function x(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,l={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(l,"",r)):window.history.replaceState(l,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function C(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function A(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,l=null!==r?r:n;return(0,a.useDeferredValue)(n,l)}function T(e){let t,{actionQueue:n,assetPrefix:r,globalError:u}=e,f=(0,s.useActionQueue)(n),{canonicalUrl:p}=f,{searchParams:$,pathname:E}=(0,a.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,y.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[p]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(R.pendingMpaPath=void 0,(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,w.isRedirectError)(t)){e.preventDefault();let n=(0,P.getURLFromRedirectError)(t);(0,P.getRedirectTypeFromError)(t)===w.RedirectType.push?_.publicAppRouterInstance.push(n,{}):_.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:j}=f;if(j.mpaNavigation){if(R.pendingMpaPath!==p){let e=window.location;j.pendingPush?e.assign(p):e.replace(p),R.pendingMpaPath=p}(0,a.use)(b.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,l){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=C(t),l&&n(l)),e(t,r,l)},window.history.replaceState=function(e,r,l){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=C(e),l&&n(l)),t(e,r,l)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,_.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:S,tree:T,nextUrl:M,focusAndScrollRef:I}=f,N=(0,a.useMemo)(()=>(0,g.findHeadInCache)(S,T[1]),[S,T]),k=(0,a.useMemo)(()=>(0,O.getSelectedParams)(T),[T]),B=(0,a.useMemo)(()=>({parentTree:T,parentCacheNode:S,parentSegmentPath:null,url:p}),[T,S,p]),L=(0,a.useMemo)(()=>({tree:T,focusAndScrollRef:I,nextUrl:M}),[T,I,M]);if(null!==N){let[e,n]=N;t=(0,l.jsx)(A,{headCacheNode:e},n)}else t=null;let H=(0,l.jsxs)(h.RedirectBoundary,{children:[t,S.rsc,(0,l.jsx)(m.AppRouterAnnouncer,{tree:T})]});return H=(0,l.jsx)(d.ErrorBoundary,{errorComponent:u[0],errorStyles:u[1],children:H}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(x,{appRouterState:f}),(0,l.jsx)(z,{}),(0,l.jsx)(c.PathParamsContext.Provider,{value:k,children:(0,l.jsx)(c.PathnameContext.Provider,{value:E,children:(0,l.jsx)(c.SearchParamsContext.Provider,{value:$,children:(0,l.jsx)(o.GlobalLayoutRouterContext.Provider,{value:L,children:(0,l.jsx)(o.AppRouterContext.Provider,{value:_.publicAppRouterInstance,children:(0,l.jsx)(o.LayoutRouterContext.Provider,{value:B,children:H})})})})})})]})}function M(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,$.useNavFailureHandler)(),(0,l.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,l.jsx)(T,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let I=new Set,N=new Set;function z(){let[,e]=a.default.useState(0),t=I.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return N.add(n),t!==I.size&&n(),()=>{N.delete(n)}},[t,e]),[...I].map((e,t)=>(0,l.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=I.size;return I.add(e),I.size!==t&&N.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92799:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(80828),l=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var o=n(21898);let i=l.forwardRef(function(e,t){return l.createElement(o.A,(0,r.A)({},e,{ref:t,icon:a}))})},93004:(e,t,n)=>{n.d(t,{P:()=>w,A:()=>E});var r=n(43210),l=n(80828);let a={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};var o=n(21898),i=r.forwardRef(function(e,t){return r.createElement(o.A,(0,l.A)({},e,{ref:t,icon:a}))}),u=n(92799),c=n(57314),s=n(69662),d=n.n(s),f=n(11056),p=n(82378),m=n(71802),h=n(46450),g=n(42411),b=n(86591),v=n(13581);let y=e=>{let{componentCls:t,siderBg:n,motionDurationMid:r,motionDurationSlow:l,antCls:a,triggerHeight:o,triggerColor:i,triggerBg:u,headerHeight:c,zeroTriggerWidth:s,zeroTriggerHeight:d,borderRadiusLG:f,lightSiderBg:p,lightTriggerColor:m,lightTriggerBg:h,bodyBg:b}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:`all ${r}, background 0s`,"&-has-trigger":{paddingBottom:o},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${a}-menu${a}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:o,color:i,lineHeight:(0,g.zA)(o),textAlign:"center",background:u,cursor:"pointer",transition:`all ${r}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:c,insetInlineEnd:e.calc(s).mul(-1).equal(),zIndex:1,width:s,height:d,color:i,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:`0 ${(0,g.zA)(f)} ${(0,g.zA)(f)} 0`,cursor:"pointer",transition:`background ${l} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${l}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(s).mul(-1).equal(),borderRadius:`${(0,g.zA)(f)} 0 0 ${(0,g.zA)(f)}`}},"&-light":{background:p,[`${t}-trigger`]:{color:m,background:h},[`${t}-zero-width-trigger`]:{color:m,background:h,border:`1px solid ${b}`,borderInlineStart:0}}}}},O=(0,v.OF)(["Layout","Sider"],e=>[y(e)],b.cH,{deprecatedTokens:b.lB});var $=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let _={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},P=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),w=r.createContext({}),R=(()=>{let e=0;return (t="")=>(e+=1,`${t}${e}`)})(),E=r.forwardRef((e,t)=>{let{prefixCls:n,className:l,trigger:a,children:o,defaultCollapsed:s=!1,theme:g="dark",style:b={},collapsible:v=!1,reverseArrow:y=!1,width:E=200,collapsedWidth:j=80,zeroWidthTriggerStyle:x,breakpoint:S,onCollapse:C,onBreakpoint:A}=e,T=$(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:M}=(0,r.useContext)(h.M),[I,N]=(0,r.useState)("collapsed"in e?e.collapsed:s),[z,k]=(0,r.useState)(!1);(0,r.useEffect)(()=>{"collapsed"in e&&N(e.collapsed)},[e.collapsed]);let B=(t,n)=>{"collapsed"in e||N(t),null==C||C(t,n)},{getPrefixCls:L,direction:H}=(0,r.useContext)(m.QO),U=L("layout-sider",n),[D,F,K]=O(U),W=(0,r.useRef)(null);W.current=e=>{k(e.matches),null==A||A(e.matches),I!==e.matches&&B(e.matches,"responsive")},(0,r.useEffect)(()=>{let e;function t(e){var t;return null==(t=W.current)?void 0:t.call(W,e)}return void 0!==(null==window?void 0:window.matchMedia)&&S&&S in _&&(e=window.matchMedia(`screen and (max-width: ${_[S]})`),(0,p.e)(e,t),t(e)),()=>{(0,p.p)(e,t)}},[S]),(0,r.useEffect)(()=>{let e=R("ant-sider-");return M.addSider(e),()=>M.removeSider(e)},[]);let V=()=>{B(!I,"clickTrigger")},q=(0,f.A)(T,["collapsed"]),G=I?j:E,X=P(G)?`${G}px`:String(G),Y=0===parseFloat(String(j||0))?r.createElement("span",{onClick:V,className:d()(`${U}-zero-width-trigger`,`${U}-zero-width-trigger-${y?"right":"left"}`),style:x},a||r.createElement(i,null)):null,Q="rtl"===H==!y,J={expanded:Q?r.createElement(c.A,null):r.createElement(u.A,null),collapsed:Q?r.createElement(u.A,null):r.createElement(c.A,null)}[I?"collapsed":"expanded"],Z=null!==a?Y||r.createElement("div",{className:`${U}-trigger`,onClick:V,style:{width:X}},a||J):null,ee=Object.assign(Object.assign({},b),{flex:`0 0 ${X}`,maxWidth:X,minWidth:X,width:X}),et=d()(U,`${U}-${g}`,{[`${U}-collapsed`]:!!I,[`${U}-has-trigger`]:v&&null!==a&&!Y,[`${U}-below`]:!!z,[`${U}-zero-width`]:0===parseFloat(X)},l,F,K),en=r.useMemo(()=>({siderCollapsed:I}),[I]);return D(r.createElement(w.Provider,{value:en},r.createElement("aside",Object.assign({className:et},q,{style:ee,ref:t}),r.createElement("div",{className:`${U}-children`},o),v||z&&Y?Z:null)))})},95796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let r=n(98834),l=n(54674);function a(e,t){return(0,l.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return l}});let r=n(25232);function l(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let o=a.length<=2,[i,u]=a,c=(0,l.createRouterCacheKey)(u),s=n.parallelRoutes.get(i),d=t.parallelRoutes.get(i);d&&d!==s||(d=new Map(s),t.parallelRoutes.set(i,d));let f=null==s?void 0:s.get(c),p=d.get(c);if(o){p&&p.lazyData&&p!==f||d.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!f){p||d.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(c,p)),e(p,f,(0,r.getNextFlightSegmentPath)(a))}}});let r=n(74007),l=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return l}});let r=n(19169);function l(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:l,hash:a}=(0,r.parsePath)(e);return""+t+n+l+a}},98836:(e,t,n)=>{n.d(t,{A:()=>$});var r=n(78651),l=n(43210),a=n(69662),o=n.n(a),i=n(11056),u=n(71802),c=n(46450),s=n(26851),d=n(93004),f=n(86591),p=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};function m({suffixCls:e,tagName:t,displayName:n}){return n=>l.forwardRef((r,a)=>l.createElement(n,Object.assign({ref:a,suffixCls:e,tagName:t},r)))}let h=l.forwardRef((e,t)=>{let{prefixCls:n,suffixCls:r,className:a,tagName:i}=e,c=p(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=l.useContext(u.QO),d=s("layout",n),[m,h,g]=(0,f.Ay)(d),b=r?`${d}-${r}`:d;return m(l.createElement(i,Object.assign({className:o()(n||b,a,h,g),ref:t},c)))}),g=l.forwardRef((e,t)=>{let{direction:n}=l.useContext(u.QO),[a,m]=l.useState([]),{prefixCls:h,className:g,rootClassName:b,children:v,hasSider:y,tagName:O,style:$}=e,_=p(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),P=(0,i.A)(_,["suffixCls"]),{getPrefixCls:w,className:R,style:E}=(0,u.TP)("layout"),j=w("layout",h),x=function(e,t,n){return"boolean"==typeof n?n:!!e.length||(0,s.A)(t).some(e=>e.type===d.A)}(a,v,y),[S,C,A]=(0,f.Ay)(j),T=o()(j,{[`${j}-has-sider`]:x,[`${j}-rtl`]:"rtl"===n},R,g,b,C,A),M=l.useMemo(()=>({siderHook:{addSider:e=>{m(t=>[].concat((0,r.A)(t),[e]))},removeSider:e=>{m(t=>t.filter(t=>t!==e))}}}),[]);return S(l.createElement(c.M.Provider,{value:M},l.createElement(O,Object.assign({ref:t,className:T,style:Object.assign(Object.assign({},E),$)},P),v)))}),b=m({tagName:"div",displayName:"Layout"})(g),v=m({suffixCls:"header",tagName:"header",displayName:"Header"})(h),y=m({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(h),O=m({suffixCls:"content",tagName:"main",displayName:"Content"})(h);b.Header=v,b.Footer=y,b.Content=O,b.Sider=d.A,b._InternalSiderContext=d.P;let $=b}};