{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/styles/responsive.css"], "sourcesContent": ["/* 响应式设计优化 */\n\n/* 移动端优化 */\n@media (max-width: 768px) {\n  /* 侧边栏优化 */\n  .ant-layout-sider {\n    position: fixed !important;\n    left: -200px;\n    transition: left 0.3s ease;\n    z-index: 1000;\n  }\n\n  .ant-layout-sider.ant-layout-sider-collapsed {\n    left: -80px;\n  }\n\n  .ant-layout-sider-trigger {\n    position: fixed;\n    top: 16px;\n    left: 16px;\n    z-index: 1001;\n    background: #001529;\n    color: white;\n    border: none;\n    border-radius: 4px;\n    width: 40px;\n    height: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n\n  /* 主内容区域优化 */\n  .ant-layout-content {\n    margin-left: 0 !important;\n    padding: 16px 8px;\n  }\n\n  /* 表格优化 */\n  .ant-table-wrapper {\n    overflow-x: auto;\n  }\n\n  .ant-table {\n    min-width: 600px;\n  }\n\n  /* 卡片优化 */\n  .ant-card {\n    margin-bottom: 16px;\n  }\n\n  .ant-card-head-title {\n    font-size: 16px;\n  }\n\n  /* 统计卡片优化 */\n  .ant-statistic-title {\n    font-size: 12px;\n  }\n\n  .ant-statistic-content {\n    font-size: 18px;\n  }\n\n  /* 表单优化 */\n  .ant-form-item-label {\n    font-size: 14px;\n  }\n\n  /* 按钮组优化 */\n  .ant-space-item {\n    margin-bottom: 8px;\n  }\n\n  /* 模态框优化 */\n  .ant-modal {\n    margin: 16px;\n    max-width: calc(100vw - 32px);\n  }\n\n  .ant-modal-content {\n    border-radius: 8px;\n  }\n\n  /* 抽屉优化 */\n  .ant-drawer-content-wrapper {\n    width: 90vw !important;\n    max-width: 400px;\n  }\n}\n\n/* 平板端优化 */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .ant-layout-content {\n    padding: 24px 16px;\n  }\n\n  .ant-col {\n    margin-bottom: 16px;\n  }\n\n  /* 表格列宽优化 */\n  .ant-table-thead > tr > th,\n  .ant-table-tbody > tr > td {\n    padding: 12px 8px;\n  }\n}\n\n/* 大屏幕优化 */\n@media (min-width: 1200px) {\n  .ant-layout-content {\n    padding: 24px;\n  }\n\n  /* 增加最大宽度限制 */\n  .content-wrapper {\n    max-width: 1400px;\n    margin: 0 auto;\n  }\n}\n\n/* 通用优化 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n}\n\n.empty-container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 300px;\n  color: #999;\n}\n\n/* 滚动条优化 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 动画优化 */\n.fade-in {\n  animation: fadeIn 0.3s ease-in;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateX(-20px);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n/* 表格响应式优化 */\n.responsive-table {\n  overflow-x: auto;\n}\n\n.responsive-table .ant-table {\n  min-width: 800px;\n}\n\n@media (max-width: 768px) {\n  .responsive-table .ant-table {\n    min-width: 600px;\n  }\n\n  .responsive-table .ant-table-thead > tr > th,\n  .responsive-table .ant-table-tbody > tr > td {\n    padding: 8px 4px;\n    font-size: 12px;\n  }\n\n  .responsive-table .ant-btn {\n    padding: 4px 8px;\n    font-size: 12px;\n  }\n}\n\n/* 卡片网格优化 */\n.card-grid {\n  display: grid;\n  gap: 16px;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n}\n\n@media (max-width: 768px) {\n  .card-grid {\n    grid-template-columns: 1fr;\n    gap: 12px;\n  }\n}\n\n/* 统计卡片网格 */\n.stats-grid {\n  display: grid;\n  gap: 16px;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n}\n\n@media (max-width: 768px) {\n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* 操作按钮组优化 */\n.action-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n@media (max-width: 768px) {\n  .action-buttons {\n    flex-direction: column;\n  }\n\n  .action-buttons .ant-btn {\n    width: 100%;\n  }\n}\n\n/* 搜索栏优化 */\n.search-bar {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n@media (max-width: 768px) {\n  .search-bar {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .search-bar > * {\n    width: 100%;\n  }\n}\n\n/* 页面标题优化 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-header h2 {\n  margin-bottom: 8px;\n}\n\n@media (max-width: 768px) {\n  .page-header {\n    margin-bottom: 16px;\n  }\n\n  .page-header h2 {\n    font-size: 20px;\n  }\n}\n\n/* 内容区域优化 */\n.content-section {\n  margin-bottom: 24px;\n}\n\n@media (max-width: 768px) {\n  .content-section {\n    margin-bottom: 16px;\n  }\n}\n\n/* 表单布局优化 */\n.form-grid {\n  display: grid;\n  gap: 16px;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n}\n\n@media (max-width: 768px) {\n  .form-grid {\n    grid-template-columns: 1fr;\n    gap: 12px;\n  }\n}\n\n/* 模态框内容优化 */\n.modal-content {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n/* 抽屉内容优化 */\n.drawer-content {\n  padding: 16px;\n}\n\n@media (max-width: 768px) {\n  .drawer-content {\n    padding: 12px;\n  }\n}\n\n/* 加载状态优化 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 10;\n}\n\n/* 错误状态优化 */\n.error-container {\n  text-align: center;\n  padding: 40px 20px;\n  color: #999;\n}\n\n.error-container .ant-result {\n  padding: 20px;\n}\n\n/* 成功状态优化 */\n.success-container {\n  text-align: center;\n  padding: 40px 20px;\n  color: #52c41a;\n}\n\n/* 无数据状态优化 */\n.no-data-container {\n  text-align: center;\n  padding: 60px 20px;\n  color: #999;\n}\n\n.no-data-container .ant-empty {\n  margin: 0;\n}\n\n/* 工具提示优化 */\n.ant-tooltip {\n  max-width: 300px;\n}\n\n@media (max-width: 768px) {\n  .ant-tooltip {\n    max-width: 250px;\n  }\n}\n\n/* 标签页优化 */\n.ant-tabs-content-holder {\n  padding-top: 16px;\n}\n\n@media (max-width: 768px) {\n  .ant-tabs-tab {\n    padding: 8px 12px;\n    font-size: 14px;\n  }\n}\n\n/* 进度条优化 */\n.progress-container {\n  margin: 16px 0;\n}\n\n.progress-container .ant-progress-text {\n  font-size: 12px;\n}\n\n/* 徽章优化 */\n.badge-container {\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n}\n\n/* 时间线优化 */\n.timeline-container {\n  max-height: 400px;\n  overflow-y: auto;\n  padding-right: 8px;\n}\n\n@media (max-width: 768px) {\n  .timeline-container {\n    max-height: 300px;\n  }\n}\n\n/* 描述列表优化 */\n@media (max-width: 768px) {\n  .ant-descriptions-item-label {\n    font-size: 12px;\n  }\n\n  .ant-descriptions-item-content {\n    font-size: 14px;\n  }\n}\n"], "names": [], "mappings": "AAGA;EAEE;;;;;;;EAOA;;;;EAIA;;;;;;;;;;;;;;;;EAiBA;;;;;EAMA;;;;EAIA;;;;EAKA;;;;EAIA;;;;EAKA;;;;EAIA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAKA;;;;EAKA;;;;;;AAOF;EACE;;;;EAIA;;;;EAKA;;;;;AAOF;EACE;;;;EAKA;;;;;;AAOF;;;;;;;AAOA;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;EACE;;;;EAIA;;;;;EAMA;;;;;;AAOF;;;;;;AAMA;EACE;;;;;;AAOF;;;;;;AAMA;EACE;;;;;;AAMF;EACE;;;;;AAMF;;;;;;AAMA;EACE;;;;EAIA;;;;;AAMF;;;;;;;;AAQA;EACE;;;;;EAKA;;;;;AAMF;;;;AAIA;;;;AAIA;EACE;;;;EAIA;;;;;AAMF;;;;AAIA;EACE;;;;;AAMF;;;;;;AAMA;EACE;;;;;;AAOF;;;;;AAMA;;;;AAIA;EACE;;;;;AAMF;;;;;;;;;;AAcA;;;;;;AAMA;;;;AAKA;;;;;;AAOA;;;;;;AAMA;;;;AAKA;;;;AAIA;EACE;;;;;AAMF;;;;AAIA;EACE;;;;;;AAOF;;;;AAIA;;;;AAKA;;;;;;AAOA;;;;;;AAMA;EACE;;;;EAOA;;;;EAIA", "debugId": null}}]}