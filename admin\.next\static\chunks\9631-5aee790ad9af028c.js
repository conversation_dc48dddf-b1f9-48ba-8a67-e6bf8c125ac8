"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9631],{89631:(e,t,n)=>{n.d(t,{A:()=>k});var l=n(12115),a=n(29300),o=n.n(a),c=n(39496),i=n(15982),s=n(9836),r=n(51854);let d={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},b=l.createContext({});var m=n(63715),g=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(n[l[a]]=e[l[a]]);return n};let p=e=>(0,m.A)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var u=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(n[l[a]]=e[l[a]]);return n};let O=(e,t)=>{let[n,a]=(0,l.useMemo)(()=>(function(e,t){let n=[],l=[],a=!1,o=0;return e.filter(e=>e).forEach(e=>{let{filled:c}=e,i=u(e,["filled"]);if(c){l.push(i),n.push(l),l=[],o=0;return}let s=t-o;(o+=e.span||1)>=t?(o>t?(a=!0,l.push(Object.assign(Object.assign({},i),{span:s}))):l.push(i),n.push(l),l=[],o=0):l.push(i)}),l.length>0&&n.push(l),[n=n.map(e=>{let n=e.reduce((e,t)=>e+(t.span||1),0);if(n<t){let l=e[e.length-1];l.span=t-(n-(l.span||1))}return e}),a]})(t,e),[t,e]);return n},y=e=>{let{itemPrefixCls:t,component:n,span:a,className:c,style:i,labelStyle:s,contentStyle:r,bordered:d,label:m,content:g,colon:p,type:u,styles:O}=e,{classNames:y}=l.useContext(b);if(d)return l.createElement(n,{className:o()({["".concat(t,"-item-label")]:"label"===u,["".concat(t,"-item-content")]:"content"===u,["".concat(null==y?void 0:y.label)]:"label"===u,["".concat(null==y?void 0:y.content)]:"content"===u},c),style:i,colSpan:a},null!=m&&l.createElement("span",{style:Object.assign(Object.assign({},s),null==O?void 0:O.label)},m),null!=g&&l.createElement("span",{style:Object.assign(Object.assign({},s),null==O?void 0:O.content)},g));return l.createElement(n,{className:o()("".concat(t,"-item"),c),style:i,colSpan:a},l.createElement("div",{className:"".concat(t,"-item-container")},(m||0===m)&&l.createElement("span",{className:o()("".concat(t,"-item-label"),null==y?void 0:y.label,{["".concat(t,"-item-no-colon")]:!p}),style:Object.assign(Object.assign({},s),null==O?void 0:O.label)},m),(g||0===g)&&l.createElement("span",{className:o()("".concat(t,"-item-content"),null==y?void 0:y.content),style:Object.assign(Object.assign({},r),null==O?void 0:O.content)},g)))};function f(e,t,n){let{colon:a,prefixCls:o,bordered:c}=t,{component:i,type:s,showLabel:r,showContent:d,labelStyle:b,contentStyle:m,styles:g}=n;return e.map((e,t)=>{let{label:n,children:p,prefixCls:u=o,className:O,style:f,labelStyle:j,contentStyle:h,span:v=1,key:x,styles:S}=e;return"string"==typeof i?l.createElement(y,{key:"".concat(s,"-").concat(x||t),className:O,style:f,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},b),null==g?void 0:g.label),j),null==S?void 0:S.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},m),null==g?void 0:g.content),h),null==S?void 0:S.content)},span:v,colon:a,component:i,itemPrefixCls:u,bordered:c,label:r?n:null,content:d?p:null,type:s}):[l.createElement(y,{key:"label-".concat(x||t),className:O,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},b),null==g?void 0:g.label),f),j),null==S?void 0:S.label),span:1,colon:a,component:i[0],itemPrefixCls:u,bordered:c,label:n,type:"label"}),l.createElement(y,{key:"content-".concat(x||t),className:O,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},m),null==g?void 0:g.content),f),h),null==S?void 0:S.content),span:2*v-1,component:i[1],itemPrefixCls:u,bordered:c,content:p,type:"content"})]})}let j=e=>{let t=l.useContext(b),{prefixCls:n,vertical:a,row:o,index:c,bordered:i}=e;return a?l.createElement(l.Fragment,null,l.createElement("tr",{key:"label-".concat(c),className:"".concat(n,"-row")},f(o,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),l.createElement("tr",{key:"content-".concat(c),className:"".concat(n,"-row")},f(o,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):l.createElement("tr",{key:c,className:"".concat(n,"-row")},f(o,e,Object.assign({component:i?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var h=n(85573),v=n(18184),x=n(45431),S=n(61388);let E=e=>{let{componentCls:t,labelBg:n}=e;return{["&".concat(t,"-bordered")]:{["> ".concat(t,"-view")]:{border:"".concat((0,h.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"> table":{tableLayout:"auto"},["".concat(t,"-row")]:{borderBottom:"".concat((0,h.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,h.zA)(e.padding)," ").concat((0,h.zA)(e.paddingLG)),borderInlineEnd:"".concat((0,h.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderInlineEnd:"none"}},["> ".concat(t,"-item-label")]:{color:e.colorTextSecondary,backgroundColor:n,"&::after":{display:"none"}}}},["&".concat(t,"-middle")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,h.zA)(e.paddingSM)," ").concat((0,h.zA)(e.paddingLG))}}},["&".concat(t,"-small")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,h.zA)(e.paddingXS)," ").concat((0,h.zA)(e.padding))}}}}}},w=e=>{let{componentCls:t,extraColor:n,itemPaddingBottom:l,itemPaddingEnd:a,colonMarginRight:o,colonMarginLeft:c,titleMarginBottom:i}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,v.dF)(e)),E(e)),{"&-rtl":{direction:"rtl"},["".concat(t,"-header")]:{display:"flex",alignItems:"center",marginBottom:i},["".concat(t,"-title")]:Object.assign(Object.assign({},v.L9),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),["".concat(t,"-extra")]:{marginInlineStart:"auto",color:n,fontSize:e.fontSize},["".concat(t,"-view")]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},["".concat(t,"-row")]:{"> th, > td":{paddingBottom:l,paddingInlineEnd:a},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},["".concat(t,"-item-label")]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:"".concat((0,h.zA)(c)," ").concat((0,h.zA)(o))},["&".concat(t,"-item-no-colon::after")]:{content:'""'}},["".concat(t,"-item-no-label")]:{"&::after":{margin:0,content:'""'}},["".concat(t,"-item-content")]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},["".concat(t,"-item")]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",["".concat(t,"-item-label")]:{display:"inline-flex",alignItems:"baseline"},["".concat(t,"-item-content")]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},N=(0,x.OF)("Descriptions",e=>w((0,S.oX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}));var C=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(n[l[a]]=e[l[a]]);return n};let z=e=>{let{prefixCls:t,title:n,extra:a,column:m,colon:u=!0,bordered:y,layout:f,children:h,className:v,rootClassName:x,style:S,size:E,labelStyle:w,contentStyle:z,styles:k,items:A,classNames:P}=e,B=C(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:I,direction:L,className:M,style:T,classNames:W,styles:G}=(0,i.TP)("descriptions"),H=I("descriptions",t),R=(0,r.A)(),X=l.useMemo(()=>{var e;return"number"==typeof m?m:null!=(e=(0,c.ko)(R,Object.assign(Object.assign({},d),m)))?e:3},[R,m]),F=function(e,t,n){let a=l.useMemo(()=>t||p(n),[t,n]);return l.useMemo(()=>a.map(t=>{var{span:n}=t,l=g(t,["span"]);return"filled"===n?Object.assign(Object.assign({},l),{filled:!0}):Object.assign(Object.assign({},l),{span:"number"==typeof n?n:(0,c.ko)(e,n)})}),[a,e])}(R,A,h),_=(0,s.A)(E),D=O(X,F),[q,J,K]=N(H),Q=l.useMemo(()=>({labelStyle:w,contentStyle:z,styles:{content:Object.assign(Object.assign({},G.content),null==k?void 0:k.content),label:Object.assign(Object.assign({},G.label),null==k?void 0:k.label)},classNames:{label:o()(W.label,null==P?void 0:P.label),content:o()(W.content,null==P?void 0:P.content)}}),[w,z,k,P,W,G]);return q(l.createElement(b.Provider,{value:Q},l.createElement("div",Object.assign({className:o()(H,M,W.root,null==P?void 0:P.root,{["".concat(H,"-").concat(_)]:_&&"default"!==_,["".concat(H,"-bordered")]:!!y,["".concat(H,"-rtl")]:"rtl"===L},v,x,J,K),style:Object.assign(Object.assign(Object.assign(Object.assign({},T),G.root),null==k?void 0:k.root),S)},B),(n||a)&&l.createElement("div",{className:o()("".concat(H,"-header"),W.header,null==P?void 0:P.header),style:Object.assign(Object.assign({},G.header),null==k?void 0:k.header)},n&&l.createElement("div",{className:o()("".concat(H,"-title"),W.title,null==P?void 0:P.title),style:Object.assign(Object.assign({},G.title),null==k?void 0:k.title)},n),a&&l.createElement("div",{className:o()("".concat(H,"-extra"),W.extra,null==P?void 0:P.extra),style:Object.assign(Object.assign({},G.extra),null==k?void 0:k.extra)},a)),l.createElement("div",{className:"".concat(H,"-view")},l.createElement("table",null,l.createElement("tbody",null,D.map((e,t)=>l.createElement(j,{key:t,index:t,colon:u,prefixCls:H,vertical:"vertical"===f,bordered:y,row:e}))))))))};z.Item=e=>{let{children:t}=e;return t};let k=z}}]);