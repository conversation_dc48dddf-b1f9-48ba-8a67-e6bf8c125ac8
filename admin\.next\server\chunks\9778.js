exports.id=9778,exports.ids=[9778],exports.modules={128:(a,e,t)=>{"use strict";t.d(e,{f:()=>i});var s=t(49895);let i={getAll:async a=>(await s.Ay.get("/user-stars",{params:a})).data,exportData:async a=>(await s.Ay.get("/user-stars/export",{params:a,responseType:"blob"})).data};i.getAll},10535:(a,e,t)=>{"use strict";t.d(e,{y:()=>i});var s=t(49895);let i={login:async a=>(await s.Ay.post("/auth/login",a)).data,logout:()=>{localStorage.removeItem("admin_token"),window.location.href="/login"},getToken:()=>localStorage.getItem("admin_token"),setToken:a=>{localStorage.setItem("admin_token",a)},isLoggedIn:()=>!!localStorage.getItem("admin_token")};i.login},10814:(a,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},15444:()=>{},23870:(a,e,t)=>{"use strict";t.d(e,{k3:()=>r,m9:()=>g.m9,Yi:()=>y,LY:()=>n,Dv:()=>c,HK:()=>o});var s=t(49895),i=t(10535);let n={getAll:async()=>(await s.Ay.get("/phrases")).data,getById:async a=>(await s.Ay.get(`/phrases/${a}`)).data,create:async a=>(await s.Ay.post("/phrases",a)).data,update:async(a,e)=>(await s.Ay.patch(`/phrases/${a}`,e)).data,delete:async a=>{await s.Ay.delete(`/phrases/${a}`)}};var l=t(52931);let r={getAll:async a=>(await s.Ay.get("/levels",{params:a})).data,getAllSimple:async()=>(await s.Ay.get("/levels")).data,getById:async a=>(await s.Ay.get(`/levels/${a}`)).data,create:async a=>(await s.Ay.post("/levels",a)).data,update:async(a,e)=>(await s.Ay.patch(`/levels/${a}`,e)).data,delete:async a=>{await s.Ay.delete(`/levels/${a}`)},addPhrase:async(a,e)=>(await s.Ay.post(`/levels/${a}/phrases`,e)).data,removePhrase:async(a,e)=>(await s.Ay.delete(`/levels/${a}/phrases/${e}`)).data,getCount:async()=>(await s.Ay.get("/levels/count")).data,getByDifficulty:async a=>(await s.Ay.get(`/levels/difficulty/${a}`)).data,getWithPhrases:async a=>(await s.Ay.get(`/levels/${a}`)).data,getLevelStarAnalytics:async a=>(await s.Ay.get(`/levels/${a}/star-analytics`)).data};r.getAll;let c={getAll:async()=>(await s.FH.get("/users")).data,getById:async a=>(await s.FH.get(`/users/${a}`)).data,getByOpenid:async a=>(await s.FH.get(`/users/by-openid?openid=${a}`)).data,getByPhone:async a=>(await s.FH.get(`/users/by-phone?phone=${a}`)).data,create:async a=>(await s.KY.post("/users",a,"用户创建成功")).data,update:async(a,e)=>(await s.KY.patch(`/users/${a}`,e,"用户更新成功")).data,delete:async a=>{await s.KY.delete(`/users/${a}`,"用户删除成功")},completeLevel:async(a,e)=>(await s.FH.post(`/users/${a}/complete-level`,e)).data,startGame:async a=>(await s.FH.post(`/users/${a}/start-game`)).data,getStats:async a=>(await s.FH.get(`/users/${a}/stats`)).data,resetProgress:async a=>(await s.KY.post(`/users/${a}/reset-progress`,{},"用户进度重置成功")).data,setVipStatus:async(a,e)=>(await s.KY.post(`/users/${a}/set-vip-package`,{packageId:e.packageId,reason:e.reason||"管理员手动设置"},"设置VIP成功")).data,cancelVipStatus:async(a,e)=>(await s.KY.post(`/users/${a}/cancel-vip`,e,"取消VIP成功")).data,batchVipOperation:async a=>{await s.FH.post("/users/batch-vip-package",a)},batchSetVipStatus:async(a,e)=>{await c.batchVipOperation({userIds:a,packageId:e.packageId,reason:e.reason||"管理员批量设置"})},batchCancelVipStatus:async(a,e)=>{let t=a.map(a=>c.cancelVipStatus(a,e));await Promise.all(t)}};var d=t(43910);let o={getList:async a=>(await s.Ay.get("/payment/packages",{params:a})).data,getById:async a=>(await s.Ay.get(`/payment/packages/${a}`)).data,create:async a=>(await s.Ay.post("/payment/packages",a)).data,update:async(a,e)=>(await s.Ay.put(`/payment/packages/${a}`,e)).data,async delete(a){await s.Ay.delete(`/payment/packages/${a}`)},toggleStatus:async(a,e)=>(await s.Ay.put(`/payment/packages/${a}`,{isActive:e})).data},y={getList:async a=>(await s.Ay.get("/payment/orders",{params:a})).data,getById:async a=>(await s.Ay.get(`/payment/orders/${a}`)).data,refund:async(a,e)=>(await s.Ay.post(`/payment/orders/${a}/refund`,{reason:e})).data,async cancel(a,e){await s.Ay.post(`/payment/cancel/${a}`,{userId:e})}};var p=t(90134),g=t(88555),u=t(128),h=t(89900),A=t(86722);i.y,l.Au,d.Dw,p.f,g.m9,u.f,h.r,A.L8},24600:(a,e,t)=>{Promise.resolve().then(t.bind(t,10814))},37912:(a,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>P});var s=t(60687),i=t(43210),n=t(85814),l=t.n(n),r=t(16189),c=t(98836),d=t(99053),o=t(63736),y=t(56072),p=t(78620);t(15444);var g=t(60203),u=t(81945),h=t(53788),A=t(9242),w=t(3788),v=t(73237),m=t(47453),b=t(31189),f=t(62727),k=t(14723),$=t(72061),x=t(80461),I=t(71103);let{Header:S,Content:j,Sider:C,Footer:B}=c.A,K=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,s.jsx)(g.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,s.jsx)(u.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,s.jsx)(h.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,s.jsx)(A.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,s.jsx)(w.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,s.jsx)(v.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,s.jsx)(m.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,s.jsx)(b.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,s.jsx)(f.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,s.jsx)(k.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,s.jsx)($.A,{})}];function P({children:a}){let e=(0,r.useRouter)(),t=(0,r.usePathname)(),[n,g]=(0,i.useState)(!1),u=[{key:"logout",icon:(0,s.jsx)(x.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),e.push("/login")}}],h=K.find(a=>t.startsWith(a.path))?.key||"dashboard";return(0,s.jsxs)(c.A,{style:{minHeight:"100vh"},children:[(0,s.jsxs)(C,{collapsible:!0,collapsed:n,onCollapse:a=>g(a),children:[(0,s.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(d.A.Text,{style:{color:"white",fontSize:n?"10px":"16px",transition:"font-size 0.2s"},children:n?"后台":"游戏管理后台"})}),(0,s.jsx)(o.A,{theme:"dark",selectedKeys:[h],mode:"inline",items:K.map(a=>({key:a.key,icon:a.icon,label:(0,s.jsx)(l(),{href:a.path,children:a.label})}))})]}),(0,s.jsxs)(c.A,{children:[(0,s.jsxs)(S,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,s.jsx)(y.A,{menu:{items:u},placement:"bottomRight",children:(0,s.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,s.jsx)(I.A,{})})}),(0,s.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,s.jsx)(j,{style:{margin:"16px"},children:a}),(0,s.jsxs)(B,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},43910:(a,e,t)=>{"use strict";t.d(e,{Dw:()=>i,WS:()=>n,cm:()=>l});var s=t(49895);class i{static async getAllShareConfigs(){return(await s.Ay.get("/share")).data}static async getActiveShareConfigs(){return(await s.Ay.get("/share/active")).data}static async getDefaultShareConfig(){return(await s.Ay.get("/share/default")).data}static async getShareConfigByType(a){return(await s.Ay.get(`/share/type/${a}`)).data}static async getShareConfigById(a){return(await s.Ay.get(`/share/${a}`)).data}static async createShareConfig(a){return(await s.Ay.post("/share",a)).data}static async updateShareConfig(a,e){return(await s.Ay.patch(`/share/${a}`,e)).data}static async toggleShareConfig(a){return(await s.Ay.put(`/share/${a}/toggle`)).data}static async deleteShareConfig(a){return(await s.Ay.delete(`/share/${a}`)).data}}let n=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],l=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},52931:(a,e,t)=>{"use strict";t.d(e,{Au:()=>i,Io:()=>l,LQ:()=>n,zN:()=>r});var s=t(49895);let i={getAll:async()=>(await s.Ay.get("/thesauruses")).data,getById:async a=>(await s.Ay.get(`/thesauruses/${a}`)).data,create:async a=>(await s.Ay.post("/thesauruses",a)).data,update:async(a,e)=>(await s.Ay.patch(`/thesauruses/${a}`,e)).data,delete:async a=>{await s.Ay.delete(`/thesauruses/${a}`)},addPhrase:async(a,e)=>(await s.Ay.post(`/thesauruses/${a}/phrases`,e)).data,removePhrase:async(a,e)=>(await s.Ay.delete(`/thesauruses/${a}/phrases/${e}`)).data},n=i.create;i.update,i.delete;let l=i.delete;i.getById,i.getAll;let r=i.getAll},59448:(a,e,t)=>{Promise.resolve().then(t.bind(t,37912))},86722:(a,e,t)=>{"use strict";t.d(e,{L8:()=>i});var s=t(49895);let i={getAll:async a=>(await s.FH.get("/activation-codes",{params:a})).data,getByCode:async a=>(await s.FH.get(`/activation-codes/${a}`)).data,generate:async a=>(await s.KY.post("/activation-codes/generate",a,"激活码生成成功")).data,create:async a=>(await s.KY.post("/activation-codes/create",a,"激活码创建成功")).data,batchGenerate:async a=>(await s.KY.post("/activation-codes/generate",a,"批量生成激活码成功")).data,disable:async a=>{await s.KY.put(`/activation-codes/${a}/disable`,{},"激活码已禁用")},enable:async a=>{await s.KY.put(`/activation-codes/${a}/enable`,{},"激活码已启用")},getAllPackages:async()=>(await s.FH.get("/payment/packages")).data||[],getPopularPackages:async a=>(await s.FH.get("/payment/packages/popular",{params:{limit:a}})).data,delete:async(a,e)=>(await s.KY.delete(`/activation-codes/${a}`,"激活码删除成功",{data:e||{}})).data};i.getAll,i.generate,i.create,i.batchGenerate,i.disable,i.enable},88555:(a,e,t)=>{"use strict";t.d(e,{m9:()=>n});var s=t(49895);let i=a=>({id:a.id||"",name:a.name||"",description:a.description||"",color:a.color||"#1890ff",isVip:!!a.isVip,status:a.status||"active",icon:a.icon,createdAt:a.createdAt||new Date().toISOString(),updatedAt:a.updatedAt||new Date().toISOString()}),n={getAll:async()=>((await s.Ay.get("/tags")).data||[]).map(i),getById:async a=>i((await s.Ay.get(`/tags/${a}`)).data),create:async a=>i((await s.Ay.post("/tags",a)).data),update:async(a,e)=>i((await s.Ay.put(`/tags/${a}`,e)).data),delete:async a=>{await s.Ay.delete(`/tags/${a}`)}};n.getAll,n.create,n.update,n.delete},89900:(a,e,t)=>{"use strict";t.d(e,{r:()=>i});var s=t(49895);let i={getAll:async a=>(await s.Ay.get("/user-favorites",{params:a})).data,exportData:async a=>(await s.Ay.get("/user-favorites/export",{params:a,responseType:"blob"})).data};i.getAll},90134:(a,e,t)=>{"use strict";t.d(e,{f:()=>i});var s=t(49895);let i={getAll:async()=>(await s.Ay.get("/settings")).data,getById:async a=>(await s.Ay.get(`/settings/${a}`)).data,getByKey:async a=>(await s.Ay.get(`/settings/key/${a}`)).data,update:async(a,e)=>(await s.Ay.patch(`/settings/${a}`,e)).data,updateByKey:async(a,e)=>(await s.Ay.patch(`/settings/key/${a}`,{value:e})).data,async initializeDefaults(){await s.Ay.post("/settings/initialize")},async updateAppConfig(a){let e=[];void 0!==a.helpUrl&&e.push(this.updateByKey("help_url",a.helpUrl)),void 0!==a.backgroundMusicUrl&&e.push(this.updateByKey("background_music_url",a.backgroundMusicUrl)),await Promise.all(e)},async getAppConfig(){try{let[a,e]=await Promise.all([this.getByKey("help_url").catch(()=>null),this.getByKey("background_music_url").catch(()=>null)]);return{helpUrl:a?.value||"",backgroundMusicUrl:e?.value||""}}catch(a){return console.error("获取小程序配置失败:",a),{helpUrl:"",backgroundMusicUrl:""}}},async testWeixinAppSettings(){try{return(await s.Ay.get("/weixin/app-settings")).data}catch(a){throw console.error("测试微信小程序设置接口失败:",a),a}},async testWeixinGlobalConfig(){try{return(await s.Ay.get("/weixin/global-config")).data}catch(a){throw console.error("测试微信小程序全局配置接口失败:",a),a}}}}};