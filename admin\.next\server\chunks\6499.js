"use strict";exports.id=6499,exports.ids=[6499],exports.modules={8662:(e,n,t)=>{t.d(n,{A:()=>I});var a=t(43210),i=t(96201),l=t(53428),r=t(56883),c=t(69662),o=t.n(c),d=t(44666),s=t(71802),u=t(37510);let m=e=>{let n,{value:t,formatter:i,precision:l,decimalSeparator:r,groupSeparator:c="",prefixCls:o}=e;if("function"==typeof i)n=i(t);else{let e=String(t),i=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(i&&"-"!==e){let e=i[1],t=i[2]||"0",d=i[4]||"";t=t.replace(/\B(?=(\d{3})+(?!\d))/g,c),"number"==typeof l&&(d=d.padEnd(l,"0").slice(0,l>0?l:0)),d&&(d=`${r}${d}`),n=[a.createElement("span",{key:"int",className:`${o}-content-value-int`},e,t),d&&a.createElement("span",{key:"decimal",className:`${o}-content-value-decimal`},d)]}else n=e}return a.createElement("span",{className:`${o}-content-value`},n)};var g=t(32476),h=t(13581),p=t(60254);let $=e=>{let{componentCls:n,marginXXS:t,padding:a,colorTextDescription:i,titleFontSize:l,colorTextHeading:r,contentFontSize:c,fontFamily:o}=e;return{[n]:Object.assign(Object.assign({},(0,g.dF)(e)),{[`${n}-title`]:{marginBottom:t,color:i,fontSize:l},[`${n}-skeleton`]:{paddingTop:a},[`${n}-content`]:{color:r,fontSize:c,fontFamily:o,[`${n}-content-value`]:{display:"inline-block",direction:"ltr"},[`${n}-content-prefix, ${n}-content-suffix`]:{display:"inline-block"},[`${n}-content-prefix`]:{marginInlineEnd:t},[`${n}-content-suffix`]:{marginInlineStart:t}}})}},f=(0,h.OF)("Statistic",e=>[$((0,p.oX)(e,{}))],e=>{let{fontSizeHeading3:n,fontSize:t}=e;return{titleFontSize:t,contentFontSize:n}});var b=function(e,n){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>n.indexOf(a)&&(t[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>n.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(t[a[i]]=e[a[i]]);return t};let S=a.forwardRef((e,n)=>{let{prefixCls:t,className:i,rootClassName:l,style:r,valueStyle:c,value:g=0,title:h,valueRender:p,prefix:$,suffix:S,loading:v=!1,formatter:k,precision:w,decimalSeparator:y=".",groupSeparator:I=",",onMouseEnter:E,onMouseLeave:O}=e,x=b(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:C,direction:A,className:j,style:z}=(0,s.TP)("statistic"),M=C("statistic",t),[N,q,D]=f(M),H=a.createElement(m,{decimalSeparator:y,groupSeparator:I,prefixCls:M,formatter:k,precision:w,value:g}),L=o()(M,{[`${M}-rtl`]:"rtl"===A},j,i,l,q,D),T=a.useRef(null);a.useImperativeHandle(n,()=>({nativeElement:T.current}));let P=(0,d.A)(x,{aria:!0,data:!0});return N(a.createElement("div",Object.assign({},P,{ref:T,className:L,style:Object.assign(Object.assign({},z),r),onMouseEnter:E,onMouseLeave:O}),h&&a.createElement("div",{className:`${M}-title`},h),a.createElement(u.A,{paragraph:!1,loading:v,className:`${M}-skeleton`},a.createElement("div",{style:c,className:`${M}-content`},$&&a.createElement("span",{className:`${M}-content-prefix`},$),p?p(H):H,S&&a.createElement("span",{className:`${M}-content-suffix`},S)))))}),v=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var k=function(e,n){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>n.indexOf(a)&&(t[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>n.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(t[a[i]]=e[a[i]]);return t};let w=e=>{let{value:n,format:t="HH:mm:ss",onChange:c,onFinish:o,type:d}=e,s=k(e,["value","format","onChange","onFinish","type"]),u="countdown"===d,[m,g]=a.useState(null),h=(0,i._q)(()=>{let e=Date.now(),t=new Date(n).getTime();return g({}),null==c||c(u?t-e:e-t),!u||!(t<e)||(null==o||o(),!1)});return a.useEffect(()=>{let e,n=()=>{e=(0,l.A)(()=>{h()&&n()})};return n(),()=>l.A.cancel(e)},[n,u]),a.useEffect(()=>{g({})},[]),a.createElement(S,Object.assign({},s,{value:n,valueRender:e=>(0,r.Ob)(e,{title:void 0}),formatter:(e,n)=>m?function(e,n,t){let{format:a=""}=n,i=new Date(e).getTime(),l=Date.now();return function(e,n){let t=e,a=/\[[^\]]*]/g,i=(n.match(a)||[]).map(e=>e.slice(1,-1)),l=n.replace(a,"[]"),r=v.reduce((e,[n,a])=>{if(e.includes(n)){let i=Math.floor(t/a);return t-=i*a,e.replace(RegExp(`${n}+`,"g"),e=>{let n=e.length;return i.toString().padStart(n,"0")})}return e},l),c=0;return r.replace(a,()=>{let e=i[c];return c+=1,e})}(t?Math.max(i-l,0):Math.max(l-i,0),a)}(e,Object.assign(Object.assign({},n),{format:t}),u):"-"}))},y=a.memo(e=>a.createElement(w,Object.assign({},e,{type:"countdown"})));S.Timer=w,S.Countdown=y;let I=S},59823:(e,n,t)=>{t.d(n,{A:()=>M});var a=t(43210),i=t(39759),l=t(69662),r=t.n(l),c=t(80828),o=t(95243),d=t(82853),s=t(78135),u=t(28344),m=t(2291),g=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],h=a.forwardRef(function(e,n){var t,i=e.prefixCls,l=void 0===i?"rc-switch":i,h=e.className,p=e.checked,$=e.defaultChecked,f=e.disabled,b=e.loadingIcon,S=e.checkedChildren,v=e.unCheckedChildren,k=e.onClick,w=e.onChange,y=e.onKeyDown,I=(0,s.A)(e,g),E=(0,u.A)(!1,{value:p,defaultValue:$}),O=(0,d.A)(E,2),x=O[0],C=O[1];function A(e,n){var t=x;return f||(C(t=e),null==w||w(t,n)),t}var j=r()(l,h,(t={},(0,o.A)(t,"".concat(l,"-checked"),x),(0,o.A)(t,"".concat(l,"-disabled"),f),t));return a.createElement("button",(0,c.A)({},I,{type:"button",role:"switch","aria-checked":x,disabled:f,className:j,ref:n,onKeyDown:function(e){e.which===m.A.LEFT?A(!1,e):e.which===m.A.RIGHT&&A(!0,e),null==y||y(e)},onClick:function(e){var n=A(!x,e);null==k||k(n,e)}}),b,a.createElement("span",{className:"".concat(l,"-inner")},a.createElement("span",{className:"".concat(l,"-inner-checked")},S),a.createElement("span",{className:"".concat(l,"-inner-unchecked")},v)))});h.displayName="Switch";var p=t(17727),$=t(71802),f=t(57026),b=t(40908),S=t(42411),v=t(73117),k=t(32476),w=t(13581),y=t(60254);let I=e=>{let{componentCls:n,trackHeightSM:t,trackPadding:a,trackMinWidthSM:i,innerMinMarginSM:l,innerMaxMarginSM:r,handleSizeSM:c,calc:o}=e,d=`${n}-inner`,s=(0,S.zA)(o(c).add(o(a).mul(2)).equal()),u=(0,S.zA)(o(r).mul(2).equal());return{[n]:{[`&${n}-small`]:{minWidth:i,height:t,lineHeight:(0,S.zA)(t),[`${n}-inner`]:{paddingInlineStart:r,paddingInlineEnd:l,[`${d}-checked, ${d}-unchecked`]:{minHeight:t},[`${d}-checked`]:{marginInlineStart:`calc(-100% + ${s} - ${u})`,marginInlineEnd:`calc(100% - ${s} + ${u})`},[`${d}-unchecked`]:{marginTop:o(t).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${n}-handle`]:{width:c,height:c},[`${n}-loading-icon`]:{top:o(o(c).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${n}-checked`]:{[`${n}-inner`]:{paddingInlineStart:l,paddingInlineEnd:r,[`${d}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${d}-unchecked`]:{marginInlineStart:`calc(100% - ${s} + ${u})`,marginInlineEnd:`calc(-100% + ${s} - ${u})`}},[`${n}-handle`]:{insetInlineStart:`calc(100% - ${(0,S.zA)(o(c).add(a).equal())})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${d}`]:{[`${d}-unchecked`]:{marginInlineStart:o(e.marginXXS).div(2).equal(),marginInlineEnd:o(e.marginXXS).mul(-1).div(2).equal()}},[`&${n}-checked ${d}`]:{[`${d}-checked`]:{marginInlineStart:o(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:o(e.marginXXS).div(2).equal()}}}}}}},E=e=>{let{componentCls:n,handleSize:t,calc:a}=e;return{[n]:{[`${n}-loading-icon${e.iconCls}`]:{position:"relative",top:a(a(t).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${n}-checked ${n}-loading-icon`]:{color:e.switchColor}}}},O=e=>{let{componentCls:n,trackPadding:t,handleBg:a,handleShadow:i,handleSize:l,calc:r}=e,c=`${n}-handle`;return{[n]:{[c]:{position:"absolute",top:t,insetInlineStart:t,width:l,height:l,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:r(l).div(2).equal(),boxShadow:i,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${n}-checked ${c}`]:{insetInlineStart:`calc(100% - ${(0,S.zA)(r(l).add(t).equal())})`},[`&:not(${n}-disabled):active`]:{[`${c}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${n}-checked ${c}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},x=e=>{let{componentCls:n,trackHeight:t,trackPadding:a,innerMinMargin:i,innerMaxMargin:l,handleSize:r,calc:c}=e,o=`${n}-inner`,d=(0,S.zA)(c(r).add(c(a).mul(2)).equal()),s=(0,S.zA)(c(l).mul(2).equal());return{[n]:{[o]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:l,paddingInlineEnd:i,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${o}-checked, ${o}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:t},[`${o}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${s})`,marginInlineEnd:`calc(100% - ${d} + ${s})`},[`${o}-unchecked`]:{marginTop:c(t).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${n}-checked ${o}`]:{paddingInlineStart:i,paddingInlineEnd:l,[`${o}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${o}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${s})`,marginInlineEnd:`calc(-100% + ${d} - ${s})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${o}`]:{[`${o}-unchecked`]:{marginInlineStart:c(a).mul(2).equal(),marginInlineEnd:c(a).mul(-1).mul(2).equal()}},[`&${n}-checked ${o}`]:{[`${o}-checked`]:{marginInlineStart:c(a).mul(-1).mul(2).equal(),marginInlineEnd:c(a).mul(2).equal()}}}}}},C=e=>{let{componentCls:n,trackHeight:t,trackMinWidth:a}=e;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,k.dF)(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:t,lineHeight:(0,S.zA)(t),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${n}-disabled)`]:{background:e.colorTextTertiary}}),(0,k.K8)(e)),{[`&${n}-checked`]:{background:e.switchColor,[`&:hover:not(${n}-disabled)`]:{background:e.colorPrimaryHover}},[`&${n}-loading, &${n}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${n}-rtl`]:{direction:"rtl"}})}},A=(0,w.OF)("Switch",e=>{let n=(0,y.oX)(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[C(n),x(n),O(n),E(n),I(n)]},e=>{let{fontSize:n,lineHeight:t,controlHeight:a,colorWhite:i}=e,l=n*t,r=a/2,c=l-4,o=r-4;return{trackHeight:l,trackHeightSM:r,trackMinWidth:2*c+8,trackMinWidthSM:2*o+4,trackPadding:2,handleBg:i,handleSize:c,handleSizeSM:o,handleShadow:`0 2px 4px 0 ${new v.Y("#00230b").setA(.2).toRgbString()}`,innerMinMargin:c/2,innerMaxMargin:c+2+4,innerMinMarginSM:o/2,innerMaxMarginSM:o+2+4}});var j=function(e,n){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>n.indexOf(a)&&(t[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>n.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(t[a[i]]=e[a[i]]);return t};let z=a.forwardRef((e,n)=>{let{prefixCls:t,size:l,disabled:c,loading:o,className:d,rootClassName:s,style:m,checked:g,value:S,defaultChecked:v,defaultValue:k,onChange:w}=e,y=j(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[I,E]=(0,u.A)(!1,{value:null!=g?g:S,defaultValue:null!=v?v:k}),{getPrefixCls:O,direction:x,switch:C}=a.useContext($.QO),z=a.useContext(f.A),M=(null!=c?c:z)||o,N=O("switch",t),q=a.createElement("div",{className:`${N}-handle`},o&&a.createElement(i.A,{className:`${N}-loading-icon`})),[D,H,L]=A(N),T=(0,b.A)(l),P=r()(null==C?void 0:C.className,{[`${N}-small`]:"small"===T,[`${N}-loading`]:o,[`${N}-rtl`]:"rtl"===x},d,s,H,L),R=Object.assign(Object.assign({},null==C?void 0:C.style),m);return D(a.createElement(p.A,{component:"Switch"},a.createElement(h,Object.assign({},y,{checked:I,onChange:(...e)=>{E(e[0]),null==w||w.apply(void 0,e)},prefixCls:N,className:P,style:R,disabled:M,ref:n,loadingIcon:q}))))});z.__ANT_SWITCH=!0;let M=z},94858:(e,n,t)=>{t.d(n,{A:()=>c});var a=t(80828),i=t(43210);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};var r=t(21898);let c=i.forwardRef(function(e,n){return i.createElement(r.A,(0,a.A)({},e,{ref:n,icon:l}))})}};