import React from 'react';

// 分享配置类型定义
export interface ShareConfig {
  id: string;
  name: string;
  title: string;
  path: string;
  imageUrl?: string;
  description?: string;
  type: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// 创建分享配置请求
export interface CreateShareConfigRequest {
  name: string;
  title: string;
  path: string;
  imageUrl?: string;
  description?: string;
  type?: string;
  isActive?: boolean;
  sortOrder?: number;
}

// 更新分享配置请求
export interface UpdateShareConfigRequest {
  name?: string;
  title?: string;
  path?: string;
  imageUrl?: string;
  description?: string;
  type?: string;
  isActive?: boolean;
  sortOrder?: number;
}

// 分享配置响应
export interface ShareConfigResponse {
  id: string;
  name: string;
  title: string;
  path: string;
  imageUrl?: string;
  description?: string;
  type: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// 分享类型选项
export interface ShareTypeOption {
  value: string;
  label: string;
  description: string;
}

// 分享配置表格列
export interface ShareConfigTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  render?: (value: unknown, record: ShareConfig) => React.ReactNode;
}

// 分享配置表单字段
export interface ShareConfigFormData {
  name: string;
  title: string;
  path: string;
  imageUrl?: string;
  description?: string;
  type: string;
  isActive: boolean;
  sortOrder: number;
}
