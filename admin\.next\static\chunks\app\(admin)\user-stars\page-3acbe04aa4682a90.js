(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1771],{15125:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(79630),r=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};var n=s(62764);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},16913:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(79630),r=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var n=s(62764);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},19361:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=s(90510).A},23130:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(79630),r=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};var n=s(62764);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},31592:(e,t,s)=>{Promise.resolve().then(s.bind(s,59433))},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}}),s.o(a,"useServerInsertedHTML")&&s.d(t,{useServerInsertedHTML:function(){return a.useServerInsertedHTML}})},36449:()=>{},41008:(e,t,s)=>{"use strict";s.d(t,{i:()=>a});let a={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},59433:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>L});var a=s(95155),r=s(12115),l=s(20778),n=s(24971),o=s(19868),i=s(12320),c=s(59474),d=s(26922),u=s(77325),h=s(16467),m=s(6124),v=s(19361),p=s(74947),g=s(56020),f=s(51087),x=s(64413),y=s(23130),j=s(15125),w=s(34140),A=s(16913),k=s(93156),b=s(35695);let{Option:S}=l.A,{RangePicker:T}=n.A;function C(){let e=(0,b.useRouter)(),t=(0,b.useSearchParams)(),[s,n]=(0,r.useState)([]),[C,L]=(0,r.useState)(!1),[I,z]=(0,r.useState)(null),[R,M]=(0,r.useState)(void 0),[E,U]=(0,r.useState)(void 0),[P,N]=(0,r.useState)(void 0),[_,D]=(0,r.useState)({current:1,pageSize:20,total:0,totalPages:0}),O=async e=>{L(!0);try{let t=await k.f.getAll({...e,page:(null==e?void 0:e.page)||_.current,pageSize:(null==e?void 0:e.pageSize)||_.pageSize});n(t.data||[]),D({current:t.page,pageSize:t.pageSize,total:t.total,totalPages:t.totalPages})}catch(e){console.error("Error fetching stars:",e),n([{id:"1",userId:"user1",levelId:"level1",stars:5,completedAt:"2024-01-10T10:30:00Z",playTime:120,user:{id:"user1",nickname:"张三",avatar:""},level:{id:"level1",title:"基础词汇练习1",difficulty:1}},{id:"2",userId:"user2",levelId:"level1",stars:3,completedAt:"2024-01-10T11:15:00Z",playTime:180,user:{id:"user2",nickname:"李四",avatar:""},level:{id:"level1",title:"基础词汇练习1",difficulty:1}},{id:"3",userId:"user3",levelId:"level2",stars:4,completedAt:"2024-01-10T14:20:00Z",playTime:150,user:{id:"user3",nickname:"王五",avatar:""},level:{id:"level2",title:"商务英语入门",difficulty:2}}])}finally{L(!1)}};(0,r.useEffect)(()=>{let e=t.get("levelId");e&&M(e),O()},[]),(0,r.useEffect)(()=>{R&&B()},[R]);let B=()=>{let e={page:1};I&&(e.startDate=I[0],e.endDate=I[1]),R&&(e.levelId=R),E&&(e.stars=E),P&&(e.userId=P),O(e)},Y=async()=>{try{let e={startDate:null==I?void 0:I[0],endDate:null==I?void 0:I[1],levelId:R,stars:E,userId:P,format:"excel"},t=await k.f.exportData(e),s=window.URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download="user-stars-".concat(new Date().toISOString().split("T")[0],".xlsx"),document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s),o.Ay.success("导出成功")}catch(e){console.error("Export error:",e),o.Ay.error("导出失败")}},H=(e,t)=>{let s={page:e,pageSize:t||_.pageSize};I&&(s.startDate=I[0],s.endDate=I[1]),R&&(s.levelId=R),E&&(s.stars=E),P&&(s.userId=P),O(s)},V=(e,t)=>{let s=e/t*100;return(0,a.jsxs)(i.A,{children:[(0,a.jsxs)("span",{children:[e,"/",t]}),(0,a.jsx)("div",{style:{display:"flex",gap:"2px"},children:Array.from({length:t},(t,s)=>(0,a.jsx)(x.A,{style:{color:s<e?"#fadb14":"#d9d9d9",fontSize:"14px"}},s))}),(0,a.jsx)(c.A,{percent:s,size:"small",showInfo:!1,strokeColor:s>=80?"#52c41a":s>=60?"#faad14":"#ff4d4f"})]})},F=[{title:"用户",key:"user",render:(e,t)=>{var s;return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{children:null==(s=t.user)?void 0:s.nickname}),(0,a.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",t.userId]})]})})}},{title:"关卡",key:"level",render:(t,s)=>{var r;return(0,a.jsxs)(i.A,{children:[(0,a.jsx)("span",{children:null==(r=s.level)?void 0:r.title}),(0,a.jsx)(d.A,{title:"跳转到关卡管理",children:(0,a.jsx)(u.Ay,{type:"link",size:"small",icon:(0,a.jsx)(y.A,{}),onClick:()=>e.push("/levels?highlight=".concat(s.levelId))})})]})}},{title:"星级评分",key:"stars",render:(e,t)=>V(t.stars,5)},{title:"游戏时长",dataIndex:"playTime",key:"playTime",render:e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))}},{title:"完成时间",dataIndex:"completedAt",key:"completedAt",render:e=>new Date(e).toLocaleString()}];return C&&0===s.length?(0,a.jsxs)("div",{style:{textAlign:"center",padding:"100px 0"},children:[(0,a.jsx)(h.A,{size:"large"}),(0,a.jsx)("div",{style:{marginTop:16},children:"加载星级数据中..."})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{style:{marginBottom:24},children:[(0,a.jsx)("h2",{children:"星级管理"}),(0,a.jsx)("p",{children:"查看和分析用户的关卡完成星级评分"})]}),(0,a.jsx)(m.A,{size:"small",style:{marginBottom:16},children:(0,a.jsxs)(v.A,{gutter:16,align:"middle",children:[(0,a.jsx)(p.A,{span:5,children:(0,a.jsx)(T,{value:I?[I[0],I[1]]:null,onChange:e=>{e?z([e[0].format("YYYY-MM-DD"),e[1].format("YYYY-MM-DD")]):z(null)},placeholder:["开始日期","结束日期"],style:{width:"100%"}})}),(0,a.jsx)(p.A,{span:4,children:(0,a.jsxs)(l.A,{placeholder:"选择关卡",value:R,onChange:M,allowClear:!0,style:{width:"100%"},children:[(0,a.jsx)(S,{value:"level1",children:"基础词汇练习1"}),(0,a.jsx)(S,{value:"level2",children:"商务英语入门"})]})}),(0,a.jsx)(p.A,{span:3,children:(0,a.jsxs)(l.A,{placeholder:"星级筛选",value:E,onChange:U,allowClear:!0,style:{width:"100%"},children:[(0,a.jsx)(S,{value:1,children:"1星"}),(0,a.jsx)(S,{value:2,children:"2星"}),(0,a.jsx)(S,{value:3,children:"3星"}),(0,a.jsx)(S,{value:4,children:"4星"}),(0,a.jsx)(S,{value:5,children:"5星"})]})}),(0,a.jsx)(p.A,{span:4,children:(0,a.jsx)(g.A,{placeholder:"用户ID",value:P,onChange:e=>N(e.target.value||void 0),allowClear:!0,style:{width:"100%"}})}),(0,a.jsx)(p.A,{span:8,children:(0,a.jsxs)(i.A,{children:[(0,a.jsx)(u.Ay,{type:"primary",icon:(0,a.jsx)(j.A,{}),onClick:B,children:"筛选"}),(0,a.jsx)(u.Ay,{icon:(0,a.jsx)(w.A,{}),onClick:()=>{z(null),M(void 0),U(void 0),N(void 0),O({page:1})},children:"重置"}),(0,a.jsx)(u.Ay,{icon:(0,a.jsx)(A.A,{}),onClick:Y,children:"导出"})]})})]})}),(0,a.jsx)(m.A,{children:(0,a.jsx)(f.A,{columns:F,dataSource:s,rowKey:"id",loading:C,pagination:{current:_.current,pageSize:_.pageSize,total:_.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条，共 ").concat(e," 条记录"),onChange:H,onShowSizeChange:H,pageSizeOptions:["10","20","50","100"]}})})]})}function L(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(C,{})})}},64413:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(79630),r=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};var n=s(62764);let o=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:l}))})},73629:()=>{},74947:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=s(62623).A},83899:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>c,FH:()=>o,KY:()=>i});var a=s(23464),r=s(90285),l=s(41008);let n=a.A.create({baseURL:l.i.FULL_BASE_URL,timeout:l.i.TIMEOUT,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{var t;let s=localStorage.getItem("admin_token");return s&&(e.headers.Authorization="Bearer ".concat(s)),console.log("\uD83D\uDE80 发送请求:",{method:null==(t=e.method)?void 0:t.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),n.interceptors.response.use(e=>{var t;let s=e.config;return console.log("✅ 请求成功:",{method:null==(t=s.method)?void 0:t.toUpperCase(),url:s.url,status:e.status,statusText:e.statusText,data:e.data}),s.showSuccess&&s.successMessage&&r.i.success(s.successMessage),e},e=>{var t,s,a,l,n,o,i,c,d;console.error("❌ 请求失败:",{method:null==(s=e.config)||null==(t=s.method)?void 0:t.toUpperCase(),url:null==(a=e.config)?void 0:a.url,baseURL:null==(l=e.config)?void 0:l.baseURL,fullURL:"".concat(null==(n=e.config)?void 0:n.baseURL).concat(null==(o=e.config)?void 0:o.url),status:null==(i=e.response)?void 0:i.status,statusText:null==(c=e.response)?void 0:c.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:t,data:s}=e.response,a="";switch(t){case 401:a="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a=(null==s?void 0:s.message)||"没有权限访问该资源";break;case 404:a=(null==s?void 0:s.message)||"请求的资源不存在";break;case 422:a=(null==s?void 0:s.message)||"请求参数验证失败";break;case 500:a=(null==s?void 0:s.message)||"服务器内部错误";break;default:a=(null==s?void 0:s.message)||"请求失败 (".concat(t,")")}r.i.error(a)}else e.request?r.i.error("网络连接失败，请检查网络"):r.i.error("请求配置错误");return Promise.reject(e)});let o={get:(e,t)=>n.get(e,t),post:(e,t,s)=>n.post(e,t,s),put:(e,t,s)=>n.put(e,t,s),patch:(e,t,s)=>n.patch(e,t,s),delete:(e,t)=>n.delete(e,t)},i={post:(e,t,s,a)=>o.post(e,t,{...a,showSuccess:!0,successMessage:s||"操作成功"}),put:(e,t,s,a)=>o.put(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),patch:(e,t,s,a)=>o.patch(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),delete:(e,t,s)=>o.delete(e,{...s,showSuccess:!0,successMessage:t||"删除成功"})},c=o},90285:(e,t,s)=>{"use strict";s.d(t,{a:()=>i,i:()=>h});var a=s(95155),r=s(12115),l=s(12669);s(36449);let n=e=>{let{title:t,content:s,children:l,visible:n=!1,width:o=520,centered:i=!1,closable:c=!0,maskClosable:d=!0,footer:u,okText:h="确定",cancelText:m="取消",okType:v="primary",confirmLoading:p=!1,onOk:g,onCancel:f,afterClose:x,className:y="",style:j={}}=e,[w,A]=(0,r.useState)(n),[k,b]=(0,r.useState)(!1);(0,r.useEffect)(()=>{n?(A(!0),b(!0),document.body.style.overflow="hidden"):(b(!1),setTimeout(()=>{A(!1),document.body.style.overflow="",null==x||x()},300))},[n,x]);let S=async()=>{if(g)try{await g()}catch(e){console.error("Modal onOk error:",e)}},T=()=>{null==f||f()};return w?(0,a.jsx)("div",{className:"custom-modal-mask ".concat(k?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==f||f())},children:(0,a.jsx)("div",{className:"custom-modal-wrap ".concat(i?"custom-modal-centered":""),children:(0,a.jsxs)("div",{className:"custom-modal ".concat(y," ").concat(k?"custom-modal-show":"custom-modal-hide"),style:{width:o,...j},children:[(t||c)&&(0,a.jsxs)("div",{className:"custom-modal-header",children:[t&&(0,a.jsx)("div",{className:"custom-modal-title",children:t}),c&&(0,a.jsx)("button",{className:"custom-modal-close",onClick:T,"aria-label":"Close",children:"\xd7"})]}),(0,a.jsx)("div",{className:"custom-modal-body",children:s||l}),null===u?null:u||(0,a.jsxs)("div",{className:"custom-modal-footer",children:[m&&(0,a.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:T,children:m}),(0,a.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(v),onClick:S,disabled:p,children:[p&&(0,a.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),h]})]})]})})}):null};class o{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,l.createRoot)(this.container)),this.container}confirm(e){return new Promise((t,s)=>{let r=!1,l=async()=>{if(!r)try{e.onOk&&await e.onOk(),r=!0,this.destroy(),t()}catch(e){s(e)}};this.getContainer(),this.root.render((0,a.jsx)(n,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:l,onCancel:()=>{var t;r||(r=!0,null==(t=e.onCancel)||t.call(e),this.destroy(),s(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}n.confirm=e=>new o().confirm({...e,okType:e.okType||"primary"}),n.info=e=>new o().confirm({...e,okType:"primary",cancelText:void 0}),n.success=e=>new o().confirm({...e,okType:"primary",cancelText:void 0}),n.error=e=>new o().confirm({...e,okType:"danger",cancelText:void 0}),n.warning=e=>new o().confirm({...e,okType:"primary",cancelText:void 0});let i=n;s(73629);let c=e=>{let{messages:t}=e;return(0,a.jsx)("div",{className:"custom-message-container",children:t.map(e=>(0,a.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,a.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,a.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,l.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,a.jsx)(c,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var t;let s=e.key||this.generateId(),a=null!=(t=e.duration)?t:3e3;e.key&&(this.messages=this.messages.filter(t=>t.id!==e.key));let r={...e,id:s,visible:!0};return this.messages.push(r),this.getContainer(),this.render(),a>0&&setTimeout(()=>{this.hide(s)},a),s}hide(e){let t=this.messages.findIndex(t=>t.id===e);t>-1&&(this.messages[t].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(t=>t.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,h={success:(e,t)=>u.show({content:e,type:"success",duration:t}),error:(e,t)=>u.show({content:e,type:"error",duration:t}),warning:(e,t)=>u.show({content:e,type:"warning",duration:t}),info:(e,t)=>u.show({content:e,type:"info",duration:t}),destroy:()=>u.destroy()}},93156:(e,t,s)=>{"use strict";s.d(t,{f:()=>r});var a=s(83899);let r={getAll:async e=>(await a.Ay.get("/user-stars",{params:e})).data,exportData:async e=>(await a.Ay.get("/user-stars/export",{params:e,responseType:"blob"})).data};r.getAll}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,2343,1087,6226,9474,8441,1684,7358],()=>t(31592)),_N_E=e.O()}]);