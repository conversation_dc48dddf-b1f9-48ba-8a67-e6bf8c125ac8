(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{10202:e=>{e.exports={container:"LoginPage_container__uSVwT"}},35695:(e,s,t)=>{"use strict";var o=t(18999);t.o(o,"useParams")&&t.d(s,{useParams:function(){return o.useParams}}),t.o(o,"usePathname")&&t.d(s,{usePathname:function(){return o.usePathname}}),t.o(o,"useRouter")&&t.d(s,{useRouter:function(){return o.useRouter}}),t.o(o,"useSearchParams")&&t.d(s,{useSearchParams:function(){return o.useSearchParams}}),t.o(o,"useServerInsertedHTML")&&t.d(s,{useServerInsertedHTML:function(){return o.useServerInsertedHTML}})},36449:()=>{},41008:(e,s,t)=>{"use strict";t.d(s,{i:()=>o});let o={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},42993:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var o=t(95155),a=t(12115),n=t(19868),r=t(6124),c=t(97605),i=t(86615),l=t(56020),d=t(77325),u=t(50274),m=t(79630);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};var g=t(62764),p=a.forwardRef(function(e,s){return a.createElement(g.A,(0,m.A)({},e,{ref:s,icon:h}))}),v=t(35695),y=t(59959),f=t(10202),w=t.n(f);let x=()=>{let e=(0,v.useRouter)(),[s,t]=(0,a.useState)(!1),m=async s=>{t(!0);try{let t=await y.y.login(s);t.accessToken?(n.Ay.success("登录成功！"),y.y.setToken(t.accessToken),e.push("/dashboard")):n.Ay.error(t.message||"登录失败，请检查用户名或密码！")}catch(e){console.error("登录失败:",e)}finally{t(!1)}};return(0,o.jsx)("div",{className:w().container,children:(0,o.jsx)(r.A,{title:(0,o.jsx)(c.A.Title,{level:3,style:{textAlign:"center",marginBottom:0},children:"后台登录"}),style:{width:400},children:(0,o.jsxs)(i.A,{name:"admin_login",initialValues:{remember:!0},onFinish:m,children:[(0,o.jsx)(i.A.Item,{name:"username",rules:[{required:!0,message:"请输入用户名!"}],children:(0,o.jsx)(l.A,{prefix:(0,o.jsx)(u.A,{}),placeholder:"用户名 (例如: admin)"})}),(0,o.jsx)(i.A.Item,{name:"password",rules:[{required:!0,message:"请输入密码!"}],children:(0,o.jsx)(l.A.Password,{prefix:(0,o.jsx)(p,{}),placeholder:"密码 (例如: password123)"})}),(0,o.jsx)(i.A.Item,{children:(0,o.jsx)(d.Ay,{type:"primary",htmlType:"submit",style:{width:"100%"},loading:s,children:"登录"})})]})})})}},50274:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var o=t(79630),a=t(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var r=t(62764);let c=a.forwardRef(function(e,s){return a.createElement(r.A,(0,o.A)({},e,{ref:s,icon:n}))})},59959:(e,s,t)=>{"use strict";t.d(s,{y:()=>a});var o=t(83899);let a={login:async e=>(await o.Ay.post("/auth/login",e)).data,logout:()=>{localStorage.removeItem("admin_token"),window.location.href="/login"},getToken:()=>localStorage.getItem("admin_token"),setToken:e=>{localStorage.setItem("admin_token",e)},isLoggedIn:()=>!!localStorage.getItem("admin_token")};a.login},73629:()=>{},82038:(e,s,t)=>{Promise.resolve().then(t.bind(t,42993))},83899:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>l,FH:()=>c,KY:()=>i});var o=t(23464),a=t(90285),n=t(41008);let r=o.A.create({baseURL:n.i.FULL_BASE_URL,timeout:n.i.TIMEOUT,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{var s;let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),console.log("\uD83D\uDE80 发送请求:",{method:null==(s=e.method)?void 0:s.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),r.interceptors.response.use(e=>{var s;let t=e.config;return console.log("✅ 请求成功:",{method:null==(s=t.method)?void 0:s.toUpperCase(),url:t.url,status:e.status,statusText:e.statusText,data:e.data}),t.showSuccess&&t.successMessage&&a.i.success(t.successMessage),e},e=>{var s,t,o,n,r,c,i,l,d;console.error("❌ 请求失败:",{method:null==(t=e.config)||null==(s=t.method)?void 0:s.toUpperCase(),url:null==(o=e.config)?void 0:o.url,baseURL:null==(n=e.config)?void 0:n.baseURL,fullURL:"".concat(null==(r=e.config)?void 0:r.baseURL).concat(null==(c=e.config)?void 0:c.url),status:null==(i=e.response)?void 0:i.status,statusText:null==(l=e.response)?void 0:l.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:s,data:t}=e.response,o="";switch(s){case 401:o="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:o=(null==t?void 0:t.message)||"没有权限访问该资源";break;case 404:o=(null==t?void 0:t.message)||"请求的资源不存在";break;case 422:o=(null==t?void 0:t.message)||"请求参数验证失败";break;case 500:o=(null==t?void 0:t.message)||"服务器内部错误";break;default:o=(null==t?void 0:t.message)||"请求失败 (".concat(s,")")}a.i.error(o)}else e.request?a.i.error("网络连接失败，请检查网络"):a.i.error("请求配置错误");return Promise.reject(e)});let c={get:(e,s)=>r.get(e,s),post:(e,s,t)=>r.post(e,s,t),put:(e,s,t)=>r.put(e,s,t),patch:(e,s,t)=>r.patch(e,s,t),delete:(e,s)=>r.delete(e,s)},i={post:(e,s,t,o)=>c.post(e,s,{...o,showSuccess:!0,successMessage:t||"操作成功"}),put:(e,s,t,o)=>c.put(e,s,{...o,showSuccess:!0,successMessage:t||"更新成功"}),patch:(e,s,t,o)=>c.patch(e,s,{...o,showSuccess:!0,successMessage:t||"更新成功"}),delete:(e,s,t)=>c.delete(e,{...t,showSuccess:!0,successMessage:s||"删除成功"})},l=c},90285:(e,s,t)=>{"use strict";t.d(s,{a:()=>i,i:()=>m});var o=t(95155),a=t(12115),n=t(12669);t(36449);let r=e=>{let{title:s,content:t,children:n,visible:r=!1,width:c=520,centered:i=!1,closable:l=!0,maskClosable:d=!0,footer:u,okText:m="确定",cancelText:h="取消",okType:g="primary",confirmLoading:p=!1,onOk:v,onCancel:y,afterClose:f,className:w="",style:x={}}=e,[k,b]=(0,a.useState)(r),[T,j]=(0,a.useState)(!1);(0,a.useEffect)(()=>{r?(b(!0),j(!0),document.body.style.overflow="hidden"):(j(!1),setTimeout(()=>{b(!1),document.body.style.overflow="",null==f||f()},300))},[r,f]);let S=async()=>{if(v)try{await v()}catch(e){console.error("Modal onOk error:",e)}},A=()=>{null==y||y()};return k?(0,o.jsx)("div",{className:"custom-modal-mask ".concat(T?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==y||y())},children:(0,o.jsx)("div",{className:"custom-modal-wrap ".concat(i?"custom-modal-centered":""),children:(0,o.jsxs)("div",{className:"custom-modal ".concat(w," ").concat(T?"custom-modal-show":"custom-modal-hide"),style:{width:c,...x},children:[(s||l)&&(0,o.jsxs)("div",{className:"custom-modal-header",children:[s&&(0,o.jsx)("div",{className:"custom-modal-title",children:s}),l&&(0,o.jsx)("button",{className:"custom-modal-close",onClick:A,"aria-label":"Close",children:"\xd7"})]}),(0,o.jsx)("div",{className:"custom-modal-body",children:t||n}),null===u?null:u||(0,o.jsxs)("div",{className:"custom-modal-footer",children:[h&&(0,o.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:A,children:h}),(0,o.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(g),onClick:S,disabled:p,children:[p&&(0,o.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),m]})]})]})})}):null};class c{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}confirm(e){return new Promise((s,t)=>{let a=!1,n=async()=>{if(!a)try{e.onOk&&await e.onOk(),a=!0,this.destroy(),s()}catch(e){t(e)}};this.getContainer(),this.root.render((0,o.jsx)(r,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:n,onCancel:()=>{var s;a||(a=!0,null==(s=e.onCancel)||s.call(e),this.destroy(),t(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}r.confirm=e=>new c().confirm({...e,okType:e.okType||"primary"}),r.info=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),r.success=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),r.error=e=>new c().confirm({...e,okType:"danger",cancelText:void 0}),r.warning=e=>new c().confirm({...e,okType:"primary",cancelText:void 0});let i=r;t(73629);let l=e=>{let{messages:s}=e;return(0,o.jsx)("div",{className:"custom-message-container",children:s.map(e=>(0,o.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,o.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,o.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,o.jsx)(l,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var s;let t=e.key||this.generateId(),o=null!=(s=e.duration)?s:3e3;e.key&&(this.messages=this.messages.filter(s=>s.id!==e.key));let a={...e,id:t,visible:!0};return this.messages.push(a),this.getContainer(),this.render(),o>0&&setTimeout(()=>{this.hide(t)},o),t}hide(e){let s=this.messages.findIndex(s=>s.id===e);s>-1&&(this.messages[s].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(s=>s.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,m={success:(e,s)=>u.show({content:e,type:"success",duration:s}),error:(e,s)=>u.show({content:e,type:"error",duration:s}),warning:(e,s)=>u.show({content:e,type:"warning",duration:s}),info:(e,s)=>u.show({content:e,type:"info",duration:s}),destroy:()=>u.destroy()}}},e=>{var s=s=>e(e.s=s);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6615,7605,8441,1684,7358],()=>s(82038)),_N_E=e.O()}]);