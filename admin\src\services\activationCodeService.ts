import { api, successApi } from "./request";

// 激活码数据类型（根据B端API文档）
export interface ActivationCode {
  code: string;
  packageId: string;
  packageName: string;
  status: string; // 'unused' | 'used' | 'expired' | 'disabled'
  maxRedemptions: number; // 最大兑换次数，-1表示无限次
  currentRedemptions: number; // 当前已兑换次数
  redemptionHistory?: string[]; // 兑换记录
  usedBy?: string; // 已废弃，保留兼容性
  usedAt?: string; // 已废弃，保留兼容性
  expireDate: string;
  source?: string;
  batchId?: string;
  createdAt: string;
  updatedAt: string;
}

// 生成激活码参数（根据B端API文档）
export interface GenerateActivationCodeParams {
  packageId: string;
  count: number;
  expireDate?: string;
  source?: string;
}

// 手动创建激活码参数
export interface CreateActivationCodeParams {
  customCode?: string; // 自定义激活码，如果不提供则自动生成
  packageId: string;
  expireDate?: string;
  source?: string;
  maxRedemptions?: number; // 最大兑换次数，-1表示无限次，默认为1
}

// 删除激活码参数
export interface DeleteActivationCodeParams {
  reason?: string; // 删除原因
}

// 批量生成激活码参数
export interface BatchGenerateParams {
  packageId: string;
  count: number;
  expireDate?: string;
  source?: string;
  prefix?: string; // 激活码前缀
  maxRedemptions?: number; // 最大兑换次数，-1表示无限次，默认为1
}

// 套餐数据类型
export interface Package {
  id: string;
  name: string;
  description?: string;
  type: string;
  duration: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 查询参数类型（根据B端API文档）
export interface ActivationCodeQueryParams {
  status?: "unused" | "used" | "expired" | "disabled";
  packageId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

// 激活码管理服务
export const activationCodeService = {
  // 获取所有激活码
  getAll: async (
    params?: ActivationCodeQueryParams
  ): Promise<{
    codes: ActivationCode[];
    total: number;
  }> => {
    const response = await api.get("/activation-codes", { params });
    return response.data;
  },

  // 根据激活码获取详情
  getByCode: async (code: string): Promise<ActivationCode> => {
    const response = await api.get<ActivationCode>(`/activation-codes/${code}`);
    return response.data;
  },

  // 生成激活码
  generate: async (
    params: GenerateActivationCodeParams
  ): Promise<{
    codes: string[];
    batchId: string;
    count: number;
    package: Package;
  }> => {
    const response = await successApi.post(
      "/activation-codes/generate",
      params,
      "激活码生成成功"
    );
    return response.data;
  },

  // 手动创建单个激活码
  create: async (
    params: CreateActivationCodeParams
  ): Promise<ActivationCode> => {
    const response = await successApi.post<ActivationCode>(
      "/activation-codes/create",
      params,
      "激活码创建成功"
    );
    return response.data;
  },

  // 批量生成激活码
  batchGenerate: async (
    params: BatchGenerateParams
  ): Promise<{
    codes: string[];
    batchId: string;
    count: number;
    package: Package;
    successCount: number;
    failedCount: number;
  }> => {
    const response = await successApi.post(
      "/activation-codes/generate",
      params,
      "批量生成激活码成功"
    );
    return response.data;
  },

  // 禁用激活码
  disable: async (code: string): Promise<void> => {
    await successApi.put(
      `/activation-codes/${code}/disable`,
      {},
      "激活码已禁用"
    );
  },

  // 启用激活码
  enable: async (code: string): Promise<void> => {
    await successApi.put(
      `/activation-codes/${code}/enable`,
      {},
      "激活码已启用"
    );
  },

  // 删除统计相关方法

  // 获取所有套餐（VIP套餐，用于激活码兑换）
  getAllPackages: async (): Promise<Package[]> => {
    const response = await api.get<Package[]>("/payment/packages");
    return response.data || [];
  },

  // 获取热门套餐
  getPopularPackages: async (limit?: number): Promise<Package[]> => {
    const response = await api.get<Package[]>("/payment/packages/popular", {
      params: { limit },
    });
    return response.data;
  },

  // 删除激活码
  delete: async (
    code: string,
    params?: DeleteActivationCodeParams
  ): Promise<{ message: string }> => {
    const response = await successApi.delete<{ message: string }>(
      `/activation-codes/${code}`,
      "激活码删除成功",
      {
        data: params || {},
      }
    );
    return response.data;
  },
};

// 保持向后兼容的导出
export const getActivationCodes = activationCodeService.getAll;
export const generateActivationCodes = activationCodeService.generate;
export const createActivationCode = activationCodeService.create;
export const batchGenerateActivationCodes = activationCodeService.batchGenerate;
export const disableActivationCode = activationCodeService.disable;
export const enableActivationCode = activationCodeService.enable;
