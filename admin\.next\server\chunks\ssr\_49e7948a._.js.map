{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/app/layout.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { AntdRegistry } from '@ant-design/nextjs-registry';\r\nimport '@/styles/globals.css'; // 导入全局样式\r\n\r\nconst RootLayout = ({ children }: React.PropsWithChildren) => (\r\n  <html lang=\"en\">\r\n    <head>\r\n      <meta charSet=\"utf-8\" />\r\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n      <meta name=\"description\" content=\"游戏后台管理系统\" />\r\n      <link rel=\"icon\" href=\"/favicon.ico\" />\r\n      <title>游戏管理后台</title>\r\n    </head>\r\n    <body>\r\n      <AntdRegistry>{children}</AntdRegistry>\r\n    </body>\r\n  </html>\r\n);\r\nexport default RootLayout;"], "names": [], "mappings": ";;;;AACA;AAAA;;;;AAGA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAA2B,iBACvD,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,SAAQ;;;;;;kCACd,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;kCAAM;;;;;;;;;;;;0BAET,8OAAC;0BACC,cAAA,8OAAC,sNAAA,CAAA,eAAY;8BAAE;;;;;;;;;;;;;;;;;uCAIN", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/node_modules/%40ant-design/nextjs-registry/es/AntdRegistry.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/node_modules/%40ant-design/nextjs-registry/es/AntdRegistry.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/node_modules/%40ant-design/nextjs-registry/es/index.js"], "sourcesContent": ["export { default as AntdRegistry } from \"./AntdRegistry\";"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}]}