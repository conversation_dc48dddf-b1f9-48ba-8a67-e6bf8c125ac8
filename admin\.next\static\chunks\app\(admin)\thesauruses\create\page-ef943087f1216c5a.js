(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[221],{21073:(e,s,t)=>{"use strict";t.d(s,{Au:()=>o,Io:()=>n,LQ:()=>r,zN:()=>c});var a=t(83899);let o={getAll:async()=>(await a.Ay.get("/thesauruses")).data,getById:async e=>(await a.Ay.get("/thesauruses/".concat(e))).data,create:async e=>(await a.Ay.post("/thesauruses",e)).data,update:async(e,s)=>(await a.Ay.patch("/thesauruses/".concat(e),s)).data,delete:async e=>{await a.Ay.delete("/thesauruses/".concat(e))},addPhrase:async(e,s)=>(await a.Ay.post("/thesauruses/".concat(e,"/phrases"),s)).data,removePhrase:async(e,s)=>(await a.Ay.delete("/thesauruses/".concat(e,"/phrases/").concat(s))).data},r=o.create;o.update,o.delete;let n=o.delete;o.getById,o.getAll;let c=o.getAll},35695:(e,s,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}}),t.o(a,"useServerInsertedHTML")&&t.d(s,{useServerInsertedHTML:function(){return a.useServerInsertedHTML}})},36449:()=>{},41008:(e,s,t)=>{"use strict";t.d(s,{i:()=>a});let a={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},44435:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(95155),o=t(12115),r=t(97605),n=t(86615),c=t(19868),i=t(6124),l=t(56020),u=t(77325),d=t(35695),m=t(21073);let{Title:h}=r.A,p=()=>{let[e]=n.A.useForm(),s=(0,d.useRouter)(),[t,r]=(0,o.useState)(!1),p=async e=>{r(!0);try{await (0,m.LQ)(e),c.Ay.success("词库创建成功！"),s.push("/thesauruses")}catch(e){c.Ay.error(e.message||"词库创建失败！")}finally{r(!1)}};return(0,a.jsxs)(i.A,{children:[(0,a.jsx)(h,{level:3,children:"创建新词库"}),(0,a.jsxs)(n.A,{form:e,layout:"vertical",onFinish:p,children:[(0,a.jsx)(n.A.Item,{name:"name",label:"词库名称",rules:[{required:!0,message:"请输入词库名称"}],children:(0,a.jsx)(l.A,{placeholder:"例如：日常用语"})}),(0,a.jsx)(n.A.Item,{name:"description",label:"描述 (可选)",children:(0,a.jsx)(l.A.TextArea,{rows:3,placeholder:"词库简介"})}),(0,a.jsxs)(n.A.Item,{children:[(0,a.jsx)(u.Ay,{type:"primary",htmlType:"submit",loading:t,children:"创建词库"}),(0,a.jsx)(u.Ay,{style:{marginLeft:8},onClick:()=>s.back(),children:"取消"})]})]})]})}},69765:(e,s,t)=>{Promise.resolve().then(t.bind(t,44435))},73629:()=>{},83899:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>l,FH:()=>c,KY:()=>i});var a=t(23464),o=t(90285),r=t(41008);let n=a.A.create({baseURL:r.i.FULL_BASE_URL,timeout:r.i.TIMEOUT,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{var s;let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),console.log("\uD83D\uDE80 发送请求:",{method:null==(s=e.method)?void 0:s.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),n.interceptors.response.use(e=>{var s;let t=e.config;return console.log("✅ 请求成功:",{method:null==(s=t.method)?void 0:s.toUpperCase(),url:t.url,status:e.status,statusText:e.statusText,data:e.data}),t.showSuccess&&t.successMessage&&o.i.success(t.successMessage),e},e=>{var s,t,a,r,n,c,i,l,u;console.error("❌ 请求失败:",{method:null==(t=e.config)||null==(s=t.method)?void 0:s.toUpperCase(),url:null==(a=e.config)?void 0:a.url,baseURL:null==(r=e.config)?void 0:r.baseURL,fullURL:"".concat(null==(n=e.config)?void 0:n.baseURL).concat(null==(c=e.config)?void 0:c.url),status:null==(i=e.response)?void 0:i.status,statusText:null==(l=e.response)?void 0:l.statusText,data:null==(u=e.response)?void 0:u.data,message:e.message});let d=e.config;if((null==d?void 0:d.showError)===!1)return Promise.reject(e);if(e.response){let{status:s,data:t}=e.response,a="";switch(s){case 401:a="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a=(null==t?void 0:t.message)||"没有权限访问该资源";break;case 404:a=(null==t?void 0:t.message)||"请求的资源不存在";break;case 422:a=(null==t?void 0:t.message)||"请求参数验证失败";break;case 500:a=(null==t?void 0:t.message)||"服务器内部错误";break;default:a=(null==t?void 0:t.message)||"请求失败 (".concat(s,")")}o.i.error(a)}else e.request?o.i.error("网络连接失败，请检查网络"):o.i.error("请求配置错误");return Promise.reject(e)});let c={get:(e,s)=>n.get(e,s),post:(e,s,t)=>n.post(e,s,t),put:(e,s,t)=>n.put(e,s,t),patch:(e,s,t)=>n.patch(e,s,t),delete:(e,s)=>n.delete(e,s)},i={post:(e,s,t,a)=>c.post(e,s,{...a,showSuccess:!0,successMessage:t||"操作成功"}),put:(e,s,t,a)=>c.put(e,s,{...a,showSuccess:!0,successMessage:t||"更新成功"}),patch:(e,s,t,a)=>c.patch(e,s,{...a,showSuccess:!0,successMessage:t||"更新成功"}),delete:(e,s,t)=>c.delete(e,{...t,showSuccess:!0,successMessage:s||"删除成功"})},l=c},90285:(e,s,t)=>{"use strict";t.d(s,{a:()=>i,i:()=>m});var a=t(95155),o=t(12115),r=t(12669);t(36449);let n=e=>{let{title:s,content:t,children:r,visible:n=!1,width:c=520,centered:i=!1,closable:l=!0,maskClosable:u=!0,footer:d,okText:m="确定",cancelText:h="取消",okType:p="primary",confirmLoading:y=!1,onOk:g,onCancel:v,afterClose:w,className:f="",style:b={}}=e,[x,k]=(0,o.useState)(n),[A,T]=(0,o.useState)(!1);(0,o.useEffect)(()=>{n?(k(!0),T(!0),document.body.style.overflow="hidden"):(T(!1),setTimeout(()=>{k(!1),document.body.style.overflow="",null==w||w()},300))},[n,w]);let j=async()=>{if(g)try{await g()}catch(e){console.error("Modal onOk error:",e)}},C=()=>{null==v||v()};return x?(0,a.jsx)("div",{className:"custom-modal-mask ".concat(A?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&u&&(null==v||v())},children:(0,a.jsx)("div",{className:"custom-modal-wrap ".concat(i?"custom-modal-centered":""),children:(0,a.jsxs)("div",{className:"custom-modal ".concat(f," ").concat(A?"custom-modal-show":"custom-modal-hide"),style:{width:c,...b},children:[(s||l)&&(0,a.jsxs)("div",{className:"custom-modal-header",children:[s&&(0,a.jsx)("div",{className:"custom-modal-title",children:s}),l&&(0,a.jsx)("button",{className:"custom-modal-close",onClick:C,"aria-label":"Close",children:"\xd7"})]}),(0,a.jsx)("div",{className:"custom-modal-body",children:t||r}),null===d?null:d||(0,a.jsxs)("div",{className:"custom-modal-footer",children:[h&&(0,a.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:C,children:h}),(0,a.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(p),onClick:j,disabled:y,children:[y&&(0,a.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),m]})]})]})})}):null};class c{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,r.createRoot)(this.container)),this.container}confirm(e){return new Promise((s,t)=>{let o=!1,r=async()=>{if(!o)try{e.onOk&&await e.onOk(),o=!0,this.destroy(),s()}catch(e){t(e)}};this.getContainer(),this.root.render((0,a.jsx)(n,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:r,onCancel:()=>{var s;o||(o=!0,null==(s=e.onCancel)||s.call(e),this.destroy(),t(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}n.confirm=e=>new c().confirm({...e,okType:e.okType||"primary"}),n.info=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),n.success=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),n.error=e=>new c().confirm({...e,okType:"danger",cancelText:void 0}),n.warning=e=>new c().confirm({...e,okType:"primary",cancelText:void 0});let i=n;t(73629);let l=e=>{let{messages:s}=e;return(0,a.jsx)("div",{className:"custom-message-container",children:s.map(e=>(0,a.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,a.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,a.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class u{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,r.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,a.jsx)(l,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var s;let t=e.key||this.generateId(),a=null!=(s=e.duration)?s:3e3;e.key&&(this.messages=this.messages.filter(s=>s.id!==e.key));let o={...e,id:t,visible:!0};return this.messages.push(o),this.getContainer(),this.render(),a>0&&setTimeout(()=>{this.hide(t)},a),t}hide(e){let s=this.messages.findIndex(s=>s.id===e);s>-1&&(this.messages[s].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(s=>s.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let d=new u,m={success:(e,s)=>d.show({content:e,type:"success",duration:s}),error:(e,s)=>d.show({content:e,type:"error",duration:s}),warning:(e,s)=>d.show({content:e,type:"warning",duration:s}),info:(e,s)=>d.show({content:e,type:"info",duration:s}),destroy:()=>d.destroy()}}},e=>{var s=s=>e(e.s=s);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6615,7605,8441,1684,7358],()=>s(69765)),_N_E=e.O()}]);