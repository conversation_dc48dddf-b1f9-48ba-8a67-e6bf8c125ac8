{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/globals.css"], "sourcesContent": ["/* antd/dist/reset.css 导入 Ant Design 的重置样式，确保样式一致性 */\r\n/* 在 Next.js 13+ App Router 中，通常在 layout.tsx 中导入 */\r\n/* 在 Pages Router 中，可以在 _app.tsx 中导入 */\r\n@import 'antd/dist/reset.css'; /* antd v5 使用 reset.css */\r\n\r\nbody {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}"], "names": [], "mappings": "AAKA"}}]}