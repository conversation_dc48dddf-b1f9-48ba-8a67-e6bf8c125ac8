(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2648],{37923:(e,t,s)=>{Promise.resolve().then(s.bind(s,41266))},41266:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var i=s(95155),n=s(12115),r=s(56020),l=s(24971),d=s(19868),o=s(37974),c=s(12320),a=s(26922),x=s(77325),h=s(6124),j=s(19361),p=s(74947),g=s(20778),A=s(51087),u=s(46002),m=s(73203),y=s(90765),v=s(31511),D=s(52092),f=s(34140),S=s(36020),Y=s(49179),_=s(30832),w=s.n(_);let{Search:C}=r.A,{RangePicker:E}=l.A,k=()=>{let[e,t]=(0,n.useState)([]),[s,r]=(0,n.useState)(!1),[l,_]=(0,n.useState)(!1),[k,z]=(0,n.useState)(null),[I,M]=(0,n.useState)({current:1,pageSize:20,total:0}),[N,B]=(0,n.useState)(""),[H,L]=(0,n.useState)(""),[O,F]=(0,n.useState)(null),b=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:I.current,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:I.pageSize;r(!0);try{let i={page:e,pageSize:s};N&&(i.search=N),H&&(i.status=H),O&&(i.startDate=O[0].format("YYYY-MM-DD"),i.endDate=O[1].format("YYYY-MM-DD"));let n=await Y.Yi.getList(i);t(n.orders),M({current:n.page,pageSize:n.pageSize,total:n.total})}catch(e){d.Ay.error("获取支付订单列表失败")}finally{r(!1)}};(0,n.useEffect)(()=>{b(1)},[N,H,O]),(0,n.useEffect)(()=>{b()},[]);let P=e=>{z(e),_(!0)},U=async()=>{try{await b(),d.Ay.success("订单列表已刷新")}catch(e){d.Ay.error("刷新订单列表失败")}},G=e=>{let t={PENDING:{color:"processing",text:"待支付",icon:(0,i.jsx)(m.A,{})},SUCCESS:{color:"success",text:"支付成功",icon:(0,i.jsx)(y.A,{})},FAILED:{color:"error",text:"支付失败",icon:(0,i.jsx)(v.A,{})},CANCELLED:{color:"default",text:"已取消",icon:(0,i.jsx)(v.A,{})},REFUNDED:{color:"warning",text:"已退款",icon:(0,i.jsx)(v.A,{})}},s=t[e]||t.FAILED;return(0,i.jsx)(o.A,{color:s.color,icon:s.icon,children:s.text})},R=[{title:"订单信息",key:"orderInfo",width:280,render:(e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{style:{fontWeight:"bold",marginBottom:4},children:t.description}),(0,i.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginBottom:2},children:["订单号: ",t.out_trade_no]}),t.transaction_id&&(0,i.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginBottom:2},children:["微信订单: ",t.transaction_id]}),(0,i.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:["用户ID: ",t.userId]})]})},{title:"金额",key:"amount",width:120,render:(e,t)=>(0,i.jsx)("div",{style:{textAlign:"right"},children:(0,i.jsxs)("div",{style:{fontSize:"16px",fontWeight:"bold",color:"#f50"},children:["\xa5",(t.total/100).toFixed(2)]})})},{title:"状态",key:"status",width:120,render:(e,t)=>G(t.status)},{title:"创建时间",key:"created_at",width:160,render:(e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:w()(t.created_at).format("YYYY-MM-DD")}),(0,i.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:w()(t.created_at).format("HH:mm:ss")})]})},{title:"支付时间",key:"paid_at",width:160,render:(e,t)=>t.paid_at?(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:w()(t.paid_at).format("YYYY-MM-DD")}),(0,i.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:w()(t.paid_at).format("HH:mm:ss")})]}):(0,i.jsx)("span",{style:{color:"#999"},children:"-"})},{title:"操作",key:"action",width:150,render:(e,t)=>(0,i.jsxs)(c.A,{size:"small",children:[(0,i.jsx)(a.A,{title:"查看详情",children:(0,i.jsx)(x.Ay,{type:"link",size:"small",icon:(0,i.jsx)(D.A,{}),onClick:()=>P(t),children:"详情"})}),"PENDING"===t.status&&(0,i.jsx)(a.A,{title:"刷新状态",children:(0,i.jsx)(x.Ay,{type:"link",size:"small",icon:(0,i.jsx)(f.A,{}),onClick:()=>U(),children:"刷新"})})]})}];return(0,i.jsxs)("div",{style:{padding:"24px"},children:[(0,i.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,i.jsxs)("h1",{style:{fontSize:"24px",fontWeight:"bold",margin:0},children:[(0,i.jsx)(S.A,{style:{color:"#52c41a",marginRight:"8px"}}),"支付订单管理"]}),(0,i.jsx)("p",{style:{color:"#666",margin:"8px 0 0 0"},children:"查看和管理所有支付订单，监控支付状态和统计数据"})]}),(0,i.jsx)(h.A,{style:{marginBottom:"16px"},children:(0,i.jsxs)(j.A,{gutter:16,align:"middle",children:[(0,i.jsx)(p.A,{span:8,children:(0,i.jsx)(C,{placeholder:"搜索订单号、用户ID或商品名称",allowClear:!0,onSearch:B,style:{width:"100%"}})}),(0,i.jsx)(p.A,{span:4,children:(0,i.jsxs)(g.A,{placeholder:"订单状态",allowClear:!0,style:{width:"100%"},value:H||void 0,onChange:L,children:[(0,i.jsx)(g.A.Option,{value:"PENDING",children:"待支付"}),(0,i.jsx)(g.A.Option,{value:"SUCCESS",children:"支付成功"}),(0,i.jsx)(g.A.Option,{value:"FAILED",children:"支付失败"}),(0,i.jsx)(g.A.Option,{value:"CANCELLED",children:"已取消"}),(0,i.jsx)(g.A.Option,{value:"REFUNDED",children:"已退款"})]})}),(0,i.jsx)(p.A,{span:8,children:(0,i.jsx)(E,{style:{width:"100%"},value:O,onChange:e=>F(e),placeholder:["开始日期","结束日期"]})}),(0,i.jsx)(p.A,{span:4,children:(0,i.jsx)(x.Ay,{icon:(0,i.jsx)(f.A,{}),onClick:()=>b(1),loading:s,style:{width:"100%"},children:"刷新"})})]})}),(0,i.jsx)(A.A,{columns:R,dataSource:e,rowKey:"id",loading:s,pagination:{current:I.current,pageSize:I.pageSize,total:I.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条，共 ").concat(e," 个订单"),onChange:(e,t)=>{b(e,t)},onShowSizeChange:(e,t)=>{b(1,t)}},scroll:{x:1200}}),(0,i.jsx)(u.A,{title:"订单详情",open:l,onCancel:()=>_(!1),footer:[(0,i.jsx)(x.Ay,{onClick:()=>_(!1),children:"关闭"},"close")],width:600,children:k&&(0,i.jsxs)("div",{children:[(0,i.jsxs)(j.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"订单ID:"})," ",k.id]})}),(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"状态:"})," ",G(k.status)]})})]}),(0,i.jsxs)(j.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"商户订单号:"})," ",k.out_trade_no]})}),(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"微信订单号:"})," ",k.transaction_id||"-"]})})]}),(0,i.jsxs)(j.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"用户ID:"})," ",k.userId]})}),(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"OpenID:"})," ",k.openid]})})]}),(0,i.jsxs)(j.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"商品描述:"})," ",k.description]})}),(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"VIP套餐ID:"})," ",k.vip_package_id]})})]}),(0,i.jsxs)(j.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"订单金额:"})," \xa5",(k.total/100).toFixed(2)]})}),(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"过期时间:"})," ",w()(k.expires_at).format("YYYY-MM-DD HH:mm:ss")]})})]}),(0,i.jsxs)(j.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"创建时间:"})," ",w()(k.created_at).format("YYYY-MM-DD HH:mm:ss")]})}),(0,i.jsx)(p.A,{span:12,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"支付时间:"})," ",k.paid_at?w()(k.paid_at).format("YYYY-MM-DD HH:mm:ss"):"-"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,2343,1087,404,6002,6226,6865,3884,9179,8441,1684,7358],()=>t(37923)),_N_E=e.O()}]);