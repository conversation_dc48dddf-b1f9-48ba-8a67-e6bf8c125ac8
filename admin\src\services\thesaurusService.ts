import request from './request';

// 词库数据类型
export interface Thesaurus {
  id: string;
  name: string;
  description?: string;
  phraseIds: string[];
  createdAt: string;
  updatedAt: string;
}

// 创建词库参数
export interface CreateThesaurusParams {
  name: string;
  description?: string;
}

// 更新词库参数
export interface UpdateThesaurusParams {
  name?: string;
  description?: string;
}

// 添加词组到词库参数
export interface AddPhraseToThesaurusParams {
  phraseId: string;
}

// 词库服务
export const thesaurusService = {
  // 获取所有词库
  getAll: async (): Promise<Thesaurus[]> => {
    const response = await request.get<Thesaurus[]>('/thesauruses');
    return response.data;
  },

  // 根据ID获取词库
  getById: async (id: string): Promise<Thesaurus> => {
    const response = await request.get<Thesaurus>(`/thesauruses/${id}`);
    return response.data;
  },

  // 创建词库
  create: async (params: CreateThesaurusParams): Promise<Thesaurus> => {
    const response = await request.post<Thesaurus>('/thesauruses', params);
    return response.data;
  },

  // 更新词库
  update: async (id: string, params: UpdateThesaurusParams): Promise<Thesaurus> => {
    const response = await request.patch<Thesaurus>(`/thesauruses/${id}`, params);
    return response.data;
  },

  // 删除词库
  delete: async (id: string): Promise<void> => {
    await request.delete(`/thesauruses/${id}`);
  },

  // 向词库添加词组
  addPhrase: async (thesaurusId: string, params: AddPhraseToThesaurusParams): Promise<Thesaurus> => {
    const response = await request.post<Thesaurus>(`/thesauruses/${thesaurusId}/phrases`, params);
    return response.data;
  },

  // 从词库移除词组
  removePhrase: async (thesaurusId: string, phraseId: string): Promise<Thesaurus> => {
    const response = await request.delete<Thesaurus>(`/thesauruses/${thesaurusId}/phrases/${phraseId}`);
    return response.data;
  },
};

// 导出便捷函数以保持向后兼容
export const createThesaurus = thesaurusService.create;
export const updateThesaurus = thesaurusService.update;
export const deleteThesaurus = thesaurusService.delete;
export const deleteThesaurusById = thesaurusService.delete; // 别名
export const getThesaurusById = thesaurusService.getById;
export const getAllThesauruses = thesaurusService.getAll;
export const getThesauruses = thesaurusService.getAll; // 别名
