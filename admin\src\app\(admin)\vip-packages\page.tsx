'use client';

import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, message, Popconfirm, Card, Tag, Statistic, Row, Col, Switch, InputNumber } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, CrownOutlined, GiftOutlined } from '@ant-design/icons';
import { vipPackageService } from '../../../services';

// VIP套餐数据类型
interface VipPackage {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 创建/更新VIP套餐参数
interface VipPackageParams {
  name: string;
  description: string;
  price: number;
  duration: number;
  sortOrder: number;
  isActive: boolean;
}

const VipPackagesPage: React.FC = () => {
  const [packages, setPackages] = useState<VipPackage[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPackage, setEditingPackage] = useState<VipPackage | null>(null);
  const [form] = Form.useForm();

  // 获取VIP套餐列表
  const fetchPackages = async () => {
    setLoading(true);
    try {
      const result = await vipPackageService.getList();
      setPackages(result || []);
    } catch {
      message.error('获取VIP套餐列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
  }, []);

  // 处理创建/编辑VIP套餐
  const handleSubmit = async (values: VipPackageParams) => {
    try {
      if (editingPackage) {
        await vipPackageService.update(editingPackage.id, values);
        message.success('VIP套餐更新成功');
      } else {
        await vipPackageService.create(values);
        message.success('VIP套餐创建成功');
      }
      setModalVisible(false);
      setEditingPackage(null);
      form.resetFields();
      fetchPackages();
    } catch {
      message.error(editingPackage ? '更新VIP套餐失败' : '创建VIP套餐失败');
    }
  };

  // 删除VIP套餐
  const handleDelete = async (id: string) => {
    try {
      await vipPackageService.delete(id);
      message.success('VIP套餐删除成功');
      fetchPackages();
    } catch {
      message.error('删除VIP套餐失败');
    }
  };

  // 切换套餐状态
  const handleToggleStatus = async (pkg: VipPackage) => {
    try {
      await vipPackageService.toggleStatus(pkg.id, !pkg.isActive);
      message.success(`套餐${pkg.isActive ? '禁用' : '启用'}成功`);
      fetchPackages();
    } catch {
      message.error('更新套餐状态失败');
    }
  };

  // 打开创建模态框
  const handleCreate = () => {
    setEditingPackage(null);
    form.resetFields();
    form.setFieldsValue({
      sortOrder: packages.length + 1,
      isActive: true,
    });
    setModalVisible(true);
  };

  // 打开编辑模态框
  const handleEdit = (pkg: VipPackage) => {
    setEditingPackage(pkg);
    form.setFieldsValue({
      name: pkg.name,
      description: pkg.description,
      price: pkg.price / 100, // 转换为元
      duration: pkg.duration,
      sortOrder: pkg.sortOrder,
      isActive: pkg.isActive,
    });
    setModalVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '套餐信息',
      key: 'info',
      width: 250,
      render: (_: unknown, record: VipPackage) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: 4 }}>
            <CrownOutlined style={{ color: '#faad14', marginRight: 8 }} />
            {record.name}
          </div>
          <div style={{ color: '#666', fontSize: '12px', marginBottom: 4 }}>
            {record.description}
          </div>
          <div style={{ fontSize: '12px', color: '#999' }}>
            ID: {record.id}
          </div>
        </div>
      ),
    },
    {
      title: '价格',
      key: 'price',
      width: 120,
      render: (_: unknown, record: VipPackage) => (
        <div>
          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#f50' }}>
            ¥{(record.price / 100).toFixed(2)}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.duration}天
          </div>
        </div>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
      sorter: (a: VipPackage, b: VipPackage) => a.sortOrder - b.sortOrder,
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_: unknown, record: VipPackage) => (
        <Tag color={record.isActive ? 'green' : 'red'}>
          {record.isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: unknown, record: VipPackage) => (
        <Space size="small" wrap>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleToggleStatus(record)}
            style={{ color: record.isActive ? '#ff4d4f' : '#52c41a' }}
          >
            {record.isActive ? '禁用' : '启用'}
          </Button>
          <Popconfirm
            title="确定要删除这个VIP套餐吗？"
            description="删除后无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 统计数据
  const stats = {
    total: packages.length,
    active: packages.filter(p => p.isActive).length,
    inactive: packages.filter(p => !p.isActive).length,
    totalRevenue: packages.reduce((sum, p) => sum + p.price, 0) / 100,
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          <CrownOutlined style={{ color: '#faad14', marginRight: '8px' }} />
          VIP套餐管理
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          管理VIP会员套餐，设置价格、时长和状态
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总套餐数"
              value={stats.total}
              prefix={<GiftOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="启用套餐"
              value={stats.active}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CrownOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="禁用套餐"
              value={stats.inactive}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="套餐总价值"
              value={stats.totalRevenue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新增套餐
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchPackages}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* VIP套餐表格 */}
      <Table
        columns={columns}
        dataSource={packages}
        rowKey="id"
        loading={loading}
        pagination={{
          total: packages.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个套餐`,
        }}
        scroll={{ x: 1000 }}
      />

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingPackage ? '编辑VIP套餐' : '新增VIP套餐'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingPackage(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => {
            // 将价格从元转换为分
            const submitValues = {
              ...values,
              price: Math.round(values.price * 100),
            };
            handleSubmit(submitValues);
          }}
        >
          {!editingPackage && (
            <div style={{
              background: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '6px',
              padding: '12px',
              marginBottom: '16px',
              fontSize: '14px',
              color: '#52c41a'
            }}>
              <strong>提示：</strong>套餐ID将根据套餐名称和时长自动生成，格式如：vip_monthly_30d_a1b2
            </div>
          )}

          <Form.Item
            name="name"
            label="套餐名称"
            rules={[{ required: true, message: '请输入套餐名称' }]}
          >
            <Input placeholder="例如：VIP月卡" />
          </Form.Item>

          <Form.Item
            name="description"
            label="套餐描述"
            rules={[{ required: true, message: '请输入套餐描述' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="例如：30天VIP特权，无限制解锁关卡"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格（元）"
                rules={[
                  { required: true, message: '请输入价格' },
                  { type: 'number', min: 0.01, message: '价格必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="29.00"
                  precision={2}
                  min={0.01}
                  max={9999.99}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="duration"
                label="有效期（天）"
                rules={[
                  { required: true, message: '请输入有效期' },
                  { type: 'number', min: 1, message: '有效期必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="30"
                  min={1}
                  max={9999999}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="sortOrder"
                label="排序权重"
                rules={[{ required: true, message: '请输入排序权重' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="1"
                  min={1}
                  max={999}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="启用状态"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingPackage ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VipPackagesPage;
