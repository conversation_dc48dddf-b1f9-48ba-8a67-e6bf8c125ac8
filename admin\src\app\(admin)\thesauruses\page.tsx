'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Typography, Table, Button, message, Modal, Space } from 'antd';
import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { getThesauruses, deleteThesaurusById } from '@/services/thesaurusService';
import { ThesaurusResponseDto } from '@/types/thesaurus';

const ThesaurusManagementPage: React.FC = () => {
  const [thesauruses, setThesauruses] = useState<ThesaurusResponseDto[]>([]);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const fetchThesauruses = useCallback(async () => {
    setLoading(true);
    try {
      const data = await getThesauruses();
      setThesauruses(data);
    } catch (error: any) {
      console.error("获取词库列表失败:", error);
      message.error(error.message || "获取词库列表失败！");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchThesauruses();
  }, [fetchThesauruses]);

  const handleDelete = useCallback(async (thesaurusId: string) => {
    try {
      await deleteThesaurusById(thesaurusId);
      message.success("词库删除成功！");
      fetchThesauruses(); // 重新加载列表
    } catch (error: any) {
      console.error(`删除词库 ${thesaurusId} 失败:`, error);
      message.error(error.message || "词库删除失败！");
    }
  }, [fetchThesauruses]);

  const showDeleteConfirm = useCallback((record: ThesaurusResponseDto) => {
    Modal.confirm({
      title: `确定要删除词库 "${record.name}" 吗?`,
      icon: <ExclamationCircleOutlined />,
      content: '此操作不可撤销。如果有关联的关卡，请谨慎操作。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        handleDelete(record.id);
      },
    });
  }, [handleDelete]);

  const columns = [
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
    { title: '词组数', dataIndex: 'phraseIds', key: 'phraseCount', render: (phraseIds: string[]) => phraseIds?.length || 0 },
    { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', render: (text: string) => new Date(text).toLocaleString() },
    { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt', render: (text: string) => new Date(text).toLocaleString() },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: ThesaurusResponseDto) => (
        <Space size="middle">
          <Button
            size="small"
            onClick={() => {
              // TODO: router.push(`/thesauruses/${record.id}/edit`);
              message.info(`编辑词库 ${record.id} (功能待实现)`);
            }}
          >
            编辑
          </Button>
          <Button
            size="small"
            danger
            onClick={() => showDeleteConfirm(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Typography.Title level={2}>词库管理</Typography.Title>
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => router.push('/thesauruses/create')}
        style={{ marginBottom: 16 }}
      >
        创建词库
      </Button>
      <Table columns={columns} dataSource={thesauruses} rowKey="id" loading={loading} />
    </Card>
  );
};

export default ThesaurusManagementPage;