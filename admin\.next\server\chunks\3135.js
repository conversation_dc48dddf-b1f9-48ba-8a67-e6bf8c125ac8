exports.id=3135,exports.ids=[3135],exports.modules={4691:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=a(7565).A},10814:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});let n=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},15444:()=>{},24600:(e,t,a)=>{Promise.resolve().then(a.bind(a,10814))},29220:(e,t,a)=>{"use strict";a.d(t,{A:()=>P});var n=a(43210),o=a(91039),r=a(41514),i=a(15693),s=a(51297),l=a(74550),c=a(69662),d=a.n(c),p=a(13934),u=a(44666),m=a(7224),g=a(56883),h=a(71802),y=a(42411),f=a(32476),b=a(13581);let v=(e,t,a,n,o)=>({background:e,border:`${(0,y.zA)(n.lineWidth)} ${n.lineType} ${t}`,[`${o}-icon`]:{color:a}}),x=e=>{let{componentCls:t,motionDurationSlow:a,marginXS:n,marginSM:o,fontSize:r,fontSizeLG:i,lineHeight:s,borderRadiusLG:l,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:p,colorTextHeading:u,withDescriptionPadding:m,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:l,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:r,lineHeight:s},"&-message":{color:u},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${a} ${c}, opacity ${a} ${c},
        padding-top ${a} ${c}, padding-bottom ${a} ${c},
        margin-bottom ${a} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:m,[`${t}-icon`]:{marginInlineEnd:o,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:n,color:u,fontSize:i},[`${t}-description`]:{display:"block",color:p}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},A=e=>{let{componentCls:t,colorSuccess:a,colorSuccessBorder:n,colorSuccessBg:o,colorWarning:r,colorWarningBorder:i,colorWarningBg:s,colorError:l,colorErrorBorder:c,colorErrorBg:d,colorInfo:p,colorInfoBorder:u,colorInfoBg:m}=e;return{[t]:{"&-success":v(o,n,a,e,t),"&-info":v(m,u,p,e,t),"&-warning":v(s,i,r,e,t),"&-error":Object.assign(Object.assign({},v(d,c,l,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},$=e=>{let{componentCls:t,iconCls:a,motionDurationMid:n,marginXS:o,fontSizeIcon:r,colorIcon:i,colorIconHover:s}=e;return{[t]:{"&-action":{marginInlineStart:o},[`${t}-close-icon`]:{marginInlineStart:o,padding:0,overflow:"hidden",fontSize:r,lineHeight:(0,y.zA)(r),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${a}-close`]:{color:i,transition:`color ${n}`,"&:hover":{color:s}}},"&-close-text":{color:i,transition:`color ${n}`,"&:hover":{color:s}}}}},j=(0,b.OF)("Alert",e=>[x(e),A(e),$(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var k=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};let w={success:o.A,info:l.A,error:r.A,warning:s.A},C=e=>{let{icon:t,prefixCls:a,type:o}=e,r=w[o]||null;return t?(0,g.fx)(t,n.createElement("span",{className:`${a}-icon`},t),()=>({className:d()(`${a}-icon`,t.props.className)})):n.createElement(r,{className:`${a}-icon`})},S=e=>{let{isClosable:t,prefixCls:a,closeIcon:o,handleClose:r,ariaProps:s}=e,l=!0===o||void 0===o?n.createElement(i.A,null):o;return t?n.createElement("button",Object.assign({type:"button",onClick:r,className:`${a}-close-icon`,tabIndex:0},s),l):null},I=n.forwardRef((e,t)=>{let{description:a,prefixCls:o,message:r,banner:i,className:s,rootClassName:l,style:c,onMouseEnter:g,onMouseLeave:y,onClick:f,afterClose:b,showIcon:v,closable:x,closeText:A,closeIcon:$,action:w,id:I}=e,E=k(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[O,N]=n.useState(!1),z=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:z.current}));let{getPrefixCls:H,direction:D,closable:M,closeIcon:P,className:L,style:R}=(0,h.TP)("alert"),B=H("alert",o),[T,W,F]=j(B),G=t=>{var a;N(!0),null==(a=e.onClose)||a.call(e,t)},K=n.useMemo(()=>void 0!==e.type?e.type:i?"warning":"info",[e.type,i]),V=n.useMemo(()=>"object"==typeof x&&!!x.closeIcon||!!A||("boolean"==typeof x?x:!1!==$&&null!=$||!!M),[A,$,x,M]),X=!!i&&void 0===v||v,Y=d()(B,`${B}-${K}`,{[`${B}-with-description`]:!!a,[`${B}-no-icon`]:!X,[`${B}-banner`]:!!i,[`${B}-rtl`]:"rtl"===D},L,s,l,F,W),_=(0,u.A)(E,{aria:!0,data:!0}),q=n.useMemo(()=>"object"==typeof x&&x.closeIcon?x.closeIcon:A||(void 0!==$?$:"object"==typeof M&&M.closeIcon?M.closeIcon:P),[$,x,A,P]),J=n.useMemo(()=>{let e=null!=x?x:M;if("object"==typeof e){let{closeIcon:t}=e;return k(e,["closeIcon"])}return{}},[x,M]);return T(n.createElement(p.Ay,{visible:!O,motionName:`${B}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:b},({className:t,style:o},i)=>n.createElement("div",Object.assign({id:I,ref:(0,m.K4)(z,i),"data-show":!O,className:d()(Y,t),style:Object.assign(Object.assign(Object.assign({},R),c),o),onMouseEnter:g,onMouseLeave:y,onClick:f,role:"alert"},_),X?n.createElement(C,{description:a,icon:e.icon,prefixCls:B,type:K}):null,n.createElement("div",{className:`${B}-content`},r?n.createElement("div",{className:`${B}-message`},r):null,a?n.createElement("div",{className:`${B}-description`},a):null),w?n.createElement("div",{className:`${B}-action`},w):null,n.createElement(S,{isClosable:V,prefixCls:B,closeIcon:q,handleClose:G,ariaProps:J}))))});var E=a(67737),O=a(49617),N=a(30402),z=a(85764),H=a(1630),D=a(69561);let M=function(e){function t(){var e,a,n;return(0,E.A)(this,t),a=t,n=arguments,a=(0,N.A)(a),(e=(0,H.A)(this,(0,z.A)()?Reflect.construct(a,n||[],(0,N.A)(this).constructor):a.apply(this,n))).state={error:void 0,info:{componentStack:""}},e}return(0,D.A)(t,e),(0,O.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:a,children:o}=this.props,{error:r,info:i}=this.state,s=(null==i?void 0:i.componentStack)||null,l=void 0===e?(r||"").toString():e;return r?n.createElement(I,{id:a,type:"error",message:l,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?s:t)}):o}}])}(n.Component);I.ErrorBoundary=M;let P=I},37912:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var n=a(60687),o=a(43210),r=a(85814),i=a.n(r),s=a(16189),l=a(98836),c=a(99053),d=a(63736),p=a(56072),u=a(78620);a(15444);var m=a(60203),g=a(81945),h=a(53788),y=a(9242),f=a(3788),b=a(73237),v=a(47453),x=a(31189),A=a(62727),$=a(14723),j=a(72061),k=a(80461),w=a(71103);let{Header:C,Content:S,Sider:I,Footer:E}=l.A,O=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,n.jsx)(m.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,n.jsx)(g.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,n.jsx)(h.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,n.jsx)(y.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,n.jsx)(f.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,n.jsx)(b.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,n.jsx)(v.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,n.jsx)(x.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,n.jsx)(A.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,n.jsx)($.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,n.jsx)(j.A,{})}];function N({children:e}){let t=(0,s.useRouter)(),a=(0,s.usePathname)(),[r,m]=(0,o.useState)(!1),g=[{key:"logout",icon:(0,n.jsx)(k.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],h=O.find(e=>a.startsWith(e.path))?.key||"dashboard";return(0,n.jsxs)(l.A,{style:{minHeight:"100vh"},children:[(0,n.jsxs)(I,{collapsible:!0,collapsed:r,onCollapse:e=>m(e),children:[(0,n.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,n.jsx)(c.A.Text,{style:{color:"white",fontSize:r?"10px":"16px",transition:"font-size 0.2s"},children:r?"后台":"游戏管理后台"})}),(0,n.jsx)(d.A,{theme:"dark",selectedKeys:[h],mode:"inline",items:O.map(e=>({key:e.key,icon:e.icon,label:(0,n.jsx)(i(),{href:e.path,children:e.label})}))})]}),(0,n.jsxs)(l.A,{children:[(0,n.jsxs)(C,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,n.jsx)(p.A,{menu:{items:g},placement:"bottomRight",children:(0,n.jsx)(u.A,{style:{cursor:"pointer"},icon:(0,n.jsx)(w.A,{})})}),(0,n.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,n.jsx)(S,{style:{margin:"16px"},children:e}),(0,n.jsxs)(E,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},43910:(e,t,a)=>{"use strict";a.d(t,{Dw:()=>o,WS:()=>r,cm:()=>i});var n=a(49895);class o{static async getAllShareConfigs(){return(await n.Ay.get("/share")).data}static async getActiveShareConfigs(){return(await n.Ay.get("/share/active")).data}static async getDefaultShareConfig(){return(await n.Ay.get("/share/default")).data}static async getShareConfigByType(e){return(await n.Ay.get(`/share/type/${e}`)).data}static async getShareConfigById(e){return(await n.Ay.get(`/share/${e}`)).data}static async createShareConfig(e){return(await n.Ay.post("/share",e)).data}static async updateShareConfig(e,t){return(await n.Ay.patch(`/share/${e}`,t)).data}static async toggleShareConfig(e){return(await n.Ay.put(`/share/${e}/toggle`)).data}static async deleteShareConfig(e){return(await n.Ay.delete(`/share/${e}`)).data}}let r=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],i=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},59448:(e,t,a)=>{Promise.resolve().then(a.bind(a,37912))},85975:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var n=a(80828),o=a(43210);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};var i=a(21898);let s=o.forwardRef(function(e,t){return o.createElement(i.A,(0,n.A)({},e,{ref:t,icon:r}))})},96625:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=a(20775).A}};