import request from './request';

// 标签数据处理函数，确保所有字段都有合适的默认值
const processTagData = (tag: any): LevelTag => ({
  id: tag.id || '',
  name: tag.name || '',
  description: tag.description || '',
  color: tag.color || '#1890ff',
  isVip: Bo<PERSON>an(tag.isVip),
  status: tag.status || 'active',
  icon: tag.icon,
  createdAt: tag.createdAt || new Date().toISOString(),
  updatedAt: tag.updatedAt || new Date().toISOString(),
});

// 标签数据类型
export interface LevelTag {
  id: string;
  name: string;
  description?: string;
  color: string;
  isVip: boolean;
  status: string; // 'active' | 'inactive'
  icon?: string;
  createdAt: string;
  updatedAt: string;
}



// 创建标签参数
export interface CreateLevelTagParams {
  name: string;
  description?: string;
  color?: string;
  isVip?: boolean;
  status?: string; // 'active' | 'inactive'
  icon?: string;
}

// 更新标签参数
export interface UpdateLevelTagParams {
  name?: string;
  description?: string;
  color?: string;
  isVip?: boolean;
  status?: string; // 'active' | 'inactive'
  icon?: string;
}

// 标签管理服务
export const levelTagService = {
  // 获取所有标签
  getAll: async (): Promise<LevelTag[]> => {
    const response = await request.get<any[]>('/tags');
    // 确保每个标签都有正确的字段和默认值
    const tags = response.data || [];
    return tags.map(processTagData);
  },

  // 根据ID获取标签
  getById: async (id: string): Promise<LevelTag> => {
    const response = await request.get<any>(`/tags/${id}`);
    return processTagData(response.data);
  },

  // 创建标签
  create: async (params: CreateLevelTagParams): Promise<LevelTag> => {
    const response = await request.post<any>('/tags', params);
    return processTagData(response.data);
  },

  // 更新标签
  update: async (id: string, params: UpdateLevelTagParams): Promise<LevelTag> => {
    const response = await request.put<any>(`/tags/${id}`, params);
    return processTagData(response.data);
  },

  // 删除标签
  delete: async (id: string): Promise<void> => {
    await request.delete(`/tags/${id}`);
  },
};

// 保持向后兼容的导出
export const getLevelTags = levelTagService.getAll;
export const createLevelTag = levelTagService.create;
export const updateLevelTag = levelTagService.update;
export const deleteLevelTag = levelTagService.delete;
