'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  message, 
  Typography, 
  Space, 
  Divider,
  Alert,
  Spin
} from 'antd';
import { 
  SaveOutlined, 
  ReloadOutlined,
  LinkOutlined,
  SoundOutlined,
  SettingOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { settingsService } from '@/services/settingsService';

const { Title, Paragraph } = Typography;

interface AppConfig {
  helpUrl: string;
  backgroundMusicUrl: string;
}

export default function SettingsPage() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [testing, setTesting] = useState(false);

  // 获取当前配置
  const fetchConfig = async () => {
    setFetchLoading(true);
    try {
      const config = await settingsService.getAppConfig();
      form.setFieldsValue(config);
      console.log('✅ 配置加载成功:', config);
    } catch (error) {
      console.error('❌ 获取配置失败:', error);
      // 如果获取失败，尝试初始化默认设置
      try {
        await settingsService.initializeDefaults();
        message.success('已自动初始化默认设置');
        // 重新获取配置
        const config = await settingsService.getAppConfig();
        form.setFieldsValue(config);
      } catch (initError) {
        console.error('❌ 初始化设置失败:', initError);
        message.error('获取配置失败，请检查服务器连接');
      }
    } finally {
      setFetchLoading(false);
    }
  };

  // 保存配置
  const handleSave = async (values: AppConfig) => {
    setLoading(true);
    try {
      await settingsService.updateAppConfig(values);
      message.success('设置保存成功');
      console.log('✅ 设置保存成功:', values);
    } catch (error) {
      console.error('❌ 保存设置失败:', error);
      message.error('保存设置失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 初始化默认设置
  const handleInitialize = async () => {
    setInitializing(true);
    try {
      await settingsService.initializeDefaults();
      message.success('默认设置初始化成功');
      console.log('✅ 默认设置初始化成功');
      await fetchConfig(); // 重新获取配置
    } catch (error) {
      console.error('❌ 初始化设置失败:', error);
      message.error('初始化设置失败，请检查服务器连接');
    } finally {
      setInitializing(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    fetchConfig();
  };

  // 测试微信小程序接口
  const handleTestWeixin = async () => {
    setTesting(true);
    try {
      const [appSettings, globalConfig] = await Promise.all([
        settingsService.testWeixinAppSettings(),
        settingsService.testWeixinGlobalConfig(),
      ]);

      console.log('✅ 微信小程序设置接口测试成功:', appSettings);
      console.log('✅ 微信小程序全局配置接口测试成功:', globalConfig);

      message.success('微信小程序接口测试成功，请查看控制台输出');
    } catch (error) {
      console.error('❌ 微信小程序接口测试失败:', error);
      message.error('微信小程序接口测试失败，请检查服务器连接');
    } finally {
      setTesting(false);
    }
  };

  useEffect(() => {
    fetchConfig();
  }, []);

  if (fetchLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Paragraph>正在加载设置...</Paragraph>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Title level={2}>
            <SettingOutlined style={{ marginRight: 8 }} />
            小程序设置
          </Title>
          <Paragraph>
            配置小程序的功能设置，包括帮助链接和背景音乐等。
          </Paragraph>
        </div>

        <Alert
          message="设置说明"
          description="这些设置将影响小程序的功能表现。系统已自动初始化默认配置，您可以根据需要修改。请确保链接有效且可访问。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Card 
            title={
              <Space>
                <LinkOutlined />
                帮助功能设置
              </Space>
            }
            size="small"
            style={{ marginBottom: 16 }}
          >
            <Form.Item
              label="帮助页面链接"
              name="helpUrl"
              rules={[
                { required: true, message: '请输入帮助页面链接' },
                { type: 'url', message: '请输入有效的URL地址' },
              ]}
              extra="用户点击帮助按钮时跳转的页面链接"
            >
              <Input
                placeholder="https://help.example.com"
                prefix={<LinkOutlined />}
              />
            </Form.Item>
          </Card>

          <Card 
            title={
              <Space>
                <SoundOutlined />
                音乐功能设置
              </Space>
            }
            size="small"
            style={{ marginBottom: 24 }}
          >
            <Form.Item
              label="背景音乐链接"
              name="backgroundMusicUrl"
              rules={[
                { required: true, message: '请输入背景音乐链接' },
                { type: 'url', message: '请输入有效的URL地址' },
              ]}
              extra="小程序背景音乐文件的链接地址，支持 MP3 格式"
            >
              <Input
                placeholder="https://music.example.com/background.mp3"
                prefix={<SoundOutlined />}
              />
            </Form.Item>
          </Card>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Space size="middle">
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={loading}
                size="large"
              >
                保存设置
              </Button>
              
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
                disabled={loading}
                size="large"
              >
                重置
              </Button>
              
              <Button
                onClick={handleInitialize}
                loading={initializing}
                disabled={loading}
                size="large"
              >
                重置为默认设置
              </Button>

              <Button
                icon={<ApiOutlined />}
                onClick={handleTestWeixin}
                loading={testing}
                disabled={loading || initializing}
                size="large"
              >
                测试微信接口
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
}
