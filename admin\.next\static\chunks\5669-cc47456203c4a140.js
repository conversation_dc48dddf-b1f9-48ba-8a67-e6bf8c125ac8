"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5669],{6124:(t,e,n)=>{n.d(e,{A:()=>R});var a=n(12115),o=n(29300),c=n.n(o),r=n(17980),i=n(15982),l=n(9836),s=n(70802),d=n(35125),u=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let b=t=>{var{prefixCls:e,className:n,hoverable:o=!0}=t,r=u(t,["prefixCls","className","hoverable"]);let{getPrefixCls:l}=a.useContext(i.QO),s=l("card",e),d=c()("".concat(s,"-grid"),n,{["".concat(s,"-grid-hoverable")]:o});return a.createElement("div",Object.assign({},r,{className:d}))};var p=n(85573),f=n(18184),g=n(45431),v=n(61388);let m=t=>{let{antCls:e,componentCls:n,headerHeight:a,headerPadding:o,tabsMarginBottom:c}=t;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:"0 ".concat((0,p.zA)(o)),color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.headerFontSize,background:t.headerBg,borderBottom:"".concat((0,p.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorderSecondary),borderRadius:"".concat((0,p.zA)(t.borderRadiusLG)," ").concat((0,p.zA)(t.borderRadiusLG)," 0 0")},(0,f.t6)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},f.L9),{["\n          > ".concat(n,"-typography,\n          > ").concat(n,"-typography-edit-content\n        ")]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),["".concat(e,"-tabs-top")]:{clear:"both",marginBottom:c,color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,"&-bar":{borderBottom:"".concat((0,p.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorderSecondary)}}})},h=t=>{let{cardPaddingBase:e,colorBorderSecondary:n,cardShadow:a,lineWidth:o}=t;return{width:"33.33%",padding:e,border:0,borderRadius:0,boxShadow:"\n      ".concat((0,p.zA)(o)," 0 0 0 ").concat(n,",\n      0 ").concat((0,p.zA)(o)," 0 0 ").concat(n,",\n      ").concat((0,p.zA)(o)," ").concat((0,p.zA)(o)," 0 0 ").concat(n,",\n      ").concat((0,p.zA)(o)," 0 0 0 ").concat(n," inset,\n      0 ").concat((0,p.zA)(o)," 0 0 ").concat(n," inset;\n    "),transition:"all ".concat(t.motionDurationMid),"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},y=t=>{let{componentCls:e,iconCls:n,actionsLiMargin:a,cardActionsIconSize:o,colorBorderSecondary:c,actionsBg:r}=t;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:r,borderTop:"".concat((0,p.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(c),display:"flex",borderRadius:"0 0 ".concat((0,p.zA)(t.borderRadiusLG)," ").concat((0,p.zA)(t.borderRadiusLG))},(0,f.t6)()),{"& > li":{margin:a,color:t.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:t.calc(t.cardActionsIconSize).mul(2).equal(),fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer","&:hover":{color:t.colorPrimary,transition:"color ".concat(t.motionDurationMid)},["a:not(".concat(e,"-btn), > ").concat(n)]:{display:"inline-block",width:"100%",color:t.colorIcon,lineHeight:(0,p.zA)(t.fontHeight),transition:"color ".concat(t.motionDurationMid),"&:hover":{color:t.colorPrimary}},["> ".concat(n)]:{fontSize:o,lineHeight:(0,p.zA)(t.calc(o).mul(t.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:"".concat((0,p.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(c)}}})},k=t=>Object.assign(Object.assign({margin:"".concat((0,p.zA)(t.calc(t.marginXXS).mul(-1).equal())," 0"),display:"flex"},(0,f.t6)()),{"&-avatar":{paddingInlineEnd:t.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:t.marginXS}},"&-title":Object.assign({color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.fontSizeLG},f.L9),"&-description":{color:t.colorTextDescription}}),x=t=>{let{componentCls:e,colorFillAlter:n,headerPadding:a,bodyPadding:o}=t;return{["".concat(e,"-head")]:{padding:"0 ".concat((0,p.zA)(a)),background:n,"&-title":{fontSize:t.fontSize}},["".concat(e,"-body")]:{padding:"".concat((0,p.zA)(t.padding)," ").concat((0,p.zA)(o))}}},A=t=>{let{componentCls:e}=t;return{overflow:"hidden",["".concat(e,"-body")]:{userSelect:"none"}}},w=t=>{let{componentCls:e,cardShadow:n,cardHeadPadding:a,colorBorderSecondary:o,boxShadowTertiary:c,bodyPadding:r,extraColor:i}=t;return{[e]:Object.assign(Object.assign({},(0,f.dF)(t)),{position:"relative",background:t.colorBgContainer,borderRadius:t.borderRadiusLG,["&:not(".concat(e,"-bordered)")]:{boxShadow:c},["".concat(e,"-head")]:m(t),["".concat(e,"-extra")]:{marginInlineStart:"auto",color:i,fontWeight:"normal",fontSize:t.fontSize},["".concat(e,"-body")]:Object.assign({padding:r,borderRadius:"0 0 ".concat((0,p.zA)(t.borderRadiusLG)," ").concat((0,p.zA)(t.borderRadiusLG))},(0,f.t6)()),["".concat(e,"-grid")]:h(t),["".concat(e,"-cover")]:{"> *":{display:"block",width:"100%",borderRadius:"".concat((0,p.zA)(t.borderRadiusLG)," ").concat((0,p.zA)(t.borderRadiusLG)," 0 0")}},["".concat(e,"-actions")]:y(t),["".concat(e,"-meta")]:k(t)}),["".concat(e,"-bordered")]:{border:"".concat((0,p.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(o),["".concat(e,"-cover")]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},["".concat(e,"-hoverable")]:{cursor:"pointer",transition:"box-shadow ".concat(t.motionDurationMid,", border-color ").concat(t.motionDurationMid),"&:hover":{borderColor:"transparent",boxShadow:n}},["".concat(e,"-contain-grid")]:{borderRadius:"".concat((0,p.zA)(t.borderRadiusLG)," ").concat((0,p.zA)(t.borderRadiusLG)," 0 0 "),["".concat(e,"-body")]:{display:"flex",flexWrap:"wrap"},["&:not(".concat(e,"-loading) ").concat(e,"-body")]:{marginBlockStart:t.calc(t.lineWidth).mul(-1).equal(),marginInlineStart:t.calc(t.lineWidth).mul(-1).equal(),padding:0}},["".concat(e,"-contain-tabs")]:{["> div".concat(e,"-head")]:{minHeight:0,["".concat(e,"-head-title, ").concat(e,"-extra")]:{paddingTop:a}}},["".concat(e,"-type-inner")]:x(t),["".concat(e,"-loading")]:A(t),["".concat(e,"-rtl")]:{direction:"rtl"}}},S=t=>{let{componentCls:e,bodyPaddingSM:n,headerPaddingSM:a,headerHeightSM:o,headerFontSizeSM:c}=t;return{["".concat(e,"-small")]:{["> ".concat(e,"-head")]:{minHeight:o,padding:"0 ".concat((0,p.zA)(a)),fontSize:c,["> ".concat(e,"-head-wrapper")]:{["> ".concat(e,"-extra")]:{fontSize:t.fontSize}}},["> ".concat(e,"-body")]:{padding:n}},["".concat(e,"-small").concat(e,"-contain-tabs")]:{["> ".concat(e,"-head")]:{["".concat(e,"-head-title, ").concat(e,"-extra")]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},O=(0,g.OF)("Card",t=>{let e=(0,v.oX)(t,{cardShadow:t.boxShadowCard,cardHeadPadding:t.padding,cardPaddingBase:t.paddingLG,cardActionsIconSize:t.fontSize});return[w(e),S(e)]},t=>{var e,n;return{headerBg:"transparent",headerFontSize:t.fontSizeLG,headerFontSizeSM:t.fontSize,headerHeight:t.fontSizeLG*t.lineHeightLG+2*t.padding,headerHeightSM:t.fontSize*t.lineHeight+2*t.paddingXS,actionsBg:t.colorBgContainer,actionsLiMargin:"".concat(t.paddingSM,"px 0"),tabsMarginBottom:-t.padding-t.lineWidth,extraColor:t.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!=(e=t.bodyPadding)?e:t.paddingLG,headerPadding:null!=(n=t.headerPadding)?n:t.paddingLG}});var z=n(63893),E=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let C=t=>{let{actionClasses:e,actions:n=[],actionStyle:o}=t;return a.createElement("ul",{className:e,style:o},n.map((t,e)=>a.createElement("li",{style:{width:"".concat(100/n.length,"%")},key:"action-".concat(e)},a.createElement("span",null,t))))},j=a.forwardRef((t,e)=>{let n,{prefixCls:o,className:u,rootClassName:p,style:f,extra:g,headStyle:v={},bodyStyle:m={},title:h,loading:y,bordered:k,variant:x,size:A,type:w,cover:S,actions:j,tabList:_,children:R,activeTabKey:I,defaultActiveTabKey:T,tabBarExtraContent:M,hoverable:P,tabProps:L={},classNames:N,styles:B}=t,D=E(t,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:H,direction:G,card:W}=a.useContext(i.QO),[q]=(0,z.A)("card",x,k),X=t=>{var e;return c()(null==(e=null==W?void 0:W.classNames)?void 0:e[t],null==N?void 0:N[t])},F=t=>{var e;return Object.assign(Object.assign({},null==(e=null==W?void 0:W.styles)?void 0:e[t]),null==B?void 0:B[t])},K=a.useMemo(()=>{let t=!1;return a.Children.forEach(R,e=>{(null==e?void 0:e.type)===b&&(t=!0)}),t},[R]),Q=H("card",o),[V,Y,U]=O(Q),J=a.createElement(s.A,{loading:!0,active:!0,paragraph:{rows:4},title:!1},R),Z=void 0!==I,$=Object.assign(Object.assign({},L),{[Z?"activeKey":"defaultActiveKey"]:Z?I:T,tabBarExtraContent:M}),tt=(0,l.A)(A),te=tt&&"default"!==tt?tt:"large",tn=_?a.createElement(d.A,Object.assign({size:te},$,{className:"".concat(Q,"-head-tabs"),onChange:e=>{var n;null==(n=t.onTabChange)||n.call(t,e)},items:_.map(t=>{var{tab:e}=t;return Object.assign({label:e},E(t,["tab"]))})})):null;if(h||g||tn){let t=c()("".concat(Q,"-head"),X("header")),e=c()("".concat(Q,"-head-title"),X("title")),o=c()("".concat(Q,"-extra"),X("extra")),r=Object.assign(Object.assign({},v),F("header"));n=a.createElement("div",{className:t,style:r},a.createElement("div",{className:"".concat(Q,"-head-wrapper")},h&&a.createElement("div",{className:e,style:F("title")},h),g&&a.createElement("div",{className:o,style:F("extra")},g)),tn)}let ta=c()("".concat(Q,"-cover"),X("cover")),to=S?a.createElement("div",{className:ta,style:F("cover")},S):null,tc=c()("".concat(Q,"-body"),X("body")),tr=Object.assign(Object.assign({},m),F("body")),ti=a.createElement("div",{className:tc,style:tr},y?J:R),tl=c()("".concat(Q,"-actions"),X("actions")),ts=(null==j?void 0:j.length)?a.createElement(C,{actionClasses:tl,actionStyle:F("actions"),actions:j}):null,td=(0,r.A)(D,["onTabChange"]),tu=c()(Q,null==W?void 0:W.className,{["".concat(Q,"-loading")]:y,["".concat(Q,"-bordered")]:"borderless"!==q,["".concat(Q,"-hoverable")]:P,["".concat(Q,"-contain-grid")]:K,["".concat(Q,"-contain-tabs")]:null==_?void 0:_.length,["".concat(Q,"-").concat(tt)]:tt,["".concat(Q,"-type-").concat(w)]:!!w,["".concat(Q,"-rtl")]:"rtl"===G},u,p,Y,U),tb=Object.assign(Object.assign({},null==W?void 0:W.style),f);return V(a.createElement("div",Object.assign({ref:e},td,{className:tu,style:tb}),n,to,ti,ts))});var _=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};j.Grid=b,j.Meta=t=>{let{prefixCls:e,className:n,avatar:o,title:r,description:l}=t,s=_(t,["prefixCls","className","avatar","title","description"]),{getPrefixCls:d}=a.useContext(i.QO),u=d("card",e),b=c()("".concat(u,"-meta"),n),p=o?a.createElement("div",{className:"".concat(u,"-meta-avatar")},o):null,f=r?a.createElement("div",{className:"".concat(u,"-meta-title")},r):null,g=l?a.createElement("div",{className:"".concat(u,"-meta-description")},l):null,v=f||g?a.createElement("div",{className:"".concat(u,"-meta-detail")},f,g):null;return a.createElement("div",Object.assign({},s,{className:b}),p,v)};let R=j},35125:(t,e,n)=>{n.d(e,{A:()=>tw});var a=n(12115),o=n(58587),c=n(83607),r=n(46996),i=n(29300),l=n.n(i),s=n(79630),d=n(40419),u=n(27061),b=n(21858),p=n(86608),f=n(52673),g=n(48804),v=n(96951);let m=(0,a.createContext)(null);var h=n(85757),y=n(32417),k=n(18885),x=n(74686),A=n(16962);let w=function(t){var e=t.activeTabOffset,n=t.horizontal,o=t.rtl,c=t.indicator,r=void 0===c?{}:c,i=r.size,l=r.align,s=void 0===l?"center":l,d=(0,a.useState)(),u=(0,b.A)(d,2),p=u[0],f=u[1],g=(0,a.useRef)(),v=a.useCallback(function(t){return"function"==typeof i?i(t):"number"==typeof i?i:t},[i]);function m(){A.A.cancel(g.current)}return(0,a.useEffect)(function(){var t={};if(e)if(n){t.width=v(e.width);var a=o?"right":"left";"start"===s&&(t[a]=e[a]),"center"===s&&(t[a]=e[a]+e.width/2,t.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===s&&(t[a]=e[a]+e.width,t.transform="translateX(-100%)")}else t.height=v(e.height),"start"===s&&(t.top=e.top),"center"===s&&(t.top=e.top+e.height/2,t.transform="translateY(-50%)"),"end"===s&&(t.top=e.top+e.height,t.transform="translateY(-100%)");return m(),g.current=(0,A.A)(function(){p&&t&&Object.keys(t).every(function(e){var n=t[e],a=p[e];return"number"==typeof n&&"number"==typeof a?Math.round(n)===Math.round(a):n===a})||f(t)}),m},[JSON.stringify(e),n,o,s,v]),{style:p}};var S={width:0,height:0,left:0,top:0};function O(t,e){var n=a.useRef(t),o=a.useState({}),c=(0,b.A)(o,2)[1];return[n.current,function(t){var a="function"==typeof t?t(n.current):t;a!==n.current&&e(a,n.current),n.current=a,c({})}]}var z=n(49172);function E(t){var e=(0,a.useState)(0),n=(0,b.A)(e,2),o=n[0],c=n[1],r=(0,a.useRef)(0),i=(0,a.useRef)();return i.current=t,(0,z.o)(function(){var t;null==(t=i.current)||t.call(i)},[o]),function(){r.current===o&&(r.current+=1,c(r.current))}}var C={width:0,height:0,left:0,top:0,right:0};function j(t){var e;return t instanceof Map?(e={},t.forEach(function(t,n){e[n]=t})):e=t,JSON.stringify(e)}function _(t){return String(t).replace(/"/g,"TABS_DQ")}function R(t,e,n,a){return!!n&&!a&&!1!==t&&(void 0!==t||!1!==e&&null!==e)}var I=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.editable,c=t.locale,r=t.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:e,type:"button",className:"".concat(n,"-nav-add"),style:r,"aria-label":(null==c?void 0:c.addAriaLabel)||"Add tab",onClick:function(t){o.onEdit("add",{event:t})}},o.addIcon||"+"):null}),T=a.forwardRef(function(t,e){var n,o=t.position,c=t.prefixCls,r=t.extra;if(!r)return null;var i={};return"object"!==(0,p.A)(r)||a.isValidElement(r)?i.right=r:i=r,"right"===o&&(n=i.right),"left"===o&&(n=i.left),n?a.createElement("div",{className:"".concat(c,"-extra-content"),ref:e},n):null}),M=n(10177),P=n(91187),L=n(17233),N=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.id,c=t.tabs,r=t.locale,i=t.mobile,u=t.more,p=void 0===u?{}:u,f=t.style,g=t.className,v=t.editable,m=t.tabBarGutter,h=t.rtl,y=t.removeAriaLabel,k=t.onTabClick,x=t.getPopupContainer,A=t.popupClassName,w=(0,a.useState)(!1),S=(0,b.A)(w,2),O=S[0],z=S[1],E=(0,a.useState)(null),C=(0,b.A)(E,2),j=C[0],_=C[1],T=p.icon,N="".concat(o,"-more-popup"),B="".concat(n,"-dropdown"),D=null!==j?"".concat(N,"-").concat(j):null,H=null==r?void 0:r.dropdownAriaLabel,G=a.createElement(P.Ay,{onClick:function(t){k(t.key,t.domEvent),z(!1)},prefixCls:"".concat(B,"-menu"),id:N,tabIndex:-1,role:"listbox","aria-activedescendant":D,selectedKeys:[j],"aria-label":void 0!==H?H:"expanded dropdown"},c.map(function(t){var e=t.closable,n=t.disabled,c=t.closeIcon,r=t.key,i=t.label,l=R(e,c,v,n);return a.createElement(P.Dr,{key:r,id:"".concat(N,"-").concat(r),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(r),disabled:n},a.createElement("span",null,i),l&&a.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(B,"-menu-item-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),v.onEdit("remove",{key:r,event:t})}},c||v.removeIcon||"\xd7"))}));function W(t){for(var e=c.filter(function(t){return!t.disabled}),n=e.findIndex(function(t){return t.key===j})||0,a=e.length,o=0;o<a;o+=1){var r=e[n=(n+t+a)%a];if(!r.disabled)return void _(r.key)}}(0,a.useEffect)(function(){var t=document.getElementById(D);t&&t.scrollIntoView&&t.scrollIntoView(!1)},[j]),(0,a.useEffect)(function(){O||_(null)},[O]);var q=(0,d.A)({},h?"marginRight":"marginLeft",m);c.length||(q.visibility="hidden",q.order=1);var X=l()((0,d.A)({},"".concat(B,"-rtl"),h)),F=i?null:a.createElement(M.A,(0,s.A)({prefixCls:B,overlay:G,visible:!!c.length&&O,onVisibleChange:z,overlayClassName:l()(X,A),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:x},p),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:q,"aria-haspopup":"listbox","aria-controls":N,id:"".concat(o,"-more"),"aria-expanded":O,onKeyDown:function(t){var e=t.which;if(!O){[L.A.DOWN,L.A.SPACE,L.A.ENTER].includes(e)&&(z(!0),t.preventDefault());return}switch(e){case L.A.UP:W(-1),t.preventDefault();break;case L.A.DOWN:W(1),t.preventDefault();break;case L.A.ESC:z(!1);break;case L.A.SPACE:case L.A.ENTER:null!==j&&k(j,t)}}},void 0===T?"More":T));return a.createElement("div",{className:l()("".concat(n,"-nav-operations"),g),style:f,ref:e},F,a.createElement(I,{prefixCls:n,locale:r,editable:v}))});let B=a.memo(N,function(t,e){return e.tabMoving}),D=function(t){var e=t.prefixCls,n=t.id,o=t.active,c=t.focus,r=t.tab,i=r.key,s=r.label,u=r.disabled,b=r.closeIcon,p=r.icon,f=t.closable,g=t.renderWrapper,v=t.removeAriaLabel,m=t.editable,h=t.onClick,y=t.onFocus,k=t.onBlur,x=t.onKeyDown,A=t.onMouseDown,w=t.onMouseUp,S=t.style,O=t.tabCount,z=t.currentPosition,E="".concat(e,"-tab"),C=R(f,b,m,u);function j(t){u||h(t)}var I=a.useMemo(function(){return p&&"string"==typeof s?a.createElement("span",null,s):s},[s,p]),T=a.useRef(null);a.useEffect(function(){c&&T.current&&T.current.focus()},[c]);var M=a.createElement("div",{key:i,"data-node-key":_(i),className:l()(E,(0,d.A)((0,d.A)((0,d.A)((0,d.A)({},"".concat(E,"-with-remove"),C),"".concat(E,"-active"),o),"".concat(E,"-disabled"),u),"".concat(E,"-focus"),c)),style:S,onClick:j},a.createElement("div",{ref:T,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(i),className:"".concat(E,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(i),"aria-disabled":u,tabIndex:u?null:o?0:-1,onClick:function(t){t.stopPropagation(),j(t)},onKeyDown:x,onMouseDown:A,onMouseUp:w,onFocus:y,onBlur:k},c&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(z," of ").concat(O)),p&&a.createElement("span",{className:"".concat(E,"-icon")},p),s&&I),C&&a.createElement("button",{type:"button",role:"tab","aria-label":v||"remove",tabIndex:o?0:-1,className:"".concat(E,"-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:i,event:t})}},b||m.removeIcon||"\xd7"));return g?g(M):M};var H=function(t,e){var n=t.offsetWidth,a=t.offsetHeight,o=t.offsetTop,c=t.offsetLeft,r=t.getBoundingClientRect(),i=r.width,l=r.height,s=r.left,d=r.top;return 1>Math.abs(i-n)?[i,l,s-e.left,d-e.top]:[n,a,c,o]},G=function(t){var e=t.current||{},n=e.offsetWidth,a=void 0===n?0:n,o=e.offsetHeight;if(t.current){var c=t.current.getBoundingClientRect(),r=c.width,i=c.height;if(1>Math.abs(r-a))return[r,i]}return[a,void 0===o?0:o]},W=function(t,e){return t[+!e]},q=a.forwardRef(function(t,e){var n,o,c,r,i,p,f,g,v,A,z,M,P,L,N,q,X,F,K,Q,V,Y,U,J,Z,$,tt,te,tn,ta,to,tc,tr,ti,tl,ts,td,tu,tb,tp=t.className,tf=t.style,tg=t.id,tv=t.animated,tm=t.activeKey,th=t.rtl,ty=t.extra,tk=t.editable,tx=t.locale,tA=t.tabPosition,tw=t.tabBarGutter,tS=t.children,tO=t.onTabClick,tz=t.onTabScroll,tE=t.indicator,tC=a.useContext(m),tj=tC.prefixCls,t_=tC.tabs,tR=(0,a.useRef)(null),tI=(0,a.useRef)(null),tT=(0,a.useRef)(null),tM=(0,a.useRef)(null),tP=(0,a.useRef)(null),tL=(0,a.useRef)(null),tN=(0,a.useRef)(null),tB="top"===tA||"bottom"===tA,tD=O(0,function(t,e){tB&&tz&&tz({direction:t>e?"left":"right"})}),tH=(0,b.A)(tD,2),tG=tH[0],tW=tH[1],tq=O(0,function(t,e){!tB&&tz&&tz({direction:t>e?"top":"bottom"})}),tX=(0,b.A)(tq,2),tF=tX[0],tK=tX[1],tQ=(0,a.useState)([0,0]),tV=(0,b.A)(tQ,2),tY=tV[0],tU=tV[1],tJ=(0,a.useState)([0,0]),tZ=(0,b.A)(tJ,2),t$=tZ[0],t0=tZ[1],t1=(0,a.useState)([0,0]),t2=(0,b.A)(t1,2),t4=t2[0],t7=t2[1],t8=(0,a.useState)([0,0]),t5=(0,b.A)(t8,2),t6=t5[0],t3=t5[1],t9=(n=new Map,o=(0,a.useRef)([]),c=(0,a.useState)({}),r=(0,b.A)(c,2)[1],i=(0,a.useRef)("function"==typeof n?n():n),p=E(function(){var t=i.current;o.current.forEach(function(e){t=e(t)}),o.current=[],i.current=t,r({})}),[i.current,function(t){o.current.push(t),p()}]),et=(0,b.A)(t9,2),ee=et[0],en=et[1],ea=(f=t$[0],(0,a.useMemo)(function(){for(var t=new Map,e=ee.get(null==(o=t_[0])?void 0:o.key)||S,n=e.left+e.width,a=0;a<t_.length;a+=1){var o,c,r=t_[a].key,i=ee.get(r);i||(i=ee.get(null==(c=t_[a-1])?void 0:c.key)||S);var l=t.get(r)||(0,u.A)({},i);l.right=n-l.left-l.width,t.set(r,l)}return t},[t_.map(function(t){return t.key}).join("_"),ee,f])),eo=W(tY,tB),ec=W(t$,tB),er=W(t4,tB),ei=W(t6,tB),el=Math.floor(eo)<Math.floor(ec+er),es=el?eo-ei:eo-er,ed="".concat(tj,"-nav-operations-hidden"),eu=0,eb=0;function ep(t){return t<eu?eu:t>eb?eb:t}tB&&th?(eu=0,eb=Math.max(0,ec-es)):(eu=Math.min(0,es-ec),eb=0);var ef=(0,a.useRef)(null),eg=(0,a.useState)(),ev=(0,b.A)(eg,2),em=ev[0],eh=ev[1];function ey(){eh(Date.now())}function ek(){ef.current&&clearTimeout(ef.current)}g=function(t,e){function n(t,e){t(function(t){return ep(t+e)})}return!!el&&(tB?n(tW,t):n(tK,e),ek(),ey(),!0)},v=(0,a.useState)(),z=(A=(0,b.A)(v,2))[0],M=A[1],P=(0,a.useState)(0),N=(L=(0,b.A)(P,2))[0],q=L[1],X=(0,a.useState)(0),K=(F=(0,b.A)(X,2))[0],Q=F[1],V=(0,a.useState)(),U=(Y=(0,b.A)(V,2))[0],J=Y[1],Z=(0,a.useRef)(),$=(0,a.useRef)(),(tt=(0,a.useRef)(null)).current={onTouchStart:function(t){var e=t.touches[0];M({x:e.screenX,y:e.screenY}),window.clearInterval(Z.current)},onTouchMove:function(t){if(z){var e=t.touches[0],n=e.screenX,a=e.screenY;M({x:n,y:a});var o=n-z.x,c=a-z.y;g(o,c);var r=Date.now();q(r),Q(r-N),J({x:o,y:c})}},onTouchEnd:function(){if(z&&(M(null),J(null),U)){var t=U.x/K,e=U.y/K;if(!(.1>Math.max(Math.abs(t),Math.abs(e)))){var n=t,a=e;Z.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(a))return void window.clearInterval(Z.current);n*=.9046104802746175,a*=.9046104802746175,g(20*n,20*a)},20)}}},onWheel:function(t){var e=t.deltaX,n=t.deltaY,a=0,o=Math.abs(e),c=Math.abs(n);o===c?a="x"===$.current?e:n:o>c?(a=e,$.current="x"):(a=n,$.current="y"),g(-a,-a)&&t.preventDefault()}},a.useEffect(function(){function t(t){tt.current.onTouchMove(t)}function e(t){tt.current.onTouchEnd(t)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",e,{passive:!0}),tM.current.addEventListener("touchstart",function(t){tt.current.onTouchStart(t)},{passive:!0}),tM.current.addEventListener("wheel",function(t){tt.current.onWheel(t)},{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",e)}},[]),(0,a.useEffect)(function(){return ek(),em&&(ef.current=setTimeout(function(){eh(0)},100)),ek},[em]);var ex=(te=tB?tG:tF,tr=(tn=(0,u.A)((0,u.A)({},t),{},{tabs:t_})).tabs,ti=tn.tabPosition,tl=tn.rtl,["top","bottom"].includes(ti)?(ta="width",to=tl?"right":"left",tc=Math.abs(te)):(ta="height",to="top",tc=-te),(0,a.useMemo)(function(){if(!tr.length)return[0,0];for(var t=tr.length,e=t,n=0;n<t;n+=1){var a=ea.get(tr[n].key)||C;if(Math.floor(a[to]+a[ta])>Math.floor(tc+es)){e=n-1;break}}for(var o=0,c=t-1;c>=0;c-=1)if((ea.get(tr[c].key)||C)[to]<tc){o=c+1;break}return o>=e?[0,0]:[o,e]},[ea,es,ec,er,ei,tc,ti,tr.map(function(t){return t.key}).join("_"),tl])),eA=(0,b.A)(ex,2),ew=eA[0],eS=eA[1],eO=(0,k.A)(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tm,e=ea.get(t)||{width:0,height:0,left:0,right:0,top:0};if(tB){var n=tG;th?e.right<tG?n=e.right:e.right+e.width>tG+es&&(n=e.right+e.width-es):e.left<-tG?n=-e.left:e.left+e.width>-tG+es&&(n=-(e.left+e.width-es)),tK(0),tW(ep(n))}else{var a=tF;e.top<-tF?a=-e.top:e.top+e.height>-tF+es&&(a=-(e.top+e.height-es)),tW(0),tK(ep(a))}}),ez=(0,a.useState)(),eE=(0,b.A)(ez,2),eC=eE[0],ej=eE[1],e_=(0,a.useState)(!1),eR=(0,b.A)(e_,2),eI=eR[0],eT=eR[1],eM=t_.filter(function(t){return!t.disabled}).map(function(t){return t.key}),eP=function(t){var e=eM.indexOf(eC||tm),n=eM.length;ej(eM[(e+t+n)%n])},eL=function(t){var e=t.code,n=th&&tB,a=eM[0],o=eM[eM.length-1];switch(e){case"ArrowLeft":tB&&eP(n?1:-1);break;case"ArrowRight":tB&&eP(n?-1:1);break;case"ArrowUp":t.preventDefault(),tB||eP(-1);break;case"ArrowDown":t.preventDefault(),tB||eP(1);break;case"Home":t.preventDefault(),ej(a);break;case"End":t.preventDefault(),ej(o);break;case"Enter":case"Space":t.preventDefault(),tO(null!=eC?eC:tm,t);break;case"Backspace":case"Delete":var c=eM.indexOf(eC),r=t_.find(function(t){return t.key===eC});R(null==r?void 0:r.closable,null==r?void 0:r.closeIcon,tk,null==r?void 0:r.disabled)&&(t.preventDefault(),t.stopPropagation(),tk.onEdit("remove",{key:eC,event:t}),c===eM.length-1?eP(-1):eP(1))}},eN={};tB?eN[th?"marginRight":"marginLeft"]=tw:eN.marginTop=tw;var eB=t_.map(function(t,e){var n=t.key;return a.createElement(D,{id:tg,prefixCls:tj,key:n,tab:t,style:0===e?void 0:eN,closable:t.closable,editable:tk,active:n===tm,focus:n===eC,renderWrapper:tS,removeAriaLabel:null==tx?void 0:tx.removeAriaLabel,tabCount:eM.length,currentPosition:e+1,onClick:function(t){tO(n,t)},onKeyDown:eL,onFocus:function(){eI||ej(n),eO(n),ey(),tM.current&&(th||(tM.current.scrollLeft=0),tM.current.scrollTop=0)},onBlur:function(){ej(void 0)},onMouseDown:function(){eT(!0)},onMouseUp:function(){eT(!1)}})}),eD=function(){return en(function(){var t,e=new Map,n=null==(t=tP.current)?void 0:t.getBoundingClientRect();return t_.forEach(function(t){var a,o=t.key,c=null==(a=tP.current)?void 0:a.querySelector('[data-node-key="'.concat(_(o),'"]'));if(c){var r=H(c,n),i=(0,b.A)(r,4),l=i[0],s=i[1],d=i[2],u=i[3];e.set(o,{width:l,height:s,left:d,top:u})}}),e})};(0,a.useEffect)(function(){eD()},[t_.map(function(t){return t.key}).join("_")]);var eH=E(function(){var t=G(tR),e=G(tI),n=G(tT);tU([t[0]-e[0]-n[0],t[1]-e[1]-n[1]]);var a=G(tN);t7(a),t3(G(tL));var o=G(tP);t0([o[0]-a[0],o[1]-a[1]]),eD()}),eG=t_.slice(0,ew),eW=t_.slice(eS+1),eq=[].concat((0,h.A)(eG),(0,h.A)(eW)),eX=ea.get(tm),eF=w({activeTabOffset:eX,horizontal:tB,indicator:tE,rtl:th}).style;(0,a.useEffect)(function(){eO()},[tm,eu,eb,j(eX),j(ea),tB]),(0,a.useEffect)(function(){eH()},[th]);var eK=!!eq.length,eQ="".concat(tj,"-nav-wrap");return tB?th?(td=tG>0,ts=tG!==eb):(ts=tG<0,td=tG!==eu):(tu=tF<0,tb=tF!==eu),a.createElement(y.A,{onResize:eH},a.createElement("div",{ref:(0,x.xK)(e,tR),role:"tablist","aria-orientation":tB?"horizontal":"vertical",className:l()("".concat(tj,"-nav"),tp),style:tf,onKeyDown:function(){ey()}},a.createElement(T,{ref:tI,position:"left",extra:ty,prefixCls:tj}),a.createElement(y.A,{onResize:eH},a.createElement("div",{className:l()(eQ,(0,d.A)((0,d.A)((0,d.A)((0,d.A)({},"".concat(eQ,"-ping-left"),ts),"".concat(eQ,"-ping-right"),td),"".concat(eQ,"-ping-top"),tu),"".concat(eQ,"-ping-bottom"),tb)),ref:tM},a.createElement(y.A,{onResize:eH},a.createElement("div",{ref:tP,className:"".concat(tj,"-nav-list"),style:{transform:"translate(".concat(tG,"px, ").concat(tF,"px)"),transition:em?"none":void 0}},eB,a.createElement(I,{ref:tN,prefixCls:tj,locale:tx,editable:tk,style:(0,u.A)((0,u.A)({},0===eB.length?void 0:eN),{},{visibility:eK?"hidden":null})}),a.createElement("div",{className:l()("".concat(tj,"-ink-bar"),(0,d.A)({},"".concat(tj,"-ink-bar-animated"),tv.inkBar)),style:eF}))))),a.createElement(B,(0,s.A)({},t,{removeAriaLabel:null==tx?void 0:tx.removeAriaLabel,ref:tL,prefixCls:tj,tabs:eq,className:!eK&&ed,tabMoving:!!em})),a.createElement(T,{ref:tT,position:"right",extra:ty,prefixCls:tj})))}),X=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.className,c=t.style,r=t.id,i=t.active,s=t.tabKey,d=t.children;return a.createElement("div",{id:r&&"".concat(r,"-panel-").concat(s),role:"tabpanel",tabIndex:i?0:-1,"aria-labelledby":r&&"".concat(r,"-tab-").concat(s),"aria-hidden":!i,style:c,className:l()(n,i&&"".concat(n,"-active"),o),ref:e},d)}),F=["renderTabBar"],K=["label","key"];let Q=function(t){var e=t.renderTabBar,n=(0,f.A)(t,F),o=a.useContext(m).tabs;return e?e((0,u.A)((0,u.A)({},n),{},{panes:o.map(function(t){var e=t.label,n=t.key,o=(0,f.A)(t,K);return a.createElement(X,(0,s.A)({tab:e,key:n,tabKey:n},o))})}),q):a.createElement(q,n)};var V=n(82870),Y=["key","forceRender","style","className","destroyInactiveTabPane"];let U=function(t){var e=t.id,n=t.activeKey,o=t.animated,c=t.tabPosition,r=t.destroyInactiveTabPane,i=a.useContext(m),b=i.prefixCls,p=i.tabs,g=o.tabPane,v="".concat(b,"-tabpane");return a.createElement("div",{className:l()("".concat(b,"-content-holder"))},a.createElement("div",{className:l()("".concat(b,"-content"),"".concat(b,"-content-").concat(c),(0,d.A)({},"".concat(b,"-content-animated"),g))},p.map(function(t){var c=t.key,i=t.forceRender,d=t.style,b=t.className,p=t.destroyInactiveTabPane,m=(0,f.A)(t,Y),h=c===n;return a.createElement(V.Ay,(0,s.A)({key:c,visible:h,forceRender:i,removeOnLeave:!!(r||p),leavedClassName:"".concat(v,"-hidden")},o.tabPaneMotion),function(t,n){var o=t.style,r=t.className;return a.createElement(X,(0,s.A)({},m,{prefixCls:v,id:e,tabKey:c,animated:g,active:h,style:(0,u.A)((0,u.A)({},d),o),className:l()(b,r),ref:n}))})})))};n(9587);var J=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],Z=0,$=a.forwardRef(function(t,e){var n=t.id,o=t.prefixCls,c=void 0===o?"rc-tabs":o,r=t.className,i=t.items,h=t.direction,y=t.activeKey,k=t.defaultActiveKey,x=t.editable,A=t.animated,w=t.tabPosition,S=void 0===w?"top":w,O=t.tabBarGutter,z=t.tabBarStyle,E=t.tabBarExtraContent,C=t.locale,j=t.more,_=t.destroyInactiveTabPane,R=t.renderTabBar,I=t.onChange,T=t.onTabClick,M=t.onTabScroll,P=t.getPopupContainer,L=t.popupClassName,N=t.indicator,B=(0,f.A)(t,J),D=a.useMemo(function(){return(i||[]).filter(function(t){return t&&"object"===(0,p.A)(t)&&"key"in t})},[i]),H="rtl"===h,G=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(t=!1===e?{inkBar:!1,tabPane:!1}:!0===e?{inkBar:!0,tabPane:!1}:(0,u.A)({inkBar:!0},"object"===(0,p.A)(e)?e:{})).tabPaneMotion&&void 0===t.tabPane&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}(A),W=(0,a.useState)(!1),q=(0,b.A)(W,2),X=q[0],F=q[1];(0,a.useEffect)(function(){F((0,v.A)())},[]);var K=(0,g.A)(function(){var t;return null==(t=D[0])?void 0:t.key},{value:y,defaultValue:k}),V=(0,b.A)(K,2),Y=V[0],$=V[1],tt=(0,a.useState)(function(){return D.findIndex(function(t){return t.key===Y})}),te=(0,b.A)(tt,2),tn=te[0],ta=te[1];(0,a.useEffect)(function(){var t,e=D.findIndex(function(t){return t.key===Y});-1===e&&(e=Math.max(0,Math.min(tn,D.length-1)),$(null==(t=D[e])?void 0:t.key)),ta(e)},[D.map(function(t){return t.key}).join("_"),Y,tn]);var to=(0,g.A)(null,{value:n}),tc=(0,b.A)(to,2),tr=tc[0],ti=tc[1];(0,a.useEffect)(function(){n||(ti("rc-tabs-".concat(Z)),Z+=1)},[]);var tl={id:tr,activeKey:Y,animated:G,tabPosition:S,rtl:H,mobile:X},ts=(0,u.A)((0,u.A)({},tl),{},{editable:x,locale:C,more:j,tabBarGutter:O,onTabClick:function(t,e){null==T||T(t,e);var n=t!==Y;$(t),n&&(null==I||I(t))},onTabScroll:M,extra:E,style:z,panes:null,getPopupContainer:P,popupClassName:L,indicator:N});return a.createElement(m.Provider,{value:{tabs:D,prefixCls:c}},a.createElement("div",(0,s.A)({ref:e,id:n,className:l()(c,"".concat(c,"-").concat(S),(0,d.A)((0,d.A)((0,d.A)({},"".concat(c,"-mobile"),X),"".concat(c,"-editable"),x),"".concat(c,"-rtl"),H),r)},B),a.createElement(Q,(0,s.A)({},ts,{renderTabBar:R})),a.createElement(U,(0,s.A)({destroyInactiveTabPane:_},tl,{animated:G}))))}),tt=n(15982),te=n(68151),tn=n(9836),ta=n(93666);let to={motionAppear:!1,motionEnter:!0,motionLeave:!0};var tc=n(63715),tr=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n},ti=n(85573),tl=n(18184),ts=n(45431),td=n(61388),tu=n(53272);let tb=t=>{let{componentCls:e,motionDurationSlow:n}=t;return[{[e]:{["".concat(e,"-switch")]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:"opacity ".concat(n)}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:"opacity ".concat(n)}}}}},[(0,tu._j)(t,"slide-up"),(0,tu._j)(t,"slide-down")]]},tp=t=>{let{componentCls:e,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:c,itemSelectedColor:r}=t;return{["".concat(e,"-card")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:0,padding:n,background:a,border:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(c),transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut)},["".concat(e,"-tab-active")]:{color:r,background:t.colorBgContainer},["".concat(e,"-tab-focus:has(").concat(e,"-tab-btn:focus-visible)")]:(0,tl.jk)(t,-3),["& ".concat(e,"-tab").concat(e,"-tab-focus ").concat(e,"-tab-btn:focus-visible")]:{outline:"none"},["".concat(e,"-ink-bar")]:{visibility:"hidden"}},["&".concat(e,"-top, &").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginLeft:{_skip_check_:!0,value:(0,ti.zA)(o)}}}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0 0")},["".concat(e,"-tab-active")]:{borderBottomColor:t.colorBgContainer}}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG))},["".concat(e,"-tab-active")]:{borderTopColor:t.colorBgContainer}}},["&".concat(e,"-left, &").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginTop:(0,ti.zA)(o)}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.borderRadiusLG)," 0 0 ").concat((0,ti.zA)(t.borderRadiusLG))}},["".concat(e,"-tab-active")]:{borderRightColor:{_skip_check_:!0,value:t.colorBgContainer}}}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0")}},["".concat(e,"-tab-active")]:{borderLeftColor:{_skip_check_:!0,value:t.colorBgContainer}}}}}}},tf=t=>{let{componentCls:e,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=t;return{["".concat(e,"-dropdown")]:Object.assign(Object.assign({},(0,tl.dF)(t)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:t.zIndexPopup,display:"block","&-hidden":{display:"none"},["".concat(e,"-dropdown-menu")]:{maxHeight:t.tabsDropdownHeight,margin:0,padding:"".concat((0,ti.zA)(a)," 0"),overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:t.colorBgContainer,backgroundClip:"padding-box",borderRadius:t.borderRadiusLG,outline:"none",boxShadow:t.boxShadowSecondary,"&-item":Object.assign(Object.assign({},tl.L9),{display:"flex",alignItems:"center",minWidth:t.tabsDropdownWidth,margin:0,padding:"".concat((0,ti.zA)(t.paddingXXS)," ").concat((0,ti.zA)(t.paddingSM)),color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:t.marginSM},color:t.colorIcon,fontSize:t.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:t.controlItemBgHover},"&-disabled":{"&, &:hover":{color:t.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},tg=t=>{let{componentCls:e,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:c,verticalItemMargin:r,calc:i}=t;return{["".concat(e,"-top, ").concat(e,"-bottom")]:{flexDirection:"column",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(a),content:"''"},["".concat(e,"-ink-bar")]:{height:t.lineWidthBold,"&-animated":{transition:"width ".concat(t.motionDurationSlow,", left ").concat(t.motionDurationSlow,",\n            right ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-wrap")]:{"&::before, &::after":{top:0,bottom:0,width:t.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowRight},["&".concat(e,"-nav-wrap-ping-left::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-right::after")]:{opacity:1}}}},["".concat(e,"-top")]:{["> ".concat(e,"-nav,\n        > div > ").concat(e,"-nav")]:{"&::before":{bottom:0},["".concat(e,"-ink-bar")]:{bottom:0}}},["".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},["".concat(e,"-ink-bar")]:{top:0}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0}},["".concat(e,"-left, ").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{flexDirection:"column",minWidth:i(t.controlHeight).mul(1.25).equal(),["".concat(e,"-tab")]:{padding:c,textAlign:"center"},["".concat(e,"-tab + ").concat(e,"-tab")]:{margin:r},["".concat(e,"-nav-wrap")]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:t.controlHeight},"&::before":{top:0,boxShadow:t.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:t.boxShadowTabsOverflowBottom},["&".concat(e,"-nav-wrap-ping-top::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-bottom::after")]:{opacity:1}},["".concat(e,"-ink-bar")]:{width:t.lineWidthBold,"&-animated":{transition:"height ".concat(t.motionDurationSlow,", top ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-list, ").concat(e,"-nav-operations")]:{flex:"1 0 auto",flexDirection:"column"}}},["".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-ink-bar")]:{right:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{marginLeft:{_skip_check_:!0,value:(0,ti.zA)(i(t.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingLeft:{_skip_check_:!0,value:t.paddingLG}}}},["".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,["".concat(e,"-ink-bar")]:{left:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0,marginRight:{_skip_check_:!0,value:i(t.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingRight:{_skip_check_:!0,value:t.paddingLG}}}}}},tv=t=>{let{componentCls:e,cardPaddingSM:n,cardPaddingLG:a,cardHeightSM:o,cardHeightLG:c,horizontalItemPaddingSM:r,horizontalItemPaddingLG:i}=t;return{[e]:{"&-small":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:r,fontSize:t.titleFontSizeSM}}},"&-large":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:i,fontSize:t.titleFontSizeLG,lineHeight:t.lineHeightLG}}}},["".concat(e,"-card")]:{["&".concat(e,"-small")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:n},["".concat(e,"-nav-add")]:{minWidth:o,minHeight:o}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius))}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius)," 0 0")}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius)," 0")}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.borderRadius)," 0 0 ").concat((0,ti.zA)(t.borderRadius))}}}},["&".concat(e,"-large")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:a},["".concat(e,"-nav-add")]:{minWidth:c,minHeight:c}}}}}},tm=t=>{let{componentCls:e,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:c,horizontalItemPadding:r,itemSelectedColor:i,itemColor:l}=t,s="".concat(e,"-tab");return{[s]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:r,fontSize:t.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:"all ".concat(t.motionDurationSlow),["".concat(s,"-icon:not(:last-child)")]:{marginInlineEnd:t.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:t.calc(t.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:t.marginXS},color:t.colorIcon,fontSize:t.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"&:hover":{color:t.colorTextHeading}},(0,tl.K8)(t)),"&:hover":{color:a},["&".concat(s,"-active ").concat(s,"-btn")]:{color:i,textShadow:t.tabsActiveTextShadow},["&".concat(s,"-focus ").concat(s,"-btn:focus-visible")]:(0,tl.jk)(t),["&".concat(s,"-disabled")]:{color:t.colorTextDisabled,cursor:"not-allowed"},["&".concat(s,"-disabled ").concat(s,"-btn, &").concat(s,"-disabled ").concat(e,"-remove")]:{"&:focus, &:active":{color:t.colorTextDisabled}},["& ".concat(s,"-remove ").concat(o)]:{margin:0},["".concat(o,":not(:last-child)")]:{marginRight:{_skip_check_:!0,value:t.marginSM}}},["".concat(s," + ").concat(s)]:{margin:{_skip_check_:!0,value:c}}}},th=t=>{let{componentCls:e,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:c}=t;return{["".concat(e,"-rtl")]:{direction:"rtl",["".concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:{_skip_check_:!0,value:n},["".concat(e,"-tab:last-of-type")]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,ti.zA)(t.marginSM)}},["".concat(e,"-tab-remove")]:{marginRight:{_skip_check_:!0,value:(0,ti.zA)(t.marginXS)},marginLeft:{_skip_check_:!0,value:(0,ti.zA)(c(t.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav")]:{order:1},["> ".concat(e,"-content-holder")]:{order:0}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav")]:{order:0},["> ".concat(e,"-content-holder")]:{order:1}},["&".concat(e,"-card").concat(e,"-top, &").concat(e,"-card").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},["".concat(e,"-dropdown-rtl")]:{direction:"rtl"},["".concat(e,"-menu-item")]:{["".concat(e,"-dropdown-rtl")]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ty=t=>{let{componentCls:e,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:c,itemActiveColor:r,colorBorderSecondary:i}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,tl.dF)(t)),{display:"flex",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{position:"relative",display:"flex",flex:"none",alignItems:"center",["".concat(e,"-nav-wrap")]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:"opacity ".concat(t.motionDurationSlow),content:"''",pointerEvents:"none"}},["".concat(e,"-nav-list")]:{position:"relative",display:"flex",transition:"opacity ".concat(t.motionDurationSlow)},["".concat(e,"-nav-operations")]:{display:"flex",alignSelf:"stretch"},["".concat(e,"-nav-operations-hidden")]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},["".concat(e,"-nav-more")]:{position:"relative",padding:n,background:"transparent",border:0,color:t.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:t.calc(t.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},["".concat(e,"-nav-add")]:Object.assign({minWidth:a,minHeight:a,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(i),borderRadius:"".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0 0"),outline:"none",cursor:"pointer",color:t.colorText,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut),"&:hover":{color:c},"&:active, &:focus:not(:focus-visible)":{color:r}},(0,tl.K8)(t,-3))},["".concat(e,"-extra-content")]:{flex:"none"},["".concat(e,"-ink-bar")]:{position:"absolute",background:t.inkBarColor,pointerEvents:"none"}}),tm(t)),{["".concat(e,"-content")]:{position:"relative",width:"100%"},["".concat(e,"-content-holder")]:{flex:"auto",minWidth:0,minHeight:0},["".concat(e,"-tabpane")]:Object.assign(Object.assign({},(0,tl.K8)(t)),{"&-hidden":{display:"none"}})}),["".concat(e,"-centered")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-nav-wrap")]:{["&:not([class*='".concat(e,"-nav-wrap-ping']) > ").concat(e,"-nav-list")]:{margin:"auto"}}}}}},tk=(0,ts.OF)("Tabs",t=>{let e=(0,td.oX)(t,{tabsCardPadding:t.cardPadding,dropdownEdgeChildVerticalPadding:t.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:"0 0 0 ".concat((0,ti.zA)(t.horizontalItemGutter)),tabsHorizontalItemMarginRTL:"0 0 0 ".concat((0,ti.zA)(t.horizontalItemGutter))});return[tv(e),th(e),tg(e),tf(e),tp(e),ty(e),tb(e)]},t=>{let{cardHeight:e,cardHeightSM:n,cardHeightLG:a,controlHeight:o,controlHeightLG:c}=t,r=e||c,i=n||o,l=a||c+8;return{zIndexPopup:t.zIndexPopupBase+50,cardBg:t.colorFillAlter,cardHeight:r,cardHeightSM:i,cardHeightLG:l,cardPadding:"".concat((r-t.fontHeight)/2-t.lineWidth,"px ").concat(t.padding,"px"),cardPaddingSM:"".concat((i-t.fontHeight)/2-t.lineWidth,"px ").concat(t.paddingXS,"px"),cardPaddingLG:"".concat((l-t.fontHeightLG)/2-t.lineWidth,"px ").concat(t.padding,"px"),titleFontSize:t.fontSize,titleFontSizeLG:t.fontSizeLG,titleFontSizeSM:t.fontSize,inkBarColor:t.colorPrimary,horizontalMargin:"0 0 ".concat(t.margin,"px 0"),horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:"".concat(t.paddingSM,"px 0"),horizontalItemPaddingSM:"".concat(t.paddingXS,"px 0"),horizontalItemPaddingLG:"".concat(t.padding,"px 0"),verticalItemPadding:"".concat(t.paddingXS,"px ").concat(t.paddingLG,"px"),verticalItemMargin:"".concat(t.margin,"px 0 0 0"),itemColor:t.colorText,itemSelectedColor:t.colorPrimary,itemHoverColor:t.colorPrimaryHover,itemActiveColor:t.colorPrimaryActive,cardGutter:t.marginXXS/2}});var tx=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let tA=t=>{var e,n,i,s,d,u,b,p,f,g,v;let m,{type:h,className:y,rootClassName:k,size:x,onEdit:A,hideAdd:w,centered:S,addIcon:O,removeIcon:z,moreIcon:E,more:C,popupClassName:j,children:_,items:R,animated:I,style:T,indicatorSize:M,indicator:P,destroyInactiveTabPane:L,destroyOnHidden:N}=t,B=tx(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:D}=B,{direction:H,tabs:G,getPrefixCls:W,getPopupContainer:q}=a.useContext(tt.QO),X=W("tabs",D),F=(0,te.A)(X),[K,Q,V]=tk(X,F);"editable-card"===h&&(m={onEdit:(t,e)=>{let{key:n,event:a}=e;null==A||A("add"===t?a:n,t)},removeIcon:null!=(e=null!=z?z:null==G?void 0:G.removeIcon)?e:a.createElement(o.A,null),addIcon:(null!=O?O:null==G?void 0:G.addIcon)||a.createElement(r.A,null),showAdd:!0!==w});let Y=W(),U=(0,tn.A)(x),J=function(t,e){return t?t.map(t=>{var e;let n=null!=(e=t.destroyOnHidden)?e:t.destroyInactiveTabPane;return Object.assign(Object.assign({},t),{destroyInactiveTabPane:n})}):(0,tc.A)(e).map(t=>{if(a.isValidElement(t)){let{key:e,props:n}=t,a=n||{},{tab:o}=a,c=tr(a,["tab"]);return Object.assign(Object.assign({key:String(e)},c),{label:o})}return null}).filter(t=>t)}(R,_),Z=function(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return(e=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{})).tabPane&&(e.tabPaneMotion=Object.assign(Object.assign({},to),{motionName:(0,ta.b)(t,"switch")})),e}(X,I),ti=Object.assign(Object.assign({},null==G?void 0:G.style),T),tl={align:null!=(n=null==P?void 0:P.align)?n:null==(i=null==G?void 0:G.indicator)?void 0:i.align,size:null!=(b=null!=(d=null!=(s=null==P?void 0:P.size)?s:M)?d:null==(u=null==G?void 0:G.indicator)?void 0:u.size)?b:null==G?void 0:G.indicatorSize};return K(a.createElement($,Object.assign({direction:H,getPopupContainer:q},B,{items:J,className:l()({["".concat(X,"-").concat(U)]:U,["".concat(X,"-card")]:["card","editable-card"].includes(h),["".concat(X,"-editable-card")]:"editable-card"===h,["".concat(X,"-centered")]:S},null==G?void 0:G.className,y,k,Q,V,F),popupClassName:l()(j,Q,V,F),style:ti,editable:m,more:Object.assign({icon:null!=(v=null!=(g=null!=(f=null==(p=null==G?void 0:G.more)?void 0:p.icon)?f:null==G?void 0:G.moreIcon)?g:E)?v:a.createElement(c.A,null),transitionName:"".concat(Y,"-slide-up")},C),prefixCls:X,animated:Z,indicator:tl,destroyInactiveTabPane:null!=N?N:L})))};tA.TabPane=()=>null;let tw=tA},46996:(t,e,n)=>{n.d(e,{A:()=>i});var a=n(79630),o=n(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};var r=n(62764);let i=o.forwardRef(function(t,e){return o.createElement(r.A,(0,a.A)({},t,{ref:e,icon:c}))})},47852:(t,e,n)=>{n.d(e,{A:()=>i});var a=n(79630),o=n(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"exclamation-circle",theme:"filled"};var r=n(62764);let i=o.forwardRef(function(t,e){return o.createElement(r.A,(0,a.A)({},t,{ref:e,icon:c}))})},50199:(t,e,n)=>{n.d(e,{L3:()=>d,i4:()=>u,xV:()=>b});var a=n(85573),o=n(45431),c=n(61388);let r=t=>{let{componentCls:e}=t;return{[e]:{position:"relative",maxWidth:"100%",minHeight:1}}},i=(t,e)=>{let{prefixCls:n,componentCls:a,gridColumns:o}=t,c={};for(let t=o;t>=0;t--)0===t?(c["".concat(a).concat(e,"-").concat(t)]={display:"none"},c["".concat(a,"-push-").concat(t)]={insetInlineStart:"auto"},c["".concat(a,"-pull-").concat(t)]={insetInlineEnd:"auto"},c["".concat(a).concat(e,"-push-").concat(t)]={insetInlineStart:"auto"},c["".concat(a).concat(e,"-pull-").concat(t)]={insetInlineEnd:"auto"},c["".concat(a).concat(e,"-offset-").concat(t)]={marginInlineStart:0},c["".concat(a).concat(e,"-order-").concat(t)]={order:0}):(c["".concat(a).concat(e,"-").concat(t)]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:"0 0 ".concat(t/o*100,"%"),maxWidth:"".concat(t/o*100,"%")}],c["".concat(a).concat(e,"-push-").concat(t)]={insetInlineStart:"".concat(t/o*100,"%")},c["".concat(a).concat(e,"-pull-").concat(t)]={insetInlineEnd:"".concat(t/o*100,"%")},c["".concat(a).concat(e,"-offset-").concat(t)]={marginInlineStart:"".concat(t/o*100,"%")},c["".concat(a).concat(e,"-order-").concat(t)]={order:t});return c["".concat(a).concat(e,"-flex")]={flex:"var(--".concat(n).concat(e,"-flex)")},c},l=(t,e)=>i(t,e),s=(t,e,n)=>({["@media (min-width: ".concat((0,a.zA)(e),")")]:Object.assign({},l(t,n))}),d=(0,o.OF)("Grid",t=>{let{componentCls:e}=t;return{[e]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},()=>({})),u=t=>({xs:t.screenXSMin,sm:t.screenSMMin,md:t.screenMDMin,lg:t.screenLGMin,xl:t.screenXLMin,xxl:t.screenXXLMin}),b=(0,o.OF)("Grid",t=>{let e=(0,c.oX)(t,{gridColumns:24}),n=u(e);return delete n.xs,[r(e),l(e,""),l(e,"-xs"),Object.keys(n).map(t=>s(e,n[t],"-".concat(t))).reduce((t,e)=>Object.assign(Object.assign({},t),e),{})]},()=>({}))},58587:(t,e,n)=>{n.d(e,{A:()=>i});var a=n(79630),o=n(12115);let c={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"};var r=n(62764);let i=o.forwardRef(function(t,e){return o.createElement(r.A,(0,a.A)({},t,{ref:e,icon:c}))})},70802:(t,e,n)=>{n.d(e,{A:()=>R});var a=n(12115),o=n(29300),c=n.n(o),r=n(15982),i=n(17980);let l=t=>{let{prefixCls:e,className:n,style:o,size:r,shape:i}=t,l=c()({["".concat(e,"-lg")]:"large"===r,["".concat(e,"-sm")]:"small"===r}),s=c()({["".concat(e,"-circle")]:"circle"===i,["".concat(e,"-square")]:"square"===i,["".concat(e,"-round")]:"round"===i}),d=a.useMemo(()=>"number"==typeof r?{width:r,height:r,lineHeight:"".concat(r,"px")}:{},[r]);return a.createElement("span",{className:c()(e,l,s,n),style:Object.assign(Object.assign({},d),o)})};var s=n(85573),d=n(45431),u=n(61388);let b=new s.Mo("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),p=t=>({height:t,lineHeight:(0,s.zA)(t)}),f=t=>Object.assign({width:t},p(t)),g=t=>({background:t.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:b,animationDuration:t.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),v=(t,e)=>Object.assign({width:e(t).mul(5).equal(),minWidth:e(t).mul(5).equal()},p(t)),m=t=>{let{skeletonAvatarCls:e,gradientFromColor:n,controlHeight:a,controlHeightLG:o,controlHeightSM:c}=t;return{[e]:Object.assign({display:"inline-block",verticalAlign:"top",background:n},f(a)),["".concat(e).concat(e,"-circle")]:{borderRadius:"50%"},["".concat(e).concat(e,"-lg")]:Object.assign({},f(o)),["".concat(e).concat(e,"-sm")]:Object.assign({},f(c))}},h=t=>{let{controlHeight:e,borderRadiusSM:n,skeletonInputCls:a,controlHeightLG:o,controlHeightSM:c,gradientFromColor:r,calc:i}=t;return{[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:r,borderRadius:n},v(e,i)),["".concat(a,"-lg")]:Object.assign({},v(o,i)),["".concat(a,"-sm")]:Object.assign({},v(c,i))}},y=t=>Object.assign({width:t},p(t)),k=t=>{let{skeletonImageCls:e,imageSizeBase:n,gradientFromColor:a,borderRadiusSM:o,calc:c}=t;return{[e]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:a,borderRadius:o},y(c(n).mul(2).equal())),{["".concat(e,"-path")]:{fill:"#bfbfbf"},["".concat(e,"-svg")]:Object.assign(Object.assign({},y(n)),{maxWidth:c(n).mul(4).equal(),maxHeight:c(n).mul(4).equal()}),["".concat(e,"-svg").concat(e,"-svg-circle")]:{borderRadius:"50%"}}),["".concat(e).concat(e,"-circle")]:{borderRadius:"50%"}}},x=(t,e,n)=>{let{skeletonButtonCls:a}=t;return{["".concat(n).concat(a,"-circle")]:{width:e,minWidth:e,borderRadius:"50%"},["".concat(n).concat(a,"-round")]:{borderRadius:e}}},A=(t,e)=>Object.assign({width:e(t).mul(2).equal(),minWidth:e(t).mul(2).equal()},p(t)),w=t=>{let{borderRadiusSM:e,skeletonButtonCls:n,controlHeight:a,controlHeightLG:o,controlHeightSM:c,gradientFromColor:r,calc:i}=t;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:r,borderRadius:e,width:i(a).mul(2).equal(),minWidth:i(a).mul(2).equal()},A(a,i))},x(t,a,n)),{["".concat(n,"-lg")]:Object.assign({},A(o,i))}),x(t,o,"".concat(n,"-lg"))),{["".concat(n,"-sm")]:Object.assign({},A(c,i))}),x(t,c,"".concat(n,"-sm")))},S=t=>{let{componentCls:e,skeletonAvatarCls:n,skeletonTitleCls:a,skeletonParagraphCls:o,skeletonButtonCls:c,skeletonInputCls:r,skeletonImageCls:i,controlHeight:l,controlHeightLG:s,controlHeightSM:d,gradientFromColor:u,padding:b,marginSM:p,borderRadius:v,titleHeight:y,blockRadius:x,paragraphLiHeight:A,controlHeightXS:S,paragraphMarginTop:O}=t;return{[e]:{display:"table",width:"100%",["".concat(e,"-header")]:{display:"table-cell",paddingInlineEnd:b,verticalAlign:"top",[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:u},f(l)),["".concat(n,"-circle")]:{borderRadius:"50%"},["".concat(n,"-lg")]:Object.assign({},f(s)),["".concat(n,"-sm")]:Object.assign({},f(d))},["".concat(e,"-content")]:{display:"table-cell",width:"100%",verticalAlign:"top",[a]:{width:"100%",height:y,background:u,borderRadius:x,["+ ".concat(o)]:{marginBlockStart:d}},[o]:{padding:0,"> li":{width:"100%",height:A,listStyle:"none",background:u,borderRadius:x,"+ li":{marginBlockStart:S}}},["".concat(o,"> li:last-child:not(:first-child):not(:nth-child(2))")]:{width:"61%"}},["&-round ".concat(e,"-content")]:{["".concat(a,", ").concat(o," > li")]:{borderRadius:v}}},["".concat(e,"-with-avatar ").concat(e,"-content")]:{[a]:{marginBlockStart:p,["+ ".concat(o)]:{marginBlockStart:O}}},["".concat(e).concat(e,"-element")]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},w(t)),m(t)),h(t)),k(t)),["".concat(e).concat(e,"-block")]:{width:"100%",[c]:{width:"100%"},[r]:{width:"100%"}},["".concat(e).concat(e,"-active")]:{["\n        ".concat(a,",\n        ").concat(o," > li,\n        ").concat(n,",\n        ").concat(c,",\n        ").concat(r,",\n        ").concat(i,"\n      ")]:Object.assign({},g(t))}}},O=(0,d.OF)("Skeleton",t=>{let{componentCls:e,calc:n}=t;return[S((0,u.oX)(t,{skeletonAvatarCls:"".concat(e,"-avatar"),skeletonTitleCls:"".concat(e,"-title"),skeletonParagraphCls:"".concat(e,"-paragraph"),skeletonButtonCls:"".concat(e,"-button"),skeletonInputCls:"".concat(e,"-input"),skeletonImageCls:"".concat(e,"-image"),imageSizeBase:n(t.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:"linear-gradient(90deg, ".concat(t.gradientFromColor," 25%, ").concat(t.gradientToColor," 37%, ").concat(t.gradientFromColor," 63%)"),skeletonLoadingMotionDuration:"1.4s"}))]},t=>{let{colorFillContent:e,colorFill:n}=t;return{color:e,colorGradientEnd:n,gradientFromColor:e,gradientToColor:n,titleHeight:t.controlHeight/2,blockRadius:t.borderRadiusSM,paragraphMarginTop:t.marginLG+t.marginXXS,paragraphLiHeight:t.controlHeight/2}},{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),z=(t,e)=>{let{width:n,rows:a=2}=e;return Array.isArray(n)?n[t]:a-1===t?n:void 0},E=t=>{let{prefixCls:e,className:n,style:o,rows:r=0}=t,i=Array.from({length:r}).map((e,n)=>a.createElement("li",{key:n,style:{width:z(n,t)}}));return a.createElement("ul",{className:c()(e,n),style:o},i)},C=t=>{let{prefixCls:e,className:n,width:o,style:r}=t;return a.createElement("h3",{className:c()(e,n),style:Object.assign({width:o},r)})};function j(t){return t&&"object"==typeof t?t:{}}let _=t=>{let{prefixCls:e,loading:n,className:o,rootClassName:i,style:s,children:d,avatar:u=!1,title:b=!0,paragraph:p=!0,active:f,round:g}=t,{getPrefixCls:v,direction:m,className:h,style:y}=(0,r.TP)("skeleton"),k=v("skeleton",e),[x,A,w]=O(k);if(n||!("loading"in t)){let t,e,n=!!u,r=!!b,d=!!p;if(n){let e=Object.assign(Object.assign({prefixCls:"".concat(k,"-avatar")},r&&!d?{size:"large",shape:"square"}:{size:"large",shape:"circle"}),j(u));t=a.createElement("div",{className:"".concat(k,"-header")},a.createElement(l,Object.assign({},e)))}if(r||d){let t,o;if(r){let e=Object.assign(Object.assign({prefixCls:"".concat(k,"-title")},function(t,e){return!t&&e?{width:"38%"}:t&&e?{width:"50%"}:{}}(n,d)),j(b));t=a.createElement(C,Object.assign({},e))}if(d){let t=Object.assign(Object.assign({prefixCls:"".concat(k,"-paragraph")},function(t,e){let n={};return t&&e||(n.width="61%"),!t&&e?n.rows=3:n.rows=2,n}(n,r)),j(p));o=a.createElement(E,Object.assign({},t))}e=a.createElement("div",{className:"".concat(k,"-content")},t,o)}let v=c()(k,{["".concat(k,"-with-avatar")]:n,["".concat(k,"-active")]:f,["".concat(k,"-rtl")]:"rtl"===m,["".concat(k,"-round")]:g},h,o,i,A,w);return x(a.createElement("div",{className:v,style:Object.assign(Object.assign({},y),s)},t,e))}return null!=d?d:null};_.Button=t=>{let{prefixCls:e,className:n,rootClassName:o,active:s,block:d=!1,size:u="default"}=t,{getPrefixCls:b}=a.useContext(r.QO),p=b("skeleton",e),[f,g,v]=O(p),m=(0,i.A)(t,["prefixCls"]),h=c()(p,"".concat(p,"-element"),{["".concat(p,"-active")]:s,["".concat(p,"-block")]:d},n,o,g,v);return f(a.createElement("div",{className:h},a.createElement(l,Object.assign({prefixCls:"".concat(p,"-button"),size:u},m))))},_.Avatar=t=>{let{prefixCls:e,className:n,rootClassName:o,active:s,shape:d="circle",size:u="default"}=t,{getPrefixCls:b}=a.useContext(r.QO),p=b("skeleton",e),[f,g,v]=O(p),m=(0,i.A)(t,["prefixCls","className"]),h=c()(p,"".concat(p,"-element"),{["".concat(p,"-active")]:s},n,o,g,v);return f(a.createElement("div",{className:h},a.createElement(l,Object.assign({prefixCls:"".concat(p,"-avatar"),shape:d,size:u},m))))},_.Input=t=>{let{prefixCls:e,className:n,rootClassName:o,active:s,block:d,size:u="default"}=t,{getPrefixCls:b}=a.useContext(r.QO),p=b("skeleton",e),[f,g,v]=O(p),m=(0,i.A)(t,["prefixCls"]),h=c()(p,"".concat(p,"-element"),{["".concat(p,"-active")]:s,["".concat(p,"-block")]:d},n,o,g,v);return f(a.createElement("div",{className:h},a.createElement(l,Object.assign({prefixCls:"".concat(p,"-input"),size:u},m))))},_.Image=t=>{let{prefixCls:e,className:n,rootClassName:o,style:i,active:l}=t,{getPrefixCls:s}=a.useContext(r.QO),d=s("skeleton",e),[u,b,p]=O(d),f=c()(d,"".concat(d,"-element"),{["".concat(d,"-active")]:l},n,o,b,p);return u(a.createElement("div",{className:f},a.createElement("div",{className:c()("".concat(d,"-image"),n),style:i},a.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:"".concat(d,"-image-svg")},a.createElement("title",null,"Image placeholder"),a.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:"".concat(d,"-image-path")})))))},_.Node=t=>{let{prefixCls:e,className:n,rootClassName:o,style:i,active:l,children:s}=t,{getPrefixCls:d}=a.useContext(r.QO),u=d("skeleton",e),[b,p,f]=O(u),g=c()(u,"".concat(u,"-element"),{["".concat(u,"-active")]:l},p,n,o,f);return b(a.createElement("div",{className:g},a.createElement("div",{className:c()("".concat(u,"-image"),n),style:i},s)))};let R=_}}]);