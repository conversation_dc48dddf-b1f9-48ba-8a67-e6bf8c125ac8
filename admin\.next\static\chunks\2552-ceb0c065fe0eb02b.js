"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[171,2552],{35695:(e,t,o)=>{var n=o(18999);o.o(n,"useParams")&&o.d(t,{useParams:function(){return n.useParams}}),o.o(n,"usePathname")&&o.d(t,{usePathname:function(){return n.usePathname}}),o.o(n,"useRouter")&&o.d(t,{useRouter:function(){return n.useRouter}}),o.o(n,"useSearchParams")&&o.d(t,{useSearchParams:function(){return n.useSearchParams}}),o.o(n,"useServerInsertedHTML")&&o.d(t,{useServerInsertedHTML:function(){return n.useServerInsertedHTML}})},37974:(e,t,o)=>{o.d(t,{A:()=>j});var n=o(12115),r=o(29300),a=o.n(r),c=o(17980),l=o(77696),i=o(50497),s=o(80163),d=o(47195),u=o(15982),p=o(85573),m=o(34162),g=o(18184),b=o(61388),f=o(45431);let h=e=>{let{paddingXXS:t,lineWidth:o,tagPaddingHorizontal:n,componentCls:r,calc:a}=e,c=a(n).sub(o).equal(),l=a(t).sub(o).equal();return{[r]:Object.assign(Object.assign({},(0,g.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(r,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(r,"-close-icon")]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(r,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(r,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:c}}),["".concat(r,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=e=>{let{lineWidth:t,fontSizeIcon:o,calc:n}=e,r=e.fontSizeSM;return(0,b.oX)(e,{tagFontSize:r,tagLineHeight:(0,p.zA)(n(e.lineHeightSM).mul(r).equal()),tagIconSize:n(o).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new m.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),S=(0,f.OF)("Tag",e=>h(v(e)),y);var C=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let x=n.forwardRef((e,t)=>{let{prefixCls:o,style:r,className:c,checked:l,onChange:i,onClick:s}=e,d=C(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=n.useContext(u.QO),g=p("tag",o),[b,f,h]=S(g),v=a()(g,"".concat(g,"-checkable"),{["".concat(g,"-checkable-checked")]:l},null==m?void 0:m.className,c,f,h);return b(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},r),null==m?void 0:m.style),className:v,onClick:e=>{null==i||i(!l),null==s||s(e)}})))});var k=o(18741);let O=e=>(0,k.A)(e,(t,o)=>{let{textColor:n,lightBorderColor:r,lightColor:a,darkColor:c}=o;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:n,background:a,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}}),w=(0,f.bf)(["Tag","preset"],e=>O(v(e)),y),E=(e,t,o)=>{let n=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(o);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(o)],background:e["color".concat(n,"Bg")],borderColor:e["color".concat(n,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}},I=(0,f.bf)(["Tag","status"],e=>{let t=v(e);return[E(t,"success","Success"),E(t,"processing","Info"),E(t,"error","Error"),E(t,"warning","Warning")]},y);var z=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let A=n.forwardRef((e,t)=>{let{prefixCls:o,className:r,rootClassName:p,style:m,children:g,icon:b,color:f,onClose:h,bordered:v=!0,visible:y}=e,C=z(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:x,direction:k,tag:O}=n.useContext(u.QO),[E,A]=n.useState(!0),j=(0,c.A)(C,["closeIcon","closable"]);n.useEffect(()=>{void 0!==y&&A(y)},[y]);let P=(0,l.nP)(f),N=(0,l.ZZ)(f),B=P||N,H=Object.assign(Object.assign({backgroundColor:f&&!B?f:void 0},null==O?void 0:O.style),m),M=x("tag",o),[T,L,R]=S(M),W=a()(M,null==O?void 0:O.className,{["".concat(M,"-").concat(f)]:B,["".concat(M,"-has-color")]:f&&!B,["".concat(M,"-hidden")]:!E,["".concat(M,"-rtl")]:"rtl"===k,["".concat(M,"-borderless")]:!v},r,p,L,R),F=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||A(!1)},[,D]=(0,i.A)((0,i.d)(e),(0,i.d)(O),{closable:!1,closeIconRender:e=>{let t=n.createElement("span",{className:"".concat(M,"-close-icon"),onClick:F},e);return(0,s.fx)(e,t,e=>({onClick:t=>{var o;null==(o=null==e?void 0:e.onClick)||o.call(e,t),F(t)},className:a()(null==e?void 0:e.className,"".concat(M,"-close-icon"))}))}}),G="function"==typeof C.onClick||g&&"a"===g.type,X=b||null,q=X?n.createElement(n.Fragment,null,X,g&&n.createElement("span",null,g)):g,_=n.createElement("span",Object.assign({},j,{ref:t,className:W,style:H}),q,D,P&&n.createElement(w,{key:"preset",prefixCls:M}),N&&n.createElement(I,{key:"status",prefixCls:M}));return T(G?n.createElement(d.A,{component:"Tag"},_):_)});A.CheckableTag=x;let j=A},56170:(e,t,o)=>{o.d(t,{A:()=>l});var n=o(79630),r=o(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var c=o(62764);let l=r.forwardRef(function(e,t){return r.createElement(c.A,(0,n.A)({},e,{ref:t,icon:a}))})},94600:(e,t,o)=>{o.d(t,{A:()=>h});var n=o(12115),r=o(29300),a=o.n(r),c=o(15982),l=o(9836),i=o(85573),s=o(18184),d=o(45431),u=o(61388);let p=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{["&".concat(t)]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},m=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:o,colorSplit:n,lineWidth:r,textPaddingInline:a,orientationMargin:c,verticalMarginInline:l}=e;return{[t]:Object.assign(Object.assign({},(0,s.dF)(e)),{borderBlockStart:"".concat((0,i.zA)(r)," solid ").concat(n),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,i.zA)(r)," solid ").concat(n)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,i.zA)(e.marginLG)," 0")},["&-horizontal".concat(t,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,i.zA)(e.dividerHorizontalWithTextGutterMargin)," 0"),color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(n),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,i.zA)(r)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(t,"-with-text-start")]:{"&::before":{width:"calc(".concat(c," * 100%)")},"&::after":{width:"calc(100% - ".concat(c," * 100%)")}},["&-horizontal".concat(t,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(c," * 100%)")},"&::after":{width:"calc(".concat(c," * 100%)")}},["".concat(t,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:a},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:"".concat((0,i.zA)(r)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(t,"-dashed")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:"".concat((0,i.zA)(r)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(t,"-dotted")]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(t,"-with-text")]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},["&-horizontal".concat(t,"-with-text-start").concat(t,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(t,"-inner-text")]:{paddingInlineStart:o}},["&-horizontal".concat(t,"-with-text-end").concat(t,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(t,"-inner-text")]:{paddingInlineEnd:o}}})}},g=(0,d.OF)("Divider",e=>{let t=(0,u.oX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[m(t),p(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var b=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let f={small:"sm",middle:"md"},h=e=>{let{getPrefixCls:t,direction:o,className:r,style:i}=(0,c.TP)("divider"),{prefixCls:s,type:d="horizontal",orientation:u="center",orientationMargin:p,className:m,rootClassName:h,children:v,dashed:y,variant:S="solid",plain:C,style:x,size:k}=e,O=b(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),w=t("divider",s),[E,I,z]=g(w),A=f[(0,l.A)(k)],j=!!v,P=n.useMemo(()=>"left"===u?"rtl"===o?"end":"start":"right"===u?"rtl"===o?"start":"end":u,[o,u]),N="start"===P&&null!=p,B="end"===P&&null!=p,H=a()(w,r,I,z,"".concat(w,"-").concat(d),{["".concat(w,"-with-text")]:j,["".concat(w,"-with-text-").concat(P)]:j,["".concat(w,"-dashed")]:!!y,["".concat(w,"-").concat(S)]:"solid"!==S,["".concat(w,"-plain")]:!!C,["".concat(w,"-rtl")]:"rtl"===o,["".concat(w,"-no-default-orientation-margin-start")]:N,["".concat(w,"-no-default-orientation-margin-end")]:B,["".concat(w,"-").concat(A)]:!!A},m,h),M=n.useMemo(()=>"number"==typeof p?p:/^\d+$/.test(p)?Number(p):p,[p]);return E(n.createElement("div",Object.assign({className:H,style:Object.assign(Object.assign({},i),x)},O,{role:"separator"}),v&&"vertical"!==d&&n.createElement("span",{className:"".concat(w,"-inner-text"),style:{marginInlineStart:N?M:void 0,marginInlineEnd:B?M:void 0}},v)))}},95108:(e,t,o)=>{o.d(t,{A:()=>T});var n=o(12115),r=o(4931),a=o(87773),c=o(58587),l=o(47852),i=o(38142),s=o(29300),d=o.n(s),u=o(82870),p=o(40032),m=o(74686),g=o(80163),b=o(15982),f=o(85573),h=o(18184),v=o(45431);let y=(e,t,o,n,r)=>({background:e,border:"".concat((0,f.zA)(n.lineWidth)," ").concat(n.lineType," ").concat(t),["".concat(r,"-icon")]:{color:o}}),S=e=>{let{componentCls:t,motionDurationSlow:o,marginXS:n,marginSM:r,fontSize:a,fontSizeLG:c,lineHeight:l,borderRadiusLG:i,motionEaseInOutCirc:s,withDescriptionIconSize:d,colorText:u,colorTextHeading:p,withDescriptionPadding:m,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:i,["&".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-content")]:{flex:1,minWidth:0},["".concat(t,"-icon")]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:a,lineHeight:l},"&-message":{color:p},["&".concat(t,"-motion-leave")]:{overflow:"hidden",opacity:1,transition:"max-height ".concat(o," ").concat(s,", opacity ").concat(o," ").concat(s,",\n        padding-top ").concat(o," ").concat(s,", padding-bottom ").concat(o," ").concat(s,",\n        margin-bottom ").concat(o," ").concat(s)},["&".concat(t,"-motion-leave-active")]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),["".concat(t,"-with-description")]:{alignItems:"flex-start",padding:m,["".concat(t,"-icon")]:{marginInlineEnd:r,fontSize:d,lineHeight:0},["".concat(t,"-message")]:{display:"block",marginBottom:n,color:p,fontSize:c},["".concat(t,"-description")]:{display:"block",color:u}},["".concat(t,"-banner")]:{marginBottom:0,border:"0 !important",borderRadius:0}}},C=e=>{let{componentCls:t,colorSuccess:o,colorSuccessBorder:n,colorSuccessBg:r,colorWarning:a,colorWarningBorder:c,colorWarningBg:l,colorError:i,colorErrorBorder:s,colorErrorBg:d,colorInfo:u,colorInfoBorder:p,colorInfoBg:m}=e;return{[t]:{"&-success":y(r,n,o,e,t),"&-info":y(m,p,u,e,t),"&-warning":y(l,c,a,e,t),"&-error":Object.assign(Object.assign({},y(d,s,i,e,t)),{["".concat(t,"-description > pre")]:{margin:0,padding:0}})}}},x=e=>{let{componentCls:t,iconCls:o,motionDurationMid:n,marginXS:r,fontSizeIcon:a,colorIcon:c,colorIconHover:l}=e;return{[t]:{"&-action":{marginInlineStart:r},["".concat(t,"-close-icon")]:{marginInlineStart:r,padding:0,overflow:"hidden",fontSize:a,lineHeight:(0,f.zA)(a),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",["".concat(o,"-close")]:{color:c,transition:"color ".concat(n),"&:hover":{color:l}}},"&-close-text":{color:c,transition:"color ".concat(n),"&:hover":{color:l}}}}},k=(0,v.OF)("Alert",e=>[S(e),C(e),x(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:"".concat(e.paddingContentVerticalSM,"px ").concat(12,"px"),withDescriptionPadding:"".concat(e.paddingMD,"px ").concat(e.paddingContentHorizontalLG,"px")}));var O=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let w={success:r.A,info:i.A,error:a.A,warning:l.A},E=e=>{let{icon:t,prefixCls:o,type:r}=e,a=w[r]||null;return t?(0,g.fx)(t,n.createElement("span",{className:"".concat(o,"-icon")},t),()=>({className:d()("".concat(o,"-icon"),t.props.className)})):n.createElement(a,{className:"".concat(o,"-icon")})},I=e=>{let{isClosable:t,prefixCls:o,closeIcon:r,handleClose:a,ariaProps:l}=e,i=!0===r||void 0===r?n.createElement(c.A,null):r;return t?n.createElement("button",Object.assign({type:"button",onClick:a,className:"".concat(o,"-close-icon"),tabIndex:0},l),i):null},z=n.forwardRef((e,t)=>{let{description:o,prefixCls:r,message:a,banner:c,className:l,rootClassName:i,style:s,onMouseEnter:g,onMouseLeave:f,onClick:h,afterClose:v,showIcon:y,closable:S,closeText:C,closeIcon:x,action:w,id:z}=e,A=O(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[j,P]=n.useState(!1),N=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:N.current}));let{getPrefixCls:B,direction:H,closable:M,closeIcon:T,className:L,style:R}=(0,b.TP)("alert"),W=B("alert",r),[F,D,G]=k(W),X=t=>{var o;P(!0),null==(o=e.onClose)||o.call(e,t)},q=n.useMemo(()=>void 0!==e.type?e.type:c?"warning":"info",[e.type,c]),_=n.useMemo(()=>"object"==typeof S&&!!S.closeIcon||!!C||("boolean"==typeof S?S:!1!==x&&null!=x||!!M),[C,x,S,M]),Q=!!c&&void 0===y||y,Y=d()(W,"".concat(W,"-").concat(q),{["".concat(W,"-with-description")]:!!o,["".concat(W,"-no-icon")]:!Q,["".concat(W,"-banner")]:!!c,["".concat(W,"-rtl")]:"rtl"===H},L,l,i,G,D),Z=(0,p.A)(A,{aria:!0,data:!0}),K=n.useMemo(()=>"object"==typeof S&&S.closeIcon?S.closeIcon:C||(void 0!==x?x:"object"==typeof M&&M.closeIcon?M.closeIcon:T),[x,S,C,T]),U=n.useMemo(()=>{let e=null!=S?S:M;if("object"==typeof e){let{closeIcon:t}=e;return O(e,["closeIcon"])}return{}},[S,M]);return F(n.createElement(u.Ay,{visible:!j,motionName:"".concat(W,"-motion"),motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:v},(t,r)=>{let{className:c,style:l}=t;return n.createElement("div",Object.assign({id:z,ref:(0,m.K4)(N,r),"data-show":!j,className:d()(Y,c),style:Object.assign(Object.assign(Object.assign({},R),s),l),onMouseEnter:g,onMouseLeave:f,onClick:h,role:"alert"},Z),Q?n.createElement(E,{description:o,icon:e.icon,prefixCls:W,type:q}):null,n.createElement("div",{className:"".concat(W,"-content")},a?n.createElement("div",{className:"".concat(W,"-message")},a):null,o?n.createElement("div",{className:"".concat(W,"-description")},o):null),w?n.createElement("div",{className:"".concat(W,"-action")},w):null,n.createElement(I,{isClosable:_,prefixCls:W,closeIcon:K,handleClose:X,ariaProps:U}))}))});var A=o(30857),j=o(28383),P=o(85522),N=o(45144),B=o(5892),H=o(38289);let M=function(e){function t(){var e,o,n;return(0,A.A)(this,t),o=t,n=arguments,o=(0,P.A)(o),(e=(0,B.A)(this,(0,N.A)()?Reflect.construct(o,n||[],(0,P.A)(this).constructor):o.apply(this,n))).state={error:void 0,info:{componentStack:""}},e}return(0,H.A)(t,e),(0,j.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:o,children:r}=this.props,{error:a,info:c}=this.state,l=(null==c?void 0:c.componentStack)||null,i=void 0===e?(a||"").toString():e;return a?n.createElement(z,{id:o,type:"error",message:i,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?l:t)}):r}}])}(n.Component);z.ErrorBoundary=M;let T=z}}]);