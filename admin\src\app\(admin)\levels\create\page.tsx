'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Form, Input, Button, Select, message, Card, Typography, Space, Divider, Alert, Tag, Table, Modal } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { levelService, levelTagService, CreateLevelParams } from '@/services';
import { LevelTag } from '@/services/levelTagService';

const { Title } = Typography;
const { TextArea } = Input;

interface PhraseItem {
  id: string;
  text: string;
  meaning: string;
}

export default function CreateLevelPage() {
  const [form] = Form.useForm();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [phrases, setPhrases] = useState<PhraseItem[]>([]);
  const [tags, setTags] = useState<LevelTag[]>([]);
  const [levelStats, setLevelStats] = useState<{ total: number; maxLevels: number; remaining: number } | null>(null);

  // 添加词组的表单状态
  const [phraseForm] = Form.useForm();
  const [showAddPhrase, setShowAddPhrase] = useState(false);

  // 添加词组到关卡
  const handleAddPhrase = (values: { text: string; meaning: string }) => {
    // 检查是否已存在相同的英文词组
    const isDuplicate = phrases.some(phrase =>
      phrase.text.toLowerCase().trim() === values.text.toLowerCase().trim()
    );

    if (isDuplicate) {
      message.error('该英文词组已存在，请勿重复添加');
      return;
    }

    const newPhrase: PhraseItem = {
      id: `temp_${Date.now()}`, // 临时ID
      text: values.text.trim(),
      meaning: values.meaning.trim(),
    };

    setPhrases(prev => [...prev, newPhrase]);
    phraseForm.resetFields();
    setShowAddPhrase(false);
    message.success('词组添加成功');
  };

  // 删除词组
  const handleDeletePhrase = (id: string) => {
    setPhrases(prev => prev.filter(phrase => phrase.id !== id));
    message.success('词组删除成功');
  };



  // 获取标签列表
  const fetchTags = async () => {
    try {
      const data = await levelTagService.getAll();
      setTags(data.filter(tag => tag.status === 'active')); // 只显示激活的标签
    } catch (error) {
      message.error('获取标签列表失败');
    }
  };

  // 获取关卡统计
  const fetchLevelStats = async () => {
    try {
      const stats = await levelService.getCount();
      setLevelStats(stats);
    } catch (error) {
      message.error('获取关卡统计失败');
    }
  };

  useEffect(() => {
    fetchTags();
    fetchLevelStats();
  }, []);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    // 验证添加了词组
    if (phrases.length === 0) {
      message.error('请添加词组');
      return;
    }

    setLoading(true);
    try {
      const params: CreateLevelParams = {
        name: values.name,
        difficulty: 1,
        description: values.description,
        thesaurusIds: [], // 保持为空数组以兼容API
        phrases: phrases.map(phrase => ({
          text: phrase.text,
          meaning: phrase.meaning,
        })),
        tagIds: values.tagIds || [], // 新增：标签ID列表
      };

      await levelService.create(params);
      message.success('关卡创建成功');
      router.push('/levels');
    } catch (error) {
      message.error('创建关卡失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* 关卡统计提示 */}
      {levelStats && (
        <Alert
          message={`当前已创建 ${levelStats.total} 个关卡，还可以创建 ${levelStats.remaining} 个关卡（最大限制：${levelStats.maxLevels}）`}
          type={levelStats.remaining > 0 ? 'info' : 'warning'}
          style={{ marginBottom: 16 }}
          showIcon
        />
      )}

      <Card>
        <Title level={2}>创建新关卡</Title>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ difficulty: 3 }}
        >
          <Form.Item
            name="name"
            label="关卡名称"
            rules={[{ required: true, message: '请输入关卡名称' }]}
          >
            <Input placeholder="例如：第1关 - 基础词汇" />
          </Form.Item>

          <Form.Item
            name="description"
            label="关卡描述"
          >
            <TextArea rows={3} placeholder="请输入关卡描述（可选）" />
          </Form.Item>

          <Divider>添加关卡词组</Divider>

          <Alert
            message="提示：请添加要包含在关卡中的词组。"
            type="info"
            style={{ marginBottom: 16 }}
          />

          <div style={{ marginBottom: 16 }}>
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={() => setShowAddPhrase(true)}
              style={{ width: '100%' }}
            >
              添加词组
            </Button>
          </div>

          {/* 词组列表 */}
          <div style={{ marginBottom: 16 }}>
            <div style={{ marginBottom: 8, fontSize: '12px', color: '#666' }}>
              当前词组数量: {phrases.length}
            </div>
     
            <Table
              dataSource={phrases}
              rowKey={(record) => record.id}
              pagination={false}
              size="small"
              columns={[
                {
                  title: '英文',
                  dataIndex: 'text',
                  key: 'text',
                  render: (text) => text || '未设置',
                },
                {
                  title: '中文',
                  dataIndex: 'meaning',
                  key: 'meaning',
                  render: (meaning) => meaning || '未设置',
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 80,
                  render: (_, record) => (
                    <Button
                      type="link"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeletePhrase(record.id)}
                    />
                  ),
                },
              ]}
              locale={{ emptyText: '暂无词组，请点击上方按钮添加' }}
            />
          </div>



          <Divider>关卡标签</Divider>

          <Form.Item
            name="tagIds"
            label="选择标签"
            help="为关卡添加标签，便于分类和筛选"
          >
            <Select
              mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择关卡标签"
              filterOption={(input, option) =>
                (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
              }
              optionRender={(option) => (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Tag color={tags.find(tag => tag.id === option.value)?.color}>
                    {option.label}
                  </Tag>
                  {tags.find(tag => tag.id === option.value)?.isVip && (
                    <Tag color="gold" style={{ fontSize: '10px', padding: '0 4px' }}>VIP</Tag>
                  )}
                </div>
              )}
              options={tags.map(tag => ({
                label: tag.name,
                value: tag.id,
              }))}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={levelStats?.remaining === 0}
              >
                创建关卡
              </Button>
              <Button onClick={() => router.back()}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 添加词组的模态框 */}
      <Modal
        title="添加词组"
        open={showAddPhrase}
        onCancel={() => setShowAddPhrase(false)}
        footer={null}
        width={500}
      >
        <Form
          form={phraseForm}
          layout="vertical"
          onFinish={handleAddPhrase}
        >
          <Form.Item
            name="text"
            label="英文"
            rules={[
              { required: true, message: '请输入英文' },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  const isDuplicate = phrases.some(phrase =>
                    phrase.text.toLowerCase().trim() === value.toLowerCase().trim()
                  );
                  if (isDuplicate) {
                    return Promise.reject(new Error('该英文词组已存在'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="请输入英文词组" />
          </Form.Item>
          <Form.Item
            name="meaning"
            label="中文"
            rules={[{ required: true, message: '请输入中文' }]}
          >
            <Input placeholder="请输入中文意思" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                添加词组
              </Button>
              <Button onClick={() => setShowAddPhrase(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}