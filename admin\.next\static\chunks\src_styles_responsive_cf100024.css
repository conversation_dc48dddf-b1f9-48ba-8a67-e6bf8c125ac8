/* [project]/src/styles/responsive.css [app-client] (css) */
@media (width <= 768px) {
  .ant-layout-sider {
    z-index: 1000;
    transition: left .3s;
    left: -200px;
    position: fixed !important;
  }

  .ant-layout-sider.ant-layout-sider-collapsed {
    left: -80px;
  }

  .ant-layout-sider-trigger {
    z-index: 1001;
    color: #fff;
    background: #001529;
    border: none;
    border-radius: 4px;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    display: flex;
    position: fixed;
    top: 16px;
    left: 16px;
  }

  .ant-layout-content {
    padding: 16px 8px;
    margin-left: 0 !important;
  }

  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table {
    min-width: 600px;
  }

  .ant-card {
    margin-bottom: 16px;
  }

  .ant-card-head-title {
    font-size: 16px;
  }

  .ant-statistic-title {
    font-size: 12px;
  }

  .ant-statistic-content {
    font-size: 18px;
  }

  .ant-form-item-label {
    font-size: 14px;
  }

  .ant-space-item {
    margin-bottom: 8px;
  }

  .ant-modal {
    max-width: calc(100vw - 32px);
    margin: 16px;
  }

  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-drawer-content-wrapper {
    max-width: 400px;
    width: 90vw !important;
  }
}

@media (width >= 769px) and (width <= 1024px) {
  .ant-layout-content {
    padding: 24px 16px;
  }

  .ant-col {
    margin-bottom: 16px;
  }

  .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }
}

@media (width >= 1200px) {
  .ant-layout-content {
    padding: 24px;
  }

  .content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
  }
}

.loading-container {
  justify-content: center;
  align-items: center;
  min-height: 200px;
  display: flex;
}

.empty-container {
  color: #999;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  display: flex;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.fade-in {
  animation: .3s ease-in fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: .3s ease-out slideIn;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.responsive-table {
  overflow-x: auto;
}

.responsive-table .ant-table {
  min-width: 800px;
}

@media (width <= 768px) {
  .responsive-table .ant-table {
    min-width: 600px;
  }

  .responsive-table .ant-table-thead > tr > th, .responsive-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }

  .responsive-table .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}

.card-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  display: grid;
}

@media (width <= 768px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.stats-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  display: grid;
}

@media (width <= 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

@media (width <= 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.action-buttons {
  flex-wrap: wrap;
  gap: 8px;
  display: flex;
}

@media (width <= 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .ant-btn {
    width: 100%;
  }
}

.search-bar {
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  display: flex;
}

@media (width <= 768px) {
  .search-bar {
    flex-direction: column;
    gap: 8px;
  }

  .search-bar > * {
    width: 100%;
  }
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin-bottom: 8px;
}

@media (width <= 768px) {
  .page-header {
    margin-bottom: 16px;
  }

  .page-header h2 {
    font-size: 20px;
  }
}

.content-section {
  margin-bottom: 24px;
}

@media (width <= 768px) {
  .content-section {
    margin-bottom: 16px;
  }
}

.form-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  display: grid;
}

@media (width <= 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.modal-content {
  max-height: 70vh;
  overflow-y: auto;
}

.drawer-content {
  padding: 16px;
}

@media (width <= 768px) {
  .drawer-content {
    padding: 12px;
  }
}

.loading-overlay {
  z-index: 10;
  background: #fffc;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  inset: 0;
}

.error-container {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.error-container .ant-result {
  padding: 20px;
}

.success-container {
  text-align: center;
  color: #52c41a;
  padding: 40px 20px;
}

.no-data-container {
  text-align: center;
  color: #999;
  padding: 60px 20px;
}

.no-data-container .ant-empty {
  margin: 0;
}

.ant-tooltip {
  max-width: 300px;
}

@media (width <= 768px) {
  .ant-tooltip {
    max-width: 250px;
  }
}

.ant-tabs-content-holder {
  padding-top: 16px;
}

@media (width <= 768px) {
  .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 14px;
  }
}

.progress-container {
  margin: 16px 0;
}

.progress-container .ant-progress-text {
  font-size: 12px;
}

.badge-container {
  align-items: center;
  gap: 8px;
  display: inline-flex;
}

.timeline-container {
  max-height: 400px;
  padding-right: 8px;
  overflow-y: auto;
}

@media (width <= 768px) {
  .timeline-container {
    max-height: 300px;
  }

  .ant-descriptions-item-label {
    font-size: 12px;
  }

  .ant-descriptions-item-content {
    font-size: 14px;
  }
}


/*# sourceMappingURL=src_styles_responsive_cf100024.css.map*/