/* [project]/src/components/Modal/modal.css [app-client] (css) */
.custom-modal-container {
  z-index: 1000;
  position: relative;
}

.custom-modal-mask {
  z-index: 1000;
  background-color: #00000073;
  height: 100%;
  transition: opacity .3s;
  position: fixed;
  inset: 0;
}

.custom-modal-mask-show {
  opacity: 1;
}

.custom-modal-mask-hide {
  opacity: 0;
}

.custom-modal-wrap {
  outline: 0;
  justify-content: center;
  align-items: flex-start;
  padding: 100px 0;
  display: flex;
  position: fixed;
  inset: 0;
  overflow: auto;
}

.custom-modal-centered {
  align-items: center;
  padding: 0;
}

.custom-modal {
  background: #fff;
  border-radius: 6px;
  max-width: calc(100vw - 32px);
  transition: all .3s;
  position: relative;
  box-shadow: 0 6px 16px #00000014, 0 3px 6px -4px #0000001f, 0 9px 28px 8px #0000000d;
}

.custom-modal-show {
  opacity: 1;
  transform: scale(1);
}

.custom-modal-hide {
  opacity: 0;
  transform: scale(.9);
}

.custom-modal-header {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 6px 6px 0 0;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  display: flex;
}

.custom-modal-title {
  color: #000000e0;
  word-wrap: break-word;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
}

.custom-modal-close {
  z-index: 10;
  color: #00000073;
  cursor: pointer;
  background: none;
  border: 0;
  outline: 0;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  padding: 0;
  font-size: 22px;
  font-weight: 700;
  line-height: 1;
  text-decoration: none;
  transition: color .3s;
  display: flex;
  position: absolute;
  top: 16px;
  right: 16px;
}

.custom-modal-close:hover {
  color: #000000bf;
}

.custom-modal-body {
  word-wrap: break-word;
  padding: 24px;
  font-size: 14px;
  line-height: 1.5715;
}

.custom-modal-footer {
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 6px 6px;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 16px;
  display: flex;
}

.custom-modal-btn {
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  user-select: none;
  touch-action: manipulation;
  background-image: none;
  border: 1px solid #0000;
  border-radius: 6px;
  outline: 0;
  height: 32px;
  padding: 4px 15px;
  font-size: 14px;
  font-weight: 400;
  text-decoration: none;
  transition: all .2s cubic-bezier(.645, .045, .355, 1);
  display: inline-block;
  position: relative;
}

.custom-modal-btn:hover {
  text-decoration: none;
}

.custom-modal-btn:focus {
  outline: 0;
}

.custom-modal-btn-default {
  color: #000000e0;
  background: #fff;
  border-color: #d9d9d9;
}

.custom-modal-btn-default:hover {
  color: #4096ff;
  background: #fff;
  border-color: #4096ff;
}

.custom-modal-btn-primary {
  color: #fff;
  background: #1677ff;
  border-color: #1677ff;
}

.custom-modal-btn-primary:hover {
  background: #4096ff;
  border-color: #4096ff;
}

.custom-modal-btn-danger {
  color: #fff;
  background: #ff4d4f;
  border-color: #ff4d4f;
}

.custom-modal-btn-danger:hover {
  background: #ff7875;
  border-color: #ff7875;
}

.custom-modal-btn:disabled {
  color: #00000040;
  cursor: not-allowed;
  background: #f5f5f5;
  border-color: #d9d9d9;
}

.custom-modal-loading {
  margin-right: 8px;
  animation: 1s linear infinite customModalSpin;
  display: inline-block;
}

@keyframes customModalSpin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@media (width <= 768px) {
  .custom-modal {
    max-width: calc(100vw - 16px);
    margin: 8px;
  }

  .custom-modal-wrap {
    padding: 16px 0;
  }

  .custom-modal-body {
    padding: 16px;
  }

  .custom-modal-header {
    padding: 12px 16px;
  }

  .custom-modal-footer {
    padding: 8px 12px;
  }
}


/* [project]/src/components/Message/message.css [app-client] (css) */
.custom-message-wrapper {
  z-index: 1010;
  pointer-events: none;
  position: fixed;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
}

.custom-message-container {
  flex-direction: column;
  align-items: center;
  gap: 8px;
  display: flex;
}

.custom-message {
  pointer-events: auto;
  word-wrap: break-word;
  background: #fff;
  border-radius: 6px;
  align-items: center;
  max-width: 400px;
  padding: 8px 16px;
  font-size: 14px;
  line-height: 1.5715;
  transition: all .3s;
  display: flex;
  box-shadow: 0 6px 16px #00000014, 0 3px 6px -4px #0000001f, 0 9px 28px 8px #0000000d;
}

.custom-message-icon {
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
}

.custom-message-content {
  flex: 1;
}

.custom-message-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.custom-message-success .custom-message-icon, .custom-message-success .custom-message-content {
  color: #52c41a;
}

.custom-message-error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.custom-message-error .custom-message-icon, .custom-message-error .custom-message-content {
  color: #ff4d4f;
}

.custom-message-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

.custom-message-warning .custom-message-icon, .custom-message-warning .custom-message-content {
  color: #faad14;
}

.custom-message-info {
  background-color: #f0f5ff;
  border: 1px solid #91caff;
}

.custom-message-info .custom-message-icon, .custom-message-info .custom-message-content {
  color: #1677ff;
}

.custom-message-show {
  opacity: 1;
  animation: .3s customMessageSlideIn;
  transform: translateY(0);
}

.custom-message-hide {
  opacity: 0;
  animation: .3s customMessageSlideOut;
  transform: translateY(-100%);
}

@keyframes customMessageSlideIn {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes customMessageSlideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }

  to {
    opacity: 0;
    transform: translateY(-100%);
  }
}

@media (width <= 768px) {
  .custom-message {
    max-width: calc(100vw - 32px);
    margin: 0 16px;
  }
}


/* [project]/src/styles/LoginPage.module.css [app-client] (css) */
.LoginPage-module__dp1EYa__container {
  background: #f0f2f5;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  display: flex;
}


/*# sourceMappingURL=src_135384ba._.css.map*/