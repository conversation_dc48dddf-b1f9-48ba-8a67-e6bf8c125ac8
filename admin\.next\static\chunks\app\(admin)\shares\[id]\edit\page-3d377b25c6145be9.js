(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9019],{14874:(e,s,l)=>{Promise.resolve().then(l.bind(l,45561))},19558:(e,s,l)=>{"use strict";l.d(s,{A:()=>t});var r=l(79630),i=l(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};var n=l(62764);let t=i.forwardRef(function(e,s){return i.createElement(n.A,(0,r.A)({},e,{ref:s,icon:a}))})},45561:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>S});var r=l(95155),i=l(12115),a=l(35695),n=l(97605),t=l(56020),c=l(20778),d=l(86615),h=l(19868),o=l(16467),x=l(95108),p=l(77325),j=l(6124),m=l(19361),u=l(74947),A=l(12320),y=l(10642),g=l(13324),v=l(81730),f=l(44318),b=l(19558),w=l(73884);let{Title:C,Text:I}=n.A,{TextArea:k}=t.A,{Option:z}=c.A;function S(){let e=(0,a.useParams)(),s=(0,a.useRouter)(),[l,n]=(0,i.useState)(null),[S,R]=(0,i.useState)(!0),[U,V]=(0,i.useState)(!1),[L]=d.A.useForm(),_=e.id,q=async()=>{if(_){R(!0);try{let e=await w.Dw.getShareConfigById(_);n(e),L.setFieldsValue({name:e.name,title:e.title,path:e.path,imageUrl:e.imageUrl,description:e.description,type:e.type,isActive:e.isActive,sortOrder:e.sortOrder})}catch(e){h.Ay.error("获取分享配置详情失败"),console.error("获取分享配置详情失败:",e)}finally{R(!1)}}};(0,i.useEffect)(()=>{q()},[_]);let E=()=>{s.push("/shares/".concat(_))},O=async()=>{try{let e=await L.validateFields();V(!0),await w.Dw.updateShareConfig(_,e),h.Ay.success("分享配置更新成功"),s.push("/shares/".concat(_))}catch(e){if(e&&"object"==typeof e&&"errorFields"in e)h.Ay.error("请检查表单输入");else{let s=e&&"object"==typeof e&&"message"in e?e.message:"更新失败";h.Ay.error(s)}}finally{V(!1)}};return S?(0,r.jsx)("div",{style:{padding:"24px",textAlign:"center"},children:(0,r.jsx)(o.A,{size:"large"})}):l?(0,r.jsx)("div",{style:{padding:"24px"},children:(0,r.jsxs)(j.A,{children:[(0,r.jsx)("div",{style:{marginBottom:"24px"},children:(0,r.jsxs)(m.A,{justify:"space-between",align:"middle",children:[(0,r.jsx)(u.A,{children:(0,r.jsxs)(A.A,{children:[(0,r.jsx)(p.Ay,{icon:(0,r.jsx)(v.A,{}),onClick:E,children:"返回"}),(0,r.jsxs)(C,{level:3,style:{margin:0},children:[(0,r.jsx)(f.A,{style:{marginRight:"8px"}}),"编辑分享配置"]})]})}),(0,r.jsx)(u.A,{children:(0,r.jsxs)(A.A,{children:[(0,r.jsx)(p.Ay,{onClick:E,children:"取消"}),(0,r.jsx)(p.Ay,{type:"primary",icon:(0,r.jsx)(b.A,{}),loading:U,onClick:O,children:"保存"})]})})]})}),(0,r.jsxs)(d.A,{form:L,layout:"vertical",size:"large",children:[(0,r.jsxs)(m.A,{gutter:24,children:[(0,r.jsx)(u.A,{span:12,children:(0,r.jsx)(d.A.Item,{name:"name",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:(0,r.jsx)(t.A,{placeholder:"请输入配置名称"})})}),(0,r.jsx)(u.A,{span:12,children:(0,r.jsx)(d.A.Item,{name:"type",label:"分享类型",rules:[{required:!0,message:"请选择分享类型"}],children:(0,r.jsx)(c.A,{placeholder:"请选择分享类型",children:w.WS.map(e=>(0,r.jsx)(z,{value:e.value,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:e.label}),(0,r.jsx)(I,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})})})]}),(0,r.jsx)(d.A.Item,{name:"title",label:"分享标题",rules:[{required:!0,message:"请输入分享标题"}],children:(0,r.jsx)(t.A,{placeholder:"请输入分享标题"})}),(0,r.jsx)(d.A.Item,{name:"path",label:"分享路径",rules:[{required:!0,message:"请输入分享路径"}],children:(0,r.jsx)(c.A,{placeholder:"请选择或输入分享路径",mode:"tags",allowClear:!0,children:w.cm.map(e=>(0,r.jsx)(z,{value:e.value,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:e.label}),(0,r.jsx)(I,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})}),(0,r.jsx)(d.A.Item,{name:"imageUrl",label:"分享图片URL",rules:[{type:"url",message:"请输入有效的URL"}],children:(0,r.jsx)(t.A,{placeholder:"请输入分享图片URL（可选）"})}),(0,r.jsx)(d.A.Item,{name:"description",label:"分享描述",children:(0,r.jsx)(k,{placeholder:"请输入分享描述（可选）",rows:4,maxLength:200,showCount:!0})}),(0,r.jsxs)(m.A,{gutter:24,children:[(0,r.jsx)(u.A,{span:12,children:(0,r.jsx)(d.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,r.jsx)(y.A,{min:1,max:999,placeholder:"排序权重",style:{width:"100%"}})})}),(0,r.jsx)(u.A,{span:12,children:(0,r.jsx)(d.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,r.jsx)(g.A,{checkedChildren:"启用",unCheckedChildren:"禁用",size:"default"})})})]})]}),(0,r.jsx)(x.A,{message:"编辑提示",description:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:"• 分享标题和路径是必填项，将直接影响小程序的分享效果"}),(0,r.jsx)("p",{children:"• 分享图片建议使用5:4比例，推荐尺寸500x400px"}),(0,r.jsxs)("p",{children:["• 路径中可以使用变量，如 ","{levelId}"," 会被替换为实际的关卡ID"]}),(0,r.jsx)("p",{children:"• 默认类型的配置不能删除，但可以禁用"})]}),type:"info",showIcon:!0,style:{marginTop:"24px"}})]})}):(0,r.jsx)("div",{style:{padding:"24px"},children:(0,r.jsx)(x.A,{message:"分享配置不存在",description:"请检查URL是否正确，或者该配置已被删除。",type:"error",showIcon:!0,action:(0,r.jsx)(p.Ay,{size:"small",onClick:()=>s.push("/shares"),children:"返回列表"})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,6615,7605,642,5634,7578,3884,8441,1684,7358],()=>s(14874)),_N_E=e.O()}]);