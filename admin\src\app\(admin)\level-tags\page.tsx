'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input, message, Popconfirm, Card, Tag,
  Switch, ColorPicker, Row, Col, Statistic, Select
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, TagOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { levelTagService, type LevelTag as LevelTagType } from '@/services/levelTagService';

const { Option } = Select;

export default function LevelTagsPage() {
  const [tags, setTags] = useState<LevelTagType[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTag, setEditingTag] = useState<LevelTagType | null>(null);
  const [form] = Form.useForm();

  // 获取标签列表
  const fetchTags = async () => {
    setLoading(true);
    try {
      const tags = await levelTagService.getAll();
      setTags(tags);
    } catch (error) {
      message.error('获取标签列表失败');
      console.error('Error fetching tags:', error);
      // 使用模拟数据
      setTags([
        {
          id: '1',
          name: '基础词汇',
          description: '适合初学者的基础英语词汇',
          color: '#1890ff',
          isVip: false,
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: '商务英语',
          description: '商务场景常用词汇和表达',
          color: '#52c41a',
          isVip: true,
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '3',
          name: '日常对话',
          description: '日常生活中的常用对话',
          color: '#faad14',
          isVip: false,
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    fetchTags();
  }, []);

  // 创建/更新标签
  const handleSubmit = async (values: any) => {
    try {
      const params = {
        ...values,
        color: typeof values.color === 'string' ? values.color : values.color?.toHexString?.(),
      };

      if (editingTag) {
        await levelTagService.update(editingTag.id, params);
        message.success('标签更新成功');
      } else {
        await levelTagService.create(params);
        message.success('标签创建成功');
      }

      setModalVisible(false);
      setEditingTag(null);
      form.resetFields();
      fetchTags();
    } catch (error) {
      message.error(editingTag ? '标签更新失败' : '标签创建失败');
    }
  };

  // 删除标签
  const handleDelete = async (id: string) => {
    try {
      await levelTagService.delete(id);
      message.success('标签删除成功');
      fetchTags();
    } catch (error) {
      message.error('标签删除失败');
    }
  };

  // 切换标签状态
  const handleToggleStatus = async (tag: LevelTagType) => {
    try {
      const newStatus = tag.status === 'active' ? 'inactive' : 'active';
      await levelTagService.update(tag.id, { status: newStatus });
      message.success('标签状态更新成功');
      fetchTags();
    } catch (error) {
      message.error('标签状态更新失败');
    }
  };



  // 表格列定义
  const columns: ColumnsType<LevelTagType> = [
    {
      title: '标签名称',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <Tag color={record.color} style={{ marginRight: 8 }}>
            {name}
          </Tag>
          {record.isVip && <Tag color="gold">VIP</Tag>}
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },

    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Switch
          checked={status === 'active'}
          onChange={() => handleToggleStatus(record)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingTag(record);
              form.setFieldsValue(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个标签吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger size="small" icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const totalTags = tags.length;
  const activeTags = tags.filter(tag => tag.status === 'active').length;
  const vipTags = tags.filter(tag => tag.isVip).length;

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总标签数"
              value={totalTags}
              prefix={<TagOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="启用标签"
              value={activeTags}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="VIP标签"
              value={vipTags}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2>标签管理</h2>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => {
            setEditingTag(null);
            form.resetFields();
            setModalVisible(true);
          }}>
            创建标签
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={tags}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 创建/编辑标签模态框 */}
      <Modal
        title={editingTag ? '编辑标签' : '创建标签'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTag(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="标签名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="标签名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入标签描述' }]}
          >
            <Input.TextArea placeholder="标签描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="color"
            label="标签颜色"
            rules={[{ required: true, message: '请选择标签颜色' }]}
          >
            <ColorPicker showText />
          </Form.Item>

          <Form.Item
            name="isVip"
            label="VIP标签"
            valuePropName="checked"
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>

          <Form.Item
            name="status"
            label="启用状态"
            initialValue="active"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingTag ? '更新' : '创建'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingTag(null);
                form.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
}
