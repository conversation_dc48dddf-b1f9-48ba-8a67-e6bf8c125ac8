/* [project]/src/components/Message/message.css [app-client] (css) */
.custom-message-wrapper {
  z-index: 1010;
  pointer-events: none;
  position: fixed;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
}

.custom-message-container {
  flex-direction: column;
  align-items: center;
  gap: 8px;
  display: flex;
}

.custom-message {
  pointer-events: auto;
  word-wrap: break-word;
  background: #fff;
  border-radius: 6px;
  align-items: center;
  max-width: 400px;
  padding: 8px 16px;
  font-size: 14px;
  line-height: 1.5715;
  transition: all .3s;
  display: flex;
  box-shadow: 0 6px 16px #00000014, 0 3px 6px -4px #0000001f, 0 9px 28px 8px #0000000d;
}

.custom-message-icon {
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
}

.custom-message-content {
  flex: 1;
}

.custom-message-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.custom-message-success .custom-message-icon, .custom-message-success .custom-message-content {
  color: #52c41a;
}

.custom-message-error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.custom-message-error .custom-message-icon, .custom-message-error .custom-message-content {
  color: #ff4d4f;
}

.custom-message-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

.custom-message-warning .custom-message-icon, .custom-message-warning .custom-message-content {
  color: #faad14;
}

.custom-message-info {
  background-color: #f0f5ff;
  border: 1px solid #91caff;
}

.custom-message-info .custom-message-icon, .custom-message-info .custom-message-content {
  color: #1677ff;
}

.custom-message-show {
  opacity: 1;
  animation: .3s customMessageSlideIn;
  transform: translateY(0);
}

.custom-message-hide {
  opacity: 0;
  animation: .3s customMessageSlideOut;
  transform: translateY(-100%);
}

@keyframes customMessageSlideIn {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes customMessageSlideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }

  to {
    opacity: 0;
    transform: translateY(-100%);
  }
}

@media (width <= 768px) {
  .custom-message {
    max-width: calc(100vw - 32px);
    margin: 0 16px;
  }
}

/*# sourceMappingURL=src_components_Message_message_css_f9ee138c._.single.css.map*/