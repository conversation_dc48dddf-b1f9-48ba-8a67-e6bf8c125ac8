// src/types/thesaurus.ts

export interface ThesaurusResponseDto {
  id: string;
  name: string;
  description?: string;
  phraseIds: string[]; // 假设后端会返回关联的词组ID
  createdAt: string;
  updatedAt: string;
}

export interface CreateThesaurusDto {
  name: string;
  description?: string;
}

export type UpdateThesaurusDto = Partial<CreateThesaurusDto>;

// 如果有关联词组的具体信息，可以进一步定义
// export interface ThesaurusWithPhrasesDto extends ThesaurusResponseDto {
//   phrases: PhraseResponseDto[];
// }
