exports.id=976,exports.ids=[976],exports.modules={3283:()=>{},11235:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},35692:()=>{},44835:(e,s,t)=>{Promise.resolve().then(t.bind(t,65266))},49895:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>l,FH:()=>a,KY:()=>c});var o=t(51060),r=t(88210),n=t(70216);let i=o.A.create({baseURL:n.i.FULL_BASE_URL,timeout:n.i.TIMEOUT,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{let s=localStorage.getItem("admin_token");return s&&(e.headers.Authorization=`Bearer ${s}`),console.log("\uD83D\uDE80 发送请求:",{method:e.method?.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:`${e.baseURL}${e.url}`,data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),i.interceptors.response.use(e=>{let s=e.config;return console.log("✅ 请求成功:",{method:s.method?.toUpperCase(),url:s.url,status:e.status,statusText:e.statusText,data:e.data}),s.showSuccess&&s.successMessage&&r.i.success(s.successMessage),e},e=>{console.error("❌ 请求失败:",{method:e.config?.method?.toUpperCase(),url:e.config?.url,baseURL:e.config?.baseURL,fullURL:`${e.config?.baseURL}${e.config?.url}`,status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,message:e.message});let s=e.config;if(s?.showError===!1)return Promise.reject(e);if(e.response){let{status:s,data:t}=e.response,o="";switch(s){case 401:o="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:o=t?.message||"没有权限访问该资源";break;case 404:o=t?.message||"请求的资源不存在";break;case 422:o=t?.message||"请求参数验证失败";break;case 500:o=t?.message||"服务器内部错误";break;default:o=t?.message||`请求失败 (${s})`}r.i.error(o)}else e.request?r.i.error("网络连接失败，请检查网络"):r.i.error("请求配置错误");return Promise.reject(e)});let a={get:(e,s)=>i.get(e,s),post:(e,s,t)=>i.post(e,s,t),put:(e,s,t)=>i.put(e,s,t),patch:(e,s,t)=>i.patch(e,s,t),delete:(e,s)=>i.delete(e,s)},c={post:(e,s,t,o)=>a.post(e,s,{...o,showSuccess:!0,successMessage:t||"操作成功"}),put:(e,s,t,o)=>a.put(e,s,{...o,showSuccess:!0,successMessage:t||"更新成功"}),patch:(e,s,t,o)=>a.patch(e,s,{...o,showSuccess:!0,successMessage:t||"更新成功"}),delete:(e,s,t)=>a.delete(e,{...t,showSuccess:!0,successMessage:s||"删除成功"})},l=a},58907:(e,s,t)=>{Promise.resolve().then(t.bind(t,6468))},70216:(e,s,t)=>{"use strict";t.d(s,{i:()=>o});let o={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return`${this.BASE_URL}${this.API_PREFIX}`}}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var o=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76891:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},77591:()=>{},88210:(e,s,t)=>{"use strict";t.d(s,{a:()=>c,i:()=>h});var o=t(60687),r=t(43210),n=t(46060);t(77591);let i=({title:e,content:s,children:t,visible:n=!1,width:i=520,centered:a=!1,closable:c=!0,maskClosable:l=!0,footer:d,okText:m="确定",cancelText:h="取消",okType:u="primary",confirmLoading:p=!1,onOk:g,onCancel:b,afterClose:v,className:y="",style:x={}})=>{let[f,w]=(0,r.useState)(n),[k,j]=(0,r.useState)(!1);(0,r.useEffect)(()=>{n?(w(!0),j(!0),document.body.style.overflow="hidden"):(j(!1),setTimeout(()=>{w(!1),document.body.style.overflow="",v?.()},300))},[n,v]);let T=async()=>{if(g)try{await g()}catch(e){console.error("Modal onOk error:",e)}},P=()=>{b?.()};return f?(0,o.jsx)("div",{className:`custom-modal-mask ${k?"custom-modal-mask-show":"custom-modal-mask-hide"}`,onClick:e=>{e.target===e.currentTarget&&l&&b?.()},children:(0,o.jsx)("div",{className:`custom-modal-wrap ${a?"custom-modal-centered":""}`,children:(0,o.jsxs)("div",{className:`custom-modal ${y} ${k?"custom-modal-show":"custom-modal-hide"}`,style:{width:i,...x},children:[(e||c)&&(0,o.jsxs)("div",{className:"custom-modal-header",children:[e&&(0,o.jsx)("div",{className:"custom-modal-title",children:e}),c&&(0,o.jsx)("button",{className:"custom-modal-close",onClick:P,"aria-label":"Close",children:"\xd7"})]}),(0,o.jsx)("div",{className:"custom-modal-body",children:s||t}),null===d?null:d||(0,o.jsxs)("div",{className:"custom-modal-footer",children:[h&&(0,o.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:P,children:h}),(0,o.jsxs)("button",{className:`custom-modal-btn custom-modal-btn-${u}`,onClick:T,disabled:p,children:[p&&(0,o.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),m]})]})]})})}):null};class a{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}confirm(e){return new Promise((s,t)=>{let r=!1,n=async()=>{if(!r)try{e.onOk&&await e.onOk(),r=!0,this.destroy(),s()}catch(e){t(e)}};this.getContainer(),this.root.render((0,o.jsx)(i,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:n,onCancel:()=>{r||(r=!0,e.onCancel?.(),this.destroy(),t(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}i.confirm=e=>new a().confirm({...e,okType:e.okType||"primary"}),i.info=e=>new a().confirm({...e,okType:"primary",cancelText:void 0}),i.success=e=>new a().confirm({...e,okType:"primary",cancelText:void 0}),i.error=e=>new a().confirm({...e,okType:"danger",cancelText:void 0}),i.warning=e=>new a().confirm({...e,okType:"primary",cancelText:void 0});let c=i;t(3283);let l=({messages:e})=>(0,o.jsx)("div",{className:"custom-message-container",children:e.map(e=>(0,o.jsxs)("div",{className:`custom-message custom-message-${e.type} ${e.visible?"custom-message-show":"custom-message-hide"}`,children:[(0,o.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,o.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))});class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,o.jsx)(l,{messages:this.messages}))}generateId(){return`message_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}show(e){let s=e.key||this.generateId(),t=e.duration??3e3;e.key&&(this.messages=this.messages.filter(s=>s.id!==e.key));let o={...e,id:s,visible:!0};return this.messages.push(o),this.getContainer(),this.render(),t>0&&setTimeout(()=>{this.hide(s)},t),s}hide(e){let s=this.messages.findIndex(s=>s.id===e);s>-1&&(this.messages[s].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(s=>s.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let m=new d,h={success:(e,s)=>m.show({content:e,type:"success",duration:s}),error:(e,s)=>m.show({content:e,type:"error",duration:s}),warning:(e,s)=>m.show({content:e,type:"warning",duration:s}),info:(e,s)=>m.show({content:e,type:"info",duration:s}),destroy:()=>m.destroy()}},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var o=t(37413);t(61120);var r=t(68016);t(35692);let n=({children:e})=>(0,o.jsxs)("html",{lang:"en",children:[(0,o.jsxs)("head",{children:[(0,o.jsx)("meta",{charSet:"utf-8"}),(0,o.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,o.jsx)("meta",{name:"description",content:"游戏后台管理系统"}),(0,o.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,o.jsx)("title",{children:"游戏管理后台"})]}),(0,o.jsx)("body",{children:(0,o.jsx)(r.Z,{children:e})})]})}};