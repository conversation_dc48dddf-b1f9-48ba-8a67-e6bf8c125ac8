(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/Modal/index.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/client.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
// Modal 组件
const Modal = ({ title, content, children, visible = false, width = 520, centered = false, closable = true, maskClosable = true, footer, okText = '确定', cancelText = '取消', okType = 'primary', confirmLoading = false, onOk, onCancel, afterClose, className = '', style = {} })=>{
    _s();
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(visible);
    const [isAnimating, setIsAnimating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Modal.useEffect": ()=>{
            if (visible) {
                setIsVisible(true);
                setIsAnimating(true);
                document.body.style.overflow = 'hidden';
            } else {
                setIsAnimating(false);
                setTimeout({
                    "Modal.useEffect": ()=>{
                        setIsVisible(false);
                        document.body.style.overflow = '';
                        afterClose?.();
                    }
                }["Modal.useEffect"], 300);
            }
        }
    }["Modal.useEffect"], [
        visible,
        afterClose
    ]);
    const handleMaskClick = (e)=>{
        if (e.target === e.currentTarget && maskClosable) {
            onCancel?.();
        }
    };
    const handleOk = async ()=>{
        if (onOk) {
            try {
                await onOk();
            } catch (error) {
                console.error('Modal onOk error:', error);
            }
        }
    };
    const handleCancel = ()=>{
        onCancel?.();
    };
    const renderFooter = ()=>{
        if (footer === null) return null;
        if (footer) return footer;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "custom-modal-footer",
            children: [
                cancelText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    className: "custom-modal-btn custom-modal-btn-default",
                    onClick: handleCancel,
                    children: cancelText
                }, void 0, false, {
                    fileName: "[project]/src/components/Modal/index.tsx",
                    lineNumber: 93,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    className: `custom-modal-btn custom-modal-btn-${okType}`,
                    onClick: handleOk,
                    disabled: confirmLoading,
                    children: [
                        confirmLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "custom-modal-loading",
                            children: "⟳"
                        }, void 0, false, {
                            fileName: "[project]/src/components/Modal/index.tsx",
                            lineNumber: 105,
                            columnNumber: 30
                        }, this),
                        okText
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/Modal/index.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/Modal/index.tsx",
            lineNumber: 91,
            columnNumber: 7
        }, this);
    };
    if (!isVisible) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `custom-modal-mask ${isAnimating ? 'custom-modal-mask-show' : 'custom-modal-mask-hide'}`,
        onClick: handleMaskClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `custom-modal-wrap ${centered ? 'custom-modal-centered' : ''}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `custom-modal ${className} ${isAnimating ? 'custom-modal-show' : 'custom-modal-hide'}`,
                style: {
                    width,
                    ...style
                },
                children: [
                    (title || closable) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "custom-modal-header",
                        children: [
                            title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "custom-modal-title",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/Modal/index.tsx",
                                lineNumber: 126,
                                columnNumber: 25
                            }, this),
                            closable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "custom-modal-close",
                                onClick: handleCancel,
                                "aria-label": "Close",
                                children: "×"
                            }, void 0, false, {
                                fileName: "[project]/src/components/Modal/index.tsx",
                                lineNumber: 128,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Modal/index.tsx",
                        lineNumber: 125,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "custom-modal-body",
                        children: content || children
                    }, void 0, false, {
                        fileName: "[project]/src/components/Modal/index.tsx",
                        lineNumber: 139,
                        columnNumber: 11
                    }, this),
                    renderFooter()
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Modal/index.tsx",
                lineNumber: 120,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/Modal/index.tsx",
            lineNumber: 119,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Modal/index.tsx",
        lineNumber: 115,
        columnNumber: 5
    }, this);
};
_s(Modal, "HDnh4duRQj53Uz0LTHWpc0iExXc=");
_c = Modal;
// 确认对话框管理器
class ConfirmManager {
    container = null;
    root = null;
    getContainer() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'custom-modal-container';
            document.body.appendChild(this.container);
            this.root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRoot"])(this.container);
        }
        return this.container;
    }
    confirm(config) {
        return new Promise((resolve, reject)=>{
            let isResolved = false;
            const handleOk = async ()=>{
                if (isResolved) return;
                try {
                    if (config.onOk) {
                        await config.onOk();
                    }
                    isResolved = true;
                    this.destroy();
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            const handleCancel = ()=>{
                if (isResolved) return;
                isResolved = true;
                config.onCancel?.();
                this.destroy();
                reject(new Error('User cancelled'));
            };
            this.getContainer();
            this.root.render(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Modal, {
                visible: true,
                title: config.title,
                content: config.content,
                okText: config.okText,
                cancelText: config.cancelText,
                okType: config.okType,
                width: config.width,
                centered: config.centered,
                maskClosable: config.maskClosable,
                onOk: handleOk,
                onCancel: handleCancel,
                afterClose: ()=>this.destroy()
            }, void 0, false, {
                fileName: "[project]/src/components/Modal/index.tsx",
                lineNumber: 209,
                columnNumber: 9
            }, this));
        });
    }
    destroy() {
        if (this.container && document.body.contains(this.container)) {
            document.body.removeChild(this.container);
            this.container = null;
            this.root = null;
        }
    }
}
// 创建带有静态方法的Modal组件
const ModalWithStatic = Modal;
// 静态方法
ModalWithStatic.confirm = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: config.okType || 'primary'
    });
};
ModalWithStatic.info = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'primary',
        cancelText: undefined
    });
};
ModalWithStatic.success = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'primary',
        cancelText: undefined
    });
};
ModalWithStatic.error = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'danger',
        cancelText: undefined
    });
};
ModalWithStatic.warning = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'primary',
        cancelText: undefined
    });
};
const __TURBOPACK__default__export__ = ModalWithStatic;
var _c;
__turbopack_context__.k.register(_c, "Modal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Message/index.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "message": (()=>message)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/client.js [app-client] (ecmascript)");
;
;
;
// 消息容器组件
const MessageContainer = ({ messages })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "custom-message-container",
        children: messages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `custom-message custom-message-${message.type} ${message.visible ? 'custom-message-show' : 'custom-message-hide'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "custom-message-icon",
                        children: [
                            message.type === 'success' && '✓',
                            message.type === 'error' && '✕',
                            message.type === 'warning' && '⚠',
                            message.type === 'info' && 'ℹ'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Message/index.tsx",
                        lineNumber: 28,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "custom-message-content",
                        children: message.content
                    }, void 0, false, {
                        fileName: "[project]/src/components/Message/index.tsx",
                        lineNumber: 34,
                        columnNumber: 11
                    }, this)
                ]
            }, message.id, true, {
                fileName: "[project]/src/components/Message/index.tsx",
                lineNumber: 22,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/Message/index.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
};
_c = MessageContainer;
// 消息管理器
class MessageManager {
    messages = [];
    container = null;
    root = null;
    getContainer() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'custom-message-wrapper';
            document.body.appendChild(this.container);
            this.root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRoot"])(this.container);
        }
        return this.container;
    }
    render() {
        if (this.root) {
            this.root.render(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MessageContainer, {
                messages: this.messages
            }, void 0, false, {
                fileName: "[project]/src/components/Message/index.tsx",
                lineNumber: 59,
                columnNumber: 24
            }, this));
        }
    }
    generateId() {
        return `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    show(config) {
        const id = config.key || this.generateId();
        const duration = config.duration ?? 3000;
        // 如果已存在相同key的消息，先移除
        if (config.key) {
            this.messages = this.messages.filter((msg)=>msg.id !== config.key);
        }
        const messageItem = {
            ...config,
            id,
            visible: true
        };
        this.messages.push(messageItem);
        this.getContainer();
        this.render();
        // 自动移除
        if (duration > 0) {
            setTimeout(()=>{
                this.hide(id);
            }, duration);
        }
        return id;
    }
    hide(id) {
        const messageIndex = this.messages.findIndex((msg)=>msg.id === id);
        if (messageIndex > -1) {
            this.messages[messageIndex].visible = false;
            this.render();
            // 动画结束后移除
            setTimeout(()=>{
                this.messages = this.messages.filter((msg)=>msg.id !== id);
                this.render();
                // 如果没有消息了，清理容器
                if (this.messages.length === 0 && this.container) {
                    document.body.removeChild(this.container);
                    this.container = null;
                    this.root = null;
                }
            }, 300);
        }
    }
    destroy() {
        this.messages = [];
        if (this.container) {
            document.body.removeChild(this.container);
            this.container = null;
            this.root = null;
        }
    }
}
// 全局消息管理器实例
const messageManager = new MessageManager();
const message = {
    success: (content, duration)=>messageManager.show({
            content,
            type: 'success',
            duration
        }),
    error: (content, duration)=>messageManager.show({
            content,
            type: 'error',
            duration
        }),
    warning: (content, duration)=>messageManager.show({
            content,
            type: 'warning',
            duration
        }),
    info: (content, duration)=>messageManager.show({
            content,
            type: 'info',
            duration
        }),
    destroy: ()=>messageManager.destroy()
};
const __TURBOPACK__default__export__ = message;
var _c;
__turbopack_context__.k.register(_c, "MessageContainer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// 自定义组件统一导出
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Modal/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript)");
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Modal/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/Message/index.tsx [app-client] (ecmascript) <export default as message>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "message": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript)");
}}),
"[project]/src/config/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// API配置
__turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG),
    "ENV_CONFIG": (()=>ENV_CONFIG)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const API_CONFIG = {
    // 基础URL - 可以根据环境变量动态设置
    BASE_URL: (("TURBOPACK compile-time truthy", 1) ? ("TURBOPACK compile-time value", "http://*************:18891") : ("TURBOPACK unreachable", undefined)) || "http://localhost:18891",
    // 超时时间
    TIMEOUT: 10000,
    // API版本前缀（注意：当前使用/admin/前缀，此配置保留用于兼容性）
    API_PREFIX: "/api/v1/admin",
    // 完整的API基础URL
    get FULL_BASE_URL () {
        return `${this.BASE_URL}${this.API_PREFIX}`;
    }
};
const ENV_CONFIG = {
    isDevelopment: ("TURBOPACK compile-time value", "development") === "development",
    isProduction: ("TURBOPACK compile-time value", "development") === "production",
    isTest: ("TURBOPACK compile-time value", "development") === "test"
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/request.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "api": (()=>api),
    "default": (()=>__TURBOPACK__default__export__),
    "silentApi": (()=>silentApi),
    "successApi": (()=>successApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript) <export default as message>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/api.ts [app-client] (ecmascript)");
;
;
;
// 创建axios实例
const request = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].FULL_BASE_URL,
    timeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT,
    headers: {
        "Content-Type": "application/json"
    }
});
// 请求拦截器
request.interceptors.request.use((config)=>{
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem("admin_token");
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // 添加详细的请求日志
    console.log("🚀 发送请求:", {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL,
        fullURL: `${config.baseURL}${config.url}`,
        data: config.data,
        headers: config.headers
    });
    return config;
}, (error)=>{
    console.error("请求拦截器错误:", error);
    return Promise.reject(error);
});
// 响应拦截器
request.interceptors.response.use((response)=>{
    const config = response.config;
    // 添加响应日志
    console.log("✅ 请求成功:", {
        method: config.method?.toUpperCase(),
        url: config.url,
        status: response.status,
        statusText: response.statusText,
        data: response.data
    });
    // 处理成功提示
    if (config.showSuccess && config.successMessage) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success(config.successMessage);
    }
    return response;
}, (error)=>{
    console.error("❌ 请求失败:", {
        method: error.config?.method?.toUpperCase(),
        url: error.config?.url,
        baseURL: error.config?.baseURL,
        fullURL: `${error.config?.baseURL}${error.config?.url}`,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
    });
    const config = error.config;
    const showError = config?.showError !== false; // 默认显示错误
    if (!showError) {
        return Promise.reject(error);
    }
    // 处理常见错误
    if (error.response) {
        const { status, data } = error.response;
        let errorMessage = "";
        switch(status){
            case 401:
                errorMessage = "登录已过期，请重新登录";
                localStorage.removeItem("admin_token");
                // 可以在这里添加跳转到登录页的逻辑
                window.location.href = "/login";
                break;
            case 403:
                errorMessage = data?.message || "没有权限访问该资源";
                break;
            case 404:
                errorMessage = data?.message || "请求的资源不存在";
                break;
            case 422:
                errorMessage = data?.message || "请求参数验证失败";
                break;
            case 500:
                errorMessage = data?.message || "服务器内部错误";
                break;
            default:
                errorMessage = data?.message || `请求失败 (${status})`;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(errorMessage);
    } else if (error.request) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error("网络连接失败，请检查网络");
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error("请求配置错误");
    }
    return Promise.reject(error);
});
const api = {
    // GET请求
    get: (url, config)=>{
        return request.get(url, config);
    },
    // POST请求
    post: (url, data, config)=>{
        return request.post(url, data, config);
    },
    // PUT请求
    put: (url, data, config)=>{
        return request.put(url, data, config);
    },
    // PATCH请求
    patch: (url, data, config)=>{
        return request.patch(url, data, config);
    },
    // DELETE请求
    delete: (url, config)=>{
        return request.delete(url, config);
    }
};
const silentApi = {
    get: (url, config)=>api.get(url, {
            ...config,
            showError: false
        }),
    post: (url, data, config)=>api.post(url, data, {
            ...config,
            showError: false
        }),
    put: (url, data, config)=>api.put(url, data, {
            ...config,
            showError: false
        }),
    patch: (url, data, config)=>api.patch(url, data, {
            ...config,
            showError: false
        }),
    delete: (url, config)=>api.delete(url, {
            ...config,
            showError: false
        })
};
const successApi = {
    post: (url, data, successMessage, config)=>api.post(url, data, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "操作成功"
        }),
    put: (url, data, successMessage, config)=>api.put(url, data, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "更新成功"
        }),
    patch: (url, data, successMessage, config)=>api.patch(url, data, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "更新成功"
        }),
    delete: (url, successMessage, config)=>api.delete(url, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "删除成功"
        })
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/authService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authService": (()=>authService),
    "login": (()=>login)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const authService = {
    // 登录
    login: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/auth/login', params);
        return response.data;
    },
    // 登出
    logout: ()=>{
        localStorage.removeItem('admin_token');
        window.location.href = '/login';
    },
    // 获取当前token
    getToken: ()=>{
        return localStorage.getItem('admin_token');
    },
    // 设置token
    setToken: (token)=>{
        localStorage.setItem('admin_token', token);
    },
    // 检查是否已登录
    isLoggedIn: ()=>{
        return !!localStorage.getItem('admin_token');
    }
};
const login = authService.login;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/styles/LoginPage.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "container": "LoginPage-module__dp1EYa__container",
});
}}),
"[project]/src/app/login/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/index.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/input/index.js [app-client] (ecmascript) <export default as Input>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/card/index.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/message/index.js [app-client] (ecmascript) <export default as message>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/UserOutlined.js [app-client] (ecmascript) <export default as UserOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LockOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LockOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/LockOutlined.js [app-client] (ecmascript) <export default as LockOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/authService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$LoginPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/LoginPage.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
const LoginPage = ()=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleSubmit = async (values)=>{
        setLoading(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login(values);
            if (response.accessToken) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('登录成功！');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].setToken(response.accessToken);
                router.push('/dashboard'); // 跳转到主控面板
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(response.message || '登录失败，请检查用户名或密码！');
            }
        } catch (error) {
            console.error('登录失败:', error);
        // 错误已经在request拦截器中处理了，这里只需要记录日志
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$LoginPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].container,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
            title: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"].Title, {
                level: 3,
                style: {
                    textAlign: 'center',
                    marginBottom: 0
                },
                children: "后台登录"
            }, void 0, false, {
                fileName: "[project]/src/app/login/page.tsx",
                lineNumber: 36,
                columnNumber: 20
            }, void 0),
            style: {
                width: 400
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"], {
                name: "admin_login",
                initialValues: {
                    remember: true
                },
                onFinish: handleSubmit,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Item, {
                        name: "username",
                        rules: [
                            {
                                required: true,
                                message: '请输入用户名!'
                            }
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__["Input"], {
                            prefix: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__["UserOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/app/login/page.tsx",
                                lineNumber: 42,
                                columnNumber: 28
                            }, void 0),
                            placeholder: "用户名 (例如: admin)"
                        }, void 0, false, {
                            fileName: "[project]/src/app/login/page.tsx",
                            lineNumber: 42,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/login/page.tsx",
                        lineNumber: 38,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Item, {
                        name: "password",
                        rules: [
                            {
                                required: true,
                                message: '请输入密码!'
                            }
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__["Input"].Password, {
                            prefix: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LockOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LockOutlined$3e$__["LockOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/app/login/page.tsx",
                                lineNumber: 48,
                                columnNumber: 37
                            }, void 0),
                            placeholder: "密码 (例如: password123)"
                        }, void 0, false, {
                            fileName: "[project]/src/app/login/page.tsx",
                            lineNumber: 48,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/login/page.tsx",
                        lineNumber: 44,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Item, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                            type: "primary",
                            htmlType: "submit",
                            style: {
                                width: '100%'
                            },
                            loading: loading,
                            children: "登录"
                        }, void 0, false, {
                            fileName: "[project]/src/app/login/page.tsx",
                            lineNumber: 51,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/login/page.tsx",
                        lineNumber: 50,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/login/page.tsx",
                lineNumber: 37,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/login/page.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/login/page.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
_s(LoginPage, "OeGW3YQfIEwiDdtbkZtE38+y0P4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = LoginPage;
const __TURBOPACK__default__export__ = LoginPage;
var _c;
__turbopack_context__.k.register(_c, "LoginPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_78c3571f._.js.map