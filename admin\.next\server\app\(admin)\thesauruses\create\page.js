(()=>{var e={};e.id=221,e.ids=[221],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10715:(e,t,s)=>{Promise.resolve().then(s.bind(s,72315))},10814:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15444:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,s)=>{Promise.resolve().then(s.bind(s,10814))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37912:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),o=s(16189),l=s(98836),d=s(99053),u=s(63736),c=s(56072),p=s(78620);s(15444);var h=s(60203),m=s(81945),x=s(53788),y=s(9242),b=s(3788),f=s(73237),v=s(47453),A=s(31189),g=s(62727),j=s(14723),k=s(72061),w=s(80461),P=s(71103);let{Header:q,Content:_,Sider:C,Footer:I}=l.A,D=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,r.jsx)(h.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,r.jsx)(m.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,r.jsx)(x.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,r.jsx)(y.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,r.jsx)(b.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,r.jsx)(f.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,r.jsx)(v.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,r.jsx)(A.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,r.jsx)(g.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,r.jsx)(j.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,r.jsx)(k.A,{})}];function z({children:e}){let t=(0,o.useRouter)(),s=(0,o.usePathname)(),[n,h]=(0,a.useState)(!1),m=[{key:"logout",icon:(0,r.jsx)(w.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],x=D.find(e=>s.startsWith(e.path))?.key||"dashboard";return(0,r.jsxs)(l.A,{style:{minHeight:"100vh"},children:[(0,r.jsxs)(C,{collapsible:!0,collapsed:n,onCollapse:e=>h(e),children:[(0,r.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,r.jsx)(d.A.Text,{style:{color:"white",fontSize:n?"10px":"16px",transition:"font-size 0.2s"},children:n?"后台":"游戏管理后台"})}),(0,r.jsx)(u.A,{theme:"dark",selectedKeys:[x],mode:"inline",items:D.map(e=>({key:e.key,icon:e.icon,label:(0,r.jsx)(i(),{href:e.path,children:e.label})}))})]}),(0,r.jsxs)(l.A,{children:[(0,r.jsxs)(q,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,r.jsx)(c.A,{menu:{items:m},placement:"bottomRight",children:(0,r.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,r.jsx)(P.A,{})})}),(0,r.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,r.jsx)(_,{style:{margin:"16px"},children:e}),(0,r.jsxs)(I,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},45059:(e,t,s)=>{Promise.resolve().then(s.bind(s,65677))},52931:(e,t,s)=>{"use strict";s.d(t,{Au:()=>a,Io:()=>i,LQ:()=>n,zN:()=>o});var r=s(49895);let a={getAll:async()=>(await r.Ay.get("/thesauruses")).data,getById:async e=>(await r.Ay.get(`/thesauruses/${e}`)).data,create:async e=>(await r.Ay.post("/thesauruses",e)).data,update:async(e,t)=>(await r.Ay.patch(`/thesauruses/${e}`,t)).data,delete:async e=>{await r.Ay.delete(`/thesauruses/${e}`)},addPhrase:async(e,t)=>(await r.Ay.post(`/thesauruses/${e}/phrases`,t)).data,removePhrase:async(e,t)=>(await r.Ay.delete(`/thesauruses/${e}/phrases/${t}`)).data},n=a.create;a.update,a.delete;let i=a.delete;a.getById,a.getAll;let o=a.getAll},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59448:(e,t,s)=>{Promise.resolve().then(s.bind(s,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65677:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\thesauruses\\\\create\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx","default")},72315:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(43210),n=s(99053),i=s(10411),o=s(35899),l=s(42585),d=s(94733),u=s(77833),c=s(16189),p=s(52931);let{Title:h}=n.A,m=()=>{let[e]=i.A.useForm(),t=(0,c.useRouter)(),[s,n]=(0,a.useState)(!1),m=async e=>{n(!0);try{await (0,p.LQ)(e),o.Ay.success("词库创建成功！"),t.push("/thesauruses")}catch(e){o.Ay.error(e.message||"词库创建失败！")}finally{n(!1)}};return(0,r.jsxs)(l.A,{children:[(0,r.jsx)(h,{level:3,children:"创建新词库"}),(0,r.jsxs)(i.A,{form:e,layout:"vertical",onFinish:m,children:[(0,r.jsx)(i.A.Item,{name:"name",label:"词库名称",rules:[{required:!0,message:"请输入词库名称"}],children:(0,r.jsx)(d.A,{placeholder:"例如：日常用语"})}),(0,r.jsx)(i.A.Item,{name:"description",label:"描述 (可选)",children:(0,r.jsx)(d.A.TextArea,{rows:3,placeholder:"词库简介"})}),(0,r.jsxs)(i.A.Item,{children:[(0,r.jsx)(u.Ay,{type:"primary",htmlType:"submit",loading:s,children:"创建词库"}),(0,r.jsx)(u.Ay,{style:{marginLeft:8},onClick:()=>t.back(),children:"取消"})]})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},98473:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["(admin)",{children:["thesauruses",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65677)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(admin)/thesauruses/create/page",pathname:"/thesauruses/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,6267,1658,8161,675,5336,9196,5899,8331,411,976],()=>s(98473));module.exports=r})();