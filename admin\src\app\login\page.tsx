'use client';

import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { authService, LoginParams } from '@/services/authService';
import styles from '@/styles/LoginPage.module.css';

const LoginPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: LoginParams) => {
    setLoading(true);
    try {
      const response = await authService.login(values);

      if (response.accessToken) {
        message.success('登录成功！');
        authService.setToken(response.accessToken);
        router.push('/dashboard'); // 跳转到主控面板
      } else {
        message.error(response.message || '登录失败，请检查用户名或密码！');
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      // 错误已经在request拦截器中处理了，这里只需要记录日志
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <Card title={<Typography.Title level={3} style={{ textAlign: 'center', marginBottom: 0 }}>后台登录</Typography.Title>} style={{ width: 400 }}>
        <Form name="admin_login" initialValues={{ remember: true }} onFinish={handleSubmit}>
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input prefix={<UserOutlined />} placeholder="用户名 (例如: admin)" />
          </Form.Item>
          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="密码 (例如: password123)" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" style={{ width: '100%' }} loading={loading}>
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default LoginPage;