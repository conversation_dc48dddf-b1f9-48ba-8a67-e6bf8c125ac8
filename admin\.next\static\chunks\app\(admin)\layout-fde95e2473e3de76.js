(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4012],{77848:()=>{},86784:(e,s,t)=>{Promise.resolve().then(t.bind(t,97177))},97177:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var l=t(95155),a=t(12115),n=t(6874),i=t.n(n),r=t(35695),o=t(86253),c=t(97605),h=t(83803),d=t(82343),p=t(28562);t(77848);var x=t(63330),y=t(96097),j=t(73086),u=t(40670),k=t(44318),g=t(34095),b=t(36020),m=t(44213),A=t(64413),f=t(9622),v=t(92611),_=t(27540),C=t(50274);let{Header:I,Content:w,Sider:E,Footer:S}=o.A,N=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,l.jsx)(x.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,l.jsx)(y.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,l.jsx)(j.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,l.jsx)(u.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,l.jsx)(k.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,l.jsx)(g.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,l.jsx)(b.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,l.jsx)(m.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,l.jsx)(A.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,l.jsx)(f.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,l.jsx)(v.A,{})}];function P(e){var s;let{children:t}=e,n=(0,r.useRouter)(),x=(0,r.usePathname)(),[y,j]=(0,a.useState)(!1);(0,a.useEffect)(()=>{localStorage.getItem("admin_token")||n.replace("/login")},[n]);let u=[{key:"logout",icon:(0,l.jsx)(_.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),n.push("/login")}}],k=(null==(s=N.find(e=>x.startsWith(e.path)))?void 0:s.key)||"dashboard";return(0,l.jsxs)(o.A,{style:{minHeight:"100vh"},children:[(0,l.jsxs)(E,{collapsible:!0,collapsed:y,onCollapse:e=>j(e),children:[(0,l.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,l.jsx)(c.A.Text,{style:{color:"white",fontSize:y?"10px":"16px",transition:"font-size 0.2s"},children:y?"后台":"游戏管理后台"})}),(0,l.jsx)(h.A,{theme:"dark",selectedKeys:[k],mode:"inline",items:N.map(e=>({key:e.key,icon:e.icon,label:(0,l.jsx)(i(),{href:e.path,children:e.label})}))})]}),(0,l.jsxs)(o.A,{children:[(0,l.jsxs)(I,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,l.jsx)(d.A,{menu:{items:u},placement:"bottomRight",children:(0,l.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,l.jsx)(C.A,{})})}),(0,l.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,l.jsx)(w,{style:{margin:"16px"},children:t}),(0,l.jsxs)(S,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3097,5573,4492,2343,7605,9434,8441,1684,7358],()=>s(86784)),_N_E=e.O()}]);