(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3884],{36449:()=>{},41008:(e,t,s)=>{"use strict";s.d(t,{i:()=>a});let a={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},73629:()=>{},73884:(e,t,s)=>{"use strict";s.d(t,{Dw:()=>o,WS:()=>n,cm:()=>r});var a=s(83899);class o{static async getAllShareConfigs(){return(await a.Ay.get("/share")).data}static async getActiveShareConfigs(){return(await a.Ay.get("/share/active")).data}static async getDefaultShareConfig(){return(await a.Ay.get("/share/default")).data}static async getShareConfigByType(e){return(await a.Ay.get("/share/type/".concat(e))).data}static async getShareConfigById(e){return(await a.Ay.get("/share/".concat(e))).data}static async createShareConfig(e){return(await a.Ay.post("/share",e)).data}static async updateShareConfig(e,t){return(await a.Ay.patch("/share/".concat(e),t)).data}static async toggleShareConfig(e){return(await a.Ay.put("/share/".concat(e,"/toggle"))).data}static async deleteShareConfig(e){return(await a.Ay.delete("/share/".concat(e))).data}}let n=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],r=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},83899:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,FH:()=>c,KY:()=>i});var a=s(23464),o=s(90285),n=s(41008);let r=a.A.create({baseURL:n.i.FULL_BASE_URL,timeout:n.i.TIMEOUT,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{var t;let s=localStorage.getItem("admin_token");return s&&(e.headers.Authorization="Bearer ".concat(s)),console.log("\uD83D\uDE80 发送请求:",{method:null==(t=e.method)?void 0:t.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),r.interceptors.response.use(e=>{var t;let s=e.config;return console.log("✅ 请求成功:",{method:null==(t=s.method)?void 0:t.toUpperCase(),url:s.url,status:e.status,statusText:e.statusText,data:e.data}),s.showSuccess&&s.successMessage&&o.i.success(s.successMessage),e},e=>{var t,s,a,n,r,c,i,l,d;console.error("❌ 请求失败:",{method:null==(s=e.config)||null==(t=s.method)?void 0:t.toUpperCase(),url:null==(a=e.config)?void 0:a.url,baseURL:null==(n=e.config)?void 0:n.baseURL,fullURL:"".concat(null==(r=e.config)?void 0:r.baseURL).concat(null==(c=e.config)?void 0:c.url),status:null==(i=e.response)?void 0:i.status,statusText:null==(l=e.response)?void 0:l.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:t,data:s}=e.response,a="";switch(t){case 401:a="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a=(null==s?void 0:s.message)||"没有权限访问该资源";break;case 404:a=(null==s?void 0:s.message)||"请求的资源不存在";break;case 422:a=(null==s?void 0:s.message)||"请求参数验证失败";break;case 500:a=(null==s?void 0:s.message)||"服务器内部错误";break;default:a=(null==s?void 0:s.message)||"请求失败 (".concat(t,")")}o.i.error(a)}else e.request?o.i.error("网络连接失败，请检查网络"):o.i.error("请求配置错误");return Promise.reject(e)});let c={get:(e,t)=>r.get(e,t),post:(e,t,s)=>r.post(e,t,s),put:(e,t,s)=>r.put(e,t,s),patch:(e,t,s)=>r.patch(e,t,s),delete:(e,t)=>r.delete(e,t)},i={post:(e,t,s,a)=>c.post(e,t,{...a,showSuccess:!0,successMessage:s||"操作成功"}),put:(e,t,s,a)=>c.put(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),patch:(e,t,s,a)=>c.patch(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),delete:(e,t,s)=>c.delete(e,{...s,showSuccess:!0,successMessage:t||"删除成功"})},l=c},90285:(e,t,s)=>{"use strict";s.d(t,{a:()=>i,i:()=>m});var a=s(95155),o=s(12115),n=s(12669);s(36449);let r=e=>{let{title:t,content:s,children:n,visible:r=!1,width:c=520,centered:i=!1,closable:l=!0,maskClosable:d=!0,footer:u,okText:m="确定",cancelText:h="取消",okType:g="primary",confirmLoading:p=!1,onOk:y,onCancel:v,afterClose:f,className:b="",style:w={}}=e,[k,C]=(0,o.useState)(r),[x,T]=(0,o.useState)(!1);(0,o.useEffect)(()=>{r?(C(!0),T(!0),document.body.style.overflow="hidden"):(T(!1),setTimeout(()=>{C(!1),document.body.style.overflow="",null==f||f()},300))},[r,f]);let S=async()=>{if(y)try{await y()}catch(e){console.error("Modal onOk error:",e)}},j=()=>{null==v||v()};return k?(0,a.jsx)("div",{className:"custom-modal-mask ".concat(x?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==v||v())},children:(0,a.jsx)("div",{className:"custom-modal-wrap ".concat(i?"custom-modal-centered":""),children:(0,a.jsxs)("div",{className:"custom-modal ".concat(b," ").concat(x?"custom-modal-show":"custom-modal-hide"),style:{width:c,...w},children:[(t||l)&&(0,a.jsxs)("div",{className:"custom-modal-header",children:[t&&(0,a.jsx)("div",{className:"custom-modal-title",children:t}),l&&(0,a.jsx)("button",{className:"custom-modal-close",onClick:j,"aria-label":"Close",children:"\xd7"})]}),(0,a.jsx)("div",{className:"custom-modal-body",children:s||n}),null===u?null:u||(0,a.jsxs)("div",{className:"custom-modal-footer",children:[h&&(0,a.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:j,children:h}),(0,a.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(g),onClick:S,disabled:p,children:[p&&(0,a.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),m]})]})]})})}):null};class c{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}confirm(e){return new Promise((t,s)=>{let o=!1,n=async()=>{if(!o)try{e.onOk&&await e.onOk(),o=!0,this.destroy(),t()}catch(e){s(e)}};this.getContainer(),this.root.render((0,a.jsx)(r,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:n,onCancel:()=>{var t;o||(o=!0,null==(t=e.onCancel)||t.call(e),this.destroy(),s(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}r.confirm=e=>new c().confirm({...e,okType:e.okType||"primary"}),r.info=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),r.success=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),r.error=e=>new c().confirm({...e,okType:"danger",cancelText:void 0}),r.warning=e=>new c().confirm({...e,okType:"primary",cancelText:void 0});let i=r;s(73629);let l=e=>{let{messages:t}=e;return(0,a.jsx)("div",{className:"custom-message-container",children:t.map(e=>(0,a.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,a.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,a.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,a.jsx)(l,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var t;let s=e.key||this.generateId(),a=null!=(t=e.duration)?t:3e3;e.key&&(this.messages=this.messages.filter(t=>t.id!==e.key));let o={...e,id:s,visible:!0};return this.messages.push(o),this.getContainer(),this.render(),a>0&&setTimeout(()=>{this.hide(s)},a),s}hide(e){let t=this.messages.findIndex(t=>t.id===e);t>-1&&(this.messages[t].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(t=>t.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,m={success:(e,t)=>u.show({content:e,type:"success",duration:t}),error:(e,t)=>u.show({content:e,type:"error",duration:t}),warning:(e,t)=>u.show({content:e,type:"warning",duration:t}),info:(e,t)=>u.show({content:e,type:"info",duration:t}),destroy:()=>u.destroy()}}}]);