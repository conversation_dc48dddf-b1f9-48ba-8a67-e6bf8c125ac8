/* 分享管理页面样式 */

.shareContainer {
  padding: 24px;
}

.pageHeader {
  margin-bottom: 24px;
}

.pageTitle {
  margin: 0 !important;
  display: flex;
  align-items: center;
}

.titleIcon {
  margin-right: 8px;
}

.pageDescription {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.tableContainer {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.actionButton {
  margin-right: 8px;
}

.statusTag {
  display: flex;
  align-items: center;
}

.typeTag {
  font-weight: 500;
}

.pathCode {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.titleCell {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.idText {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

.modalForm {
  margin-top: 16px;
}

.formSection {
  margin-bottom: 24px;
}

.formSectionTitle {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.pathTemplateOption {
  padding: 8px 0;
}

.pathTemplateLabel {
  font-weight: 500;
  margin-bottom: 4px;
}

.pathTemplateDesc {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.typeOptionLabel {
  font-weight: 500;
  margin-bottom: 4px;
}

.typeOptionDesc {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.detailContainer {
  padding: 24px;
}

.detailHeader {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detailTitle {
  margin: 0 !important;
}

.detailActions {
  display: flex;
  gap: 8px;
}

.detailSection {
  margin-bottom: 32px;
}

.detailSectionTitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.copyButton {
  margin-left: 8px;
}

.imagePreview {
  max-width: 300px;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.usageAlert {
  margin-top: 24px;
}

.usageList {
  margin: 8px 0;
  padding-left: 16px;
}

.usageList li {
  margin-bottom: 4px;
}

.editContainer {
  padding: 24px;
}

.editHeader {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.editForm {
  max-width: 800px;
}

.editFormItem {
  margin-bottom: 24px;
}

.editActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.editTips {
  margin-top: 24px;
}

.editTips ul {
  margin: 8px 0;
  padding-left: 16px;
}

.editTips li {
  margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shareContainer,
  .detailContainer,
  .editContainer {
    padding: 16px;
  }
  
  .pageHeader {
    margin-bottom: 16px;
  }
  
  .detailActions {
    flex-direction: column;
  }
  
  .editActions {
    flex-direction: column-reverse;
  }
  
  .pathCode {
    max-width: 120px;
  }
  
  .titleCell {
    max-width: 120px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .pathCode {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.85);
  }
  
  .formSectionTitle {
    color: rgba(255, 255, 255, 0.85);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .detailHeader {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .editHeader {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .editActions {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
