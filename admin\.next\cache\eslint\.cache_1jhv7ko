[{"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\activation-codes\\page.tsx": "1", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\dashboard\\page.tsx": "2", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx": "3", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\level-tags\\page.tsx": "4", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\create\\page.tsx": "5", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\page.tsx": "6", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx": "7", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\payment-orders\\page.tsx": "8", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\phrases\\page.tsx": "9", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\settings\\page.tsx": "10", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx": "11", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\edit\\page.tsx": "12", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx": "13", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx": "14", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx": "15", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-favorites\\page.tsx": "16", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-stars\\page.tsx": "17", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\users\\page.tsx": "18", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-packages\\page.tsx": "19", "D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx": "20", "D:\\web\\other\\yyddp\\admin\\src\\app\\login\\page.tsx": "21", "D:\\web\\other\\yyddp\\admin\\src\\app\\page.tsx": "22", "D:\\web\\other\\yyddp\\admin\\src\\components\\index.ts": "23", "D:\\web\\other\\yyddp\\admin\\src\\components\\Message\\index.tsx": "24", "D:\\web\\other\\yyddp\\admin\\src\\components\\Modal\\index.tsx": "25", "D:\\web\\other\\yyddp\\admin\\src\\config\\api.ts": "26", "D:\\web\\other\\yyddp\\admin\\src\\examples\\apiUsage.ts": "27", "D:\\web\\other\\yyddp\\admin\\src\\services\\activationCodeService.ts": "28", "D:\\web\\other\\yyddp\\admin\\src\\services\\authService.ts": "29", "D:\\web\\other\\yyddp\\admin\\src\\services\\index.ts": "30", "D:\\web\\other\\yyddp\\admin\\src\\services\\levelService.ts": "31", "D:\\web\\other\\yyddp\\admin\\src\\services\\levelTagService.ts": "32", "D:\\web\\other\\yyddp\\admin\\src\\services\\phraseService.ts": "33", "D:\\web\\other\\yyddp\\admin\\src\\services\\request.ts": "34", "D:\\web\\other\\yyddp\\admin\\src\\services\\settingsService.ts": "35", "D:\\web\\other\\yyddp\\admin\\src\\services\\shareService.ts": "36", "D:\\web\\other\\yyddp\\admin\\src\\services\\thesaurusService.ts": "37", "D:\\web\\other\\yyddp\\admin\\src\\services\\userFavoriteService.ts": "38", "D:\\web\\other\\yyddp\\admin\\src\\services\\userService.ts": "39", "D:\\web\\other\\yyddp\\admin\\src\\services\\userStarService.ts": "40", "D:\\web\\other\\yyddp\\admin\\src\\services\\vipService.ts": "41", "D:\\web\\other\\yyddp\\admin\\src\\types\\level.ts": "42", "D:\\web\\other\\yyddp\\admin\\src\\types\\share.ts": "43", "D:\\web\\other\\yyddp\\admin\\src\\types\\thesaurus.ts": "44", "D:\\web\\other\\yyddp\\admin\\src\\utils\\performance.ts": "45"}, {"size": 17185, "mtime": 1753028090449, "results": "46", "hashOfConfig": "47"}, {"size": 4191, "mtime": 1753888636103, "results": "48", "hashOfConfig": "47"}, {"size": 4390, "mtime": 1753890032517, "results": "49", "hashOfConfig": "47"}, {"size": 9094, "mtime": 1753640107176, "results": "50", "hashOfConfig": "47"}, {"size": 10072, "mtime": 1753640044706, "results": "51", "hashOfConfig": "47"}, {"size": 16663, "mtime": 1753643836848, "results": "52", "hashOfConfig": "47"}, {"size": 12640, "mtime": 1753724868454, "results": "53", "hashOfConfig": "47"}, {"size": 13035, "mtime": 1753888411285, "results": "54", "hashOfConfig": "47"}, {"size": 6738, "mtime": 1751193919422, "results": "55", "hashOfConfig": "47"}, {"size": 7751, "mtime": 1753362853817, "results": "56", "hashOfConfig": "47"}, {"size": 13415, "mtime": 1751193919426, "results": "57", "hashOfConfig": "47"}, {"size": 9056, "mtime": 1751193919425, "results": "58", "hashOfConfig": "47"}, {"size": 9835, "mtime": 1751193919426, "results": "59", "hashOfConfig": "47"}, {"size": 1835, "mtime": 1751193919427, "results": "60", "hashOfConfig": "47"}, {"size": 3669, "mtime": 1751193919427, "results": "61", "hashOfConfig": "47"}, {"size": 12571, "mtime": 1753885382823, "results": "62", "hashOfConfig": "47"}, {"size": 11801, "mtime": 1753885131930, "results": "63", "hashOfConfig": "47"}, {"size": 38583, "mtime": 1753889957511, "results": "64", "hashOfConfig": "47"}, {"size": 13476, "mtime": 1753120479280, "results": "65", "hashOfConfig": "47"}, {"size": 636, "mtime": 1753637008202, "results": "66", "hashOfConfig": "47"}, {"size": 2246, "mtime": 1751193919432, "results": "67", "hashOfConfig": "47"}, {"size": 747, "mtime": 1751193919433, "results": "68", "hashOfConfig": "47"}, {"size": 246, "mtime": 1752991873195, "results": "69", "hashOfConfig": "47"}, {"size": 3867, "mtime": 1752992276905, "results": "70", "hashOfConfig": "47"}, {"size": 7108, "mtime": 1752992251942, "results": "71", "hashOfConfig": "47"}, {"size": 731, "mtime": 1753644372102, "results": "72", "hashOfConfig": "47"}, {"size": 4756, "mtime": 1752892695964, "results": "73", "hashOfConfig": "47"}, {"size": 5224, "mtime": 1753885422795, "results": "74", "hashOfConfig": "47"}, {"size": 1043, "mtime": 1752296309821, "results": "75", "hashOfConfig": "47"}, {"size": 1453, "mtime": 1752317091960, "results": "76", "hashOfConfig": "47"}, {"size": 5076, "mtime": 1753724810133, "results": "77", "hashOfConfig": "47"}, {"size": 2410, "mtime": 1752323877493, "results": "78", "hashOfConfig": "47"}, {"size": 1469, "mtime": 1752296378365, "results": "79", "hashOfConfig": "47"}, {"size": 6770, "mtime": 1753723179303, "results": "80", "hashOfConfig": "47"}, {"size": 3713, "mtime": 1752405980781, "results": "81", "hashOfConfig": "47"}, {"size": 3461, "mtime": 1752296448574, "results": "82", "hashOfConfig": "47"}, {"size": 2522, "mtime": 1752296412851, "results": "83", "hashOfConfig": "47"}, {"size": 2173, "mtime": 1753885797936, "results": "84", "hashOfConfig": "47"}, {"size": 6658, "mtime": 1753117634420, "results": "85", "hashOfConfig": "47"}, {"size": 2185, "mtime": 1753885768791, "results": "86", "hashOfConfig": "47"}, {"size": 3745, "mtime": 1753890082344, "results": "87", "hashOfConfig": "47"}, {"size": 247, "mtime": 1751193919443, "results": "88", "hashOfConfig": "47"}, {"size": 1664, "mtime": 1751193919444, "results": "89", "hashOfConfig": "47"}, {"size": 576, "mtime": 1751193919444, "results": "90", "hashOfConfig": "47"}, {"size": 10646, "mtime": 1752412411681, "results": "91", "hashOfConfig": "47"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1absej", {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\activation-codes\\page.tsx", ["227", "228", "229", "230", "231", "232", "233", "234"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\dashboard\\page.tsx", ["235"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx", ["236", "237"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\level-tags\\page.tsx", ["238", "239", "240", "241"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\create\\page.tsx", ["242", "243", "244", "245"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\page.tsx", ["246", "247", "248", "249", "250", "251", "252", "253"], ["254", "255"], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx", ["256", "257", "258", "259", "260", "261", "262"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\payment-orders\\page.tsx", ["263", "264", "265", "266", "267", "268", "269"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\phrases\\page.tsx", ["270", "271", "272", "273"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\settings\\page.tsx", ["274"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\edit\\page.tsx", ["275"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx", ["276"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx", ["277"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx", ["278", "279"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-favorites\\page.tsx", ["280", "281", "282", "283", "284", "285"], ["286", "287"], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-stars\\page.tsx", ["288", "289", "290", "291", "292", "293"], ["294", "295"], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\users\\page.tsx", ["296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "310", "311", "312"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-packages\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\login\\page.tsx", ["313"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\components\\index.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\components\\Message\\index.tsx", ["314"], [], "D:\\web\\other\\yyddp\\admin\\src\\components\\Modal\\index.tsx", ["315"], [], "D:\\web\\other\\yyddp\\admin\\src\\config\\api.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\examples\\apiUsage.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\activationCodeService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\authService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\index.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\levelService.ts", ["316", "317"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\levelTagService.ts", ["318", "319", "320", "321", "322"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\phraseService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\request.ts", ["323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\settingsService.ts", ["346"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\shareService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\thesaurusService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\userFavoriteService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\userService.ts", ["347"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\userStarService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\vipService.ts", ["348"], [], "D:\\web\\other\\yyddp\\admin\\src\\types\\level.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\types\\share.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\types\\thesaurus.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\utils\\performance.ts", ["349", "350", "351", "352", "353", "354", "355"], [], {"ruleId": "356", "severity": 1, "message": "357", "line": 22, "column": 44, "nodeType": "358", "messageId": "359", "endLine": 22, "endColumn": 47, "suggestions": "360"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 121, "column": 14, "nodeType": null, "messageId": "363", "endLine": 121, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 133, "column": 14, "nodeType": null, "messageId": "363", "endLine": 133, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "364", "line": 160, "column": 14, "nodeType": null, "messageId": "363", "endLine": 160, "endColumn": 24}, {"ruleId": "356", "severity": 1, "message": "357", "line": 451, "column": 51, "nodeType": "358", "messageId": "359", "endLine": 451, "endColumn": 54, "suggestions": "365"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 453, "column": 85, "nodeType": "358", "messageId": "359", "endLine": 453, "endColumn": 88, "suggestions": "366"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 548, "column": 51, "nodeType": "358", "messageId": "359", "endLine": 548, "endColumn": 54, "suggestions": "367"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 550, "column": 85, "nodeType": "358", "messageId": "359", "endLine": 550, "endColumn": 88, "suggestions": "368"}, {"ruleId": "361", "severity": 1, "message": "369", "line": 21, "column": 10, "nodeType": null, "messageId": "363", "endLine": 21, "endColumn": 17}, {"ruleId": "361", "severity": 1, "message": "370", "line": 12, "column": 3, "nodeType": null, "messageId": "363", "endLine": 12, "endColumn": 15}, {"ruleId": "361", "severity": 1, "message": "371", "line": 16, "column": 3, "nodeType": null, "messageId": "363", "endLine": 16, "endColumn": 16}, {"ruleId": "356", "severity": 1, "message": "357", "line": 77, "column": 39, "nodeType": "358", "messageId": "359", "endLine": 77, "endColumn": 42, "suggestions": "372"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 96, "column": 14, "nodeType": null, "messageId": "363", "endLine": 96, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 107, "column": 14, "nodeType": null, "messageId": "363", "endLine": 107, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 119, "column": 14, "nodeType": null, "messageId": "363", "endLine": 119, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 68, "column": 14, "nodeType": null, "messageId": "363", "endLine": 68, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 78, "column": 14, "nodeType": null, "messageId": "363", "endLine": 78, "endColumn": 19}, {"ruleId": "356", "severity": 1, "message": "357", "line": 89, "column": 39, "nodeType": "358", "messageId": "359", "endLine": 89, "endColumn": 42, "suggestions": "373"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 113, "column": 14, "nodeType": null, "messageId": "363", "endLine": 113, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "374", "line": 6, "column": 115, "nodeType": null, "messageId": "363", "endLine": 6, "endColumn": 129}, {"ruleId": "361", "severity": 1, "message": "375", "line": 8, "column": 86, "nodeType": null, "messageId": "363", "endLine": 8, "endColumn": 103}, {"ruleId": "361", "severity": 1, "message": "362", "line": 57, "column": 14, "nodeType": null, "messageId": "363", "endLine": 57, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 71, "column": 14, "nodeType": null, "messageId": "363", "endLine": 71, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 134, "column": 14, "nodeType": null, "messageId": "363", "endLine": 134, "endColumn": 19}, {"ruleId": "356", "severity": 1, "message": "357", "line": 200, "column": 57, "nodeType": "358", "messageId": "359", "endLine": 200, "endColumn": 60, "suggestions": "376"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 213, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 213, "endColumn": 24, "suggestions": "377"}, {"ruleId": "361", "severity": 1, "message": "378", "line": 219, "column": 9, "nodeType": null, "messageId": "363", "endLine": 219, "endColumn": 27}, {"ruleId": "379", "severity": 1, "message": "380", "line": 108, "column": 6, "nodeType": "381", "endLine": 108, "endColumn": 8, "suggestions": "382", "suppressions": "383"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 113, "column": 6, "nodeType": "381", "endLine": 113, "endColumn": 47, "suggestions": "384", "suppressions": "385"}, {"ruleId": "361", "severity": 1, "message": "386", "line": 30, "column": 10, "nodeType": null, "messageId": "363", "endLine": 30, "endColumn": 20}, {"ruleId": "356", "severity": 1, "message": "357", "line": 89, "column": 67, "nodeType": "358", "messageId": "359", "endLine": 89, "endColumn": 70, "suggestions": "387"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 109, "column": 14, "nodeType": null, "messageId": "363", "endLine": 109, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 119, "column": 14, "nodeType": null, "messageId": "363", "endLine": 119, "endColumn": 19}, {"ruleId": "379", "severity": 1, "message": "388", "line": 128, "column": 6, "nodeType": "381", "endLine": 128, "endColumn": 15, "suggestions": "389"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 131, "column": 39, "nodeType": "358", "messageId": "359", "endLine": 131, "endColumn": 42, "suggestions": "390"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 187, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 187, "endColumn": 24, "suggestions": "391"}, {"ruleId": "361", "severity": 1, "message": "392", "line": 5, "column": 39, "nodeType": null, "messageId": "363", "endLine": 5, "endColumn": 53}, {"ruleId": "356", "severity": 1, "message": "357", "line": 13, "column": 28, "nodeType": "358", "messageId": "359", "endLine": 13, "endColumn": 31, "suggestions": "393"}, {"ruleId": "361", "severity": 1, "message": "394", "line": 19, "column": 7, "nodeType": null, "messageId": "363", "endLine": 19, "endColumn": 17}, {"ruleId": "356", "severity": 1, "message": "357", "line": 19, "column": 28, "nodeType": "358", "messageId": "359", "endLine": 19, "endColumn": 31, "suggestions": "395"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 65, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 65, "endColumn": 24, "suggestions": "396"}, {"ruleId": "379", "severity": 1, "message": "397", "line": 94, "column": 6, "nodeType": "381", "endLine": 94, "endColumn": 43, "suggestions": "398"}, {"ruleId": "379", "severity": 1, "message": "397", "line": 98, "column": 6, "nodeType": "381", "endLine": 98, "endColumn": 8, "suggestions": "399"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 24, "column": 14, "nodeType": null, "messageId": "363", "endLine": 24, "endColumn": 19}, {"ruleId": "356", "severity": 1, "message": "357", "line": 36, "column": 39, "nodeType": "358", "messageId": "359", "endLine": 36, "endColumn": 42, "suggestions": "400"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 56, "column": 14, "nodeType": null, "messageId": "363", "endLine": 56, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 67, "column": 14, "nodeType": null, "messageId": "363", "endLine": 67, "endColumn": 19}, {"ruleId": "379", "severity": 1, "message": "401", "line": 125, "column": 6, "nodeType": "381", "endLine": 125, "endColumn": 8, "suggestions": "402"}, {"ruleId": "379", "severity": 1, "message": "403", "line": 73, "column": 6, "nodeType": "381", "endLine": 73, "endColumn": 15, "suggestions": "404"}, {"ruleId": "379", "severity": 1, "message": "403", "line": 59, "column": 6, "nodeType": "381", "endLine": 59, "endColumn": 15, "suggestions": "405"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 22, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 22, "endColumn": 24, "suggestions": "406"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 20, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 20, "endColumn": 24, "suggestions": "407"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 37, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 37, "endColumn": 24, "suggestions": "408"}, {"ruleId": "361", "severity": 1, "message": "409", "line": 5, "column": 26, "nodeType": null, "messageId": "363", "endLine": 5, "endColumn": 35}, {"ruleId": "361", "severity": 1, "message": "410", "line": 6, "column": 25, "nodeType": null, "messageId": "363", "endLine": 6, "endColumn": 33}, {"ruleId": "361", "severity": 1, "message": "411", "line": 9, "column": 3, "nodeType": null, "messageId": "363", "endLine": 9, "endColumn": 16}, {"ruleId": "361", "severity": 1, "message": "412", "line": 9, "column": 32, "nodeType": null, "messageId": "363", "endLine": 9, "endColumn": 53}, {"ruleId": "361", "severity": 1, "message": "413", "line": 9, "column": 55, "nodeType": null, "messageId": "363", "endLine": 9, "endColumn": 71}, {"ruleId": "356", "severity": 1, "message": "357", "line": 332, "column": 66, "nodeType": "358", "messageId": "359", "endLine": 332, "endColumn": 69, "suggestions": "414"}, {"ruleId": "379", "severity": 1, "message": "415", "line": 131, "column": 6, "nodeType": "381", "endLine": 131, "endColumn": 8, "suggestions": "416", "suppressions": "417"}, {"ruleId": "379", "severity": 1, "message": "418", "line": 138, "column": 6, "nodeType": "381", "endLine": 138, "endColumn": 21, "suggestions": "419", "suppressions": "420"}, {"ruleId": "361", "severity": 1, "message": "409", "line": 5, "column": 26, "nodeType": null, "messageId": "363", "endLine": 5, "endColumn": 35}, {"ruleId": "361", "severity": 1, "message": "421", "line": 6, "column": 3, "nodeType": null, "messageId": "363", "endLine": 6, "endColumn": 6}, {"ruleId": "361", "severity": 1, "message": "422", "line": 9, "column": 17, "nodeType": null, "messageId": "363", "endLine": 9, "endColumn": 31}, {"ruleId": "361", "severity": 1, "message": "423", "line": 9, "column": 33, "nodeType": null, "messageId": "363", "endLine": 9, "endColumn": 45}, {"ruleId": "361", "severity": 1, "message": "413", "line": 9, "column": 47, "nodeType": null, "messageId": "363", "endLine": 9, "endColumn": 63}, {"ruleId": "356", "severity": 1, "message": "357", "line": 323, "column": 66, "nodeType": "358", "messageId": "359", "endLine": 323, "endColumn": 69, "suggestions": "424"}, {"ruleId": "379", "severity": 1, "message": "425", "line": 134, "column": 6, "nodeType": "381", "endLine": 134, "endColumn": 8, "suggestions": "426", "suppressions": "427"}, {"ruleId": "379", "severity": 1, "message": "418", "line": 141, "column": 6, "nodeType": "381", "endLine": 141, "endColumn": 21, "suggestions": "428", "suppressions": "429"}, {"ruleId": "361", "severity": 1, "message": "430", "line": 7, "column": 50, "nodeType": null, "messageId": "363", "endLine": 7, "endColumn": 58}, {"ruleId": "361", "severity": 1, "message": "431", "line": 7, "column": 60, "nodeType": null, "messageId": "363", "endLine": 7, "endColumn": 66}, {"ruleId": "361", "severity": 1, "message": "432", "line": 7, "column": 68, "nodeType": null, "messageId": "363", "endLine": 7, "endColumn": 75}, {"ruleId": "361", "severity": 1, "message": "374", "line": 12, "column": 64, "nodeType": null, "messageId": "363", "endLine": 12, "endColumn": 78}, {"ruleId": "361", "severity": 1, "message": "433", "line": 13, "column": 3, "nodeType": null, "messageId": "363", "endLine": 13, "endColumn": 17}, {"ruleId": "361", "severity": 1, "message": "434", "line": 13, "column": 19, "nodeType": null, "messageId": "363", "endLine": 13, "endColumn": 33}, {"ruleId": "361", "severity": 1, "message": "435", "line": 13, "column": 65, "nodeType": null, "messageId": "363", "endLine": 13, "endColumn": 80}, {"ruleId": "361", "severity": 1, "message": "436", "line": 14, "column": 3, "nodeType": null, "messageId": "363", "endLine": 14, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "437", "line": 14, "column": 21, "nodeType": null, "messageId": "363", "endLine": 14, "endColumn": 35}, {"ruleId": "356", "severity": 1, "message": "357", "line": 50, "column": 52, "nodeType": "358", "messageId": "359", "endLine": 50, "endColumn": 55, "suggestions": "438"}, {"ruleId": "379", "severity": 1, "message": "439", "line": 151, "column": 6, "nodeType": "381", "endLine": 151, "endColumn": 8, "suggestions": "440"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 155, "column": 19, "nodeType": "358", "messageId": "359", "endLine": 155, "endColumn": 22, "suggestions": "441"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 179, "column": 19, "nodeType": "358", "messageId": "359", "endLine": 179, "endColumn": 22, "suggestions": "442"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 204, "column": 14, "nodeType": null, "messageId": "363", "endLine": 204, "endColumn": 19}, {"ruleId": "361", "severity": 1, "message": "362", "line": 270, "column": 18, "nodeType": null, "messageId": "363", "endLine": 270, "endColumn": 23}, {"ruleId": "356", "severity": 1, "message": "357", "line": 360, "column": 39, "nodeType": "358", "messageId": "359", "endLine": 360, "endColumn": 42, "suggestions": "443"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 668, "column": 37, "nodeType": "358", "messageId": "359", "endLine": 668, "endColumn": 40, "suggestions": "444"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 26, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 26, "endColumn": 24, "suggestions": "445"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 45, "column": 17, "nodeType": "358", "messageId": "359", "endLine": 45, "endColumn": 20, "suggestions": "446"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 167, "column": 17, "nodeType": "358", "messageId": "359", "endLine": 167, "endColumn": 20, "suggestions": "447"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 157, "column": 66, "nodeType": "358", "messageId": "359", "endLine": 157, "endColumn": 69, "suggestions": "448"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 158, "column": 59, "nodeType": "358", "messageId": "359", "endLine": 158, "endColumn": 62, "suggestions": "449"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 4, "column": 30, "nodeType": "358", "messageId": "359", "endLine": 4, "endColumn": 33, "suggestions": "450"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 55, "column": 40, "nodeType": "358", "messageId": "359", "endLine": 55, "endColumn": 43, "suggestions": "451"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 63, "column": 40, "nodeType": "358", "messageId": "359", "endLine": 63, "endColumn": 43, "suggestions": "452"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 69, "column": 41, "nodeType": "358", "messageId": "359", "endLine": 69, "endColumn": 44, "suggestions": "453"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 75, "column": 40, "nodeType": "358", "messageId": "359", "endLine": 75, "endColumn": 43, "suggestions": "454"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 130, "column": 13, "nodeType": "358", "messageId": "359", "endLine": 130, "endColumn": 16, "suggestions": "455"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 138, "column": 14, "nodeType": "358", "messageId": "359", "endLine": 138, "endColumn": 17, "suggestions": "456"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 140, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 140, "endColumn": 15, "suggestions": "457"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 147, "column": 13, "nodeType": "358", "messageId": "359", "endLine": 147, "endColumn": 16, "suggestions": "458"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 149, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 149, "endColumn": 15, "suggestions": "459"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 156, "column": 15, "nodeType": "358", "messageId": "359", "endLine": 156, "endColumn": 18, "suggestions": "460"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 158, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 158, "endColumn": 15, "suggestions": "461"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 165, "column": 16, "nodeType": "358", "messageId": "359", "endLine": 165, "endColumn": 19, "suggestions": "462"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 175, "column": 13, "nodeType": "358", "messageId": "359", "endLine": 175, "endColumn": 16, "suggestions": "463"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 178, "column": 14, "nodeType": "358", "messageId": "359", "endLine": 178, "endColumn": 17, "suggestions": "464"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 178, "column": 39, "nodeType": "358", "messageId": "359", "endLine": 178, "endColumn": 42, "suggestions": "465"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 181, "column": 13, "nodeType": "358", "messageId": "359", "endLine": 181, "endColumn": 16, "suggestions": "466"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 181, "column": 38, "nodeType": "358", "messageId": "359", "endLine": 181, "endColumn": 41, "suggestions": "467"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 184, "column": 15, "nodeType": "358", "messageId": "359", "endLine": 184, "endColumn": 18, "suggestions": "468"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 186, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 186, "endColumn": 15, "suggestions": "469"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 190, "column": 16, "nodeType": "358", "messageId": "359", "endLine": 190, "endColumn": 19, "suggestions": "470"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 196, "column": 14, "nodeType": "358", "messageId": "359", "endLine": 196, "endColumn": 17, "suggestions": "471"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 198, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 198, "endColumn": 15, "suggestions": "472"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 208, "column": 13, "nodeType": "358", "messageId": "359", "endLine": 208, "endColumn": 16, "suggestions": "473"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 210, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 210, "endColumn": 15, "suggestions": "474"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 220, "column": 15, "nodeType": "358", "messageId": "359", "endLine": 220, "endColumn": 18, "suggestions": "475"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 222, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 222, "endColumn": 15, "suggestions": "476"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 232, "column": 16, "nodeType": "358", "messageId": "359", "endLine": 232, "endColumn": 19, "suggestions": "477"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 125, "column": 43, "nodeType": "358", "messageId": "359", "endLine": 125, "endColumn": 46, "suggestions": "478"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 107, "column": 36, "nodeType": "358", "messageId": "359", "endLine": 107, "endColumn": 39, "suggestions": "479"}, {"ruleId": "480", "severity": 1, "message": "481", "line": 151, "column": 1, "nodeType": "482", "endLine": 154, "endColumn": 3}, {"ruleId": "356", "severity": 1, "message": "357", "line": 11, "column": 49, "nodeType": "358", "messageId": "359", "endLine": 11, "endColumn": 52, "suggestions": "483"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 11, "column": 59, "nodeType": "358", "messageId": "359", "endLine": 11, "endColumn": 62, "suggestions": "484"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 46, "column": 49, "nodeType": "358", "messageId": "359", "endLine": 46, "endColumn": 52, "suggestions": "485"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 46, "column": 59, "nodeType": "358", "messageId": "359", "endLine": 46, "endColumn": 62, "suggestions": "486"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 261, "column": 39, "nodeType": "358", "messageId": "359", "endLine": 261, "endColumn": 42, "suggestions": "487"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 292, "column": 55, "nodeType": "358", "messageId": "359", "endLine": 292, "endColumn": 58, "suggestions": "488"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 292, "column": 73, "nodeType": "358", "messageId": "359", "endLine": 292, "endColumn": 76, "suggestions": "489"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["490", "491"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "'modalError' is defined but never used.", ["492", "493"], ["494", "495"], ["496", "497"], ["498", "499"], "'loading' is assigned a value but never used.", "'ReadOutlined' is defined but never used.", "'CrownOutlined' is defined but never used.", ["500", "501"], ["502", "503"], "'FilterOutlined' is defined but never used.", "'LevelListResponse' is defined but never used.", ["504", "505"], ["506", "507"], "'getDifficultyColor' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchLevels'. Either include it or remove the dependency array.", "ArrayExpression", ["508"], ["509"], ["510"], ["511"], "'levelStats' is assigned a value but never used.", ["512", "513"], "React Hook useEffect has a missing dependency: 'fetchLevelDetail'. Either include it or remove the dependency array.", ["514"], ["515", "516"], ["517", "518"], "'SearchOutlined' is defined but never used.", ["519", "520"], "'safeAmount' is assigned a value but never used.", ["521", "522"], ["523", "524"], "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["525"], ["526"], ["527", "528"], "React Hook useEffect has a missing dependency: 'fetchConfig'. Either include it or remove the dependency array.", ["529"], "React Hook useEffect has a missing dependency: 'fetchShareDetail'. Either include it or remove the dependency array.", ["530"], ["531"], ["532", "533"], ["534", "535"], ["536", "537"], "'Statistic' is defined but never used.", "'Progress' is defined but never used.", "'HeartOutlined' is defined but never used.", "'UnorderedListOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", ["538", "539"], "React Hook useEffect has missing dependencies: 'fetchFavorites' and 'searchParams'. Either include them or remove the dependency array.", ["540"], ["541"], "React Hook useEffect has a missing dependency: 'handleFilter'. Either include it or remove the dependency array.", ["542"], ["543"], "'Tag' is defined but never used.", "'TrophyOutlined' is defined but never used.", "'UserOutlined' is defined but never used.", ["544", "545"], "React Hook useEffect has missing dependencies: 'fetchStars' and 'searchParams'. Either include them or remove the dependency array.", ["546"], ["547"], ["548"], ["549"], "'Checkbox' is defined but never used.", "'Upload' is defined but never used.", "'Divider' is defined but never used.", "'ExportOutlined' is defined but never used.", "'ImportOutlined' is defined but never used.", "'SettingOutlined' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'UploadOutlined' is defined but never used.", ["550", "551"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["552"], ["553", "554"], ["555", "556"], ["557", "558"], ["559", "560"], ["561", "562"], ["563", "564"], ["565", "566"], ["567", "568"], ["569", "570"], ["571", "572"], ["573", "574"], ["575", "576"], ["577", "578"], ["579", "580"], ["581", "582"], ["583", "584"], ["585", "586"], ["587", "588"], ["589", "590"], ["591", "592"], ["593", "594"], ["595", "596"], ["597", "598"], ["599", "600"], ["601", "602"], ["603", "604"], ["605", "606"], ["607", "608"], ["609", "610"], ["611", "612"], ["613", "614"], ["615", "616"], ["617", "618"], ["619", "620"], ["621", "622"], ["623", "624"], ["625", "626"], ["627", "628"], ["629", "630"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", ["631", "632"], ["633", "634"], ["635", "636"], ["637", "638"], ["639", "640"], ["641", "642"], ["643", "644"], {"messageId": "645", "fix": "646", "desc": "647"}, {"messageId": "648", "fix": "649", "desc": "650"}, {"messageId": "645", "fix": "651", "desc": "647"}, {"messageId": "648", "fix": "652", "desc": "650"}, {"messageId": "645", "fix": "653", "desc": "647"}, {"messageId": "648", "fix": "654", "desc": "650"}, {"messageId": "645", "fix": "655", "desc": "647"}, {"messageId": "648", "fix": "656", "desc": "650"}, {"messageId": "645", "fix": "657", "desc": "647"}, {"messageId": "648", "fix": "658", "desc": "650"}, {"messageId": "645", "fix": "659", "desc": "647"}, {"messageId": "648", "fix": "660", "desc": "650"}, {"messageId": "645", "fix": "661", "desc": "647"}, {"messageId": "648", "fix": "662", "desc": "650"}, {"messageId": "645", "fix": "663", "desc": "647"}, {"messageId": "648", "fix": "664", "desc": "650"}, {"messageId": "645", "fix": "665", "desc": "647"}, {"messageId": "648", "fix": "666", "desc": "650"}, {"desc": "667", "fix": "668"}, {"kind": "669", "justification": "670"}, {"desc": "671", "fix": "672"}, {"kind": "669", "justification": "670"}, {"messageId": "645", "fix": "673", "desc": "647"}, {"messageId": "648", "fix": "674", "desc": "650"}, {"desc": "675", "fix": "676"}, {"messageId": "645", "fix": "677", "desc": "647"}, {"messageId": "648", "fix": "678", "desc": "650"}, {"messageId": "645", "fix": "679", "desc": "647"}, {"messageId": "648", "fix": "680", "desc": "650"}, {"messageId": "645", "fix": "681", "desc": "647"}, {"messageId": "648", "fix": "682", "desc": "650"}, {"messageId": "645", "fix": "683", "desc": "647"}, {"messageId": "648", "fix": "684", "desc": "650"}, {"messageId": "645", "fix": "685", "desc": "647"}, {"messageId": "648", "fix": "686", "desc": "650"}, {"desc": "687", "fix": "688"}, {"desc": "689", "fix": "690"}, {"messageId": "645", "fix": "691", "desc": "647"}, {"messageId": "648", "fix": "692", "desc": "650"}, {"desc": "693", "fix": "694"}, {"desc": "695", "fix": "696"}, {"desc": "695", "fix": "697"}, {"messageId": "645", "fix": "698", "desc": "647"}, {"messageId": "648", "fix": "699", "desc": "650"}, {"messageId": "645", "fix": "700", "desc": "647"}, {"messageId": "648", "fix": "701", "desc": "650"}, {"messageId": "645", "fix": "702", "desc": "647"}, {"messageId": "648", "fix": "703", "desc": "650"}, {"messageId": "645", "fix": "704", "desc": "647"}, {"messageId": "648", "fix": "705", "desc": "650"}, {"desc": "706", "fix": "707"}, {"kind": "669", "justification": "670"}, {"desc": "708", "fix": "709"}, {"kind": "669", "justification": "670"}, {"messageId": "645", "fix": "710", "desc": "647"}, {"messageId": "648", "fix": "711", "desc": "650"}, {"desc": "712", "fix": "713"}, {"kind": "669", "justification": "670"}, {"desc": "708", "fix": "714"}, {"kind": "669", "justification": "670"}, {"messageId": "645", "fix": "715", "desc": "647"}, {"messageId": "648", "fix": "716", "desc": "650"}, {"desc": "717", "fix": "718"}, {"messageId": "645", "fix": "719", "desc": "647"}, {"messageId": "648", "fix": "720", "desc": "650"}, {"messageId": "645", "fix": "721", "desc": "647"}, {"messageId": "648", "fix": "722", "desc": "650"}, {"messageId": "645", "fix": "723", "desc": "647"}, {"messageId": "648", "fix": "724", "desc": "650"}, {"messageId": "645", "fix": "725", "desc": "647"}, {"messageId": "648", "fix": "726", "desc": "650"}, {"messageId": "645", "fix": "727", "desc": "647"}, {"messageId": "648", "fix": "728", "desc": "650"}, {"messageId": "645", "fix": "729", "desc": "647"}, {"messageId": "648", "fix": "730", "desc": "650"}, {"messageId": "645", "fix": "731", "desc": "647"}, {"messageId": "648", "fix": "732", "desc": "650"}, {"messageId": "645", "fix": "733", "desc": "647"}, {"messageId": "648", "fix": "734", "desc": "650"}, {"messageId": "645", "fix": "735", "desc": "647"}, {"messageId": "648", "fix": "736", "desc": "650"}, {"messageId": "645", "fix": "737", "desc": "647"}, {"messageId": "648", "fix": "738", "desc": "650"}, {"messageId": "645", "fix": "739", "desc": "647"}, {"messageId": "648", "fix": "740", "desc": "650"}, {"messageId": "645", "fix": "741", "desc": "647"}, {"messageId": "648", "fix": "742", "desc": "650"}, {"messageId": "645", "fix": "743", "desc": "647"}, {"messageId": "648", "fix": "744", "desc": "650"}, {"messageId": "645", "fix": "745", "desc": "647"}, {"messageId": "648", "fix": "746", "desc": "650"}, {"messageId": "645", "fix": "747", "desc": "647"}, {"messageId": "648", "fix": "748", "desc": "650"}, {"messageId": "645", "fix": "749", "desc": "647"}, {"messageId": "648", "fix": "750", "desc": "650"}, {"messageId": "645", "fix": "751", "desc": "647"}, {"messageId": "648", "fix": "752", "desc": "650"}, {"messageId": "645", "fix": "753", "desc": "647"}, {"messageId": "648", "fix": "754", "desc": "650"}, {"messageId": "645", "fix": "755", "desc": "647"}, {"messageId": "648", "fix": "756", "desc": "650"}, {"messageId": "645", "fix": "757", "desc": "647"}, {"messageId": "648", "fix": "758", "desc": "650"}, {"messageId": "645", "fix": "759", "desc": "647"}, {"messageId": "648", "fix": "760", "desc": "650"}, {"messageId": "645", "fix": "761", "desc": "647"}, {"messageId": "648", "fix": "762", "desc": "650"}, {"messageId": "645", "fix": "763", "desc": "647"}, {"messageId": "648", "fix": "764", "desc": "650"}, {"messageId": "645", "fix": "765", "desc": "647"}, {"messageId": "648", "fix": "766", "desc": "650"}, {"messageId": "645", "fix": "767", "desc": "647"}, {"messageId": "648", "fix": "768", "desc": "650"}, {"messageId": "645", "fix": "769", "desc": "647"}, {"messageId": "648", "fix": "770", "desc": "650"}, {"messageId": "645", "fix": "771", "desc": "647"}, {"messageId": "648", "fix": "772", "desc": "650"}, {"messageId": "645", "fix": "773", "desc": "647"}, {"messageId": "648", "fix": "774", "desc": "650"}, {"messageId": "645", "fix": "775", "desc": "647"}, {"messageId": "648", "fix": "776", "desc": "650"}, {"messageId": "645", "fix": "777", "desc": "647"}, {"messageId": "648", "fix": "778", "desc": "650"}, {"messageId": "645", "fix": "779", "desc": "647"}, {"messageId": "648", "fix": "780", "desc": "650"}, {"messageId": "645", "fix": "781", "desc": "647"}, {"messageId": "648", "fix": "782", "desc": "650"}, {"messageId": "645", "fix": "783", "desc": "647"}, {"messageId": "648", "fix": "784", "desc": "650"}, {"messageId": "645", "fix": "785", "desc": "647"}, {"messageId": "648", "fix": "786", "desc": "650"}, {"messageId": "645", "fix": "787", "desc": "647"}, {"messageId": "648", "fix": "788", "desc": "650"}, {"messageId": "645", "fix": "789", "desc": "647"}, {"messageId": "648", "fix": "790", "desc": "650"}, {"messageId": "645", "fix": "791", "desc": "647"}, {"messageId": "648", "fix": "792", "desc": "650"}, {"messageId": "645", "fix": "793", "desc": "647"}, {"messageId": "648", "fix": "794", "desc": "650"}, {"messageId": "645", "fix": "795", "desc": "647"}, {"messageId": "648", "fix": "796", "desc": "650"}, {"messageId": "645", "fix": "797", "desc": "647"}, {"messageId": "648", "fix": "798", "desc": "650"}, {"messageId": "645", "fix": "799", "desc": "647"}, {"messageId": "648", "fix": "800", "desc": "650"}, {"messageId": "645", "fix": "801", "desc": "647"}, {"messageId": "648", "fix": "802", "desc": "650"}, {"messageId": "645", "fix": "803", "desc": "647"}, {"messageId": "648", "fix": "804", "desc": "650"}, {"messageId": "645", "fix": "805", "desc": "647"}, {"messageId": "648", "fix": "806", "desc": "650"}, {"messageId": "645", "fix": "807", "desc": "647"}, {"messageId": "648", "fix": "808", "desc": "650"}, {"messageId": "645", "fix": "809", "desc": "647"}, {"messageId": "648", "fix": "810", "desc": "650"}, "suggestUnknown", {"range": "811", "text": "812"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "813", "text": "814"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "815", "text": "812"}, {"range": "816", "text": "814"}, {"range": "817", "text": "812"}, {"range": "818", "text": "814"}, {"range": "819", "text": "812"}, {"range": "820", "text": "814"}, {"range": "821", "text": "812"}, {"range": "822", "text": "814"}, {"range": "823", "text": "812"}, {"range": "824", "text": "814"}, {"range": "825", "text": "812"}, {"range": "826", "text": "814"}, {"range": "827", "text": "812"}, {"range": "828", "text": "814"}, {"range": "829", "text": "812"}, {"range": "830", "text": "814"}, "Update the dependencies array to be: [fetchLevels]", {"range": "831", "text": "832"}, "directive", "", "Update the dependencies array to be: [searchText, difficultyFilter, tagFilter, fetchLevels]", {"range": "833", "text": "834"}, {"range": "835", "text": "812"}, {"range": "836", "text": "814"}, "Update the dependencies array to be: [fetchLevelDetail, levelId]", {"range": "837", "text": "838"}, {"range": "839", "text": "812"}, {"range": "840", "text": "814"}, {"range": "841", "text": "812"}, {"range": "842", "text": "814"}, {"range": "843", "text": "812"}, {"range": "844", "text": "814"}, {"range": "845", "text": "812"}, {"range": "846", "text": "814"}, {"range": "847", "text": "812"}, {"range": "848", "text": "814"}, "Update the dependencies array to be: [searchText, statusFilter, dateRange, fetchOrders]", {"range": "849", "text": "850"}, "Update the dependencies array to be: [fetchOrders]", {"range": "851", "text": "852"}, {"range": "853", "text": "812"}, {"range": "854", "text": "814"}, "Update the dependencies array to be: [fetchConfig]", {"range": "855", "text": "856"}, "Update the dependencies array to be: [fetchShareDetail, shareId]", {"range": "857", "text": "858"}, {"range": "859", "text": "858"}, {"range": "860", "text": "812"}, {"range": "861", "text": "814"}, {"range": "862", "text": "812"}, {"range": "863", "text": "814"}, {"range": "864", "text": "812"}, {"range": "865", "text": "814"}, {"range": "866", "text": "812"}, {"range": "867", "text": "814"}, "Update the dependencies array to be: [fetchFavorites, searchParams]", {"range": "868", "text": "869"}, "Update the dependencies array to be: [handleFilter, selectedLevel]", {"range": "870", "text": "871"}, {"range": "872", "text": "812"}, {"range": "873", "text": "814"}, "Update the dependencies array to be: [fetchStars, searchParams]", {"range": "874", "text": "875"}, {"range": "876", "text": "871"}, {"range": "877", "text": "812"}, {"range": "878", "text": "814"}, "Update the dependencies array to be: [fetchUsers]", {"range": "879", "text": "880"}, {"range": "881", "text": "812"}, {"range": "882", "text": "814"}, {"range": "883", "text": "812"}, {"range": "884", "text": "814"}, {"range": "885", "text": "812"}, {"range": "886", "text": "814"}, {"range": "887", "text": "812"}, {"range": "888", "text": "814"}, {"range": "889", "text": "812"}, {"range": "890", "text": "814"}, {"range": "891", "text": "812"}, {"range": "892", "text": "814"}, {"range": "893", "text": "812"}, {"range": "894", "text": "814"}, {"range": "895", "text": "812"}, {"range": "896", "text": "814"}, {"range": "897", "text": "812"}, {"range": "898", "text": "814"}, {"range": "899", "text": "812"}, {"range": "900", "text": "814"}, {"range": "901", "text": "812"}, {"range": "902", "text": "814"}, {"range": "903", "text": "812"}, {"range": "904", "text": "814"}, {"range": "905", "text": "812"}, {"range": "906", "text": "814"}, {"range": "907", "text": "812"}, {"range": "908", "text": "814"}, {"range": "909", "text": "812"}, {"range": "910", "text": "814"}, {"range": "911", "text": "812"}, {"range": "912", "text": "814"}, {"range": "913", "text": "812"}, {"range": "914", "text": "814"}, {"range": "915", "text": "812"}, {"range": "916", "text": "814"}, {"range": "917", "text": "812"}, {"range": "918", "text": "814"}, {"range": "919", "text": "812"}, {"range": "920", "text": "814"}, {"range": "921", "text": "812"}, {"range": "922", "text": "814"}, {"range": "923", "text": "812"}, {"range": "924", "text": "814"}, {"range": "925", "text": "812"}, {"range": "926", "text": "814"}, {"range": "927", "text": "812"}, {"range": "928", "text": "814"}, {"range": "929", "text": "812"}, {"range": "930", "text": "814"}, {"range": "931", "text": "812"}, {"range": "932", "text": "814"}, {"range": "933", "text": "812"}, {"range": "934", "text": "814"}, {"range": "935", "text": "812"}, {"range": "936", "text": "814"}, {"range": "937", "text": "812"}, {"range": "938", "text": "814"}, {"range": "939", "text": "812"}, {"range": "940", "text": "814"}, {"range": "941", "text": "812"}, {"range": "942", "text": "814"}, {"range": "943", "text": "812"}, {"range": "944", "text": "814"}, {"range": "945", "text": "812"}, {"range": "946", "text": "814"}, {"range": "947", "text": "812"}, {"range": "948", "text": "814"}, {"range": "949", "text": "812"}, {"range": "950", "text": "814"}, {"range": "951", "text": "812"}, {"range": "952", "text": "814"}, {"range": "953", "text": "812"}, {"range": "954", "text": "814"}, {"range": "955", "text": "812"}, {"range": "956", "text": "814"}, {"range": "957", "text": "812"}, {"range": "958", "text": "814"}, {"range": "959", "text": "812"}, {"range": "960", "text": "814"}, {"range": "961", "text": "812"}, {"range": "962", "text": "814"}, {"range": "963", "text": "812"}, {"range": "964", "text": "814"}, {"range": "965", "text": "812"}, {"range": "966", "text": "814"}, {"range": "967", "text": "812"}, {"range": "968", "text": "814"}, {"range": "969", "text": "812"}, {"range": "970", "text": "814"}, {"range": "971", "text": "812"}, {"range": "972", "text": "814"}, [778, 781], "unknown", [778, 781], "never", [12642, 12645], [12642, 12645], [12786, 12789], [12786, 12789], [15330, 15333], [15330, 15333], [15474, 15477], [15474, 15477], [2068, 2071], [2068, 2071], [2516, 2519], [2516, 2519], [6761, 6764], [6761, 6764], [7133, 7136], [7133, 7136], [3743, 3745], "[fetchLevels]", [3890, 3931], "[searchText, difficultyFilter, tagFilter, fetchLevels]", [2845, 2848], [2845, 2848], [3737, 3746], "[fetchLevelDetail, levelId]", [3803, 3806], [3803, 3806], [5478, 5481], [5478, 5481], [558, 561], [558, 561], [729, 732], [729, 732], [2045, 2048], [2045, 2048], [2777, 2814], "[searchText, statusFilter, dateRange, fetchOrders]", [2866, 2868], "[fetchOrders]", [1130, 1133], [1130, 1133], [3138, 3140], "[fetchConfig]", [1797, 1806], "[fetchShareDetail, shareId]", [1341, 1350], [740, 743], [740, 743], [826, 829], [826, 829], [1285, 1288], [1285, 1288], [9010, 9013], [9010, 9013], [3531, 3533], "[fetchFavorites, searchParams]", [3686, 3701], "[handleFilter, selected<PERSON><PERSON><PERSON>]", [8399, 8402], [8399, 8402], [3494, 3496], "[fetchStars, searchParams]", [3649, 3664], [2214, 2217], [2214, 2217], [5196, 5198], "[fetchUsers]", [5265, 5268], [5265, 5268], [5915, 5918], [5915, 5918], [10673, 10676], [10673, 10676], [19223, 19226], [19223, 19226], [913, 916], [913, 916], [1235, 1238], [1235, 1238], [3987, 3990], [3987, 3990], [3969, 3972], [3969, 3972], [4042, 4045], [4042, 4045], [90, 93], [90, 93], [1180, 1183], [1180, 1183], [1410, 1413], [1410, 1413], [1600, 1603], [1600, 1603], [1803, 1806], [1803, 1806], [3438, 3441], [3438, 3441], [3611, 3614], [3611, 3614], [3647, 3650], [3647, 3650], [3806, 3809], [3806, 3809], [3842, 3845], [3842, 3845], [4004, 4007], [4004, 4007], [4040, 4043], [4040, 4043], [4206, 4209], [4206, 4209], [4420, 4423], [4420, 4423], [4547, 4550], [4547, 4550], [4572, 4575], [4572, 4575], [4692, 4695], [4692, 4695], [4717, 4720], [4717, 4720], [4838, 4841], [4838, 4841], [4874, 4877], [4874, 4877], [5002, 5005], [5002, 5005], [5183, 5186], [5183, 5186], [5219, 5222], [5219, 5222], [5448, 5451], [5448, 5451], [5484, 5487], [5484, 5487], [5714, 5717], [5714, 5717], [5750, 5753], [5750, 5753], [5983, 5986], [5983, 5986], [3160, 3163], [3160, 3163], [2473, 2476], [2473, 2476], [223, 226], [223, 226], [233, 236], [233, 236], [927, 930], [927, 930], [937, 940], [937, 940], [5771, 5774], [5771, 5774], [6500, 6503], [6500, 6503], [6518, 6521], [6518, 6521]]