(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7040],{39362:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>F});var s=l(95155),r=l(12115),i=l(97605),a=l(56020),n=l(20778),c=l(86615),d=l(19868),o=l(26922),h=l(37974),x=l(12320),A=l(77325),u=l(27212),m=l(6124),p=l(19361),y=l(74947),j=l(51087),g=l(46002),v=l(10642),f=l(13324),w=l(90765),C=l(63625),b=l(79659),k=l(56170),S=l(44318),z=l(46996),I=l(73884);let{Title:O,Text:L}=i.A,{TextArea:D}=a.A,{Option:E}=n.A;function F(){let[e,t]=(0,r.useState)([]),[l,i]=(0,r.useState)(!1),[F,R]=(0,r.useState)(!1),[U,_]=(0,r.useState)(null),[q]=c.A.useForm(),M=async()=>{i(!0);try{let e=await I.Dw.getAllShareConfigs();t(e)}catch(e){d.Ay.error("获取分享配置失败"),console.error("获取分享配置失败:",e)}finally{i(!1)}};(0,r.useEffect)(()=>{M()},[]);let W=e=>{_(e||null),R(!0),e?q.setFieldsValue({name:e.name,title:e.title,path:e.path,imageUrl:e.imageUrl,description:e.description,type:e.type,isActive:e.isActive,sortOrder:e.sortOrder}):(q.resetFields(),q.setFieldsValue({type:"custom",isActive:!0,sortOrder:1}))},B=()=>{R(!1),_(null),q.resetFields()},N=async()=>{try{let e=await q.validateFields();U?(await I.Dw.updateShareConfig(U.id,e),d.Ay.success("分享配置更新成功")):(await I.Dw.createShareConfig(e),d.Ay.success("分享配置创建成功")),B(),M()}catch(e){if(e&&"object"==typeof e&&"errorFields"in e)d.Ay.error("请检查表单输入");else{let t=e&&"object"==typeof e&&"message"in e?e.message:"操作失败";d.Ay.error(t)}}},T=async e=>{try{await I.Dw.toggleShareConfig(e.id),d.Ay.success("".concat(e.isActive?"禁用":"启用","成功")),M()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"操作失败";d.Ay.error(e)}},V=async e=>{try{await I.Dw.deleteShareConfig(e.id),d.Ay.success("删除成功"),M()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"删除失败";d.Ay.error(e)}},P=e=>({default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"})[e]||"gray",H=e=>{let t=I.WS.find(t=>t.value===e);return(null==t?void 0:t.label)||e},J=[{title:"配置名称",dataIndex:"name",key:"name",width:150,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:500},children:e}),(0,s.jsxs)(L,{type:"secondary",style:{fontSize:"12px"},children:["ID: ",t.id]})]})},{title:"分享标题",dataIndex:"title",key:"title",width:200,render:e=>(0,s.jsx)(o.A,{title:e,children:(0,s.jsx)("div",{style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})},{title:"分享路径",dataIndex:"path",key:"path",width:200,render:e=>(0,s.jsx)(o.A,{title:e,children:(0,s.jsx)(L,{code:!0,style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block"},children:e})})},{title:"类型",dataIndex:"type",key:"type",width:100,render:e=>(0,s.jsx)(h.A,{color:P(e),children:H(e)})},{title:"状态",dataIndex:"isActive",key:"isActive",width:80,render:e=>(0,s.jsx)(h.A,{color:e?"success":"default",icon:e?(0,s.jsx)(w.A,{}):(0,s.jsx)(C.A,{}),children:e?"启用":"禁用"})},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80,align:"center"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:150,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,render:(e,t)=>(0,s.jsxs)(x.A,{size:"small",children:[(0,s.jsx)(o.A,{title:"编辑",children:(0,s.jsx)(A.Ay,{type:"text",icon:(0,s.jsx)(b.A,{}),onClick:()=>W(t)})}),(0,s.jsx)(o.A,{title:t.isActive?"禁用":"启用",children:(0,s.jsx)(A.Ay,{type:"text",icon:t.isActive?(0,s.jsx)(C.A,{}):(0,s.jsx)(w.A,{}),onClick:()=>T(t)})}),"default"!==t.type&&(0,s.jsx)(o.A,{title:"删除",children:(0,s.jsx)(u.A,{title:"确定要删除这个分享配置吗？",onConfirm:()=>V(t),okText:"确定",cancelText:"取消",children:(0,s.jsx)(A.Ay,{type:"text",danger:!0,icon:(0,s.jsx)(k.A,{})})})})]})}];return(0,s.jsxs)("div",{style:{padding:"24px"},children:[(0,s.jsxs)(m.A,{children:[(0,s.jsx)("div",{style:{marginBottom:"24px"},children:(0,s.jsxs)(p.A,{justify:"space-between",align:"middle",children:[(0,s.jsxs)(y.A,{children:[(0,s.jsxs)(O,{level:3,style:{margin:0},children:[(0,s.jsx)(S.A,{style:{marginRight:"8px"}}),"分享管理"]}),(0,s.jsx)(L,{type:"secondary",children:"管理微信小程序的分享配置，包括分享标题、路径和图片等"})]}),(0,s.jsx)(y.A,{children:(0,s.jsx)(A.Ay,{type:"primary",icon:(0,s.jsx)(z.A,{}),onClick:()=>W(),children:"新建分享配置"})})]})}),(0,s.jsx)(j.A,{columns:J,dataSource:e,rowKey:"id",loading:l,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")},scroll:{x:1200}})]}),(0,s.jsx)(g.A,{title:U?"编辑分享配置":"新建分享配置",open:F,onOk:N,onCancel:B,width:600,destroyOnClose:!0,children:(0,s.jsxs)(c.A,{form:q,layout:"vertical",initialValues:{type:"custom",isActive:!0,sortOrder:1},children:[(0,s.jsx)(c.A.Item,{name:"name",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:(0,s.jsx)(a.A,{placeholder:"请输入配置名称"})}),(0,s.jsx)(c.A.Item,{name:"title",label:"分享标题",rules:[{required:!0,message:"请输入分享标题"}],children:(0,s.jsx)(a.A,{placeholder:"请输入分享标题"})}),(0,s.jsx)(c.A.Item,{name:"path",label:"分享路径",rules:[{required:!0,message:"请输入分享路径"}],children:(0,s.jsx)(n.A,{placeholder:"请选择或输入分享路径",mode:"tags",allowClear:!0,children:I.cm.map(e=>(0,s.jsx)(E,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)(L,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})}),(0,s.jsx)(c.A.Item,{name:"imageUrl",label:"分享图片URL",rules:[{type:"url",message:"请输入有效的URL"}],children:(0,s.jsx)(a.A,{placeholder:"请输入分享图片URL（可选）"})}),(0,s.jsx)(c.A.Item,{name:"description",label:"分享描述",children:(0,s.jsx)(D,{placeholder:"请输入分享描述（可选）",rows:3,maxLength:200,showCount:!0})}),(0,s.jsxs)(p.A,{gutter:16,children:[(0,s.jsx)(y.A,{span:12,children:(0,s.jsx)(c.A.Item,{name:"type",label:"分享类型",rules:[{required:!0,message:"请选择分享类型"}],children:(0,s.jsx)(n.A,{placeholder:"请选择分享类型",children:I.WS.map(e=>(0,s.jsx)(E,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)(L,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})})}),(0,s.jsx)(y.A,{span:12,children:(0,s.jsx)(c.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,s.jsx)(v.A,{min:1,max:999,placeholder:"排序权重",style:{width:"100%"}})})})]}),(0,s.jsx)(c.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,s.jsx)(f.A,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})})]})}},44318:(e,t,l)=>{"use strict";l.d(t,{A:()=>n});var s=l(79630),r=l(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M752 664c-28.5 0-54.8 10-75.4 26.7L469.4 540.8a160.68 160.68 0 000-57.6l207.2-149.9C697.2 350 723.5 360 752 360c66.2 0 120-53.8 120-120s-53.8-120-120-120-120 53.8-120 120c0 11.6 1.6 22.7 4.7 33.3L439.9 415.8C410.7 377.1 364.3 352 312 352c-88.4 0-160 71.6-160 160s71.6 160 160 160c52.3 0 98.7-25.1 127.9-63.8l196.8 142.5c-3.1 10.6-4.7 21.8-4.7 33.3 0 66.2 53.8 120 120 120s120-53.8 120-120-53.8-120-120-120zm0-476c28.7 0 52 23.3 52 52s-23.3 52-52 52-52-23.3-52-52 23.3-52 52-52zM312 600c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88zm440 236c-28.7 0-52-23.3-52-52s23.3-52 52-52 52 23.3 52 52-23.3 52-52 52z"}}]},name:"share-alt",theme:"outlined"};var a=l(62764);let n=r.forwardRef(function(e,t){return r.createElement(a.A,(0,s.A)({},e,{ref:t,icon:i}))})},63625:(e,t,l)=>{"use strict";l.d(t,{A:()=>n});var s=l(79630),r=l(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var a=l(62764);let n=r.forwardRef(function(e,t){return r.createElement(a.A,(0,s.A)({},e,{ref:t,icon:i}))})},81131:(e,t,l)=>{Promise.resolve().then(l.bind(l,39362))},90765:(e,t,l)=>{"use strict";l.d(t,{A:()=>n});var s=l(79630),r=l(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var a=l(62764);let n=r.forwardRef(function(e,t){return r.createElement(a.A,(0,s.A)({},e,{ref:t,icon:i}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,2343,1087,6615,7605,404,6002,642,7238,5634,3884,8441,1684,7358],()=>t(81131)),_N_E=e.O()}]);