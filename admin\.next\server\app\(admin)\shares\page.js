(()=>{var e={};e.id=7040,e.ids=[7040],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4691:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=n(7565).A},10814:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});let i=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15444:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,n)=>{Promise.resolve().then(n.bind(n,10814))},26323:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var i=n(80828),r=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var s=n(21898);let l=r.forwardRef(function(e,t){return r.createElement(s.A,(0,i.A)({},e,{ref:t,icon:a}))})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37912:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>E});var i=n(60687),r=n(43210),a=n(85814),s=n.n(a),l=n(16189),o=n(98836),d=n(99053),c=n(63736),h=n(56072),u=n(78620);n(15444);var p=n(60203),m=n(81945),g=n(53788),x=n(9242),y=n(3788),v=n(73237),A=n(47453),b=n(31189),f=n(62727),w=n(14723),j=n(72061),k=n(80461),$=n(71103);let{Header:S,Content:I,Sider:C,Footer:q}=o.A,z=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,i.jsx)(p.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,i.jsx)(m.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,i.jsx)(g.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,i.jsx)(x.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,i.jsx)(y.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,i.jsx)(v.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,i.jsx)(A.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,i.jsx)(b.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,i.jsx)(f.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,i.jsx)(w.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,i.jsx)(j.A,{})}];function E({children:e}){let t=(0,l.useRouter)(),n=(0,l.usePathname)(),[a,p]=(0,r.useState)(!1),m=[{key:"logout",icon:(0,i.jsx)(k.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],g=z.find(e=>n.startsWith(e.path))?.key||"dashboard";return(0,i.jsxs)(o.A,{style:{minHeight:"100vh"},children:[(0,i.jsxs)(C,{collapsible:!0,collapsed:a,onCollapse:e=>p(e),children:[(0,i.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,i.jsx)(d.A.Text,{style:{color:"white",fontSize:a?"10px":"16px",transition:"font-size 0.2s"},children:a?"后台":"游戏管理后台"})}),(0,i.jsx)(c.A,{theme:"dark",selectedKeys:[g],mode:"inline",items:z.map(e=>({key:e.key,icon:e.icon,label:(0,i.jsx)(s(),{href:e.path,children:e.label})}))})]}),(0,i.jsxs)(o.A,{children:[(0,i.jsxs)(S,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,i.jsx)(h.A,{menu:{items:m},placement:"bottomRight",children:(0,i.jsx)(u.A,{style:{cursor:"pointer"},icon:(0,i.jsx)($.A,{})})}),(0,i.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,i.jsx)(I,{style:{margin:"16px"},children:e}),(0,i.jsxs)(q,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},43910:(e,t,n)=>{"use strict";n.d(t,{Dw:()=>r,WS:()=>a,cm:()=>s});var i=n(49895);class r{static async getAllShareConfigs(){return(await i.Ay.get("/share")).data}static async getActiveShareConfigs(){return(await i.Ay.get("/share/active")).data}static async getDefaultShareConfig(){return(await i.Ay.get("/share/default")).data}static async getShareConfigByType(e){return(await i.Ay.get(`/share/type/${e}`)).data}static async getShareConfigById(e){return(await i.Ay.get(`/share/${e}`)).data}static async createShareConfig(e){return(await i.Ay.post("/share",e)).data}static async updateShareConfig(e,t){return(await i.Ay.patch(`/share/${e}`,t)).data}static async toggleShareConfig(e){return(await i.Ay.put(`/share/${e}/toggle`)).data}static async deleteShareConfig(e){return(await i.Ay.delete(`/share/${e}`)).data}}let a=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],s=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},50573:(e,t,n)=>{Promise.resolve().then(n.bind(n,74736))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56306:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var i=n(80828),r=n(43210);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var s=n(21898);let l=r.forwardRef(function(e,t){return r.createElement(s.A,(0,i.A)({},e,{ref:t,icon:a}))})},59448:(e,t,n)=>{Promise.resolve().then(n.bind(n,37912))},59823:(e,t,n)=>{"use strict";n.d(t,{A:()=>D});var i=n(43210),r=n(39759),a=n(69662),s=n.n(a),l=n(80828),o=n(95243),d=n(82853),c=n(78135),h=n(28344),u=n(2291),p=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],m=i.forwardRef(function(e,t){var n,r=e.prefixCls,a=void 0===r?"rc-switch":r,m=e.className,g=e.checked,x=e.defaultChecked,y=e.disabled,v=e.loadingIcon,A=e.checkedChildren,b=e.unCheckedChildren,f=e.onClick,w=e.onChange,j=e.onKeyDown,k=(0,c.A)(e,p),$=(0,h.A)(!1,{value:g,defaultValue:x}),S=(0,d.A)($,2),I=S[0],C=S[1];function q(e,t){var n=I;return y||(C(n=e),null==w||w(n,t)),n}var z=s()(a,m,(n={},(0,o.A)(n,"".concat(a,"-checked"),I),(0,o.A)(n,"".concat(a,"-disabled"),y),n));return i.createElement("button",(0,l.A)({},k,{type:"button",role:"switch","aria-checked":I,disabled:y,className:z,ref:t,onKeyDown:function(e){e.which===u.A.LEFT?q(!1,e):e.which===u.A.RIGHT&&q(!0,e),null==j||j(e)},onClick:function(e){var t=q(!I,e);null==f||f(t,e)}}),v,i.createElement("span",{className:"".concat(a,"-inner")},i.createElement("span",{className:"".concat(a,"-inner-checked")},A),i.createElement("span",{className:"".concat(a,"-inner-unchecked")},b)))});m.displayName="Switch";var g=n(17727),x=n(71802),y=n(57026),v=n(40908),A=n(42411),b=n(73117),f=n(32476),w=n(13581),j=n(60254);let k=e=>{let{componentCls:t,trackHeightSM:n,trackPadding:i,trackMinWidthSM:r,innerMinMarginSM:a,innerMaxMarginSM:s,handleSizeSM:l,calc:o}=e,d=`${t}-inner`,c=(0,A.zA)(o(l).add(o(i).mul(2)).equal()),h=(0,A.zA)(o(s).mul(2).equal());return{[t]:{[`&${t}-small`]:{minWidth:r,height:n,lineHeight:(0,A.zA)(n),[`${t}-inner`]:{paddingInlineStart:s,paddingInlineEnd:a,[`${d}-checked, ${d}-unchecked`]:{minHeight:n},[`${d}-checked`]:{marginInlineStart:`calc(-100% + ${c} - ${h})`,marginInlineEnd:`calc(100% - ${c} + ${h})`},[`${d}-unchecked`]:{marginTop:o(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${t}-handle`]:{width:l,height:l},[`${t}-loading-icon`]:{top:o(o(l).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${t}-checked`]:{[`${t}-inner`]:{paddingInlineStart:a,paddingInlineEnd:s,[`${d}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${d}-unchecked`]:{marginInlineStart:`calc(100% - ${c} + ${h})`,marginInlineEnd:`calc(-100% + ${c} - ${h})`}},[`${t}-handle`]:{insetInlineStart:`calc(100% - ${(0,A.zA)(o(l).add(i).equal())})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${d}`]:{[`${d}-unchecked`]:{marginInlineStart:o(e.marginXXS).div(2).equal(),marginInlineEnd:o(e.marginXXS).mul(-1).div(2).equal()}},[`&${t}-checked ${d}`]:{[`${d}-checked`]:{marginInlineStart:o(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:o(e.marginXXS).div(2).equal()}}}}}}},$=e=>{let{componentCls:t,handleSize:n,calc:i}=e;return{[t]:{[`${t}-loading-icon${e.iconCls}`]:{position:"relative",top:i(i(n).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${t}-checked ${t}-loading-icon`]:{color:e.switchColor}}}},S=e=>{let{componentCls:t,trackPadding:n,handleBg:i,handleShadow:r,handleSize:a,calc:s}=e,l=`${t}-handle`;return{[t]:{[l]:{position:"absolute",top:n,insetInlineStart:n,width:a,height:a,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:i,borderRadius:s(a).div(2).equal(),boxShadow:r,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${t}-checked ${l}`]:{insetInlineStart:`calc(100% - ${(0,A.zA)(s(a).add(n).equal())})`},[`&:not(${t}-disabled):active`]:{[`${l}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${t}-checked ${l}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},I=e=>{let{componentCls:t,trackHeight:n,trackPadding:i,innerMinMargin:r,innerMaxMargin:a,handleSize:s,calc:l}=e,o=`${t}-inner`,d=(0,A.zA)(l(s).add(l(i).mul(2)).equal()),c=(0,A.zA)(l(a).mul(2).equal());return{[t]:{[o]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:a,paddingInlineEnd:r,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${o}-checked, ${o}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:n},[`${o}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${c})`,marginInlineEnd:`calc(100% - ${d} + ${c})`},[`${o}-unchecked`]:{marginTop:l(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${t}-checked ${o}`]:{paddingInlineStart:r,paddingInlineEnd:a,[`${o}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${o}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${c})`,marginInlineEnd:`calc(-100% + ${d} - ${c})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${o}`]:{[`${o}-unchecked`]:{marginInlineStart:l(i).mul(2).equal(),marginInlineEnd:l(i).mul(-1).mul(2).equal()}},[`&${t}-checked ${o}`]:{[`${o}-checked`]:{marginInlineStart:l(i).mul(-1).mul(2).equal(),marginInlineEnd:l(i).mul(2).equal()}}}}}},C=e=>{let{componentCls:t,trackHeight:n,trackMinWidth:i}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,f.dF)(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:i,height:n,lineHeight:(0,A.zA)(n),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${t}-disabled)`]:{background:e.colorTextTertiary}}),(0,f.K8)(e)),{[`&${t}-checked`]:{background:e.switchColor,[`&:hover:not(${t}-disabled)`]:{background:e.colorPrimaryHover}},[`&${t}-loading, &${t}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${t}-rtl`]:{direction:"rtl"}})}},q=(0,w.OF)("Switch",e=>{let t=(0,j.oX)(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[C(t),I(t),S(t),$(t),k(t)]},e=>{let{fontSize:t,lineHeight:n,controlHeight:i,colorWhite:r}=e,a=t*n,s=i/2,l=a-4,o=s-4;return{trackHeight:a,trackHeightSM:s,trackMinWidth:2*l+8,trackMinWidthSM:2*o+4,trackPadding:2,handleBg:r,handleSize:l,handleSizeSM:o,handleShadow:`0 2px 4px 0 ${new b.Y("#00230b").setA(.2).toRgbString()}`,innerMinMargin:l/2,innerMaxMargin:l+2+4,innerMinMarginSM:o/2,innerMaxMarginSM:o+2+4}});var z=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)0>t.indexOf(i[r])&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n};let E=i.forwardRef((e,t)=>{let{prefixCls:n,size:a,disabled:l,loading:o,className:d,rootClassName:c,style:u,checked:p,value:A,defaultChecked:b,defaultValue:f,onChange:w}=e,j=z(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[k,$]=(0,h.A)(!1,{value:null!=p?p:A,defaultValue:null!=b?b:f}),{getPrefixCls:S,direction:I,switch:C}=i.useContext(x.QO),E=i.useContext(y.A),D=(null!=l?l:E)||o,O=S("switch",n),P=i.createElement("div",{className:`${O}-handle`},o&&i.createElement(r.A,{className:`${O}-loading-icon`})),[M,R,_]=q(O),L=(0,v.A)(a),T=s()(null==C?void 0:C.className,{[`${O}-small`]:"small"===L,[`${O}-loading`]:o,[`${O}-rtl`]:"rtl"===I},d,c,R,_),N=Object.assign(Object.assign({},null==C?void 0:C.style),u);return M(i.createElement(g.A,{component:"Switch"},i.createElement(m,Object.assign({},j,{checked:k,onChange:(...e)=>{$(e[0]),null==w||w.apply(void 0,e)},prefixCls:O,className:T,style:N,disabled:D,ref:t,loadingIcon:P}))))});E.__ANT_SWITCH=!0;let D=E},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74541:(e,t,n)=>{Promise.resolve().then(n.bind(n,93486))},74736:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});let i=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\shares\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91387:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>d});var i=n(65239),r=n(48088),a=n(88170),s=n.n(a),l=n(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);n.d(t,o);let d={children:["",{children:["(admin)",{children:["shares",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,74736)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx"],h={require:n,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(admin)/shares/page",pathname:"/shares",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93486:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>O});var i=n(60687),r=n(43210),a=n(99053),s=n(94733),l=n(70084),o=n(10411),d=n(35899),c=n(33519),h=n(52378),u=n(48111),p=n(77833),m=n(94132),g=n(42585),x=n(96625),y=n(4691),v=n(27783),A=n(10678),b=n(2535),f=n(59823),w=n(56306),j=n(26323),k=n(34308),$=n(23575),S=n(3788),I=n(53082),C=n(43910);let{Title:q,Text:z}=a.A,{TextArea:E}=s.A,{Option:D}=l.A;function O(){let[e,t]=(0,r.useState)([]),[n,a]=(0,r.useState)(!1),[O,P]=(0,r.useState)(!1),[M,R]=(0,r.useState)(null),[_]=o.A.useForm(),L=async()=>{a(!0);try{let e=await C.Dw.getAllShareConfigs();t(e)}catch(e){d.Ay.error("获取分享配置失败"),console.error("获取分享配置失败:",e)}finally{a(!1)}},T=e=>{R(e||null),P(!0),e?_.setFieldsValue({name:e.name,title:e.title,path:e.path,imageUrl:e.imageUrl,description:e.description,type:e.type,isActive:e.isActive,sortOrder:e.sortOrder}):(_.resetFields(),_.setFieldsValue({type:"custom",isActive:!0,sortOrder:1}))},N=()=>{P(!1),R(null),_.resetFields()},H=async()=>{try{let e=await _.validateFields();M?(await C.Dw.updateShareConfig(M.id,e),d.Ay.success("分享配置更新成功")):(await C.Dw.createShareConfig(e),d.Ay.success("分享配置创建成功")),N(),L()}catch(e){if(e&&"object"==typeof e&&"errorFields"in e)d.Ay.error("请检查表单输入");else{let t=e&&"object"==typeof e&&"message"in e?e.message:"操作失败";d.Ay.error(t)}}},W=async e=>{try{await C.Dw.toggleShareConfig(e.id),d.Ay.success(`${e.isActive?"禁用":"启用"}成功`),L()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"操作失败";d.Ay.error(e)}},F=async e=>{try{await C.Dw.deleteShareConfig(e.id),d.Ay.success("删除成功"),L()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"删除失败";d.Ay.error(e)}},X=e=>({default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"})[e]||"gray",G=e=>{let t=C.WS.find(t=>t.value===e);return t?.label||e},K=[{title:"配置名称",dataIndex:"name",key:"name",width:150,render:(e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{style:{fontWeight:500},children:e}),(0,i.jsxs)(z,{type:"secondary",style:{fontSize:"12px"},children:["ID: ",t.id]})]})},{title:"分享标题",dataIndex:"title",key:"title",width:200,render:e=>(0,i.jsx)(c.A,{title:e,children:(0,i.jsx)("div",{style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})},{title:"分享路径",dataIndex:"path",key:"path",width:200,render:e=>(0,i.jsx)(c.A,{title:e,children:(0,i.jsx)(z,{code:!0,style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block"},children:e})})},{title:"类型",dataIndex:"type",key:"type",width:100,render:e=>(0,i.jsx)(h.A,{color:X(e),children:G(e)})},{title:"状态",dataIndex:"isActive",key:"isActive",width:80,render:e=>(0,i.jsx)(h.A,{color:e?"success":"default",icon:e?(0,i.jsx)(w.A,{}):(0,i.jsx)(j.A,{}),children:e?"启用":"禁用"})},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80,align:"center"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:150,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,render:(e,t)=>(0,i.jsxs)(u.A,{size:"small",children:[(0,i.jsx)(c.A,{title:"编辑",children:(0,i.jsx)(p.Ay,{type:"text",icon:(0,i.jsx)(k.A,{}),onClick:()=>T(t)})}),(0,i.jsx)(c.A,{title:t.isActive?"禁用":"启用",children:(0,i.jsx)(p.Ay,{type:"text",icon:t.isActive?(0,i.jsx)(j.A,{}):(0,i.jsx)(w.A,{}),onClick:()=>W(t)})}),"default"!==t.type&&(0,i.jsx)(c.A,{title:"删除",children:(0,i.jsx)(m.A,{title:"确定要删除这个分享配置吗？",onConfirm:()=>F(t),okText:"确定",cancelText:"取消",children:(0,i.jsx)(p.Ay,{type:"text",danger:!0,icon:(0,i.jsx)($.A,{})})})})]})}];return(0,i.jsxs)("div",{style:{padding:"24px"},children:[(0,i.jsxs)(g.A,{children:[(0,i.jsx)("div",{style:{marginBottom:"24px"},children:(0,i.jsxs)(x.A,{justify:"space-between",align:"middle",children:[(0,i.jsxs)(y.A,{children:[(0,i.jsxs)(q,{level:3,style:{margin:0},children:[(0,i.jsx)(S.A,{style:{marginRight:"8px"}}),"分享管理"]}),(0,i.jsx)(z,{type:"secondary",children:"管理微信小程序的分享配置，包括分享标题、路径和图片等"})]}),(0,i.jsx)(y.A,{children:(0,i.jsx)(p.Ay,{type:"primary",icon:(0,i.jsx)(I.A,{}),onClick:()=>T(),children:"新建分享配置"})})]})}),(0,i.jsx)(v.A,{columns:K,dataSource:e,rowKey:"id",loading:n,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`},scroll:{x:1200}})]}),(0,i.jsx)(A.A,{title:M?"编辑分享配置":"新建分享配置",open:O,onOk:H,onCancel:N,width:600,destroyOnClose:!0,children:(0,i.jsxs)(o.A,{form:_,layout:"vertical",initialValues:{type:"custom",isActive:!0,sortOrder:1},children:[(0,i.jsx)(o.A.Item,{name:"name",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:(0,i.jsx)(s.A,{placeholder:"请输入配置名称"})}),(0,i.jsx)(o.A.Item,{name:"title",label:"分享标题",rules:[{required:!0,message:"请输入分享标题"}],children:(0,i.jsx)(s.A,{placeholder:"请输入分享标题"})}),(0,i.jsx)(o.A.Item,{name:"path",label:"分享路径",rules:[{required:!0,message:"请输入分享路径"}],children:(0,i.jsx)(l.A,{placeholder:"请选择或输入分享路径",mode:"tags",allowClear:!0,children:C.cm.map(e=>(0,i.jsx)(D,{value:e.value,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:e.label}),(0,i.jsx)(z,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})}),(0,i.jsx)(o.A.Item,{name:"imageUrl",label:"分享图片URL",rules:[{type:"url",message:"请输入有效的URL"}],children:(0,i.jsx)(s.A,{placeholder:"请输入分享图片URL（可选）"})}),(0,i.jsx)(o.A.Item,{name:"description",label:"分享描述",children:(0,i.jsx)(E,{placeholder:"请输入分享描述（可选）",rows:3,maxLength:200,showCount:!0})}),(0,i.jsxs)(x.A,{gutter:16,children:[(0,i.jsx)(y.A,{span:12,children:(0,i.jsx)(o.A.Item,{name:"type",label:"分享类型",rules:[{required:!0,message:"请选择分享类型"}],children:(0,i.jsx)(l.A,{placeholder:"请选择分享类型",children:C.WS.map(e=>(0,i.jsx)(D,{value:e.value,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:e.label}),(0,i.jsx)(z,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})})}),(0,i.jsx)(y.A,{span:12,children:(0,i.jsx)(o.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,i.jsx)(b.A,{min:1,max:999,placeholder:"排序权重",style:{width:"100%"}})})})]}),(0,i.jsx)(o.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,i.jsx)(f.A,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})})]})}},94735:e=>{"use strict";e.exports=require("events")},96625:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=n(20775).A}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),i=t.X(0,[4447,6267,1658,8161,675,5336,9196,5899,8331,553,84,7783,411,7503,678,2535,3145,976],()=>n(91387));module.exports=i})();