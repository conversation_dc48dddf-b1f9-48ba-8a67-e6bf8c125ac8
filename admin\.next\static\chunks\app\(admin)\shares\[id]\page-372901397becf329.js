(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9626],{37048:(e,s,i)=>{Promise.resolve().then(i.bind(i,59492))},59492:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>S});var l=i(95155),A=i(12115),c=i(35695),n=i(97605),r=i(19868),t=i(16467),d=i(95108),a=i(77325),x=i(6124),o=i(19361),h=i(74947),j=i(12320),g=i(89631),p=i(37974),y=i(94600),m=i(98617),u=i(81730),v=i(44318),b=i(63625),I=i(90765),w=i(79659),f=i(70129),k=i(73884);let{Title:D,Text:C,Paragraph:Q}=n.A;function S(){let e=(0,c.useParams)(),s=(0,c.useRouter)(),[i,n]=(0,A.useState)(null),[S,B]=(0,A.useState)(!0),E=e.id,U=async()=>{if(E){B(!0);try{let e=await k.Dw.getShareConfigById(E);n(e)}catch(e){r.Ay.error("获取分享配置详情失败"),console.error("获取分享配置详情失败:",e)}finally{B(!1)}}};(0,A.useEffect)(()=>{U()},[E]);let G=()=>{s.push("/shares")},M=async()=>{if(i)try{await k.Dw.toggleShareConfig(i.id),r.Ay.success("".concat(i.isActive?"禁用":"启用","成功")),U()}catch(s){let e=s&&"object"==typeof s&&"message"in s?s.message:"操作失败";r.Ay.error(e)}},L=e=>{navigator.clipboard.writeText(e).then(()=>{r.Ay.success("已复制到剪贴板")}).catch(()=>{r.Ay.error("复制失败")})};return S?(0,l.jsx)("div",{style:{padding:"24px",textAlign:"center"},children:(0,l.jsx)(t.A,{size:"large"})}):i?(0,l.jsx)("div",{style:{padding:"24px"},children:(0,l.jsxs)(x.A,{children:[(0,l.jsx)("div",{style:{marginBottom:"24px"},children:(0,l.jsxs)(o.A,{justify:"space-between",align:"middle",children:[(0,l.jsx)(h.A,{children:(0,l.jsxs)(j.A,{children:[(0,l.jsx)(a.Ay,{icon:(0,l.jsx)(u.A,{}),onClick:G,children:"返回"}),(0,l.jsxs)(D,{level:3,style:{margin:0},children:[(0,l.jsx)(v.A,{style:{marginRight:"8px"}}),"分享配置详情"]})]})}),(0,l.jsx)(h.A,{children:(0,l.jsxs)(j.A,{children:[(0,l.jsx)(a.Ay,{type:i.isActive?"default":"primary",icon:i.isActive?(0,l.jsx)(b.A,{}):(0,l.jsx)(I.A,{}),onClick:M,children:i.isActive?"禁用":"启用"}),(0,l.jsx)(a.Ay,{type:"primary",icon:(0,l.jsx)(w.A,{}),onClick:()=>{s.push("/shares/".concat(E,"/edit"))},children:"编辑"})]})})]})}),(0,l.jsxs)(g.A,{title:"基本信息",bordered:!0,column:2,size:"middle",children:[(0,l.jsx)(g.A.Item,{label:"配置ID",span:2,children:(0,l.jsxs)(j.A,{children:[(0,l.jsx)(C,{code:!0,children:i.id}),(0,l.jsx)(a.Ay,{type:"text",size:"small",icon:(0,l.jsx)(f.A,{}),onClick:()=>L(i.id)})]})}),(0,l.jsx)(g.A.Item,{label:"配置名称",children:i.name}),(0,l.jsx)(g.A.Item,{label:"分享类型",children:(0,l.jsx)(p.A,{color:{default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"}[i.type]||"gray",children:(e=>{let s=k.WS.find(s=>s.value===e);return(null==s?void 0:s.label)||e})(i.type)})}),(0,l.jsx)(g.A.Item,{label:"启用状态",children:(0,l.jsx)(p.A,{color:i.isActive?"success":"default",icon:i.isActive?(0,l.jsx)(I.A,{}):(0,l.jsx)(b.A,{}),children:i.isActive?"启用":"禁用"})}),(0,l.jsx)(g.A.Item,{label:"排序权重",children:i.sortOrder}),(0,l.jsx)(g.A.Item,{label:"创建时间",children:new Date(i.createdAt).toLocaleString()}),(0,l.jsx)(g.A.Item,{label:"更新时间",children:new Date(i.updatedAt).toLocaleString()})]}),(0,l.jsx)(y.A,{}),(0,l.jsx)(D,{level:4,children:"分享内容"}),(0,l.jsxs)(g.A,{bordered:!0,column:1,size:"middle",children:[(0,l.jsx)(g.A.Item,{label:"分享标题",children:(0,l.jsxs)(j.A,{children:[(0,l.jsx)(C,{strong:!0,children:i.title}),(0,l.jsx)(a.Ay,{type:"text",size:"small",icon:(0,l.jsx)(f.A,{}),onClick:()=>L(i.title)})]})}),(0,l.jsx)(g.A.Item,{label:"分享路径",children:(0,l.jsxs)(j.A,{children:[(0,l.jsx)(C,{code:!0,children:i.path}),(0,l.jsx)(a.Ay,{type:"text",size:"small",icon:(0,l.jsx)(f.A,{}),onClick:()=>L(i.path)})]})}),i.description&&(0,l.jsx)(g.A.Item,{label:"分享描述",children:(0,l.jsx)(Q,{children:i.description})}),i.imageUrl&&(0,l.jsx)(g.A.Item,{label:"分享图片",children:(0,l.jsxs)(j.A,{direction:"vertical",children:[(0,l.jsxs)(j.A,{children:[(0,l.jsx)(C,{code:!0,children:i.imageUrl}),(0,l.jsx)(a.Ay,{type:"text",size:"small",icon:(0,l.jsx)(f.A,{}),onClick:()=>L(i.imageUrl)})]}),(0,l.jsx)(m.A,{src:i.imageUrl,alt:"分享图片",style:{maxWidth:"300px",maxHeight:"200px"},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"})]})})]}),(0,l.jsx)(y.A,{}),(0,l.jsx)(D,{level:4,children:"使用说明"}),(0,l.jsx)(d.A,{message:"微信小程序分享配置",description:(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{children:"该配置可用于微信小程序的分享功能，小程序端可通过以下API获取："}),(0,l.jsxs)("ul",{children:[(0,l.jsxs)("li",{children:[(0,l.jsx)(C,{code:!0,children:"GET /api/v1/weixin/share-config"})," - 获取所有分享配置"]}),(0,l.jsxs)("li",{children:[(0,l.jsxs)(C,{code:!0,children:["GET /api/v1/weixin/share-config/",i.type]})," - 获取指定类型的分享配置"]})]}),(0,l.jsxs)("p",{children:["在小程序中使用时，可以在页面的 ",(0,l.jsx)(C,{code:!0,children:"onShareAppMessage"})," 方法中返回这些配置。"]})]}),type:"info",showIcon:!0})]})}):(0,l.jsx)("div",{style:{padding:"24px"},children:(0,l.jsx)(d.A,{message:"分享配置不存在",description:"请检查URL是否正确，或者该配置已被删除。",type:"error",showIcon:!0,action:(0,l.jsx)(a.Ay,{size:"small",onClick:G,children:"返回列表"})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[1291,5573,4492,5669,3464,9868,6312,7605,404,9631,7578,4784,3884,8441,1684,7358],()=>s(37048)),_N_E=e.O()}]);