(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5496],{12259:(e,t,s)=>{Promise.resolve().then(s.bind(s,72621))},21073:(e,t,s)=>{"use strict";s.d(t,{Au:()=>o,Io:()=>r,LQ:()=>n,zN:()=>c});var a=s(83899);let o={getAll:async()=>(await a.Ay.get("/thesauruses")).data,getById:async e=>(await a.Ay.get("/thesauruses/".concat(e))).data,create:async e=>(await a.Ay.post("/thesauruses",e)).data,update:async(e,t)=>(await a.Ay.patch("/thesauruses/".concat(e),t)).data,delete:async e=>{await a.Ay.delete("/thesauruses/".concat(e))},addPhrase:async(e,t)=>(await a.Ay.post("/thesauruses/".concat(e,"/phrases"),t)).data,removePhrase:async(e,t)=>(await a.Ay.delete("/thesauruses/".concat(e,"/phrases/").concat(t))).data},n=o.create;o.update,o.delete;let r=o.delete;o.getById,o.getAll;let c=o.getAll},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}}),s.o(a,"useServerInsertedHTML")&&s.d(t,{useServerInsertedHTML:function(){return a.useServerInsertedHTML}})},36449:()=>{},41008:(e,t,s)=>{"use strict";s.d(t,{i:()=>a});let a={BASE_URL:"http://localhost:18891",TIMEOUT:1e4,API_PREFIX:"/api/v1/admin",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}}},72621:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(95155),o=s(12115),n=s(19868),r=s(46002),c=s(12320),i=s(77325),l=s(6124),d=s(97605),u=s(51087),m=s(79630);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var y=s(62764),p=o.forwardRef(function(e,t){return o.createElement(y.A,(0,m.A)({},e,{ref:t,icon:h}))}),g=s(46996),v=s(35695),f=s(21073);let w=()=>{let[e,t]=(0,o.useState)([]),[s,m]=(0,o.useState)(!1),h=(0,v.useRouter)(),y=(0,o.useCallback)(async()=>{m(!0);try{let e=await (0,f.zN)();t(e)}catch(e){console.error("获取词库列表失败:",e),n.Ay.error(e.message||"获取词库列表失败！")}finally{m(!1)}},[]);(0,o.useEffect)(()=>{y()},[y]);let w=(0,o.useCallback)(async e=>{try{await (0,f.Io)(e),n.Ay.success("词库删除成功！"),y()}catch(t){console.error("删除词库 ".concat(e," 失败:"),t),n.Ay.error(t.message||"词库删除失败！")}},[y]),k=(0,o.useCallback)(e=>{r.A.confirm({title:'确定要删除词库 "'.concat(e.name,'" 吗?'),icon:(0,a.jsx)(p,{}),content:"此操作不可撤销。如果有关联的关卡，请谨慎操作。",okText:"删除",okType:"danger",cancelText:"取消",onOk(){w(e.id)}})},[w]),x=[{title:"名称",dataIndex:"name",key:"name"},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"词组数",dataIndex:"phraseIds",key:"phraseCount",render:e=>(null==e?void 0:e.length)||0},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleString()},{title:"更新时间",dataIndex:"updatedAt",key:"updatedAt",render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",render:(e,t)=>(0,a.jsxs)(c.A,{size:"middle",children:[(0,a.jsx)(i.Ay,{size:"small",onClick:()=>{n.Ay.info("编辑词库 ".concat(t.id," (功能待实现)"))},children:"编辑"}),(0,a.jsx)(i.Ay,{size:"small",danger:!0,onClick:()=>k(t),children:"删除"})]})}];return(0,a.jsxs)(l.A,{children:[(0,a.jsx)(d.A.Title,{level:2,children:"词库管理"}),(0,a.jsx)(i.Ay,{type:"primary",icon:(0,a.jsx)(g.A,{}),onClick:()=>h.push("/thesauruses/create"),style:{marginBottom:16},children:"创建词库"}),(0,a.jsx)(u.A,{columns:x,dataSource:e,rowKey:"id",loading:s})]})}},73629:()=>{},83899:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,FH:()=>c,KY:()=>i});var a=s(23464),o=s(90285),n=s(41008);let r=a.A.create({baseURL:n.i.FULL_BASE_URL,timeout:n.i.TIMEOUT,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{var t;let s=localStorage.getItem("admin_token");return s&&(e.headers.Authorization="Bearer ".concat(s)),console.log("\uD83D\uDE80 发送请求:",{method:null==(t=e.method)?void 0:t.toUpperCase(),url:e.url,baseURL:e.baseURL,fullURL:"".concat(e.baseURL).concat(e.url),data:e.data,headers:e.headers}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),r.interceptors.response.use(e=>{var t;let s=e.config;return console.log("✅ 请求成功:",{method:null==(t=s.method)?void 0:t.toUpperCase(),url:s.url,status:e.status,statusText:e.statusText,data:e.data}),s.showSuccess&&s.successMessage&&o.i.success(s.successMessage),e},e=>{var t,s,a,n,r,c,i,l,d;console.error("❌ 请求失败:",{method:null==(s=e.config)||null==(t=s.method)?void 0:t.toUpperCase(),url:null==(a=e.config)?void 0:a.url,baseURL:null==(n=e.config)?void 0:n.baseURL,fullURL:"".concat(null==(r=e.config)?void 0:r.baseURL).concat(null==(c=e.config)?void 0:c.url),status:null==(i=e.response)?void 0:i.status,statusText:null==(l=e.response)?void 0:l.statusText,data:null==(d=e.response)?void 0:d.data,message:e.message});let u=e.config;if((null==u?void 0:u.showError)===!1)return Promise.reject(e);if(e.response){let{status:t,data:s}=e.response,a="";switch(t){case 401:a="登录已过期，请重新登录",localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a=(null==s?void 0:s.message)||"没有权限访问该资源";break;case 404:a=(null==s?void 0:s.message)||"请求的资源不存在";break;case 422:a=(null==s?void 0:s.message)||"请求参数验证失败";break;case 500:a=(null==s?void 0:s.message)||"服务器内部错误";break;default:a=(null==s?void 0:s.message)||"请求失败 (".concat(t,")")}o.i.error(a)}else e.request?o.i.error("网络连接失败，请检查网络"):o.i.error("请求配置错误");return Promise.reject(e)});let c={get:(e,t)=>r.get(e,t),post:(e,t,s)=>r.post(e,t,s),put:(e,t,s)=>r.put(e,t,s),patch:(e,t,s)=>r.patch(e,t,s),delete:(e,t)=>r.delete(e,t)},i={post:(e,t,s,a)=>c.post(e,t,{...a,showSuccess:!0,successMessage:s||"操作成功"}),put:(e,t,s,a)=>c.put(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),patch:(e,t,s,a)=>c.patch(e,t,{...a,showSuccess:!0,successMessage:s||"更新成功"}),delete:(e,t,s)=>c.delete(e,{...s,showSuccess:!0,successMessage:t||"删除成功"})},l=c},90285:(e,t,s)=>{"use strict";s.d(t,{a:()=>i,i:()=>m});var a=s(95155),o=s(12115),n=s(12669);s(36449);let r=e=>{let{title:t,content:s,children:n,visible:r=!1,width:c=520,centered:i=!1,closable:l=!0,maskClosable:d=!0,footer:u,okText:m="确定",cancelText:h="取消",okType:y="primary",confirmLoading:p=!1,onOk:g,onCancel:v,afterClose:f,className:w="",style:k={}}=e,[x,b]=(0,o.useState)(r),[A,T]=(0,o.useState)(!1);(0,o.useEffect)(()=>{r?(b(!0),T(!0),document.body.style.overflow="hidden"):(T(!1),setTimeout(()=>{b(!1),document.body.style.overflow="",null==f||f()},300))},[r,f]);let C=async()=>{if(g)try{await g()}catch(e){console.error("Modal onOk error:",e)}},j=()=>{null==v||v()};return x?(0,a.jsx)("div",{className:"custom-modal-mask ".concat(A?"custom-modal-mask-show":"custom-modal-mask-hide"),onClick:e=>{e.target===e.currentTarget&&d&&(null==v||v())},children:(0,a.jsx)("div",{className:"custom-modal-wrap ".concat(i?"custom-modal-centered":""),children:(0,a.jsxs)("div",{className:"custom-modal ".concat(w," ").concat(A?"custom-modal-show":"custom-modal-hide"),style:{width:c,...k},children:[(t||l)&&(0,a.jsxs)("div",{className:"custom-modal-header",children:[t&&(0,a.jsx)("div",{className:"custom-modal-title",children:t}),l&&(0,a.jsx)("button",{className:"custom-modal-close",onClick:j,"aria-label":"Close",children:"\xd7"})]}),(0,a.jsx)("div",{className:"custom-modal-body",children:s||n}),null===u?null:u||(0,a.jsxs)("div",{className:"custom-modal-footer",children:[h&&(0,a.jsx)("button",{className:"custom-modal-btn custom-modal-btn-default",onClick:j,children:h}),(0,a.jsxs)("button",{className:"custom-modal-btn custom-modal-btn-".concat(y),onClick:C,disabled:p,children:[p&&(0,a.jsx)("span",{className:"custom-modal-loading",children:"⟳"}),m]})]})]})})}):null};class c{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-modal-container",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}confirm(e){return new Promise((t,s)=>{let o=!1,n=async()=>{if(!o)try{e.onOk&&await e.onOk(),o=!0,this.destroy(),t()}catch(e){s(e)}};this.getContainer(),this.root.render((0,a.jsx)(r,{visible:!0,title:e.title,content:e.content,okText:e.okText,cancelText:e.cancelText,okType:e.okType,width:e.width,centered:e.centered,maskClosable:e.maskClosable,onOk:n,onCancel:()=>{var t;o||(o=!0,null==(t=e.onCancel)||t.call(e),this.destroy(),s(Error("User cancelled")))},afterClose:()=>this.destroy()}))})}destroy(){this.container&&document.body.contains(this.container)&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.container=null,this.root=null}}r.confirm=e=>new c().confirm({...e,okType:e.okType||"primary"}),r.info=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),r.success=e=>new c().confirm({...e,okType:"primary",cancelText:void 0}),r.error=e=>new c().confirm({...e,okType:"danger",cancelText:void 0}),r.warning=e=>new c().confirm({...e,okType:"primary",cancelText:void 0});let i=r;s(73629);let l=e=>{let{messages:t}=e;return(0,a.jsx)("div",{className:"custom-message-container",children:t.map(e=>(0,a.jsxs)("div",{className:"custom-message custom-message-".concat(e.type," ").concat(e.visible?"custom-message-show":"custom-message-hide"),children:[(0,a.jsxs)("div",{className:"custom-message-icon",children:["success"===e.type&&"✓","error"===e.type&&"✕","warning"===e.type&&"⚠","info"===e.type&&"ℹ"]}),(0,a.jsx)("span",{className:"custom-message-content",children:e.content})]},e.id))})};class d{getContainer(){return this.container||(this.container=document.createElement("div"),this.container.className="custom-message-wrapper",document.body.appendChild(this.container),this.root=(0,n.createRoot)(this.container)),this.container}render(){this.root&&this.root.render((0,a.jsx)(l,{messages:this.messages}))}generateId(){return"message_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}show(e){var t;let s=e.key||this.generateId(),a=null!=(t=e.duration)?t:3e3;e.key&&(this.messages=this.messages.filter(t=>t.id!==e.key));let o={...e,id:s,visible:!0};return this.messages.push(o),this.getContainer(),this.render(),a>0&&setTimeout(()=>{this.hide(s)},a),s}hide(e){let t=this.messages.findIndex(t=>t.id===e);t>-1&&(this.messages[t].visible=!1,this.render(),setTimeout(()=>{this.messages=this.messages.filter(t=>t.id!==e),this.render(),0===this.messages.length&&this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)},300))}destroy(){this.messages=[],this.container&&(document.body.removeChild(this.container),this.container=null,this.root=null)}constructor(){this.messages=[],this.container=null,this.root=null}}let u=new d,m={success:(e,t)=>u.show({content:e,type:"success",duration:t}),error:(e,t)=>u.show({content:e,type:"error",duration:t}),warning:(e,t)=>u.show({content:e,type:"warning",duration:t}),info:(e,t)=>u.show({content:e,type:"info",duration:t}),destroy:()=>u.destroy()}}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,6312,778,2343,1087,7605,404,6002,8441,1684,7358],()=>t(12259)),_N_E=e.O()}]);