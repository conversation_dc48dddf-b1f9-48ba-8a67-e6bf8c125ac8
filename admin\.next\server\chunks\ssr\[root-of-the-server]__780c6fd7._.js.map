{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/Modal/index.tsx"], "sourcesContent": ["import React, { useState, useEffect, ReactNode } from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './modal.css';\n\nexport interface ModalProps {\n  title?: ReactNode;\n  content?: ReactNode;\n  children?: ReactNode;\n  visible?: boolean;\n  width?: number | string;\n  centered?: boolean;\n  closable?: boolean;\n  maskClosable?: boolean;\n  footer?: ReactNode;\n  okText?: string;\n  cancelText?: string;\n  okType?: 'primary' | 'danger';\n  confirmLoading?: boolean;\n  onOk?: () => void | Promise<void>;\n  onCancel?: () => void;\n  afterClose?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n// Modal 组件\nconst Modal: React.FC<ModalProps> = ({\n  title,\n  content,\n  children,\n  visible = false,\n  width = 520,\n  centered = false,\n  closable = true,\n  maskClosable = true,\n  footer,\n  okText = '确定',\n  cancelText = '取消',\n  okType = 'primary',\n  confirmLoading = false,\n  onOk,\n  onCancel,\n  afterClose,\n  className = '',\n  style = {},\n}) => {\n  const [isVisible, setIsVisible] = useState(visible);\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      setIsVisible(true);\n      setIsAnimating(true);\n      document.body.style.overflow = 'hidden';\n    } else {\n      setIsAnimating(false);\n      setTimeout(() => {\n        setIsVisible(false);\n        document.body.style.overflow = '';\n        afterClose?.();\n      }, 300);\n    }\n  }, [visible, afterClose]);\n\n  const handleMaskClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget && maskClosable) {\n      onCancel?.();\n    }\n  };\n\n  const handleOk = async () => {\n    if (onOk) {\n      try {\n        await onOk();\n      } catch (error) {\n        console.error('Modal onOk error:', error);\n      }\n    }\n  };\n\n  const handleCancel = () => {\n    onCancel?.();\n  };\n\n  const renderFooter = () => {\n    if (footer === null) return null;\n\n    if (footer) return footer;\n\n    return (\n      <div className=\"custom-modal-footer\">\n        {cancelText && (\n          <button\n            className=\"custom-modal-btn custom-modal-btn-default\"\n            onClick={handleCancel}\n          >\n            {cancelText}\n          </button>\n        )}\n        <button\n          className={`custom-modal-btn custom-modal-btn-${okType}`}\n          onClick={handleOk}\n          disabled={confirmLoading}\n        >\n          {confirmLoading && <span className=\"custom-modal-loading\">⟳</span>}\n          {okText}\n        </button>\n      </div>\n    );\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className={`custom-modal-mask ${isAnimating ? 'custom-modal-mask-show' : 'custom-modal-mask-hide'}`}\n      onClick={handleMaskClick}\n    >\n      <div className={`custom-modal-wrap ${centered ? 'custom-modal-centered' : ''}`}>\n        <div\n          className={`custom-modal ${className} ${isAnimating ? 'custom-modal-show' : 'custom-modal-hide'}`}\n          style={{ width, ...style }}\n        >\n          {(title || closable) && (\n            <div className=\"custom-modal-header\">\n              {title && <div className=\"custom-modal-title\">{title}</div>}\n              {closable && (\n                <button\n                  className=\"custom-modal-close\"\n                  onClick={handleCancel}\n                  aria-label=\"Close\"\n                >\n                  ×\n                </button>\n              )}\n            </div>\n          )}\n          \n          <div className=\"custom-modal-body\">\n            {content || children}\n          </div>\n          \n          {renderFooter()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// 确认对话框配置\nexport interface ConfirmConfig {\n  title?: ReactNode;\n  content?: ReactNode;\n  okText?: string;\n  cancelText?: string;\n  okType?: 'primary' | 'danger';\n  onOk?: () => void | Promise<void>;\n  onCancel?: () => void;\n  width?: number | string;\n  centered?: boolean;\n  maskClosable?: boolean;\n}\n\n// 确认对话框管理器\nclass ConfirmManager {\n  private container: HTMLDivElement | null = null;\n  private root: any = null;\n\n  private getContainer() {\n    if (!this.container) {\n      this.container = document.createElement('div');\n      this.container.className = 'custom-modal-container';\n      document.body.appendChild(this.container);\n      this.root = createRoot(this.container);\n    }\n    return this.container;\n  }\n\n  confirm(config: ConfirmConfig) {\n    return new Promise<void>((resolve, reject) => {\n      let isResolved = false;\n\n      const handleOk = async () => {\n        if (isResolved) return;\n        \n        try {\n          if (config.onOk) {\n            await config.onOk();\n          }\n          isResolved = true;\n          this.destroy();\n          resolve();\n        } catch (error) {\n          reject(error);\n        }\n      };\n\n      const handleCancel = () => {\n        if (isResolved) return;\n        \n        isResolved = true;\n        config.onCancel?.();\n        this.destroy();\n        reject(new Error('User cancelled'));\n      };\n\n      this.getContainer();\n      this.root.render(\n        <Modal\n          visible={true}\n          title={config.title}\n          content={config.content}\n          okText={config.okText}\n          cancelText={config.cancelText}\n          okType={config.okType}\n          width={config.width}\n          centered={config.centered}\n          maskClosable={config.maskClosable}\n          onOk={handleOk}\n          onCancel={handleCancel}\n          afterClose={() => this.destroy()}\n        />\n      );\n    });\n  }\n\n  destroy() {\n    if (this.container && document.body.contains(this.container)) {\n      document.body.removeChild(this.container);\n      this.container = null;\n      this.root = null;\n    }\n  }\n}\n\n// 创建带有静态方法的Modal组件\nconst ModalWithStatic = Modal as typeof Modal & {\n  confirm: (config: ConfirmConfig) => Promise<void>;\n  info: (config: ConfirmConfig) => Promise<void>;\n  success: (config: ConfirmConfig) => Promise<void>;\n  error: (config: ConfirmConfig) => Promise<void>;\n  warning: (config: ConfirmConfig) => Promise<void>;\n};\n\n// 静态方法\nModalWithStatic.confirm = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: config.okType || 'primary'\n  });\n};\n\nModalWithStatic.info = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined, // info模式通常只有确定按钮\n  });\n};\n\nModalWithStatic.success = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined,\n  });\n};\n\nModalWithStatic.error = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'danger',\n    cancelText: undefined,\n  });\n};\n\nModalWithStatic.warning = (config: ConfirmConfig) => {\n  const manager = new ConfirmManager();\n  return manager.confirm({\n    ...config,\n    okType: 'primary',\n    cancelText: undefined,\n  });\n};\n\nexport default ModalWithStatic;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAwBA,WAAW;AACX,MAAM,QAA8B,CAAC,EACnC,KAAK,EACL,OAAO,EACP,QAAQ,EACR,UAAU,KAAK,EACf,QAAQ,GAAG,EACX,WAAW,KAAK,EAChB,WAAW,IAAI,EACf,eAAe,IAAI,EACnB,MAAM,EACN,SAAS,IAAI,EACb,aAAa,IAAI,EACjB,SAAS,SAAS,EAClB,iBAAiB,KAAK,EACtB,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACX;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,aAAa;YACb,eAAe;YACf,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,eAAe;YACf,WAAW;gBACT,aAAa;gBACb,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B;YACF,GAAG;QACL;IACF,GAAG;QAAC;QAAS;KAAW;IAExB,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,cAAc;YAChD;QACF;IACF;IAEA,MAAM,WAAW;QACf,IAAI,MAAM;YACR,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC;QACF;IACF;IAEA,MAAM,eAAe;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,MAAM,OAAO;QAE5B,IAAI,QAAQ,OAAO;QAEnB,qBACE,8OAAC;YAAI,WAAU;;gBACZ,4BACC,8OAAC;oBACC,WAAU;oBACV,SAAS;8BAER;;;;;;8BAGL,8OAAC;oBACC,WAAW,CAAC,kCAAkC,EAAE,QAAQ;oBACxD,SAAS;oBACT,UAAU;;wBAET,gCAAkB,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;wBACzD;;;;;;;;;;;;;IAIT;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAC,kBAAkB,EAAE,cAAc,2BAA2B,0BAA0B;QACnG,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW,0BAA0B,IAAI;sBAC5E,cAAA,8OAAC;gBACC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE,cAAc,sBAAsB,qBAAqB;gBACjG,OAAO;oBAAE;oBAAO,GAAG,KAAK;gBAAC;;oBAExB,CAAC,SAAS,QAAQ,mBACjB,8OAAC;wBAAI,WAAU;;4BACZ,uBAAS,8OAAC;gCAAI,WAAU;0CAAsB;;;;;;4BAC9C,0BACC,8OAAC;gCACC,WAAU;gCACV,SAAS;gCACT,cAAW;0CACZ;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;kCACZ,WAAW;;;;;;oBAGb;;;;;;;;;;;;;;;;;AAKX;AAgBA,WAAW;AACX,MAAM;IACI,YAAmC,KAAK;IACxC,OAAY,KAAK;IAEjB,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,SAAS,aAAa,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,SAAS;QACvC;QACA,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,QAAQ,MAAqB,EAAE;QAC7B,OAAO,IAAI,QAAc,CAAC,SAAS;YACjC,IAAI,aAAa;YAEjB,MAAM,WAAW;gBACf,IAAI,YAAY;gBAEhB,IAAI;oBACF,IAAI,OAAO,IAAI,EAAE;wBACf,MAAM,OAAO,IAAI;oBACnB;oBACA,aAAa;oBACb,IAAI,CAAC,OAAO;oBACZ;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT;YACF;YAEA,MAAM,eAAe;gBACnB,IAAI,YAAY;gBAEhB,aAAa;gBACb,OAAO,QAAQ;gBACf,IAAI,CAAC,OAAO;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,IAAI,CAAC,MAAM,eACd,8OAAC;gBACC,SAAS;gBACT,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,YAAY,OAAO,UAAU;gBAC7B,QAAQ,OAAO,MAAM;gBACrB,OAAO,OAAO,KAAK;gBACnB,UAAU,OAAO,QAAQ;gBACzB,cAAc,OAAO,YAAY;gBACjC,MAAM;gBACN,UAAU;gBACV,YAAY,IAAM,IAAI,CAAC,OAAO;;;;;;QAGpC;IACF;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG;YAC5D,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,IAAI,GAAG;QACd;IACF;AACF;AAEA,mBAAmB;AACnB,MAAM,kBAAkB;AAQxB,OAAO;AACP,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ,OAAO,MAAM,IAAI;IAC3B;AACF;AAEA,gBAAgB,IAAI,GAAG,CAAC;IACtB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,KAAK,GAAG,CAAC;IACvB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;AAEA,gBAAgB,OAAO,GAAG,CAAC;IACzB,MAAM,UAAU,IAAI;IACpB,OAAO,QAAQ,OAAO,CAAC;QACrB,GAAG,MAAM;QACT,QAAQ;QACR,YAAY;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/Message/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './message.css';\n\nexport interface MessageConfig {\n  content: string;\n  duration?: number;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  key?: string;\n}\n\ninterface MessageItem extends MessageConfig {\n  id: string;\n  visible: boolean;\n}\n\n// 消息容器组件\nconst MessageContainer: React.FC<{ messages: MessageItem[] }> = ({ messages }) => {\n  return (\n    <div className=\"custom-message-container\">\n      {messages.map((message) => (\n        <div\n          key={message.id}\n          className={`custom-message custom-message-${message.type} ${\n            message.visible ? 'custom-message-show' : 'custom-message-hide'\n          }`}\n        >\n          <div className=\"custom-message-icon\">\n            {message.type === 'success' && '✓'}\n            {message.type === 'error' && '✕'}\n            {message.type === 'warning' && '⚠'}\n            {message.type === 'info' && 'ℹ'}\n          </div>\n          <span className=\"custom-message-content\">{message.content}</span>\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// 消息管理器\nclass MessageManager {\n  private messages: MessageItem[] = [];\n  private container: HTMLDivElement | null = null;\n  private root: any = null;\n\n  private getContainer() {\n    if (!this.container) {\n      this.container = document.createElement('div');\n      this.container.className = 'custom-message-wrapper';\n      document.body.appendChild(this.container);\n      this.root = createRoot(this.container);\n    }\n    return this.container;\n  }\n\n  private render() {\n    if (this.root) {\n      this.root.render(<MessageContainer messages={this.messages} />);\n    }\n  }\n\n  private generateId() {\n    return `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  show(config: MessageConfig) {\n    const id = config.key || this.generateId();\n    const duration = config.duration ?? 3000;\n    \n    // 如果已存在相同key的消息，先移除\n    if (config.key) {\n      this.messages = this.messages.filter(msg => msg.id !== config.key);\n    }\n\n    const messageItem: MessageItem = {\n      ...config,\n      id,\n      visible: true,\n    };\n\n    this.messages.push(messageItem);\n    this.getContainer();\n    this.render();\n\n    // 自动移除\n    if (duration > 0) {\n      setTimeout(() => {\n        this.hide(id);\n      }, duration);\n    }\n\n    return id;\n  }\n\n  hide(id: string) {\n    const messageIndex = this.messages.findIndex(msg => msg.id === id);\n    if (messageIndex > -1) {\n      this.messages[messageIndex].visible = false;\n      this.render();\n      \n      // 动画结束后移除\n      setTimeout(() => {\n        this.messages = this.messages.filter(msg => msg.id !== id);\n        this.render();\n        \n        // 如果没有消息了，清理容器\n        if (this.messages.length === 0 && this.container) {\n          document.body.removeChild(this.container);\n          this.container = null;\n          this.root = null;\n        }\n      }, 300);\n    }\n  }\n\n  destroy() {\n    this.messages = [];\n    if (this.container) {\n      document.body.removeChild(this.container);\n      this.container = null;\n      this.root = null;\n    }\n  }\n}\n\n// 全局消息管理器实例\nconst messageManager = new MessageManager();\n\n// 导出的API\nexport const message = {\n  success: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'success', duration }),\n  \n  error: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'error', duration }),\n  \n  warning: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'warning', duration }),\n  \n  info: (content: string, duration?: number) => \n    messageManager.show({ content, type: 'info', duration }),\n  \n  destroy: () => messageManager.destroy(),\n};\n\nexport default message;\n"], "names": [], "mappings": ";;;;;AACA;;;;AAeA,SAAS;AACT,MAAM,mBAA0D,CAAC,EAAE,QAAQ,EAAE;IAC3E,qBACE,8OAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gBAEC,WAAW,CAAC,8BAA8B,EAAE,QAAQ,IAAI,CAAC,CAAC,EACxD,QAAQ,OAAO,GAAG,wBAAwB,uBAC1C;;kCAEF,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,IAAI,KAAK,aAAa;4BAC9B,QAAQ,IAAI,KAAK,WAAW;4BAC5B,QAAQ,IAAI,KAAK,aAAa;4BAC9B,QAAQ,IAAI,KAAK,UAAU;;;;;;;kCAE9B,8OAAC;wBAAK,WAAU;kCAA0B,QAAQ,OAAO;;;;;;;eAXpD,QAAQ,EAAE;;;;;;;;;;AAgBzB;AAEA,QAAQ;AACR,MAAM;IACI,WAA0B,EAAE,CAAC;IAC7B,YAAmC,KAAK;IACxC,OAAY,KAAK;IAEjB,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,SAAS,aAAa,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,SAAS;QACvC;QACA,OAAO,IAAI,CAAC,SAAS;IACvB;IAEQ,SAAS;QACf,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,eAAC,8OAAC;gBAAiB,UAAU,IAAI,CAAC,QAAQ;;;;;;QAC5D;IACF;IAEQ,aAAa;QACnB,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC3E;IAEA,KAAK,MAAqB,EAAE;QAC1B,MAAM,KAAK,OAAO,GAAG,IAAI,IAAI,CAAC,UAAU;QACxC,MAAM,WAAW,OAAO,QAAQ,IAAI;QAEpC,oBAAoB;QACpB,IAAI,OAAO,GAAG,EAAE;YACd,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,GAAG;QACnE;QAEA,MAAM,cAA2B;YAC/B,GAAG,MAAM;YACT;YACA,SAAS;QACX;QAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,MAAM;QAEX,OAAO;QACP,IAAI,WAAW,GAAG;YAChB,WAAW;gBACT,IAAI,CAAC,IAAI,CAAC;YACZ,GAAG;QACL;QAEA,OAAO;IACT;IAEA,KAAK,EAAU,EAAE;QACf,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/D,IAAI,eAAe,CAAC,GAAG;YACrB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,GAAG;YACtC,IAAI,CAAC,MAAM;YAEX,UAAU;YACV,WAAW;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBACvD,IAAI,CAAC,MAAM;gBAEX,eAAe;gBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;oBAChD,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;oBACxC,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,IAAI,GAAG;gBACd;YACF,GAAG;QACL;IACF;IAEA,UAAU;QACR,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YACxC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,IAAI,GAAG;QACd;IACF;AACF;AAEA,YAAY;AACZ,MAAM,iBAAiB,IAAI;AAGpB,MAAM,UAAU;IACrB,SAAS,CAAC,SAAiB,WACzB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAW;QAAS;IAE3D,OAAO,CAAC,SAAiB,WACvB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAS;QAAS;IAEzD,SAAS,CAAC,SAAiB,WACzB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAW;QAAS;IAE3D,MAAM,CAAC,SAAiB,WACtB,eAAe,IAAI,CAAC;YAAE;YAAS,MAAM;YAAQ;QAAS;IAExD,SAAS,IAAM,eAAe,OAAO;AACvC;uCAEe", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/components/index.ts"], "sourcesContent": ["// 自定义组件统一导出\nexport { default as Modal } from './Modal';\nexport { default as message } from './Message';\n\n// 类型导出\nexport type { ModalProps, ConfirmConfig } from './Modal';\nexport type { MessageConfig } from './Message';\n"], "names": [], "mappings": "AAAA,YAAY;;AACZ;AACA", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/config/api.ts"], "sourcesContent": ["// API配置\r\nexport const API_CONFIG = {\r\n  // 基础URL - 可以根据环境变量动态设置\r\n  BASE_URL:\r\n    (typeof window !== \"undefined\"\r\n      ? process.env.NEXT_PUBLIC_API_BASE_URL\r\n      : null) || \"http://localhost:18891\",\r\n\r\n  // 超时时间\r\n  TIMEOUT: 10000,\r\n\r\n  // API版本前缀（注意：当前使用/admin/前缀，此配置保留用于兼容性）\r\n  API_PREFIX: \"/api/v1/admin\",\r\n\r\n  // 完整的API基础URL\r\n  get FULL_BASE_URL() {\r\n    return `${this.BASE_URL}${this.API_PREFIX}`;\r\n  },\r\n};\r\n\r\n// 环境配置\r\nexport const ENV_CONFIG = {\r\n  isDevelopment: process.env.NODE_ENV === \"development\",\r\n  isProduction: process.env.NODE_ENV === \"production\",\r\n  isTest: process.env.NODE_ENV === \"test\",\r\n};\r\n"], "names": [], "mappings": "AAAA,QAAQ;;;;;AACD,MAAM,aAAa;IACxB,uBAAuB;IACvB,UACE,CAAC,6EAEG,IAAI,KAAK;IAEf,OAAO;IACP,SAAS;IAET,uCAAuC;IACvC,YAAY;IAEZ,cAAc;IACd,IAAI,iBAAgB;QAClB,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE;IAC7C;AACF;AAGO,MAAM,aAAa;IACxB,eAAe,oDAAyB;IACxC,cAAc,oDAAyB;IACvC,QAAQ,oDAAyB;AACnC", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/request.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from \"axios\";\r\nimport { message } from \"@/components\";\r\nimport { API_CONFIG } from \"@/config/api\";\r\n\r\n// 扩展AxiosRequestConfig以支持错误提示控制\r\ninterface CustomAxiosRequestConfig extends AxiosRequestConfig {\r\n  showError?: boolean; // 是否显示错误提示，默认为true\r\n  showSuccess?: boolean; // 是否显示成功提示，默认为false\r\n  successMessage?: string; // 自定义成功提示信息\r\n}\r\n\r\n// 创建axios实例\r\nconst request: AxiosInstance = axios.create({\r\n  baseURL: API_CONFIG.FULL_BASE_URL,\r\n  timeout: API_CONFIG.TIMEOUT,\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n});\r\n\r\n// 请求拦截器\r\nrequest.interceptors.request.use(\r\n  (config) => {\r\n    // 从localStorage获取token并添加到请求头\r\n    const token = localStorage.getItem(\"admin_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // 添加详细的请求日志\r\n    console.log(\"🚀 发送请求:\", {\r\n      method: config.method?.toUpperCase(),\r\n      url: config.url,\r\n      baseURL: config.baseURL,\r\n      fullURL: `${config.baseURL}${config.url}`,\r\n      data: config.data,\r\n      headers: config.headers,\r\n    });\r\n\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error(\"请求拦截器错误:\", error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 响应拦截器\r\nrequest.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    const config = response.config as CustomAxiosRequestConfig;\r\n\r\n    // 添加响应日志\r\n    console.log(\"✅ 请求成功:\", {\r\n      method: config.method?.toUpperCase(),\r\n      url: config.url,\r\n      status: response.status,\r\n      statusText: response.statusText,\r\n      data: response.data,\r\n    });\r\n\r\n    // 处理成功提示\r\n    if (config.showSuccess && config.successMessage) {\r\n      message.success(config.successMessage);\r\n    }\r\n\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error(\"❌ 请求失败:\", {\r\n      method: error.config?.method?.toUpperCase(),\r\n      url: error.config?.url,\r\n      baseURL: error.config?.baseURL,\r\n      fullURL: `${error.config?.baseURL}${error.config?.url}`,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n    });\r\n\r\n    const config = error.config as CustomAxiosRequestConfig;\r\n    const showError = config?.showError !== false; // 默认显示错误\r\n\r\n    if (!showError) {\r\n      return Promise.reject(error);\r\n    }\r\n\r\n    // 处理常见错误\r\n    if (error.response) {\r\n      const { status, data } = error.response;\r\n      let errorMessage = \"\";\r\n\r\n      switch (status) {\r\n        case 401:\r\n          errorMessage = \"登录已过期，请重新登录\";\r\n          localStorage.removeItem(\"admin_token\");\r\n          // 可以在这里添加跳转到登录页的逻辑\r\n          window.location.href = \"/login\";\r\n          break;\r\n        case 403:\r\n          errorMessage = data?.message || \"没有权限访问该资源\";\r\n          break;\r\n        case 404:\r\n          errorMessage = data?.message || \"请求的资源不存在\";\r\n          break;\r\n        case 422:\r\n          errorMessage = data?.message || \"请求参数验证失败\";\r\n          break;\r\n        case 500:\r\n          errorMessage = data?.message || \"服务器内部错误\";\r\n          break;\r\n        default:\r\n          errorMessage = data?.message || `请求失败 (${status})`;\r\n      }\r\n\r\n      message.error(errorMessage);\r\n    } else if (error.request) {\r\n      message.error(\"网络连接失败，请检查网络\");\r\n    } else {\r\n      message.error(\"请求配置错误\");\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 封装常用的HTTP方法\r\nexport const api = {\r\n  // GET请求\r\n  get: <T = any>(\r\n    url: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.get(url, config);\r\n  },\r\n\r\n  // POST请求\r\n  post: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.post(url, data, config);\r\n  },\r\n\r\n  // PUT请求\r\n  put: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.put(url, data, config);\r\n  },\r\n\r\n  // PATCH请求\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.patch(url, data, config);\r\n  },\r\n\r\n  // DELETE请求\r\n  delete: <T = any>(\r\n    url: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ): Promise<AxiosResponse<T>> => {\r\n    return request.delete(url, config);\r\n  },\r\n};\r\n\r\n// 便捷方法：不显示错误提示的请求\r\nexport const silentApi = {\r\n  get: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>\r\n    api.get<T>(url, { ...config, showError: false }),\r\n\r\n  post: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>\r\n    api.post<T>(url, data, { ...config, showError: false }),\r\n\r\n  put: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig) =>\r\n    api.put<T>(url, data, { ...config, showError: false }),\r\n\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    config?: CustomAxiosRequestConfig\r\n  ) => api.patch<T>(url, data, { ...config, showError: false }),\r\n\r\n  delete: <T = any>(url: string, config?: CustomAxiosRequestConfig) =>\r\n    api.delete<T>(url, { ...config, showError: false }),\r\n};\r\n\r\n// 便捷方法：带成功提示的请求\r\nexport const successApi = {\r\n  post: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.post<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"操作成功\",\r\n    }),\r\n\r\n  put: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.put<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"更新成功\",\r\n    }),\r\n\r\n  patch: <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.patch<T>(url, data, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"更新成功\",\r\n    }),\r\n\r\n  delete: <T = any>(\r\n    url: string,\r\n    successMessage?: string,\r\n    config?: CustomAxiosRequestConfig\r\n  ) =>\r\n    api.delete<T>(url, {\r\n      ...config,\r\n      showSuccess: true,\r\n      successMessage: successMessage || \"删除成功\",\r\n    }),\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AACA;;;;AASA,YAAY;AACZ,MAAM,UAAyB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1C,SAAS,oHAAA,CAAA,aAAU,CAAC,aAAa;IACjC,SAAS,oHAAA,CAAA,aAAU,CAAC,OAAO;IAC3B,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,QAAQ;AACR,QAAQ,YAAY,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC;IACC,8BAA8B;IAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,YAAY;IACZ,QAAQ,GAAG,CAAC,YAAY;QACtB,QAAQ,OAAO,MAAM,EAAE;QACvB,KAAK,OAAO,GAAG;QACf,SAAS,OAAO,OAAO;QACvB,SAAS,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;QACzC,MAAM,OAAO,IAAI;QACjB,SAAS,OAAO,OAAO;IACzB;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,YAAY;IAC1B,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,QAAQ;AACR,QAAQ,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC/B,CAAC;IACC,MAAM,SAAS,SAAS,MAAM;IAE9B,SAAS;IACT,QAAQ,GAAG,CAAC,WAAW;QACrB,QAAQ,OAAO,MAAM,EAAE;QACvB,KAAK,OAAO,GAAG;QACf,QAAQ,SAAS,MAAM;QACvB,YAAY,SAAS,UAAU;QAC/B,MAAM,SAAS,IAAI;IACrB;IAEA,SAAS;IACT,IAAI,OAAO,WAAW,IAAI,OAAO,cAAc,EAAE;QAC/C,4KAAA,CAAA,UAAO,CAAC,OAAO,CAAC,OAAO,cAAc;IACvC;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,WAAW;QACvB,QAAQ,MAAM,MAAM,EAAE,QAAQ;QAC9B,KAAK,MAAM,MAAM,EAAE;QACnB,SAAS,MAAM,MAAM,EAAE;QACvB,SAAS,GAAG,MAAM,MAAM,EAAE,UAAU,MAAM,MAAM,EAAE,KAAK;QACvD,QAAQ,MAAM,QAAQ,EAAE;QACxB,YAAY,MAAM,QAAQ,EAAE;QAC5B,MAAM,MAAM,QAAQ,EAAE;QACtB,SAAS,MAAM,OAAO;IACxB;IAEA,MAAM,SAAS,MAAM,MAAM;IAC3B,MAAM,YAAY,QAAQ,cAAc,OAAO,SAAS;IAExD,IAAI,CAAC,WAAW;QACd,OAAO,QAAQ,MAAM,CAAC;IACxB;IAEA,SAAS;IACT,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ;QACvC,IAAI,eAAe;QAEnB,OAAQ;YACN,KAAK;gBACH,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,mBAAmB;gBACnB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF,KAAK;gBACH,eAAe,MAAM,WAAW;gBAChC;YACF;gBACE,eAAe,MAAM,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtD;QAEA,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB,OAAO;QACL,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;IAChB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,MAAM;IACjB,QAAQ;IACR,KAAK,CACH,KACA;QAEA,OAAO,QAAQ,GAAG,CAAC,KAAK;IAC1B;IAEA,SAAS;IACT,MAAM,CACJ,KACA,MACA;QAEA,OAAO,QAAQ,IAAI,CAAC,KAAK,MAAM;IACjC;IAEA,QAAQ;IACR,KAAK,CACH,KACA,MACA;QAEA,OAAO,QAAQ,GAAG,CAAC,KAAK,MAAM;IAChC;IAEA,UAAU;IACV,OAAO,CACL,KACA,MACA;QAEA,OAAO,QAAQ,KAAK,CAAC,KAAK,MAAM;IAClC;IAEA,WAAW;IACX,QAAQ,CACN,KACA;QAEA,OAAO,QAAQ,MAAM,CAAC,KAAK;IAC7B;AACF;AAGO,MAAM,YAAY;IACvB,KAAK,CAAU,KAAa,SAC1B,IAAI,GAAG,CAAI,KAAK;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEhD,MAAM,CAAU,KAAa,MAAY,SACvC,IAAI,IAAI,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEvD,KAAK,CAAU,KAAa,MAAY,SACtC,IAAI,GAAG,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAEtD,OAAO,CACL,KACA,MACA,SACG,IAAI,KAAK,CAAI,KAAK,MAAM;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;IAE3D,QAAQ,CAAU,KAAa,SAC7B,IAAI,MAAM,CAAI,KAAK;YAAE,GAAG,MAAM;YAAE,WAAW;QAAM;AACrD;AAGO,MAAM,aAAa;IACxB,MAAM,CACJ,KACA,MACA,gBACA,SAEA,IAAI,IAAI,CAAI,KAAK,MAAM;YACrB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,KAAK,CACH,KACA,MACA,gBACA,SAEA,IAAI,GAAG,CAAI,KAAK,MAAM;YACpB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,OAAO,CACL,KACA,MACA,gBACA,SAEA,IAAI,KAAK,CAAI,KAAK,MAAM;YACtB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;IAEF,QAAQ,CACN,KACA,gBACA,SAEA,IAAI,MAAM,CAAI,KAAK;YACjB,GAAG,MAAM;YACT,aAAa;YACb,gBAAgB,kBAAkB;QACpC;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/authService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 登录请求参数类型\r\nexport interface LoginParams {\r\n  username: string;\r\n  password: string;\r\n}\r\n\r\n// 登录响应类型\r\nexport interface LoginResponse {\r\n  message: string;\r\n  accessToken: string;\r\n}\r\n\r\n// 认证服务\r\nexport const authService = {\r\n  // 登录\r\n  login: async (params: LoginParams): Promise<LoginResponse> => {\r\n    const response = await request.post<LoginResponse>('/auth/login', params);\r\n    return response.data;\r\n  },\r\n\r\n  // 登出\r\n  logout: () => {\r\n    localStorage.removeItem('admin_token');\r\n    window.location.href = '/login';\r\n  },\r\n\r\n  // 获取当前token\r\n  getToken: (): string | null => {\r\n    return localStorage.getItem('admin_token');\r\n  },\r\n\r\n  // 设置token\r\n  setToken: (token: string): void => {\r\n    localStorage.setItem('admin_token', token);\r\n  },\r\n\r\n  // 检查是否已登录\r\n  isLoggedIn: (): boolean => {\r\n    return !!localStorage.getItem('admin_token');\r\n  },\r\n};\r\n\r\n// 保持向后兼容的导出\r\nexport const login = authService.login;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAeO,MAAM,cAAc;IACzB,KAAK;IACL,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAgB,eAAe;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,KAAK;IACL,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,YAAY;IACZ,UAAU;QACR,OAAO,aAAa,OAAO,CAAC;IAC9B;IAEA,UAAU;IACV,UAAU,CAAC;QACT,aAAa,OAAO,CAAC,eAAe;IACtC;IAEA,UAAU;IACV,YAAY;QACV,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;AACF;AAGO,MAAM,QAAQ,YAAY,KAAK", "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/phraseService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 词组数据类型\r\nexport interface Phrase {\r\n  id: string;\r\n  text: string;\r\n  meaning: string;\r\n  exampleSentence?: string;\r\n  tags?: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建词组参数\r\nexport interface CreatePhraseParams {\r\n  text: string;\r\n  meaning: string;\r\n  exampleSentence?: string;\r\n  tags?: string[];\r\n}\r\n\r\n// 更新词组参数\r\nexport interface UpdatePhraseParams {\r\n  text?: string;\r\n  meaning?: string;\r\n  exampleSentence?: string;\r\n  tags?: string[];\r\n}\r\n\r\n// 词组服务\r\nexport const phraseService = {\r\n  // 获取所有词组\r\n  getAll: async (): Promise<Phrase[]> => {\r\n    const response = await request.get<Phrase[]>('/phrases');\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取词组\r\n  getById: async (id: string): Promise<Phrase> => {\r\n    const response = await request.get<Phrase>(`/phrases/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建词组\r\n  create: async (params: CreatePhraseParams): Promise<Phrase> => {\r\n    const response = await request.post<Phrase>('/phrases', params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新词组\r\n  update: async (id: string, params: UpdatePhraseParams): Promise<Phrase> => {\r\n    const response = await request.patch<Phrase>(`/phrases/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除词组\r\n  delete: async (id: string): Promise<void> => {\r\n    await request.delete(`/phrases/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AA8BO,MAAM,gBAAgB;IAC3B,SAAS;IACT,QAAQ;QACN,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAW;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAS,CAAC,SAAS,EAAE,IAAI;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAS,YAAY;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,KAAK,CAAS,CAAC,SAAS,EAAE,IAAI,EAAE;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,IAAI;IACvC;AACF", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/thesaurusService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 词库数据类型\r\nexport interface Thesaurus {\r\n  id: string;\r\n  name: string;\r\n  description?: string;\r\n  phraseIds: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建词库参数\r\nexport interface CreateThesaurusParams {\r\n  name: string;\r\n  description?: string;\r\n}\r\n\r\n// 更新词库参数\r\nexport interface UpdateThesaurusParams {\r\n  name?: string;\r\n  description?: string;\r\n}\r\n\r\n// 添加词组到词库参数\r\nexport interface AddPhraseToThesaurusParams {\r\n  phraseId: string;\r\n}\r\n\r\n// 词库服务\r\nexport const thesaurusService = {\r\n  // 获取所有词库\r\n  getAll: async (): Promise<Thesaurus[]> => {\r\n    const response = await request.get<Thesaurus[]>('/thesauruses');\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取词库\r\n  getById: async (id: string): Promise<Thesaurus> => {\r\n    const response = await request.get<Thesaurus>(`/thesauruses/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建词库\r\n  create: async (params: CreateThesaurusParams): Promise<Thesaurus> => {\r\n    const response = await request.post<Thesaurus>('/thesauruses', params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新词库\r\n  update: async (id: string, params: UpdateThesaurusParams): Promise<Thesaurus> => {\r\n    const response = await request.patch<Thesaurus>(`/thesauruses/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除词库\r\n  delete: async (id: string): Promise<void> => {\r\n    await request.delete(`/thesauruses/${id}`);\r\n  },\r\n\r\n  // 向词库添加词组\r\n  addPhrase: async (thesaurusId: string, params: AddPhraseToThesaurusParams): Promise<Thesaurus> => {\r\n    const response = await request.post<Thesaurus>(`/thesauruses/${thesaurusId}/phrases`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 从词库移除词组\r\n  removePhrase: async (thesaurusId: string, phraseId: string): Promise<Thesaurus> => {\r\n    const response = await request.delete<Thesaurus>(`/thesauruses/${thesaurusId}/phrases/${phraseId}`);\r\n    return response.data;\r\n  },\r\n};\r\n\r\n// 导出便捷函数以保持向后兼容\r\nexport const createThesaurus = thesaurusService.create;\r\nexport const updateThesaurus = thesaurusService.update;\r\nexport const deleteThesaurus = thesaurusService.delete;\r\nexport const deleteThesaurusById = thesaurusService.delete; // 别名\r\nexport const getThesaurusById = thesaurusService.getById;\r\nexport const getAllThesauruses = thesaurusService.getAll;\r\nexport const getThesauruses = thesaurusService.getAll; // 别名\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AA8BO,MAAM,mBAAmB;IAC9B,SAAS;IACT,QAAQ;QACN,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAc;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAY,CAAC,aAAa,EAAE,IAAI;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAY,gBAAgB;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,KAAK,CAAY,CAAC,aAAa,EAAE,IAAI,EAAE;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;IAC3C;IAEA,UAAU;IACV,WAAW,OAAO,aAAqB;QACrC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAY,CAAC,aAAa,EAAE,YAAY,QAAQ,CAAC,EAAE;QACtF,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,cAAc,OAAO,aAAqB;QACxC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAY,CAAC,aAAa,EAAE,YAAY,SAAS,EAAE,UAAU;QAClG,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,kBAAkB,iBAAiB,MAAM;AAC/C,MAAM,kBAAkB,iBAAiB,MAAM;AAC/C,MAAM,kBAAkB,iBAAiB,MAAM;AAC/C,MAAM,sBAAsB,iBAAiB,MAAM,EAAE,KAAK;AAC1D,MAAM,mBAAmB,iBAAiB,OAAO;AACjD,MAAM,oBAAoB,iBAAiB,MAAM;AACjD,MAAM,iBAAiB,iBAAiB,MAAM,EAAE,KAAK", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/levelService.ts"], "sourcesContent": ["import request from \"./request\";\r\n\r\n// 关卡数据类型（基于API文档LevelResponseDto）\r\nexport interface Level {\r\n  id: string;\r\n  name: string;\r\n  difficulty: number;\r\n  description?: string;\r\n  thesaurusIds: string[]; // 保留以兼容API响应\r\n  phraseIds: string[];\r\n  tagIds: string[]; // 新增：关联的标签ID列表\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建关卡参数（基于API文档CreateLevelDto）\r\nexport interface CreateLevelParams {\r\n  name: string;\r\n  difficulty: number;\r\n  description?: string;\r\n  thesaurusIds?: string[]; // 保留以兼容API，但前端不使用\r\n  phraseIds?: string[];\r\n  phrases?: Array<{ text: string; meaning: string }>; // 新增：直接传递词组数据\r\n  tagIds?: string[]; // 新增：关联的标签ID列表\r\n}\r\n\r\n// 更新关卡参数（基于API文档UpdateLevelDto）\r\nexport interface UpdateLevelParams {\r\n  name?: string;\r\n  difficulty?: number;\r\n  description?: string;\r\n  thesaurusIds?: string[]; // 保留以兼容API，但前端不使用\r\n  phraseIds?: string[];\r\n  phrases?: Array<{ text: string; meaning: string }>; // 新增：直接传递新词组数据\r\n  tagIds?: string[]; // 新增：关联的标签ID列表\r\n}\r\n\r\n// 添加词组到关卡参数\r\nexport interface AddPhraseToLevelParams {\r\n  phraseId: string;\r\n}\r\n\r\n// 关卡查询参数（基于API文档接口参数）\r\nexport interface LevelQueryParams {\r\n  search?: string; // 搜索关键词（标题或描述）\r\n  difficulty?: number; // 难度等级过滤\r\n  isActive?: boolean; // 状态过滤\r\n  tagId?: string; // 标签ID过滤\r\n  page?: number; // 页码，默认1\r\n  pageSize?: number; // 每页数量，默认20\r\n}\r\n\r\n// 关卡列表响应类型\r\nexport interface LevelListResponse {\r\n  levels: Level[];\r\n  total: number;\r\n  page: number;\r\n  pageSize: number;\r\n  totalPages: number;\r\n}\r\n\r\n// 关卡星级统计数据类型\r\nexport interface LevelStarAnalytics {\r\n  averageStars: number;\r\n  starDistribution: {\r\n    [key: string]: number;\r\n  };\r\n  averageCompletionTime: number;\r\n  completionRate: number;\r\n  difficultyRating: number;\r\n  totalAttempts: number;\r\n  totalCompletions: number;\r\n}\r\n\r\n// 关卡服务\r\nexport const levelService = {\r\n  // 获取关卡列表（支持筛选和分页）\r\n  getAll: async (params?: LevelQueryParams): Promise<LevelListResponse> => {\r\n    const response = await request.get<LevelListResponse>(\"/levels\", {\r\n      params,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 获取所有关卡（简单版本，保持向后兼容）\r\n  getAllSimple: async (): Promise<Level[]> => {\r\n    const response = await request.get<Level[]>(\"/levels\");\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取关卡\r\n  getById: async (id: string): Promise<Level> => {\r\n    const response = await request.get<Level>(`/levels/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建关卡\r\n  create: async (params: CreateLevelParams): Promise<Level> => {\r\n    const response = await request.post<Level>(\"/levels\", params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新关卡\r\n  update: async (id: string, params: UpdateLevelParams): Promise<Level> => {\r\n    const response = await request.patch<Level>(`/levels/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除关卡\r\n  delete: async (id: string): Promise<void> => {\r\n    await request.delete(`/levels/${id}`);\r\n  },\r\n\r\n  // 向关卡添加词组\r\n  addPhrase: async (\r\n    levelId: string,\r\n    params: AddPhraseToLevelParams\r\n  ): Promise<Level> => {\r\n    const response = await request.post<Level>(\r\n      `/levels/${levelId}/phrases`,\r\n      params\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 从关卡移除词组\r\n  removePhrase: async (levelId: string, phraseId: string): Promise<Level> => {\r\n    const response = await request.delete<Level>(\r\n      `/levels/${levelId}/phrases/${phraseId}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 获取关卡统计\r\n  getCount: async (): Promise<{\r\n    total: number;\r\n    maxLevels: number;\r\n    remaining: number;\r\n  }> => {\r\n    const response = await request.get<{\r\n      total: number;\r\n      maxLevels: number;\r\n      remaining: number;\r\n    }>(\"/levels/count\");\r\n    return response.data;\r\n  },\r\n\r\n  // 根据难度获取关卡\r\n  getByDifficulty: async (difficulty: number): Promise<Level[]> => {\r\n    const response = await request.get<Level[]>(\r\n      `/levels/difficulty/${difficulty}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 获取关卡详细信息（包含词组详情）\r\n  getWithPhrases: async (id: string): Promise<Level & { phrases: any[] }> => {\r\n    const response = await request.get<Level & { phrases: any[] }>(\r\n      `/levels/${id}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 获取关卡星级统计\r\n  getLevelStarAnalytics: async (\r\n    levelId: string\r\n  ): Promise<LevelStarAnalytics> => {\r\n    const response = await request.get<LevelStarAnalytics>(\r\n      `/levels/${levelId}/star-analytics`\r\n    );\r\n    return response.data;\r\n  },\r\n};\r\n\r\n// 保持向后兼容的导出\r\nexport const getLevels = levelService.getAll;\r\n"], "names": [], "mappings": ";;;;AAAA;;AA2EO,MAAM,eAAe;IAC1B,kBAAkB;IAClB,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAoB,WAAW;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,cAAc;QACZ,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAU;QAC5C,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAQ,CAAC,QAAQ,EAAE,IAAI;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAQ,WAAW;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,KAAK,CAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI;IACtC;IAEA,UAAU;IACV,WAAW,OACT,SACA;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CACjC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAC5B;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,cAAc,OAAO,SAAiB;QACpC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CACnC,CAAC,QAAQ,EAAE,QAAQ,SAAS,EAAE,UAAU;QAE1C,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,UAAU;QAKR,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAI/B;QACH,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,iBAAiB,OAAO;QACtB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,CAAC,mBAAmB,EAAE,YAAY;QAEpC,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,CAAC,QAAQ,EAAE,IAAI;QAEjB,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,uBAAuB,OACrB;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,CAAC,QAAQ,EAAE,QAAQ,eAAe,CAAC;QAErC,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY,aAAa,MAAM", "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/userService.ts"], "sourcesContent": ["import { api, successApi } from \"./request\";\r\n\r\n// 用户数据类型\r\nexport interface User {\r\n  id: string; // 8位随机数字ID\r\n  phone: string; // 手机号（必填）\r\n  openid?: string; // 微信openid（可选）\r\n  nickname?: string;\r\n  avatarUrl?: string;\r\n  unlockedLevels: number;\r\n  completedLevelIds: string[];\r\n  totalGames: number;\r\n  totalCompletions: number;\r\n  lastPlayTime: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  // 每日解锁限制相关字段\r\n  isVip: boolean; // VIP状态\r\n  dailyUnlockLimit: number; // 每日解锁限制\r\n  dailyUnlockCount: number; // 当日解锁次数\r\n  dailyShared: boolean; // 当日是否已分享\r\n  lastPlayDate: string; // 最后游戏日期\r\n  totalShares: number; // 总分享次数\r\n}\r\n\r\n// 创建用户参数\r\nexport interface CreateUserParams {\r\n  phone: string; // 手机号（必填）\r\n  openid?: string; // 微信openid（可选）\r\n  nickname?: string;\r\n  avatarUrl?: string;\r\n}\r\n\r\n// 更新用户参数\r\nexport interface UpdateUserParams {\r\n  phone?: string; // 手机号\r\n  openid?: string; // 微信openid\r\n  nickname?: string;\r\n  avatarUrl?: string;\r\n  unlockedLevels?: number;\r\n  completedLevelIds?: string[];\r\n  totalGames?: number;\r\n  totalCompletions?: number;\r\n  isVip?: boolean; // VIP状态\r\n  dailyUnlockLimit?: number; // 每日解锁限制\r\n}\r\n\r\n// 完成关卡参数\r\nexport interface CompleteLevelParams {\r\n  levelId: string;\r\n}\r\n\r\n// 设置VIP套餐参数（对应API的SetVipPackageDto）\r\nexport interface SetVipParams {\r\n  packageId: string; // VIP套餐ID，如 \"vip_custom_111111d_nfow\"\r\n  reason?: string; // 操作原因\r\n}\r\n\r\n// 取消VIP参数（对应API的CancelVipDto）\r\nexport interface CancelVipParams {\r\n  reason?: string; // 取消原因\r\n  immediate: boolean; // 是否立即生效\r\n}\r\n\r\n// VIP状态响应（对应API的VipStatusResponseDto）\r\nexport interface VipStatusResponse {\r\n  id: string;\r\n  nickname?: string;\r\n  isVip: boolean;\r\n  packageId?: string;\r\n  packageName?: string;\r\n  vipExpiresAt?: string;\r\n  dailyUnlockLimit: number;\r\n  updatedAt: string;\r\n  message: string;\r\n}\r\n\r\n// 批量VIP套餐操作参数（对应API的BatchVipPackageOperationDto）\r\nexport interface BatchVipOperationParams {\r\n  userIds: string[];\r\n  packageId: string; // VIP套餐ID，必需\r\n  reason?: string; // 操作原因\r\n}\r\n\r\n// 用户统计数据\r\nexport interface UserStats {\r\n  totalGames: number;\r\n  totalCompletions: number;\r\n  unlockedLevels: number;\r\n  completedLevels: number;\r\n  completionRate: number;\r\n  // 每日解锁统计\r\n  dailyUnlockCount: number;\r\n  dailyUnlockLimit: number;\r\n  remainingUnlocks: number;\r\n  totalShares: number;\r\n  isVip: boolean;\r\n}\r\n\r\n// 用户服务\r\nexport const userService = {\r\n  // 获取所有用户\r\n  getAll: async (): Promise<{\r\n    users: User[];\r\n    total: number;\r\n  }> => {\r\n    const response = await api.get<any>(\"/users\");\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取用户\r\n  getById: async (id: string): Promise<User> => {\r\n    const response = await api.get<User>(`/users/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据openid获取用户\r\n  getByOpenid: async (openid: string): Promise<User> => {\r\n    const response = await api.get<User>(`/users/by-openid?openid=${openid}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据手机号获取用户\r\n  getByPhone: async (phone: string): Promise<User> => {\r\n    const response = await api.get<User>(`/users/by-phone?phone=${phone}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建用户\r\n  create: async (params: CreateUserParams): Promise<User> => {\r\n    const response = await successApi.post<User>(\r\n      \"/users\",\r\n      params,\r\n      \"用户创建成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 更新用户\r\n  update: async (id: string, params: UpdateUserParams): Promise<User> => {\r\n    const response = await successApi.patch<User>(\r\n      `/users/${id}`,\r\n      params,\r\n      \"用户更新成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 删除用户\r\n  delete: async (id: string): Promise<void> => {\r\n    await successApi.delete(`/users/${id}`, \"用户删除成功\");\r\n  },\r\n\r\n  // 用户完成关卡\r\n  completeLevel: async (\r\n    id: string,\r\n    params: CompleteLevelParams\r\n  ): Promise<User> => {\r\n    const response = await api.post<User>(\r\n      `/users/${id}/complete-level`,\r\n      params\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 用户开始游戏\r\n  startGame: async (id: string): Promise<User> => {\r\n    const response = await api.post<User>(`/users/${id}/start-game`);\r\n    return response.data;\r\n  },\r\n\r\n  // 获取用户统计\r\n  getStats: async (id: string): Promise<UserStats> => {\r\n    const response = await api.get<UserStats>(`/users/${id}/stats`);\r\n    return response.data;\r\n  },\r\n\r\n  // 重置用户进度\r\n  resetProgress: async (id: string): Promise<User> => {\r\n    const response = await successApi.post<User>(\r\n      `/users/${id}/reset-progress`,\r\n      {},\r\n      \"用户进度重置成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 设置用户VIP套餐\r\n  setVipStatus: async (\r\n    id: string,\r\n    params: SetVipParams\r\n  ): Promise<VipStatusResponse> => {\r\n    const response = await successApi.post<VipStatusResponse>(\r\n      `/users/${id}/set-vip-package`,\r\n      {\r\n        packageId: params.packageId,\r\n        reason: params.reason || \"管理员手动设置\",\r\n      },\r\n      \"设置VIP成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 取消用户VIP状态\r\n  cancelVipStatus: async (\r\n    id: string,\r\n    params: CancelVipParams\r\n  ): Promise<VipStatusResponse> => {\r\n    const response = await successApi.post<VipStatusResponse>(\r\n      `/users/${id}/cancel-vip`,\r\n      params,\r\n      \"取消VIP成功\"\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // 批量VIP操作（统一接口）\r\n  batchVipOperation: async (params: BatchVipOperationParams): Promise<void> => {\r\n    await api.post(\"/users/batch-vip-package\", params);\r\n  },\r\n\r\n  // 批量设置用户VIP套餐\r\n  batchSetVipStatus: async (\r\n    userIds: string[],\r\n    params: SetVipParams\r\n  ): Promise<void> => {\r\n    await userService.batchVipOperation({\r\n      userIds,\r\n      packageId: params.packageId,\r\n      reason: params.reason || \"管理员批量设置\",\r\n    });\r\n  },\r\n\r\n  // 批量取消用户VIP状态（使用取消VIP接口）\r\n  batchCancelVipStatus: async (\r\n    userIds: string[],\r\n    params: CancelVipParams\r\n  ): Promise<void> => {\r\n    // 批量取消VIP需要循环调用单个取消接口\r\n    const promises = userIds.map((userId) =>\r\n      userService.cancelVipStatus(userId, params)\r\n    );\r\n    await Promise.all(promises);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAoGO,MAAM,cAAc;IACzB,SAAS;IACT,QAAQ;QAIN,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAM;QACpC,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAO,CAAC,sBAAsB,EAAE,OAAO;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,UACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,KAAK,CACrC,CAAC,OAAO,EAAE,IAAI,EACd,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,0HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IAC1C;IAEA,SAAS;IACT,eAAe,OACb,IACA;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,IAAI,CAC7B,CAAC,OAAO,EAAE,GAAG,eAAe,CAAC,EAC7B;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,IAAI,CAAO,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAY,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,CAAC,OAAO,EAAE,GAAG,eAAe,CAAC,EAC7B,CAAC,GACD;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,cAAc,OACZ,IACA;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,CAAC,OAAO,EAAE,GAAG,gBAAgB,CAAC,EAC9B;YACE,WAAW,OAAO,SAAS;YAC3B,QAAQ,OAAO,MAAM,IAAI;QAC3B,GACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,iBAAiB,OACf,IACA;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,EACzB,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,mBAAmB,OAAO;QACxB,MAAM,0HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,4BAA4B;IAC7C;IAEA,cAAc;IACd,mBAAmB,OACjB,SACA;QAEA,MAAM,YAAY,iBAAiB,CAAC;YAClC;YACA,WAAW,OAAO,SAAS;YAC3B,QAAQ,OAAO,MAAM,IAAI;QAC3B;IACF;IAEA,yBAAyB;IACzB,sBAAsB,OACpB,SACA;QAEA,sBAAsB;QACtB,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAC,SAC5B,YAAY,eAAe,CAAC,QAAQ;QAEtC,MAAM,QAAQ,GAAG,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/shareService.ts"], "sourcesContent": ["import request from './request';\r\nimport type {\r\n  CreateShareConfigRequest,\r\n  UpdateShareConfigRequest,\r\n  ShareConfigResponse,\r\n} from '../types/share';\r\n\r\n// 分享管理服务\r\nexport class ShareService {\r\n  // 获取所有分享配置\r\n  static async getAllShareConfigs(): Promise<ShareConfigResponse[]> {\r\n    const response = await request.get('/share');\r\n    return response.data;\r\n  }\r\n\r\n  // 获取启用的分享配置\r\n  static async getActiveShareConfigs(): Promise<ShareConfigResponse[]> {\r\n    const response = await request.get('/share/active');\r\n    return response.data;\r\n  }\r\n\r\n  // 获取默认分享配置\r\n  static async getDefaultShareConfig(): Promise<ShareConfigResponse | null> {\r\n    const response = await request.get('/share/default');\r\n    return response.data;\r\n  }\r\n\r\n  // 根据类型获取分享配置\r\n  static async getShareConfigByType(type: string): Promise<ShareConfigResponse | null> {\r\n    const response = await request.get(`/share/type/${type}`);\r\n    return response.data;\r\n  }\r\n\r\n  // 根据ID获取分享配置\r\n  static async getShareConfigById(id: string): Promise<ShareConfigResponse> {\r\n    const response = await request.get(`/share/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  // 创建分享配置\r\n  static async createShareConfig(data: CreateShareConfigRequest): Promise<ShareConfigResponse> {\r\n    const response = await request.post('/share', data);\r\n    return response.data;\r\n  }\r\n\r\n  // 更新分享配置\r\n  static async updateShareConfig(id: string, data: UpdateShareConfigRequest): Promise<ShareConfigResponse> {\r\n    const response = await request.patch(`/share/${id}`, data);\r\n    return response.data;\r\n  }\r\n\r\n  // 启用/禁用分享配置\r\n  static async toggleShareConfig(id: string): Promise<ShareConfigResponse> {\r\n    const response = await request.put(`/share/${id}/toggle`);\r\n    return response.data;\r\n  }\r\n\r\n  // 删除分享配置\r\n  static async deleteShareConfig(id: string): Promise<{ message: string }> {\r\n    const response = await request.delete(`/share/${id}`);\r\n    return response.data;\r\n  }\r\n}\r\n\r\n// 分享类型选项\r\nexport const SHARE_TYPE_OPTIONS = [\r\n  {\r\n    value: 'default',\r\n    label: '默认分享',\r\n    description: '通用分享，适用于首页、游戏页等'\r\n  },\r\n  {\r\n    value: 'result',\r\n    label: '结果分享',\r\n    description: '展示用户成绩的分享'\r\n  },\r\n  {\r\n    value: 'level',\r\n    label: '关卡分享',\r\n    description: '邀请好友挑战特定关卡'\r\n  },\r\n  {\r\n    value: 'achievement',\r\n    label: '成就分享',\r\n    description: '展示用户获得的成就'\r\n  },\r\n  {\r\n    value: 'custom',\r\n    label: '自定义分享',\r\n    description: '特殊活动或自定义场景'\r\n  }\r\n];\r\n\r\n// 分享路径模板\r\nexport const SHARE_PATH_TEMPLATES = [\r\n  {\r\n    label: '首页',\r\n    value: '/pages/index/index',\r\n    description: '小程序首页'\r\n  },\r\n  {\r\n    label: '游戏页',\r\n    value: '/pages/game/game',\r\n    description: '游戏主页面'\r\n  },\r\n  {\r\n    label: '关卡页',\r\n    value: '/pages/level/level?id={levelId}',\r\n    description: '特定关卡页面，{levelId}会被替换为实际关卡ID'\r\n  },\r\n  {\r\n    label: '结果页',\r\n    value: '/pages/result/result?score={score}',\r\n    description: '结果展示页面，{score}会被替换为实际分数'\r\n  },\r\n  {\r\n    label: '排行榜',\r\n    value: '/pages/rank/rank',\r\n    description: '排行榜页面'\r\n  }\r\n];\r\n\r\nexport default ShareService;\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAQO,MAAM;IACX,WAAW;IACX,aAAa,qBAAqD;QAChE,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,aAAa,wBAAwD;QACnE,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,aAAa,wBAA6D;QACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,aAAa,qBAAqB,IAAY,EAAuC;QACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,aAAa,mBAAmB,EAAU,EAAgC;QACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,kBAAkB,IAA8B,EAAgC;QAC3F,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,UAAU;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,kBAAkB,EAAU,EAAE,IAA8B,EAAgC;QACvG,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,aAAa,kBAAkB,EAAU,EAAgC;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,kBAAkB,EAAU,EAAgC;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,qBAAqB;IAChC;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAGM,MAAM,uBAAuB;IAClC;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 1216, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/vipService.ts"], "sourcesContent": ["import request from \"./request\";\r\n\r\n// VIP套餐数据类型\r\nexport interface VipPackage {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  price: number;\r\n  duration: number;\r\n  sortOrder: number;\r\n  isActive: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建/更新VIP套餐参数\r\nexport interface VipPackageParams {\r\n  id?: string;\r\n  name: string;\r\n  description: string;\r\n  price: number;\r\n  duration: number;\r\n  sortOrder: number;\r\n  isActive: boolean;\r\n}\r\n\r\n// 支付订单数据类型\r\nexport interface PaymentOrder {\r\n  id: string;\r\n  userId: string;\r\n  openid: string;\r\n  out_trade_no: string;\r\n  transaction_id?: string;\r\n  description: string;\r\n  total: number;\r\n  status: \"PENDING\" | \"SUCCESS\" | \"FAILED\" | \"CANCELLED\" | \"REFUNDED\";\r\n  vip_package_id: string;\r\n  prepay_id?: string;\r\n  detail?: string;\r\n  attach?: string;\r\n  paid_at?: string;\r\n  expires_at: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\n// VIP套餐服务\r\nexport const vipPackageService = {\r\n  // 获取VIP套餐列表\r\n  async getList(params?: {\r\n    isActive?: boolean;\r\n    sortBy?: string;\r\n    sortOrder?: string;\r\n  }): Promise<VipPackage[]> {\r\n    const response = await request.get<VipPackage[]>(\"/payment/packages\", {\r\n      params,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取VIP套餐\r\n  async getById(id: string): Promise<VipPackage> {\r\n    const response = await request.get(`/payment/packages/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 创建VIP套餐\r\n  async create(params: VipPackageParams): Promise<VipPackage> {\r\n    const response = await request.post(\"/payment/packages\", params);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新VIP套餐\r\n  async update(\r\n    id: string,\r\n    params: Partial<VipPackageParams>\r\n  ): Promise<VipPackage> {\r\n    const response = await request.put(`/payment/packages/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 删除VIP套餐\r\n  async delete(id: string): Promise<void> {\r\n    await request.delete(`/payment/packages/${id}`);\r\n  },\r\n\r\n  // 切换套餐状态（通过更新接口实现）\r\n  async toggleStatus(id: string, isActive: boolean): Promise<VipPackage> {\r\n    const response = await request.put(`/payment/packages/${id}`, { isActive });\r\n    return response.data;\r\n  },\r\n\r\n  // 删除套餐统计方法\r\n};\r\n\r\n// 支付订单服务\r\nexport const paymentOrderService = {\r\n  // 获取支付订单列表\r\n  async getList(params?: {\r\n    search?: string;\r\n    status?: string;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    page?: number;\r\n    pageSize?: number;\r\n    userId?: string;\r\n    packageId?: string;\r\n  }): Promise<{\r\n    orders: PaymentOrder[];\r\n    total: number;\r\n    page: number;\r\n    pageSize: number;\r\n    totalPages: number;\r\n  }> {\r\n    const response = await request.get(\"/payment/orders\", { params });\r\n    return response.data;\r\n  },\r\n\r\n  // 删除支付订单统计方法\r\n\r\n  // 根据ID获取支付订单\r\n  async getById(id: string): Promise<PaymentOrder> {\r\n    const response = await request.get(`/payment/orders/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 申请退款\r\n  async refund(\r\n    id: string,\r\n    reason?: string\r\n  ): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    refundId?: string;\r\n  }> {\r\n    const response = await request.post(`/payment/orders/${id}/refund`, {\r\n      reason,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 取消订单\r\n  async cancel(out_trade_no: string, userId: string): Promise<void> {\r\n    await request.post(`/payment/cancel/${out_trade_no}`, { userId });\r\n  },\r\n};\r\n\r\n// 删除VIP用户服务，功能已合并到用户管理\r\n\r\n// 导出所有服务\r\nexport default {\r\n  vipPackageService,\r\n  paymentOrderService,\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AA+CO,MAAM,oBAAoB;IAC/B,YAAY;IACZ,MAAM,SAAQ,MAIb;QACC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAe,qBAAqB;YACpE;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,MAAM,QAAO,MAAwB;QACnC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,qBAAqB;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,MAAM,QACJ,EAAU,EACV,MAAiC;QAEjC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,MAAM,QAAO,EAAU;QACrB,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,IAAI;IAChD;IAEA,mBAAmB;IACnB,MAAM,cAAa,EAAU,EAAE,QAAiB;QAC9C,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE;YAAE;QAAS;QACzE,OAAO,SAAS,IAAI;IACtB;AAGF;AAGO,MAAM,sBAAsB;IACjC,WAAW;IACX,MAAM,SAAQ,MASb;QAOC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;YAAE;QAAO;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IAEb,aAAa;IACb,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QACJ,EAAU,EACV,MAAe;QAMf,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE;YAClE;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,YAAoB,EAAE,MAAc;QAC/C,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,cAAc,EAAE;YAAE;QAAO;IACjE;AACF;uCAKe;IACb;IACA;AACF", "debugId": null}}, {"offset": {"line": 1296, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/settingsService.ts"], "sourcesContent": ["import request from './request';\r\n\r\n// 设置数据类型\r\nexport interface Settings {\r\n  id: string;\r\n  key: string;\r\n  value: string;\r\n  description?: string;\r\n  type: 'string' | 'number' | 'boolean' | 'url';\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n// 创建设置参数\r\nexport interface CreateSettingsParams {\r\n  key: string;\r\n  value: string;\r\n  description?: string;\r\n  type: 'string' | 'number' | 'boolean' | 'url';\r\n}\r\n\r\n// 更新设置参数\r\nexport interface UpdateSettingsParams {\r\n  key?: string;\r\n  value?: string;\r\n  description?: string;\r\n  type?: 'string' | 'number' | 'boolean' | 'url';\r\n}\r\n\r\n// 设置服务\r\nexport const settingsService = {\r\n  // 获取所有设置\r\n  async getAll(): Promise<Settings[]> {\r\n    const response = await request.get('/settings');\r\n    return response.data;\r\n  },\r\n\r\n  // 根据ID获取设置\r\n  async getById(id: string): Promise<Settings> {\r\n    const response = await request.get(`/settings/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据键名获取设置\r\n  async getByKey(key: string): Promise<Settings> {\r\n    const response = await request.get(`/settings/key/${key}`);\r\n    return response.data;\r\n  },\r\n\r\n  // 更新设置\r\n  async update(id: string, params: UpdateSettingsParams): Promise<Settings> {\r\n    const response = await request.patch(`/settings/${id}`, params);\r\n    return response.data;\r\n  },\r\n\r\n  // 根据键名更新设置值\r\n  async updateByKey(key: string, value: string): Promise<Settings> {\r\n    const response = await request.patch(`/settings/key/${key}`, { value });\r\n    return response.data;\r\n  },\r\n\r\n  // 初始化默认设置（保留用于手动触发）\r\n  async initializeDefaults(): Promise<void> {\r\n    await request.post('/settings/initialize');\r\n  },\r\n\r\n  // 批量更新小程序配置\r\n  async updateAppConfig(config: {\r\n    helpUrl?: string;\r\n    backgroundMusicUrl?: string;\r\n  }): Promise<void> {\r\n    const promises = [];\r\n    \r\n    if (config.helpUrl !== undefined) {\r\n      promises.push(this.updateByKey('help_url', config.helpUrl));\r\n    }\r\n    \r\n    if (config.backgroundMusicUrl !== undefined) {\r\n      promises.push(this.updateByKey('background_music_url', config.backgroundMusicUrl));\r\n    }\r\n    \r\n    await Promise.all(promises);\r\n  },\r\n\r\n  // 获取小程序配置\r\n  async getAppConfig(): Promise<{\r\n    helpUrl: string;\r\n    backgroundMusicUrl: string;\r\n  }> {\r\n    try {\r\n      const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([\r\n        this.getByKey('help_url').catch(() => null),\r\n        this.getByKey('background_music_url').catch(() => null),\r\n      ]);\r\n\r\n      return {\r\n        helpUrl: helpUrlSetting?.value || '',\r\n        backgroundMusicUrl: backgroundMusicSetting?.value || '',\r\n      };\r\n    } catch (error) {\r\n      console.error('获取小程序配置失败:', error);\r\n      // 如果设置不存在，返回默认值\r\n      return {\r\n        helpUrl: '',\r\n        backgroundMusicUrl: '',\r\n      };\r\n    }\r\n  },\r\n\r\n  // 测试微信小程序获取设置接口\r\n  async testWeixinAppSettings(): Promise<{\r\n    helpUrl: string;\r\n    backgroundMusicUrl: string;\r\n  }> {\r\n    try {\r\n      const response = await request.get('/weixin/app-settings');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('测试微信小程序设置接口失败:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // 测试微信小程序全局配置接口\r\n  async testWeixinGlobalConfig(): Promise<any> {\r\n    try {\r\n      const response = await request.get('/weixin/global-config');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('测试微信小程序全局配置接口失败:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AA8BO,MAAM,kBAAkB;IAC7B,SAAS;IACT,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACnC,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,MAAM,UAAS,GAAW;QACxB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,EAAU,EAAE,MAA4B;QACnD,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,aAAY,GAAW,EAAE,KAAa;QAC1C,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE;YAAE;QAAM;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,MAAM;QACJ,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACrB;IAEA,YAAY;IACZ,MAAM,iBAAgB,MAGrB;QACC,MAAM,WAAW,EAAE;QAEnB,IAAI,OAAO,OAAO,KAAK,WAAW;YAChC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,OAAO,OAAO;QAC3D;QAEA,IAAI,OAAO,kBAAkB,KAAK,WAAW;YAC3C,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,OAAO,kBAAkB;QAClF;QAEA,MAAM,QAAQ,GAAG,CAAC;IACpB;IAEA,UAAU;IACV,MAAM;QAIJ,IAAI;YACF,MAAM,CAAC,gBAAgB,uBAAuB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjE,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,CAAC,IAAM;gBACtC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,KAAK,CAAC,IAAM;aACnD;YAED,OAAO;gBACL,SAAS,gBAAgB,SAAS;gBAClC,oBAAoB,wBAAwB,SAAS;YACvD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,gBAAgB;YAChB,OAAO;gBACL,SAAS;gBACT,oBAAoB;YACtB;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM;QAIJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YACnC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YACnC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/levelTagService.ts"], "sourcesContent": ["import request from './request';\n\n// 标签数据处理函数，确保所有字段都有合适的默认值\nconst processTagData = (tag: any): LevelTag => ({\n  id: tag.id || '',\n  name: tag.name || '',\n  description: tag.description || '',\n  color: tag.color || '#1890ff',\n  isVip: Bo<PERSON>an(tag.isVip),\n  status: tag.status || 'active',\n  icon: tag.icon,\n  createdAt: tag.createdAt || new Date().toISOString(),\n  updatedAt: tag.updatedAt || new Date().toISOString(),\n});\n\n// 标签数据类型\nexport interface LevelTag {\n  id: string;\n  name: string;\n  description?: string;\n  color: string;\n  isVip: boolean;\n  status: string; // 'active' | 'inactive'\n  icon?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n\n\n// 创建标签参数\nexport interface CreateLevelTagParams {\n  name: string;\n  description?: string;\n  color?: string;\n  isVip?: boolean;\n  status?: string; // 'active' | 'inactive'\n  icon?: string;\n}\n\n// 更新标签参数\nexport interface UpdateLevelTagParams {\n  name?: string;\n  description?: string;\n  color?: string;\n  isVip?: boolean;\n  status?: string; // 'active' | 'inactive'\n  icon?: string;\n}\n\n// 标签管理服务\nexport const levelTagService = {\n  // 获取所有标签\n  getAll: async (): Promise<LevelTag[]> => {\n    const response = await request.get<any[]>('/tags');\n    // 确保每个标签都有正确的字段和默认值\n    const tags = response.data || [];\n    return tags.map(processTagData);\n  },\n\n  // 根据ID获取标签\n  getById: async (id: string): Promise<LevelTag> => {\n    const response = await request.get<any>(`/tags/${id}`);\n    return processTagData(response.data);\n  },\n\n  // 创建标签\n  create: async (params: CreateLevelTagParams): Promise<LevelTag> => {\n    const response = await request.post<any>('/tags', params);\n    return processTagData(response.data);\n  },\n\n  // 更新标签\n  update: async (id: string, params: UpdateLevelTagParams): Promise<LevelTag> => {\n    const response = await request.put<any>(`/tags/${id}`, params);\n    return processTagData(response.data);\n  },\n\n  // 删除标签\n  delete: async (id: string): Promise<void> => {\n    await request.delete(`/tags/${id}`);\n  },\n};\n\n// 保持向后兼容的导出\nexport const getLevelTags = levelTagService.getAll;\nexport const createLevelTag = levelTagService.create;\nexport const updateLevelTag = levelTagService.update;\nexport const deleteLevelTag = levelTagService.delete;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,0BAA0B;AAC1B,MAAM,iBAAiB,CAAC,MAAuB,CAAC;QAC9C,IAAI,IAAI,EAAE,IAAI;QACd,MAAM,IAAI,IAAI,IAAI;QAClB,aAAa,IAAI,WAAW,IAAI;QAChC,OAAO,IAAI,KAAK,IAAI;QACpB,OAAO,QAAQ,IAAI,KAAK;QACxB,QAAQ,IAAI,MAAM,IAAI;QACtB,MAAM,IAAI,IAAI;QACd,WAAW,IAAI,SAAS,IAAI,IAAI,OAAO,WAAW;QAClD,WAAW,IAAI,SAAS,IAAI,IAAI,OAAO,WAAW;IACpD,CAAC;AAsCM,MAAM,kBAAkB;IAC7B,SAAS;IACT,QAAQ;QACN,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAQ;QAC1C,oBAAoB;QACpB,MAAM,OAAO,SAAS,IAAI,IAAI,EAAE;QAChC,OAAO,KAAK,GAAG,CAAC;IAClB;IAEA,WAAW;IACX,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAM,CAAC,MAAM,EAAE,IAAI;QACrD,OAAO,eAAe,SAAS,IAAI;IACrC;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAM,SAAS;QAClD,OAAO,eAAe,SAAS,IAAI;IACrC;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAM,CAAC,MAAM,EAAE,IAAI,EAAE;QACvD,OAAO,eAAe,SAAS,IAAI;IACrC;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI;IACpC;AACF;AAGO,MAAM,eAAe,gBAAgB,MAAM;AAC3C,MAAM,iBAAiB,gBAAgB,MAAM;AAC7C,MAAM,iBAAiB,gBAAgB,MAAM;AAC7C,MAAM,iBAAiB,gBAAgB,MAAM", "debugId": null}}, {"offset": {"line": 1450, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/userStarService.ts"], "sourcesContent": ["import request from \"./request\";\n\n// 用户星级数据类型（基于API文档UserStarItemDto）\nexport interface UserStar {\n  id: string;\n  userId: string;\n  levelId: string;\n  stars: number; // 1-5星级评分\n  completedAt: string; // ISO日期时间格式\n  playTime: number; // 游戏时长（秒）\n  user?: {\n    id: string;\n    nickname: string;\n    avatar?: string;\n  };\n  level?: {\n    id: string;\n    title: string;\n    difficulty: number;\n  };\n}\n\n// 用户星级列表响应类型（基于API文档UserStarListResponseDto）\nexport interface UserStarListResponse {\n  data: UserStar[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\n// 删除统计相关类型定义\n\n// 查询参数类型（基于API文档接口参数）\nexport interface UserStarQueryParams {\n  startDate?: string; // 开始日期，格式：YYYY-MM-DD\n  endDate?: string; // 结束日期，格式：YYYY-MM-DD\n  levelId?: string; // 关卡ID\n  stars?: number; // 星级筛选，1-5\n  userId?: string; // 用户ID\n  page?: number; // 页码，默认1\n  pageSize?: number; // 每页数量，默认20，最大100\n}\n\n// 删除统计查询参数类型\n\n// 导出参数类型\nexport interface UserStarExportParams {\n  startDate?: string;\n  endDate?: string;\n  levelId?: string;\n  stars?: number;\n  userId?: string;\n  format?: \"csv\" | \"excel\";\n}\n\n// 用户星级管理服务\nexport const userStarService = {\n  // 获取用户星级数据（基于API文档：GET /api/v1/admin/user-stars）\n  getAll: async (\n    params?: UserStarQueryParams\n  ): Promise<UserStarListResponse> => {\n    const response = await request.get<UserStarListResponse>(\"/user-stars\", {\n      params,\n    });\n    return response.data;\n  },\n\n  // 删除用户星级统计方法\n\n  // 导出用户星级数据（基于API文档：GET /api/v1/admin/user-stars/export）\n  exportData: async (params?: UserStarExportParams): Promise<Blob> => {\n    const response = await request.get(\"/user-stars/export\", {\n      params,\n      responseType: \"blob\",\n    });\n    return response.data;\n  },\n\n  // 删除关卡星级分析方法\n};\n\n// 保持向后兼容的导出\nexport const getUserStars = userStarService.getAll;\n"], "names": [], "mappings": ";;;;AAAA;;AAyDO,MAAM,kBAAkB;IAC7B,iDAAiD;IACjD,QAAQ,OACN;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAuB,eAAe;YACtE;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IAEb,wDAAwD;IACxD,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;YACvD;YACA,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;AAGF;AAGO,MAAM,eAAe,gBAAgB,MAAM", "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/userFavoriteService.ts"], "sourcesContent": ["import request from \"./request\";\n\n// 用户收藏数据类型（基于API文档UserFavoriteAdminItemDto）\nexport interface UserFavorite {\n  id: string;\n  userId: string;\n  levelId: string;\n  createdAt: string; // ISO日期时间格式\n  user?: {\n    id: string;\n    nickname: string;\n    avatar?: string;\n  };\n  level?: {\n    id: string;\n    title: string;\n    difficulty: number;\n    isVip?: boolean;\n  };\n}\n\n// 用户收藏列表响应类型（基于API文档UserFavoriteAdminListResponseDto）\nexport interface UserFavoriteListResponse {\n  data: UserFavorite[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\n// 删除统计相关类型定义\n\n// 查询参数类型（基于API文档接口参数）\nexport interface UserFavoriteQueryParams {\n  startDate?: string; // 开始日期，格式：YYYY-MM-DD\n  endDate?: string; // 结束日期，格式：YYYY-MM-DD\n  levelId?: string; // 关卡ID\n  userId?: string; // 用户ID\n  difficulty?: number; // 关卡难度，1-5\n  page?: number; // 页码，默认1\n  pageSize?: number; // 每页数量，默认20，最大100\n}\n\n// 删除统计查询参数类型\n\n// 导出参数类型\nexport interface UserFavoriteExportParams {\n  startDate?: string;\n  endDate?: string;\n  levelId?: string;\n  userId?: string;\n  difficulty?: number;\n  format?: \"csv\" | \"excel\";\n}\n\n// 用户收藏管理服务\nexport const userFavoriteService = {\n  // 获取用户收藏数据（基于API文档：GET /api/v1/admin/user-favorites）\n  getAll: async (\n    params?: UserFavoriteQueryParams\n  ): Promise<UserFavoriteListResponse> => {\n    const response = await request.get<UserFavoriteListResponse>(\n      \"/user-favorites\",\n      { params }\n    );\n    return response.data;\n  },\n\n  // 删除收藏统计方法\n\n  // 导出收藏数据（基于API文档：GET /api/v1/admin/user-favorites/export）\n  exportData: async (params?: UserFavoriteExportParams): Promise<Blob> => {\n    const response = await request.get(\"/user-favorites/export\", {\n      params,\n      responseType: \"blob\",\n    });\n    return response.data;\n  },\n};\n\n// 保持向后兼容的导出\nexport const getUserFavorites = userFavoriteService.getAll;\n"], "names": [], "mappings": ";;;;AAAA;;AAwDO,MAAM,sBAAsB;IACjC,qDAAqD;IACrD,QAAQ,OACN;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAChC,mBACA;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IAEX,0DAA0D;IAC1D,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B;YAC3D;YACA,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,mBAAmB,oBAAoB,MAAM", "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/activationCodeService.ts"], "sourcesContent": ["import { api, successApi } from \"./request\";\n\n// 激活码数据类型（根据B端API文档）\nexport interface ActivationCode {\n  code: string;\n  packageId: string;\n  packageName: string;\n  status: string; // 'unused' | 'used' | 'expired' | 'disabled'\n  maxRedemptions: number; // 最大兑换次数，-1表示无限次\n  currentRedemptions: number; // 当前已兑换次数\n  redemptionHistory?: string[]; // 兑换记录\n  usedBy?: string; // 已废弃，保留兼容性\n  usedAt?: string; // 已废弃，保留兼容性\n  expireDate: string;\n  source?: string;\n  batchId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 生成激活码参数（根据B端API文档）\nexport interface GenerateActivationCodeParams {\n  packageId: string;\n  count: number;\n  expireDate?: string;\n  source?: string;\n}\n\n// 手动创建激活码参数\nexport interface CreateActivationCodeParams {\n  customCode?: string; // 自定义激活码，如果不提供则自动生成\n  packageId: string;\n  expireDate?: string;\n  source?: string;\n  maxRedemptions?: number; // 最大兑换次数，-1表示无限次，默认为1\n}\n\n// 删除激活码参数\nexport interface DeleteActivationCodeParams {\n  reason?: string; // 删除原因\n}\n\n// 批量生成激活码参数\nexport interface BatchGenerateParams {\n  packageId: string;\n  count: number;\n  expireDate?: string;\n  source?: string;\n  prefix?: string; // 激活码前缀\n  maxRedemptions?: number; // 最大兑换次数，-1表示无限次，默认为1\n}\n\n// 套餐数据类型\nexport interface Package {\n  id: string;\n  name: string;\n  description?: string;\n  type: string;\n  duration: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数类型（根据B端API文档）\nexport interface ActivationCodeQueryParams {\n  status?: \"unused\" | \"used\" | \"expired\" | \"disabled\";\n  packageId?: string;\n  startDate?: string;\n  endDate?: string;\n  page?: number;\n  pageSize?: number;\n}\n\n// 激活码管理服务\nexport const activationCodeService = {\n  // 获取所有激活码\n  getAll: async (\n    params?: ActivationCodeQueryParams\n  ): Promise<{\n    codes: ActivationCode[];\n    total: number;\n  }> => {\n    const response = await api.get(\"/activation-codes\", { params });\n    return response.data;\n  },\n\n  // 根据激活码获取详情\n  getByCode: async (code: string): Promise<ActivationCode> => {\n    const response = await api.get<ActivationCode>(`/activation-codes/${code}`);\n    return response.data;\n  },\n\n  // 生成激活码\n  generate: async (\n    params: GenerateActivationCodeParams\n  ): Promise<{\n    codes: string[];\n    batchId: string;\n    count: number;\n    package: Package;\n  }> => {\n    const response = await successApi.post(\n      \"/activation-codes/generate\",\n      params,\n      \"激活码生成成功\"\n    );\n    return response.data;\n  },\n\n  // 手动创建单个激活码\n  create: async (\n    params: CreateActivationCodeParams\n  ): Promise<ActivationCode> => {\n    const response = await successApi.post<ActivationCode>(\n      \"/activation-codes/create\",\n      params,\n      \"激活码创建成功\"\n    );\n    return response.data;\n  },\n\n  // 批量生成激活码\n  batchGenerate: async (\n    params: BatchGenerateParams\n  ): Promise<{\n    codes: string[];\n    batchId: string;\n    count: number;\n    package: Package;\n    successCount: number;\n    failedCount: number;\n  }> => {\n    const response = await successApi.post(\n      \"/activation-codes/generate\",\n      params,\n      \"批量生成激活码成功\"\n    );\n    return response.data;\n  },\n\n  // 禁用激活码\n  disable: async (code: string): Promise<void> => {\n    await successApi.put(\n      `/activation-codes/${code}/disable`,\n      {},\n      \"激活码已禁用\"\n    );\n  },\n\n  // 启用激活码\n  enable: async (code: string): Promise<void> => {\n    await successApi.put(\n      `/activation-codes/${code}/enable`,\n      {},\n      \"激活码已启用\"\n    );\n  },\n\n  // 删除统计相关方法\n\n  // 获取所有套餐（VIP套餐，用于激活码兑换）\n  getAllPackages: async (): Promise<Package[]> => {\n    const response = await api.get<Package[]>(\"/payment/packages\");\n    return response.data || [];\n  },\n\n  // 获取热门套餐\n  getPopularPackages: async (limit?: number): Promise<Package[]> => {\n    const response = await api.get<Package[]>(\"/payment/packages/popular\", {\n      params: { limit },\n    });\n    return response.data;\n  },\n\n  // 删除激活码\n  delete: async (\n    code: string,\n    params?: DeleteActivationCodeParams\n  ): Promise<{ message: string }> => {\n    const response = await successApi.delete<{ message: string }>(\n      `/activation-codes/${code}`,\n      \"激活码删除成功\",\n      {\n        data: params || {},\n      }\n    );\n    return response.data;\n  },\n};\n\n// 保持向后兼容的导出\nexport const getActivationCodes = activationCodeService.getAll;\nexport const generateActivationCodes = activationCodeService.generate;\nexport const createActivationCode = activationCodeService.create;\nexport const batchGenerateActivationCodes = activationCodeService.batchGenerate;\nexport const disableActivationCode = activationCodeService.disable;\nexport const enableActivationCode = activationCodeService.enable;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AA2EO,MAAM,wBAAwB;IACnC,UAAU;IACV,QAAQ,OACN;QAKA,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAC,qBAAqB;YAAE;QAAO;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAiB,CAAC,kBAAkB,EAAE,MAAM;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,UAAU,OACR;QAOA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,8BACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,QAAQ,OACN;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,4BACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,eAAe,OACb;QASA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,IAAI,CACpC,8BACA,QACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,SAAS,OAAO;QACd,MAAM,0HAAA,CAAA,aAAU,CAAC,GAAG,CAClB,CAAC,kBAAkB,EAAE,KAAK,QAAQ,CAAC,EACnC,CAAC,GACD;IAEJ;IAEA,QAAQ;IACR,QAAQ,OAAO;QACb,MAAM,0HAAA,CAAA,aAAU,CAAC,GAAG,CAClB,CAAC,kBAAkB,EAAE,KAAK,OAAO,CAAC,EAClC,CAAC,GACD;IAEJ;IAEA,WAAW;IAEX,wBAAwB;IACxB,gBAAgB;QACd,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAY;QAC1C,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA,SAAS;IACT,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,MAAG,CAAC,GAAG,CAAY,6BAA6B;YACrE,QAAQ;gBAAE;YAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,QAAQ,OACN,MACA;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,MAAM,CACtC,CAAC,kBAAkB,EAAE,MAAM,EAC3B,WACA;YACE,MAAM,UAAU,CAAC;QACnB;QAEF,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,qBAAqB,sBAAsB,MAAM;AACvD,MAAM,0BAA0B,sBAAsB,QAAQ;AAC9D,MAAM,uBAAuB,sBAAsB,MAAM;AACzD,MAAM,+BAA+B,sBAAsB,aAAa;AACxE,MAAM,wBAAwB,sBAAsB,OAAO;AAC3D,MAAM,uBAAuB,sBAAsB,MAAM", "debugId": null}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/services/index.ts"], "sourcesContent": ["// 统一导出所有API服务\r\nexport * from './request';\r\nexport * from './authService';\r\nexport * from './phraseService';\r\nexport * from './thesaurusService';\r\nexport * from './levelService';\r\nexport * from './userService';\r\nexport * from './shareService';\r\nexport * from './vipService';\r\nexport * from './settingsService';\r\nexport * from './levelTagService';\r\nexport * from './userStarService';\r\nexport * from './userFavoriteService';\r\nexport * from './activationCodeService';\r\n\r\n// 也可以作为默认导出\r\nimport { authService } from './authService';\r\nimport { phraseService } from './phraseService';\r\nimport { thesaurusService } from './thesaurusService';\r\nimport { levelService } from './levelService';\r\nimport { userService } from './userService';\r\nimport { ShareService } from './shareService';\r\nimport { settingsService } from './settingsService';\r\nimport { levelTagService } from './levelTagService';\r\nimport { userStarService } from './userStarService';\r\nimport { userFavoriteService } from './userFavoriteService';\r\nimport { activationCodeService } from './activationCodeService';\r\n\r\nexport const services = {\r\n  auth: authService,\r\n  phrase: phraseService,\r\n  thesaurus: thesaurusService,\r\n  level: levelService,\r\n  user: userService,\r\n  share: ShareService,\r\n  settings: settingsService,\r\n  levelTag: levelTagService,\r\n  userStar: userStarService,\r\n  userFavorite: userFavoriteService,\r\n  activationCode: activationCodeService,\r\n};\r\n"], "names": [], "mappings": "AAAA,cAAc;;;;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AAeO,MAAM,WAAW;IACtB,MAAM,8HAAA,CAAA,cAAW;IACjB,QAAQ,gIAAA,CAAA,gBAAa;IACrB,WAAW,mIAAA,CAAA,mBAAgB;IAC3B,OAAO,+HAAA,CAAA,eAAY;IACnB,MAAM,8HAAA,CAAA,cAAW;IACjB,OAAO,+HAAA,CAAA,eAAY;IACnB,UAAU,kIAAA,CAAA,kBAAe;IACzB,UAAU,kIAAA,CAAA,kBAAe;IACzB,UAAU,kIAAA,CAAA,kBAAe;IACzB,cAAc,sIAAA,CAAA,sBAAmB;IACjC,gBAAgB,wIAAA,CAAA,wBAAqB;AACvC", "debugId": null}}, {"offset": {"line": 1675, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/web/other/yyddp/admin/src/app/%28admin%29/levels/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, Suspense } from 'react';\r\nimport { Card, Table, Button, Space, Tag, Statistic, Row, Col, Select, Popconfirm, Rate, Tooltip, Input } from 'antd';\r\nimport { Modal, message } from '@/components';\r\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, Bar<PERSON><PERSON>Outlined, StarOutlined, SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { levelService, levelTagService, Level, LevelStarAnalytics, LevelQueryParams, LevelListResponse } from '@/services';\r\nimport { LevelTag } from '@/services/levelTagService';\r\nimport type { ColumnsType } from 'antd/es/table';\r\n\r\nfunction LevelsPageContent() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [levels, setLevels] = useState<Level[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [levelStats, setLevelStats] = useState<{ total: number; maxLevels: number; remaining: number } | null>(null);\r\n  const [tags, setTags] = useState<LevelTag[]>([]);\r\n  const [starAnalytics, setStarAnalytics] = useState<Record<string, LevelStarAnalytics>>({});\r\n  const [loadingStars, setLoadingStars] = useState<Record<string, boolean>>({});\r\n  const [highlightLevelId, setHighlightLevelId] = useState<string | null>(null);\r\n\r\n  // 筛选状态\r\n  const [searchText, setSearchText] = useState<string>('');\r\n  const [difficultyFilter, setDifficultyFilter] = useState<number | undefined>(undefined);\r\n  const [tagFilter, setTagFilter] = useState<string | undefined>(undefined);\r\n\r\n  // 分页状态\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 20,\r\n    total: 0,\r\n    totalPages: 0\r\n  });\r\n\r\n  // 获取关卡列表\r\n  const fetchLevels = async (params?: LevelQueryParams) => {\r\n    setLoading(true);\r\n    try {\r\n      const queryParams: LevelQueryParams = {\r\n        search: searchText || undefined,\r\n        difficulty: difficultyFilter,\r\n        tagId: tagFilter,\r\n        page: params?.page || pagination.current,\r\n        pageSize: params?.pageSize || pagination.pageSize,\r\n        ...params\r\n      };\r\n\r\n      const response = await levelService.getAll(queryParams);\r\n      setLevels(response.levels);\r\n      setPagination({\r\n        current: response.page,\r\n        pageSize: response.pageSize,\r\n        total: response.total,\r\n        totalPages: response.totalPages\r\n      });\r\n    } catch (error) {\r\n      message.error('获取关卡列表失败');\r\n      // 使用模拟数据作为后备\r\n      setLevels([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取关卡统计\r\n  const fetchLevelStats = async () => {\r\n    try {\r\n      const stats = await levelService.getCount();\r\n      setLevelStats(stats);\r\n    } catch (error) {\r\n      message.error('获取关卡统计失败');\r\n    }\r\n  };\r\n\r\n  // 获取标签列表\r\n  const fetchTags = async () => {\r\n    try {\r\n      const data = await levelTagService.getAll();\r\n      setTags(data.filter(tag => tag.status === 'active'));\r\n    } catch (error) {\r\n      console.error('获取标签列表失败:', error);\r\n    }\r\n  };\r\n\r\n  // 获取关卡星级统计\r\n  const fetchLevelStarAnalytics = async (levelId: string) => {\r\n    if (starAnalytics[levelId] || loadingStars[levelId]) {\r\n      return; // 已有数据或正在加载，避免重复请求\r\n    }\r\n\r\n    setLoadingStars(prev => ({ ...prev, [levelId]: true }));\r\n    try {\r\n      const analytics = await levelService.getLevelStarAnalytics(levelId);\r\n      setStarAnalytics(prev => ({ ...prev, [levelId]: analytics }));\r\n    } catch (error) {\r\n      console.error('获取关卡星级统计失败:', error);\r\n      // 静默失败，不显示错误消息，因为这是辅助信息\r\n    } finally {\r\n      setLoadingStars(prev => ({ ...prev, [levelId]: false }));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchLevels();\r\n    fetchLevelStats();\r\n    fetchTags();\r\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  // 当筛选条件改变时重新获取数据\r\n  useEffect(() => {\r\n    fetchLevels({ page: 1 }); // 重置到第一页\r\n  }, [searchText, difficultyFilter, tagFilter]); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  // 处理URL参数中的highlight\r\n  useEffect(() => {\r\n    const highlightParam = searchParams.get('highlight');\r\n    if (highlightParam) {\r\n      setHighlightLevelId(highlightParam);\r\n      // 3秒后清除高亮\r\n      setTimeout(() => {\r\n        setHighlightLevelId(null);\r\n      }, 3000);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // 处理删除关卡\r\n  const handleDelete = async (id: string) => {\r\n    try {\r\n      await levelService.delete(id);\r\n      message.success('关卡删除成功');\r\n      fetchLevels();\r\n      fetchLevelStats();\r\n    } catch (error) {\r\n      message.error('删除关卡失败');\r\n    }\r\n  };\r\n\r\n  // 处理搜索\r\n  const handleSearch = (value: string) => {\r\n    setSearchText(value);\r\n  };\r\n\r\n  // 重置筛选\r\n  const handleReset = () => {\r\n    setSearchText('');\r\n    setDifficultyFilter(undefined);\r\n    setTagFilter(undefined);\r\n  };\r\n\r\n  // 处理分页变化\r\n  const handleTableChange = (page: number, pageSize?: number) => {\r\n    fetchLevels({ page, pageSize });\r\n  };\r\n\r\n  // 查看关卡详情\r\n  const handleViewDetails = async (id: string) => {\r\n    try {\r\n      console.log('正在获取关卡详情，ID:', id);\r\n      const levelWithPhrases = await levelService.getWithPhrases(id);\r\n      console.log('获取到的关卡详情:', levelWithPhrases);\r\n\r\n      Modal.info({\r\n        title: `关卡详情 - ${levelWithPhrases.name}`,\r\n        width: 800,\r\n        content: (\r\n          <div>\r\n            <p><strong>难度:</strong> {levelWithPhrases.difficulty}</p>\r\n            <p><strong>描述:</strong> {levelWithPhrases.description || '无'}</p>\r\n            <p><strong>词组数量:</strong> {levelWithPhrases.phrases?.length || 0}</p>\r\n\r\n            {/* 显示关卡标签 */}\r\n            <div style={{ marginBottom: 16 }}>\r\n              <strong>关卡标签:</strong>\r\n              <div style={{ marginTop: 8 }}>\r\n                {levelWithPhrases.tagIds && levelWithPhrases.tagIds.length > 0 ? (\r\n                  levelWithPhrases.tagIds.map((tagId: string) => {\r\n                    const tag = tags.find(t => t.id === tagId);\r\n                    return tag ? (\r\n                      <Tag\r\n                        key={tagId}\r\n                        color={tag.color}\r\n                        style={{ margin: 4 }}\r\n                      >\r\n                        {tag.name}\r\n                        {tag.isVip && <span style={{ marginLeft: 4, fontSize: '10px' }}>VIP</span>}\r\n                      </Tag>\r\n                    ) : null;\r\n                  })\r\n                ) : (\r\n                  <span style={{ color: '#999' }}>无标签</span>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <strong>包含的词组:</strong>\r\n              <div style={{ marginTop: 8, maxHeight: 300, overflow: 'auto' }}>\r\n                {levelWithPhrases.phrases && levelWithPhrases.phrases.length > 0 ? (\r\n                  levelWithPhrases.phrases.map((phrase: any) => (\r\n                    <Tag key={phrase.id} style={{ margin: 4 }}>\r\n                      {phrase.text}\r\n                    </Tag>\r\n                  ))\r\n                ) : (\r\n                  <span style={{ color: '#999' }}>暂无词组</span>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ),\r\n      });\r\n    } catch (error: any) {\r\n      console.error('获取关卡详情失败:', error);\r\n      message.error(`获取关卡详情失败: ${error.response?.data?.message || error.message || '未知错误'}`);\r\n    }\r\n  };\r\n\r\n  const getDifficultyColor = (difficulty: number) => {\r\n    const colors = ['', 'green', 'blue', 'orange', 'red', 'purple'];\r\n    return colors[difficulty] || 'default';\r\n  };\r\n\r\n  const columns: ColumnsType<Level> = [\r\n    {\r\n      title: '关卡名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n      width: 200,\r\n    },\r\n    {\r\n      title: '词组数量',\r\n      dataIndex: 'phraseIds',\r\n      key: 'phraseCount',\r\n      width: 100,\r\n      render: (phraseIds: string[]) => phraseIds.length,\r\n    },\r\n    {\r\n      title: '标签',\r\n      dataIndex: 'tagIds',\r\n      key: 'tags',\r\n      width: 200,\r\n      render: (tagIds: string[]) => {\r\n        if (!tagIds || tagIds.length === 0) {\r\n          return <span style={{ color: '#999' }}>无标签</span>;\r\n        }\r\n        return (\r\n          <div>\r\n            {tagIds.slice(0, 3).map(tagId => {\r\n              const tag = tags.find(t => t.id === tagId);\r\n              if (!tag) return null;\r\n              return (\r\n                <Tag\r\n                  key={tagId}\r\n                  color={tag.color}\r\n                  style={{ marginBottom: 4 }}\r\n                >\r\n                  {tag.name}\r\n                  {tag.isVip && <span style={{ marginLeft: 4, fontSize: '10px' }}>VIP</span>}\r\n                </Tag>\r\n              );\r\n            })}\r\n            {tagIds.length > 3 && (\r\n              <Tag color=\"default\">+{tagIds.length - 3}</Tag>\r\n            )}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: '创建时间',\r\n      dataIndex: 'createdAt',\r\n      key: 'createdAt',\r\n      width: 180,\r\n    },\r\n    {\r\n      title: '星级统计',\r\n      key: 'starAnalytics',\r\n      width: 150,\r\n      render: (_, record) => {\r\n        const analytics = starAnalytics[record.id];\r\n        const isLoading = loadingStars[record.id];\r\n\r\n        if (isLoading) {\r\n          return <span style={{ color: '#999' }}>加载中...</span>;\r\n        }\r\n\r\n        if (!analytics) {\r\n          return (\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              icon={<StarOutlined />}\r\n              onClick={() => fetchLevelStarAnalytics(record.id)}\r\n            >\r\n              查看星级\r\n            </Button>\r\n          );\r\n        }\r\n\r\n        return (\r\n          <div>\r\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>\r\n              <Rate\r\n                disabled\r\n                value={analytics.averageStars}\r\n                allowHalf\r\n                style={{ fontSize: 12 }}\r\n              />\r\n              <span style={{ marginLeft: 4, fontSize: 12, color: '#666' }}>\r\n                {analytics.averageStars.toFixed(1)}\r\n              </span>\r\n            </div>\r\n            <div style={{ fontSize: 11, color: '#999' }}>\r\n              完成率: {(analytics.completionRate * 100).toFixed(1)}%\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: 250,\r\n      render: (_, record) => (\r\n        <Space size=\"small\" wrap>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<EyeOutlined />}\r\n            onClick={() => handleViewDetails(record.id)}\r\n          >\r\n            详情\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<EditOutlined />}\r\n            onClick={() => router.push(`/levels/${record.id}/edit`)}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Tooltip title=\"查看该关卡的星级数据\">\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              icon={<BarChartOutlined />}\r\n              onClick={() => router.push(`/user-stars?levelId=${record.id}`)}\r\n            >\r\n              星级\r\n            </Button>\r\n          </Tooltip>\r\n          <Popconfirm\r\n            title=\"确定要删除这个关卡吗？\"\r\n            onConfirm={() => handleDelete(record.id)}\r\n            okText=\"确定\"\r\n            cancelText=\"取消\"\r\n          >\r\n            <Button type=\"link\" danger size=\"small\" icon={<DeleteOutlined />}>\r\n              删除\r\n            </Button>\r\n          </Popconfirm>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div>\r\n      <style jsx global>{`\r\n        .highlighted-row {\r\n          background-color: #fff7e6 !important;\r\n          transition: background-color 3s ease-out;\r\n        }\r\n\r\n        .highlighted-row:hover {\r\n          background-color: #ffd591 !important;\r\n        }\r\n      `}</style>\r\n      {/* 统计卡片 */}\r\n      {levelStats && (\r\n        <Row gutter={16} style={{ marginBottom: 16 }}>\r\n          <Col span={8}>\r\n            <Card>\r\n              <Statistic\r\n                title=\"当前关卡总数\"\r\n                value={levelStats.total}\r\n                prefix={<BarChartOutlined />}\r\n              />\r\n            </Card>\r\n          </Col>\r\n          <Col span={8}>\r\n            <Card>\r\n              <Statistic\r\n                title=\"最大关卡限制\"\r\n                value={levelStats.maxLevels}\r\n                prefix={<BarChartOutlined />}\r\n              />\r\n            </Card>\r\n          </Col>\r\n          <Col span={8}>\r\n            <Card>\r\n              <Statistic\r\n                title=\"剩余可创建\"\r\n                value={levelStats.remaining}\r\n                prefix={<BarChartOutlined />}\r\n                valueStyle={{ color: levelStats.remaining > 0 ? '#3f8600' : '#cf1322' }}\r\n              />\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      )}\r\n\r\n      {/* 主要内容 */}\r\n      <Card>\r\n        <div style={{ marginBottom: 16 }}>\r\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n            <h2>关卡管理</h2>\r\n            <Button\r\n              type=\"primary\"\r\n              icon={<PlusOutlined />}\r\n              onClick={() => router.push('/levels/create')}\r\n              disabled={levelStats?.remaining === 0}\r\n            >\r\n              创建关卡\r\n            </Button>\r\n          </div>\r\n\r\n          {/* 筛选区域 */}\r\n          <div style={{ display: 'flex', gap: 16, alignItems: 'center', flexWrap: 'wrap' }}>\r\n            <Input.Search\r\n              placeholder=\"搜索关卡名称或描述\"\r\n              allowClear\r\n              style={{ width: 250 }}\r\n              value={searchText}\r\n              onChange={(e) => setSearchText(e.target.value)}\r\n              onSearch={handleSearch}\r\n              enterButton={<SearchOutlined />}\r\n            />\r\n            <Select\r\n              placeholder=\"按难度筛选\"\r\n              allowClear\r\n              style={{ width: 120 }}\r\n              value={difficultyFilter}\r\n              onChange={setDifficultyFilter}\r\n              options={[\r\n                { label: '1级', value: 1 },\r\n                { label: '2级', value: 2 },\r\n                { label: '3级', value: 3 },\r\n                { label: '4级', value: 4 },\r\n                { label: '5级', value: 5 },\r\n              ]}\r\n            />\r\n            <Select\r\n              placeholder=\"按标签筛选\"\r\n              allowClear\r\n              style={{ width: 150 }}\r\n              value={tagFilter}\r\n              onChange={setTagFilter}\r\n              options={tags.map(tag => ({\r\n                label: (\r\n                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>\r\n                    <Tag color={tag.color} style={{ margin: 0 }}>{tag.name}</Tag>\r\n                    {tag.isVip && <Tag color=\"gold\" style={{ margin: 0, fontSize: '10px', padding: '0 4px' }}>VIP</Tag>}\r\n                  </div>\r\n                ),\r\n                value: tag.id,\r\n              }))}\r\n            />\r\n            <Button icon={<ReloadOutlined />} onClick={handleReset}>\r\n              重置\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <Table\r\n          columns={columns}\r\n          dataSource={levels}\r\n          rowKey=\"id\"\r\n          loading={loading}\r\n          rowClassName={(record) =>\r\n            highlightLevelId === record.id ? 'highlighted-row' : ''\r\n          }\r\n          pagination={{\r\n            current: pagination.current,\r\n            pageSize: pagination.pageSize,\r\n            total: pagination.total,\r\n            showSizeChanger: true,\r\n            showQuickJumper: true,\r\n            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,\r\n            onChange: handleTableChange,\r\n            onShowSizeChange: handleTableChange,\r\n            pageSizeOptions: ['10', '20', '50', '100'],\r\n          }}\r\n        />\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function LevelsPage() {\r\n  return (\r\n    <Suspense fallback={<div>Loading...</div>}>\r\n      <LevelsPageContent />\r\n    </Suspense>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;;AAWA,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkE;IAC7G,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC,CAAC;IACxF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,OAAO;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAE/D,OAAO;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,SAAS;QACT,UAAU;QACV,OAAO;QACP,YAAY;IACd;IAEA,SAAS;IACT,MAAM,cAAc,OAAO;QACzB,WAAW;QACX,IAAI;YACF,MAAM,cAAgC;gBACpC,QAAQ,cAAc;gBACtB,YAAY;gBACZ,OAAO;gBACP,MAAM,QAAQ,QAAQ,WAAW,OAAO;gBACxC,UAAU,QAAQ,YAAY,WAAW,QAAQ;gBACjD,GAAG,MAAM;YACX;YAEA,MAAM,WAAW,MAAM,+HAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAC3C,UAAU,SAAS,MAAM;YACzB,cAAc;gBACZ,SAAS,SAAS,IAAI;gBACtB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,YAAY,SAAS,UAAU;YACjC;QACF,EAAE,OAAO,OAAO;YACd,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,aAAa;YACb,UAAU,EAAE;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,MAAM,+HAAA,CAAA,eAAY,CAAC,QAAQ;YACzC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,OAAO,MAAM,kIAAA,CAAA,kBAAe,CAAC,MAAM;YACzC,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,WAAW;IACX,MAAM,0BAA0B,OAAO;QACrC,IAAI,aAAa,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,EAAE;YACnD,QAAQ,mBAAmB;QAC7B;QAEA,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,QAAQ,EAAE;YAAK,CAAC;QACrD,IAAI;YACF,MAAM,YAAY,MAAM,+HAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC;YAC3D,iBAAiB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,QAAQ,EAAE;gBAAU,CAAC;QAC7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,wBAAwB;QAC1B,SAAU;YACR,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,QAAQ,EAAE;gBAAM,CAAC;QACxD;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA;IACF,GAAG,EAAE,GAAG,kDAAkD;IAE1D,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;YAAE,MAAM;QAAE,IAAI,SAAS;IACrC,GAAG;QAAC;QAAY;QAAkB;KAAU,GAAG,kDAAkD;IAEjG,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,aAAa,GAAG,CAAC;QACxC,IAAI,gBAAgB;YAClB,oBAAoB;YACpB,UAAU;YACV,WAAW;gBACT,oBAAoB;YACtB,GAAG;QACL;IACF,GAAG;QAAC;KAAa;IAEjB,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,+HAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAC1B,4KAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;YACA;QACF,EAAE,OAAO,OAAO;YACd,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,cAAc;IAChB;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,cAAc;QACd,oBAAoB;QACpB,aAAa;IACf;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC,MAAc;QACvC,YAAY;YAAE;YAAM;QAAS;IAC/B;IAEA,SAAS;IACT,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,MAAM,mBAAmB,MAAM,+HAAA,CAAA,eAAY,CAAC,cAAc,CAAC;YAC3D,QAAQ,GAAG,CAAC,aAAa;YAEzB,wKAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gBACT,OAAO,CAAC,OAAO,EAAE,iBAAiB,IAAI,EAAE;gBACxC,OAAO;gBACP,uBACE,8OAAC;;sCACC,8OAAC;;8CAAE,8OAAC;8CAAO;;;;;;gCAAY;gCAAE,iBAAiB,UAAU;;;;;;;sCACpD,8OAAC;;8CAAE,8OAAC;8CAAO;;;;;;gCAAY;gCAAE,iBAAiB,WAAW,IAAI;;;;;;;sCACzD,8OAAC;;8CAAE,8OAAC;8CAAO;;;;;;gCAAc;gCAAE,iBAAiB,OAAO,EAAE,UAAU;;;;;;;sCAG/D,8OAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CAC7B,8OAAC;8CAAO;;;;;;8CACR,8OAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAE;8CACxB,iBAAiB,MAAM,IAAI,iBAAiB,MAAM,CAAC,MAAM,GAAG,IAC3D,iBAAiB,MAAM,CAAC,GAAG,CAAC,CAAC;wCAC3B,MAAM,MAAM,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wCACpC,OAAO,oBACL,8OAAC,4KAAA,CAAA,MAAG;4CAEF,OAAO,IAAI,KAAK;4CAChB,OAAO;gDAAE,QAAQ;4CAAE;;gDAElB,IAAI,IAAI;gDACR,IAAI,KAAK,kBAAI,8OAAC;oDAAK,OAAO;wDAAE,YAAY;wDAAG,UAAU;oDAAO;8DAAG;;;;;;;2CAL3D;;;;mDAOL;oCACN,mBAEA,8OAAC;wCAAK,OAAO;4CAAE,OAAO;wCAAO;kDAAG;;;;;;;;;;;;;;;;;sCAKtC,8OAAC;;8CACC,8OAAC;8CAAO;;;;;;8CACR,8OAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAG,WAAW;wCAAK,UAAU;oCAAO;8CAC1D,iBAAiB,OAAO,IAAI,iBAAiB,OAAO,CAAC,MAAM,GAAG,IAC7D,iBAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC5B,8OAAC,4KAAA,CAAA,MAAG;4CAAiB,OAAO;gDAAE,QAAQ;4CAAE;sDACrC,OAAO,IAAI;2CADJ,OAAO,EAAE;;;;kEAKrB,8OAAC;wCAAK,OAAO;4CAAE,OAAO;wCAAO;kDAAG;;;;;;;;;;;;;;;;;;;;;;;YAM5C;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,4KAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,QAAQ;QACvF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS;YAAC;YAAI;YAAS;YAAQ;YAAU;YAAO;SAAS;QAC/D,OAAO,MAAM,CAAC,WAAW,IAAI;IAC/B;IAEA,MAAM,UAA8B;QAClC;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;QACT;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,YAAwB,UAAU,MAAM;QACnD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC;gBACP,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;oBAClC,qBAAO,8OAAC;wBAAK,OAAO;4BAAE,OAAO;wBAAO;kCAAG;;;;;;gBACzC;gBACA,qBACE,8OAAC;;wBACE,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;4BACtB,MAAM,MAAM,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4BACpC,IAAI,CAAC,KAAK,OAAO;4BACjB,qBACE,8OAAC,4KAAA,CAAA,MAAG;gCAEF,OAAO,IAAI,KAAK;gCAChB,OAAO;oCAAE,cAAc;gCAAE;;oCAExB,IAAI,IAAI;oCACR,IAAI,KAAK,kBAAI,8OAAC;wCAAK,OAAO;4CAAE,YAAY;4CAAG,UAAU;wCAAO;kDAAG;;;;;;;+BAL3D;;;;;wBAQX;wBACC,OAAO,MAAM,GAAG,mBACf,8OAAC,4KAAA,CAAA,MAAG;4BAAC,OAAM;;gCAAU;gCAAE,OAAO,MAAM,GAAG;;;;;;;;;;;;;YAI/C;QACF;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,MAAM,YAAY,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC1C,MAAM,YAAY,YAAY,CAAC,OAAO,EAAE,CAAC;gBAEzC,IAAI,WAAW;oBACb,qBAAO,8OAAC;wBAAK,OAAO;4BAAE,OAAO;wBAAO;kCAAG;;;;;;gBACzC;gBAEA,IAAI,CAAC,WAAW;oBACd,qBACE,8OAAC,kMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,MAAK;wBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBACnB,SAAS,IAAM,wBAAwB,OAAO,EAAE;kCACjD;;;;;;gBAIL;gBAEA,qBACE,8OAAC;;sCACC,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,cAAc;4BAAE;;8CACnE,8OAAC,8KAAA,CAAA,OAAI;oCACH,QAAQ;oCACR,OAAO,UAAU,YAAY;oCAC7B,SAAS;oCACT,OAAO;wCAAE,UAAU;oCAAG;;;;;;8CAExB,8OAAC;oCAAK,OAAO;wCAAE,YAAY;wCAAG,UAAU;wCAAI,OAAO;oCAAO;8CACvD,UAAU,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;sCAGpC,8OAAC;4BAAI,OAAO;gCAAE,UAAU;gCAAI,OAAO;4BAAO;;gCAAG;gCACrC,CAAC,UAAU,cAAc,GAAG,GAAG,EAAE,OAAO,CAAC;gCAAG;;;;;;;;;;;;;YAI1D;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,8OAAC,gMAAA,CAAA,QAAK;oBAAC,MAAK;oBAAQ,IAAI;;sCACtB,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;4BAClB,SAAS,IAAM,kBAAkB,OAAO,EAAE;sCAC3C;;;;;;sCAGD,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;sCACvD;;;;;;sCAGD,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gCACvB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO,EAAE,EAAE;0CAC9D;;;;;;;;;;;sCAIH,8OAAC,0LAAA,CAAA,aAAU;4BACT,OAAM;4BACN,WAAW,IAAM,aAAa,OAAO,EAAE;4BACvC,QAAO;4BACP,YAAW;sCAEX,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAO,MAAM;gCAAC,MAAK;gCAAQ,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;0CAAK;;;;;;;;;;;;;;;;;QAM1E;KACD;IAED,qBACE,8OAAC;;;;;;;YAYE,4BACC,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCACzC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,KAAK;gCACvB,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;kCAI/B,8OAAC,4KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,SAAS;gCAC3B,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;kCAI/B,8OAAC,4KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,SAAS;gCAC3B,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gCACzB,YAAY;oCAAE,OAAO,WAAW,SAAS,GAAG,IAAI,YAAY;gCAAU;;;;;;;;;;;;;;;;;;;;;;0BAQhF,8OAAC,8KAAA,CAAA,OAAI;;kCACH,8OAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;;;0CAC7B,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,gBAAgB;oCAAiB,YAAY;oCAAU,cAAc;gCAAG;;;kDACrG,8OAAC;;kDAAG;;;;;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,UAAU,YAAY,cAAc;kDACrC;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;oCAAI,YAAY;oCAAU,UAAU;gCAAO;;;kDAC7E,8OAAC,gLAAA,CAAA,QAAK,CAAC,MAAM;wCACX,aAAY;wCACZ,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAI;wCACpB,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,UAAU;wCACV,2BAAa,8OAAC,sNAAA,CAAA,iBAAc;;;;;;;;;;kDAE9B,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAI;wCACpB,OAAO;wCACP,UAAU;wCACV,SAAS;4CACP;gDAAE,OAAO;gDAAM,OAAO;4CAAE;4CACxB;gDAAE,OAAO;gDAAM,OAAO;4CAAE;4CACxB;gDAAE,OAAO;gDAAM,OAAO;4CAAE;4CACxB;gDAAE,OAAO;gDAAM,OAAO;4CAAE;4CACxB;gDAAE,OAAO;gDAAM,OAAO;4CAAE;yCACzB;;;;;;kDAEH,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAI;wCACpB,OAAO;wCACP,UAAU;wCACV,SAAS,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gDACxB,qBACE,8OAAC;oDAAI,OAAO;wDAAE,SAAS;wDAAQ,YAAY;wDAAU,KAAK;oDAAE;;;sEAC1D,8OAAC,4KAAA,CAAA,MAAG;4DAAC,OAAO,IAAI,KAAK;4DAAE,OAAO;gEAAE,QAAQ;4DAAE;sEAAI,IAAI,IAAI;;;;;;wDACrD,IAAI,KAAK,kBAAI,8OAAC,4KAAA,CAAA,MAAG;4DAAC,OAAM;4DAAO,OAAO;gEAAE,QAAQ;gEAAG,UAAU;gEAAQ,SAAS;4DAAQ;sEAAG;;;;;;;;;;;;gDAG9F,OAAO,IAAI,EAAE;4CACf,CAAC;;;;;;kDAEH,8OAAC,kMAAA,CAAA,SAAM;wCAAC,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCAAK,SAAS;kDAAa;;;;;;;;;;;;;;;;;;kCAM5D,8OAAC,gLAAA,CAAA,QAAK;wBACJ,SAAS;wBACT,YAAY;wBACZ,QAAO;wBACP,SAAS;wBACT,cAAc,CAAC,SACb,qBAAqB,OAAO,EAAE,GAAG,oBAAoB;wBAEvD,YAAY;4BACV,SAAS,WAAW,OAAO;4BAC3B,UAAU,WAAW,QAAQ;4BAC7B,OAAO,WAAW,KAAK;4BACvB,iBAAiB;4BACjB,iBAAiB;4BACjB,WAAW,CAAC,OAAO,QAAU,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC;4BACzE,UAAU;4BACV,kBAAkB;4BAClB,iBAAiB;gCAAC;gCAAM;gCAAM;gCAAM;6BAAM;wBAC5C;;;;;;;;;;;;;;;;;;AAKV;AAEe,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;sBAAI;;;;;;kBACvB,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}