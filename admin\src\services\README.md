# API 服务使用指南

本项目已经封装了统一的 API 请求方法，包含了完整的错误处理、认证和类型定义。

## 目录结构

```
src/services/
├── request.ts          # 核心请求封装
├── authService.ts      # 认证服务
├── phraseService.ts    # 词组服务
├── thesaurusService.ts # 词库服务
├── levelService.ts     # 关卡服务
├── index.ts           # 统一导出
└── README.md          # 使用说明
```

## 核心特性

### 1. 统一的请求配置
- 自动添加 Bearer Token 认证
- 统一的错误处理和用户提示
- 支持环境变量配置 API 基础 URL
- 10秒请求超时设置

### 2. 完整的类型定义
- 所有 API 请求和响应都有完整的 TypeScript 类型
- 支持泛型，提供更好的类型推断

### 3. 自动错误处理
- 401: 自动清除 token 并跳转到登录页
- 403: 权限不足提示
- 404: 资源不存在提示
- 500: 服务器错误提示
- 网络错误: 网络连接失败提示

## 使用方法

### 基础用法

```typescript
import request from '@/services/request';

// GET 请求
const response = await request.get<DataType>('/endpoint');

// POST 请求
const response = await request.post<ResponseType>('/endpoint', data);

// PUT 请求
const response = await request.put<ResponseType>('/endpoint', data);

// PATCH 请求
const response = await request.patch<ResponseType>('/endpoint', data);

// DELETE 请求
await request.delete('/endpoint');
```

### 使用服务类

```typescript
import { authService, phraseService, thesaurusService, levelService } from '@/services';

// 认证
const loginResponse = await authService.login({ username: 'admin', password: 'password123' });

// 词组管理
const phrases = await phraseService.getAll();
const newPhrase = await phraseService.create({ text: '词组', meaning: '含义' });

// 词库管理
const thesauruses = await thesaurusService.getAll();
const newThesaurus = await thesaurusService.create({ name: '词库名称' });

// 关卡管理
const levels = await levelService.getAll();
const newLevel = await levelService.create({ name: '关卡名称', difficulty: 1, thesaurusIds: [] });
```

### 统一导入

```typescript
import { services } from '@/services';

// 使用服务对象
const phrases = await services.phrase.getAll();
const loginResponse = await services.auth.login({ username: 'admin', password: 'password123' });
```

## 环境配置

在项目根目录创建 `.env.local` 文件：

```env
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001

# 其他配置
NEXT_PUBLIC_APP_NAME=小游戏管理后台
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## 错误处理

所有 API 请求都会自动处理错误，但你也可以手动捕获：

```typescript
try {
  const data = await phraseService.getAll();
  // 处理成功响应
} catch (error) {
  // 错误已经在拦截器中处理并显示给用户
  // 这里可以进行额外的错误处理逻辑
  console.error('API 请求失败:', error);
}
```

## 认证流程

1. 用户登录成功后，token 会自动保存到 localStorage
2. 后续所有请求会自动在请求头中添加 Bearer Token
3. 如果 token 过期（401 错误），会自动清除 token 并跳转到登录页

## 扩展新的服务

创建新的服务文件时，请遵循以下模式：

```typescript
import { api } from './request';

// 定义数据类型
export interface YourDataType {
  id: string;
  name: string;
  // ... 其他字段
}

// 定义请求参数类型
export interface CreateYourDataParams {
  name: string;
  // ... 其他字段
}

// 创建服务对象
export const yourService = {
  getAll: async (): Promise<YourDataType[]> => {
    const response = await request.get<YourDataType[]>('/your-endpoint');
    return response.data;
  },

  create: async (params: CreateYourDataParams): Promise<YourDataType> => {
    const response = await request.post<YourDataType>('/your-endpoint', params);
    return response.data;
  },

  // ... 其他方法
};
```

## 注意事项

1. 所有 API 路径都是相对路径（如 `/users`），baseURL 已配置为包含 `/admin` 前缀
2. 请求和响应数据都应该有完整的 TypeScript 类型定义
3. 错误处理已经统一处理，通常不需要在业务代码中重复处理
4. 认证 token 的管理已经自动化，无需手动处理
