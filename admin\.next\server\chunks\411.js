"use strict";exports.id=411,exports.ids=[411],exports.modules={10411:(e,t,n)=>{n.d(t,{A:()=>eC});var r=n(38770),l=n(78651),a=n(43210),i=n(69662),o=n.n(i),s=n(13934),c=n(50604),u=n(59897);function m(e){let[t,n]=a.useState(e);return a.useEffect(()=>{let t=setTimeout(()=>{n(e)},10*!e.length);return()=>{clearTimeout(t)}},[e]),t}var d=n(42411),p=n(32476),f=n(11908),g=n(98e3),b=n(60254),h=n(13581);let $=e=>{let{componentCls:t}=e,n=`${t}-show-help`,r=`${t}-show-help-item`;return{[n]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[r]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},
                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},
                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${r}-appear, &${r}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${r}-leave-active`]:{transform:"translateY(-5px)"}}}}},y=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${(0,d.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},[`input[type='file']:focus,
  input[type='radio']:focus,
  input[type='checkbox']:focus`]:{outline:0,boxShadow:`0 0 0 ${(0,d.zA)(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),v=(e,t)=>{let{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},x=e=>{let{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},(0,p.dF)(e)),y(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},v(e,e.controlHeightSM)),"&-large":Object.assign({},v(e,e.controlHeightLG))})}},O=e=>{let{formItemCls:t,iconCls:n,rootPrefixCls:r,antCls:l,labelRequiredMarkColor:a,labelColor:i,labelFontSize:o,labelHeight:s,labelColonMarginInlineStart:c,labelColonMarginInlineEnd:u,itemMarginBottom:m}=e;return{[t]:Object.assign(Object.assign({},(0,p.dF)(e)),{marginBottom:m,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,
        &-hidden${l}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset","> label":{verticalAlign:"middle",textWrap:"balance"}},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:s,color:i,fontSize:o,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required`]:{"&::before":{display:"inline-block",marginInlineEnd:e.marginXXS,color:a,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"'},[`&${t}-required-mark-hidden, &${t}-required-mark-optional`]:{"&::before":{display:"none"}}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`&${t}-required-mark-hidden`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:c,marginInlineEnd:u},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${r}-col-'"]):not([class*="' ${r}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%",[`&:has(> ${l}-switch:only-child, > ${l}-rate:only-child)`]:{display:"flex",alignItems:"center"}}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:f.nF,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},w=(e,t)=>{let{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},E=e=>{let{componentCls:t,formItemCls:n,inlineItemMarginBottom:r}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:r,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,
        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},j=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),A=e=>{let{componentCls:t,formItemCls:n,rootPrefixCls:r}=e;return{[`${n} ${n}-label`]:j(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${r}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},S=e=>{let{componentCls:t,formItemCls:n,antCls:r}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,
        ${r}-col-24${n}-label,
        ${r}-col-xl-24${n}-label`]:j(e)}},[`@media (max-width: ${(0,d.zA)(e.screenXSMax)})`]:[A(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-xs-24${n}-label`]:j(e)}}}],[`@media (max-width: ${(0,d.zA)(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-sm-24${n}-label`]:j(e)}}},[`@media (max-width: ${(0,d.zA)(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-md-24${n}-label`]:j(e)}}},[`@media (max-width: ${(0,d.zA)(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-lg-24${n}-label`]:j(e)}}}}},C=e=>{let{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,
      ${n}-col-24${t}-label,
      ${n}-col-xl-24${t}-label`]:j(e),[`@media (max-width: ${(0,d.zA)(e.screenXSMax)})`]:[A(e),{[t]:{[`${n}-col-xs-24${t}-label`]:j(e)}}],[`@media (max-width: ${(0,d.zA)(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:j(e)}},[`@media (max-width: ${(0,d.zA)(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:j(e)}},[`@media (max-width: ${(0,d.zA)(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:j(e)}}}},k=(e,t)=>(0,b.oX)(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t}),F=(0,h.OF)("Form",(e,{rootPrefixCls:t})=>{let n=k(e,t);return[x(n),O(n),$(n),w(n,n.componentCls),w(n,n.formItemCls),E(n),S(n),C(n),(0,g.A)(n),f.nF]},e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0}),{order:-1e3}),M=[];function I(e,t,n,r=0){return{key:"string"==typeof e?e:`${t}-${r}`,error:e,errorStatus:n}}let z=({help:e,helpStatus:t,errors:n=M,warnings:i=M,className:d,fieldId:p,onVisibleChanged:f})=>{let{prefixCls:g}=a.useContext(r.hb),b=`${g}-item-explain`,h=(0,u.A)(g),[$,y,v]=F(g,h),x=a.useMemo(()=>(0,c.A)(g),[g]),O=m(n),w=m(i),E=a.useMemo(()=>null!=e?[I(e,"help",t)]:[].concat((0,l.A)(O.map((e,t)=>I(e,"error","error",t))),(0,l.A)(w.map((e,t)=>I(e,"warning","warning",t)))),[e,t,O,w]),j=a.useMemo(()=>{let e={};return E.forEach(({key:t})=>{e[t]=(e[t]||0)+1}),E.map((t,n)=>Object.assign(Object.assign({},t),{key:e[t.key]>1?`${t.key}-fallback-${n}`:t.key}))},[E]),A={};return p&&(A.id=`${p}_help`),$(a.createElement(s.Ay,{motionDeadline:x.motionDeadline,motionName:`${g}-show-help`,visible:!!j.length,onVisibleChanged:f},e=>{let{className:t,style:n}=e;return a.createElement("div",Object.assign({},A,{className:o()(b,t,v,h,d,y),style:n}),a.createElement(s.aF,Object.assign({keys:j},(0,c.A)(g),{motionName:`${g}-show-help-item`,component:!1}),e=>{let{key:t,error:n,errorStatus:r,className:l,style:i}=e;return a.createElement("div",{key:t,className:o()(l,{[`${b}-${r}`]:r}),style:i},n)}))}))};var P=n(91418),N=n(71802),q=n(57026),H=n(40908),D=n(36213),W=n(79468),R=n(1496),T=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let B=a.forwardRef((e,t)=>{let n=a.useContext(q.A),{getPrefixCls:l,direction:i,requiredMark:s,colon:c,scrollToFirstError:m,className:d,style:p}=(0,N.TP)("form"),{prefixCls:f,className:g,rootClassName:b,size:h,disabled:$=n,form:y,colon:v,labelAlign:x,labelWrap:O,labelCol:w,wrapperCol:E,hideRequiredMark:j,layout:A="horizontal",scrollToFirstError:S,requiredMark:C,onFinishFailed:k,name:M,style:I,feedbackIcons:z,variant:B}=e,L=T(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),X=(0,H.A)(h),_=a.useContext(R.A),K=a.useMemo(()=>void 0!==C?C:!j&&(void 0===s||s),[j,C,s]),V=null!=v?v:c,G=l("form",f),J=(0,u.A)(G),[Y,Q,U]=F(G,J),Z=o()(G,`${G}-${A}`,{[`${G}-hide-required-mark`]:!1===K,[`${G}-rtl`]:"rtl"===i,[`${G}-${X}`]:X},U,J,Q,d,g,b),[ee]=(0,W.A)(y),{__INTERNAL__:et}=ee;et.name=M;let en=a.useMemo(()=>({name:M,labelAlign:x,labelCol:w,labelWrap:O,wrapperCol:E,vertical:"vertical"===A,colon:V,requiredMark:K,itemRef:et.itemRef,form:ee,feedbackIcons:z}),[M,x,w,E,A,V,K,ee,z]),er=a.useRef(null);a.useImperativeHandle(t,()=>{var e;return Object.assign(Object.assign({},ee),{nativeElement:null==(e=er.current)?void 0:e.nativeElement})});let el=(e,t)=>{if(e){let n={block:"nearest"};"object"==typeof e&&(n=Object.assign(Object.assign({},n),e)),ee.scrollToField(t,n)}};return Y(a.createElement(r.Pp.Provider,{value:B},a.createElement(q.X,{disabled:$},a.createElement(D.A.Provider,{value:X},a.createElement(r.Op,{validateMessages:_},a.createElement(r.cK.Provider,{value:en},a.createElement(P.Ay,Object.assign({id:M},L,{name:M,onFinishFailed:e=>{if(null==k||k(e),e.errorFields.length){let t=e.errorFields[0].name;if(void 0!==S)return void el(S,t);void 0!==m&&el(m,t)}},form:ee,ref:er,style:Object.assign(Object.assign({},p),I),className:Z}))))))))});var L=n(45680),X=n(7224),_=n(56883),K=n(67716),V=n(26851);let G=()=>{let{status:e,errors:t=[],warnings:n=[]}=a.useContext(r.$W);return{status:e,errors:t,warnings:n}};G.Context=r.$W;var J=n(53428),Y=n(93265),Q=n(62288),U=n(37262),Z=n(11056),ee=n(20775),et=n(96201),en=n(7565);let er=e=>{let{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}},el=(0,h.bf)(["Form","item-item"],(e,{rootPrefixCls:t})=>[er(k(e,t))]);var ea=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let ei=e=>{let{prefixCls:t,status:n,labelCol:l,wrapperCol:i,children:s,errors:c,warnings:u,_internalItemRender:m,extra:d,help:p,fieldId:f,marginBottom:g,onErrorVisibleChanged:b,label:h}=e,$=`${t}-item`,y=a.useContext(r.cK),v=a.useMemo(()=>{let e=Object.assign({},i||y.wrapperCol||{});return null!==h||l||i||!y.labelCol||[void 0,"xs","sm","md","lg","xl","xxl"].forEach(t=>{let n=t?[t]:[],r=(0,et.Jt)(y.labelCol,n),l="object"==typeof r?r:{},a=(0,et.Jt)(e,n);"span"in l&&!("offset"in("object"==typeof a?a:{}))&&l.span<24&&(e=(0,et.hZ)(e,[].concat(n,["offset"]),l.span))}),e},[i,y]),x=o()(`${$}-control`,v.className),O=a.useMemo(()=>{let{labelCol:e,wrapperCol:t}=y;return ea(y,["labelCol","wrapperCol"])},[y]),w=a.useRef(null),[E,j]=a.useState(0);(0,U.A)(()=>{d&&w.current?j(w.current.clientHeight):j(0)},[d]);let A=a.createElement("div",{className:`${$}-control-input`},a.createElement("div",{className:`${$}-control-input-content`},s)),S=a.useMemo(()=>({prefixCls:t,status:n}),[t,n]),C=null!==g||c.length||u.length?a.createElement(r.hb.Provider,{value:S},a.createElement(z,{fieldId:f,errors:c,warnings:u,help:p,helpStatus:n,className:`${$}-explain-connected`,onVisibleChanged:b})):null,k={};f&&(k.id=`${f}_extra`);let F=d?a.createElement("div",Object.assign({},k,{className:`${$}-extra`,ref:w}),d):null,M=C||F?a.createElement("div",{className:`${$}-additional`,style:g?{minHeight:g+E}:{}},C,F):null,I=m&&"pro_table_render"===m.mark&&m.render?m.render(e,{input:A,errorList:C,extra:F}):a.createElement(a.Fragment,null,A,M);return a.createElement(r.cK.Provider,{value:O},a.createElement(en.A,Object.assign({},v,{className:x}),I),a.createElement(el,{prefixCls:t}))};var eo=n(80828);let es={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"};var ec=n(21898),eu=a.forwardRef(function(e,t){return a.createElement(ec.A,(0,eo.A)({},e,{ref:t,icon:es}))}),em=n(48232),ed=n(10491),ep=n(33519),ef=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};let eg=({prefixCls:e,label:t,htmlFor:n,labelCol:l,labelAlign:i,colon:s,required:c,requiredMark:u,tooltip:m,vertical:d})=>{var p;let f,[g]=(0,em.A)("Form"),{labelAlign:b,labelCol:h,labelWrap:$,colon:y}=a.useContext(r.cK);if(!t)return null;let v=l||h||{},x=`${e}-item-label`,O=o()(x,"left"===(i||b)&&`${x}-left`,v.className,{[`${x}-wrap`]:!!$}),w=t,E=!0===s||!1!==y&&!1!==s;E&&!d&&"string"==typeof t&&t.trim()&&(w=t.replace(/[:|：]\s*$/,""));let j=function(e){return null==e?null:"object"!=typeof e||(0,a.isValidElement)(e)?{title:e}:e}(m);if(j){let{icon:t=a.createElement(eu,null)}=j,n=ef(j,["icon"]),r=a.createElement(ep.A,Object.assign({},n),a.cloneElement(t,{className:`${e}-item-tooltip`,title:"",onClick:e=>{e.preventDefault()},tabIndex:null}));w=a.createElement(a.Fragment,null,w,r)}let A="optional"===u,S="function"==typeof u;S?w=u(w,{required:!!c}):A&&!c&&(w=a.createElement(a.Fragment,null,w,a.createElement("span",{className:`${e}-item-optional`,title:""},(null==g?void 0:g.optional)||(null==(p=ed.A.Form)?void 0:p.optional)))),!1===u?f="hidden":(A||S)&&(f="optional");let C=o()({[`${e}-item-required`]:c,[`${e}-item-required-mark-${f}`]:f,[`${e}-item-no-colon`]:!E});return a.createElement(en.A,Object.assign({},v,{className:O}),a.createElement("label",{htmlFor:n,className:C,title:"string"==typeof t?t:""},w))};var eb=n(91039),eh=n(41514),e$=n(51297),ey=n(39759);let ev={success:eb.A,warning:e$.A,error:eh.A,validating:ey.A};function ex({children:e,errors:t,warnings:n,hasFeedback:l,validateStatus:i,prefixCls:s,meta:c,noStyle:u,name:m}){let d=`${s}-item`,{feedbackIcons:p}=a.useContext(r.cK),f=(0,Y.BS)(t,n,c,null,!!l,i),{isFormItemInput:g,status:b,hasFeedback:h,feedbackIcon:$,name:y}=a.useContext(r.$W),v=a.useMemo(()=>{var e;let r;if(l){let i=!0!==l&&l.icons||p,s=f&&(null==(e=null==i?void 0:i({status:f,errors:t,warnings:n}))?void 0:e[f]),c=f&&ev[f];r=!1!==s&&c?a.createElement("span",{className:o()(`${d}-feedback-icon`,`${d}-feedback-icon-${f}`)},s||a.createElement(c,null)):null}let i={status:f||"",errors:t,warnings:n,hasFeedback:!!l,feedbackIcon:r,isFormItemInput:!0,name:m};return u&&(i.status=(null!=f?f:b)||"",i.isFormItemInput=g,i.hasFeedback=!!(null!=l?l:h),i.feedbackIcon=void 0!==l?i.feedbackIcon:$,i.name=null!=m?m:y),i},[f,l,u,g,b]);return a.createElement(r.$W.Provider,{value:v},e)}var eO=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};function ew(e){let{prefixCls:t,className:n,rootClassName:l,style:i,help:s,errors:c,warnings:u,validateStatus:d,meta:p,hasFeedback:f,hidden:g,children:b,fieldId:h,required:$,isRequired:y,onSubItemMetaChange:v,layout:x,name:O}=e,w=eO(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout","name"]),E=`${t}-item`,{requiredMark:j,vertical:A}=a.useContext(r.cK),S=A||"vertical"===x,C=a.useRef(null),k=m(c),F=m(u),M=null!=s,I=!!(M||c.length||u.length),z=!!C.current&&(0,Q.A)(C.current),[P,N]=a.useState(null);(0,U.A)(()=>{I&&C.current&&N(parseInt(getComputedStyle(C.current).marginBottom,10))},[I,z]);let q=((e=!1)=>{let t=e?k:p.errors,n=e?F:p.warnings;return(0,Y.BS)(t,n,p,"",!!f,d)})(),H=o()(E,n,l,{[`${E}-with-help`]:M||k.length||F.length,[`${E}-has-feedback`]:q&&f,[`${E}-has-success`]:"success"===q,[`${E}-has-warning`]:"warning"===q,[`${E}-has-error`]:"error"===q,[`${E}-is-validating`]:"validating"===q,[`${E}-hidden`]:g,[`${E}-${x}`]:x});return a.createElement("div",{className:H,style:i,ref:C},a.createElement(ee.A,Object.assign({className:`${E}-row`},(0,Z.A)(w,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),a.createElement(eg,Object.assign({htmlFor:h},e,{requiredMark:j,required:null!=$?$:y,prefixCls:t,vertical:S})),a.createElement(ei,Object.assign({},e,p,{errors:k,warnings:F,prefixCls:t,status:q,help:s,marginBottom:P,onErrorVisibleChanged:e=>{e||N(null)}}),a.createElement(r.jC.Provider,{value:v},a.createElement(ex,{prefixCls:t,meta:p,errors:p.errors,warnings:p.warnings,hasFeedback:f,validateStatus:q,name:O},b)))),!!P&&a.createElement("div",{className:`${E}-margin-offset`,style:{marginBottom:-P}}))}let eE=a.memo(({children:e})=>e,(e,t)=>(function(e,t){let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every(n=>{let r=e[n],l=t[n];return r===l||"function"==typeof r||"function"==typeof l})})(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every((e,n)=>e===t.childProps[n]));function ej(){return{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}}let eA=function(e){let{name:t,noStyle:n,className:i,dependencies:s,prefixCls:c,shouldUpdate:m,rules:d,children:p,required:f,label:g,messageVariables:b,trigger:h="onChange",validateTrigger:$,hidden:y,help:v,layout:x}=e,{getPrefixCls:O}=a.useContext(N.QO),{name:w}=a.useContext(r.cK),E=function(e){if("function"==typeof e)return e;let t=(0,V.A)(e);return t.length<=1?t[0]:t}(p),j="function"==typeof E,A=a.useContext(r.jC),{validateTrigger:S}=a.useContext(P._z),C=void 0!==$?$:S,k=null!=t,M=O("form",c),I=(0,u.A)(M),[z,q,H]=F(M,I);(0,K.rJ)("Form.Item");let D=a.useContext(P.EF),W=a.useRef(null),[R,T]=function(e){let[t,n]=a.useState(e),r=a.useRef(null),l=a.useRef([]),i=a.useRef(!1);return a.useEffect(()=>(i.current=!1,()=>{i.current=!0,J.A.cancel(r.current),r.current=null}),[]),[t,function(e){i.current||(null===r.current&&(l.current=[],r.current=(0,J.A)(()=>{r.current=null,n(e=>{let t=e;return l.current.forEach(e=>{t=e(t)}),t})})),l.current.push(e))}]}({}),[B,G]=(0,L.A)(()=>ej()),Q=(e,t)=>{T(n=>{let r=Object.assign({},n),a=[].concat((0,l.A)(e.name.slice(0,-1)),(0,l.A)(t)).join("__SPLIT__");return e.destroy?delete r[a]:r[a]=e,r})},[U,Z]=a.useMemo(()=>{let e=(0,l.A)(B.errors),t=(0,l.A)(B.warnings);return Object.values(R).forEach(n=>{e.push.apply(e,(0,l.A)(n.errors||[])),t.push.apply(t,(0,l.A)(n.warnings||[]))}),[e,t]},[R,B.errors,B.warnings]),ee=function(){let{itemRef:e}=a.useContext(r.cK),t=a.useRef({});return function(n,r){let l=r&&"object"==typeof r&&(0,X.A9)(r),a=n.join("_");return(t.current.name!==a||t.current.originRef!==l)&&(t.current.name=a,t.current.originRef=l,t.current.ref=(0,X.K4)(e(n),l)),t.current.ref}}();function et(r,l,s){return n&&!y?a.createElement(ex,{prefixCls:M,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:B,errors:U,warnings:Z,noStyle:!0,name:t},r):a.createElement(ew,Object.assign({key:"row"},e,{className:o()(i,H,I,q),prefixCls:M,fieldId:l,isRequired:s,errors:U,warnings:Z,meta:B,onSubItemMetaChange:Q,layout:x,name:t}),r)}if(!k&&!j&&!s)return z(et(E));let en={};return"string"==typeof g?en.label=g:t&&(en.label=String(t)),b&&(en=Object.assign(Object.assign({},en),b)),z(a.createElement(P.D0,Object.assign({},e,{messageVariables:en,trigger:h,validateTrigger:C,onMetaChange:e=>{let t=null==D?void 0:D.getKey(e.name);if(G(e.destroy?ej():e,!0),n&&!1!==v&&A){let n=e.name;if(e.destroy)n=W.current||n;else if(void 0!==t){let[e,r]=t;W.current=n=[e].concat((0,l.A)(r))}A(e,n)}}}),(n,r,i)=>{let o=(0,Y.$r)(t).length&&r?r.name:[],c=(0,Y.kV)(o,w),u=void 0!==f?f:!!(null==d?void 0:d.some(e=>{if(e&&"object"==typeof e&&e.required&&!e.warningOnly)return!0;if("function"==typeof e){let t=e(i);return(null==t?void 0:t.required)&&!(null==t?void 0:t.warningOnly)}return!1})),p=Object.assign({},n),g=null;if(Array.isArray(E)&&k)g=E;else if(j&&(!(m||s)||k));else if(!s||j||k)if(a.isValidElement(E)){let t=Object.assign(Object.assign({},E.props),p);if(t.id||(t.id=c),v||U.length>0||Z.length>0||e.extra){let n=[];(v||U.length>0)&&n.push(`${c}_help`),e.extra&&n.push(`${c}_extra`),t["aria-describedby"]=n.join(" ")}U.length>0&&(t["aria-invalid"]="true"),u&&(t["aria-required"]="true"),(0,X.f3)(E)&&(t.ref=ee(o,E)),new Set([].concat((0,l.A)((0,Y.$r)(h)),(0,l.A)((0,Y.$r)(C)))).forEach(e=>{t[e]=(...t)=>{var n,r,l;null==(n=p[e])||n.call.apply(n,[p].concat(t)),null==(l=(r=E.props)[e])||l.call.apply(l,[r].concat(t))}});let n=[t["aria-required"],t["aria-invalid"],t["aria-describedby"]];g=a.createElement(eE,{control:p,update:E,childProps:n},(0,_.Ob)(E,t))}else g=j&&(m||s)&&!k?E(i):E;return et(g,c,u)}))};eA.useStatus=G;var eS=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n};B.Item=eA,B.List=e=>{var{prefixCls:t,children:n}=e,l=eS(e,["prefixCls","children"]);let{getPrefixCls:i}=a.useContext(N.QO),o=i("form",t),s=a.useMemo(()=>({prefixCls:o,status:"error"}),[o]);return a.createElement(P.B8,Object.assign({},l),(e,t,l)=>a.createElement(r.hb.Provider,{value:s},n(e.map(e=>Object.assign(Object.assign({},e),{fieldKey:e.key})),t,{errors:l.errors,warnings:l.warnings})))},B.ErrorList=z,B.useForm=W.A,B.useFormInstance=function(){let{form:e}=a.useContext(r.cK);return e},B.useWatch=P.FH,B.Provider=r.Op,B.create=()=>{};let eC=B}};