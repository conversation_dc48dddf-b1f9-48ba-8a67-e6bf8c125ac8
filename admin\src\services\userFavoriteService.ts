import request from "./request";

// 用户收藏数据类型（基于API文档UserFavoriteAdminItemDto）
export interface UserFavorite {
  id: string;
  userId: string;
  levelId: string;
  createdAt: string; // ISO日期时间格式
  user?: {
    id: string;
    nickname: string;
    avatar?: string;
  };
  level?: {
    id: string;
    title: string;
    difficulty: number;
    isVip?: boolean;
  };
}

// 用户收藏列表响应类型（基于API文档UserFavoriteAdminListResponseDto）
export interface UserFavoriteListResponse {
  data: UserFavorite[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 删除统计相关类型定义

// 查询参数类型（基于API文档接口参数）
export interface UserFavoriteQueryParams {
  startDate?: string; // 开始日期，格式：YYYY-MM-DD
  endDate?: string; // 结束日期，格式：YYYY-MM-DD
  levelId?: string; // 关卡ID
  userId?: string; // 用户ID
  difficulty?: number; // 关卡难度，1-5
  page?: number; // 页码，默认1
  pageSize?: number; // 每页数量，默认20，最大100
}

// 删除统计查询参数类型

// 导出参数类型
export interface UserFavoriteExportParams {
  startDate?: string;
  endDate?: string;
  levelId?: string;
  userId?: string;
  difficulty?: number;
  format?: "csv" | "excel";
}

// 用户收藏管理服务
export const userFavoriteService = {
  // 获取用户收藏数据（基于API文档：GET /api/v1/admin/user-favorites）
  getAll: async (
    params?: UserFavoriteQueryParams
  ): Promise<UserFavoriteListResponse> => {
    const response = await request.get<UserFavoriteListResponse>(
      "/user-favorites",
      { params }
    );
    return response.data;
  },

  // 删除收藏统计方法

  // 导出收藏数据（基于API文档：GET /api/v1/admin/user-favorites/export）
  exportData: async (params?: UserFavoriteExportParams): Promise<Blob> => {
    const response = await request.get("/user-favorites/export", {
      params,
      responseType: "blob",
    });
    return response.data;
  },
};

// 保持向后兼容的导出
export const getUserFavorites = userFavoriteService.getAll;
