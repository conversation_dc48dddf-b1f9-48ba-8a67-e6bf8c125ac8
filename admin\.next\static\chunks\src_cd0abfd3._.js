(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/Modal/index.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/client.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
// Modal 组件
const Modal = ({ title, content, children, visible = false, width = 520, centered = false, closable = true, maskClosable = true, footer, okText = '确定', cancelText = '取消', okType = 'primary', confirmLoading = false, onOk, onCancel, afterClose, className = '', style = {} })=>{
    _s();
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(visible);
    const [isAnimating, setIsAnimating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Modal.useEffect": ()=>{
            if (visible) {
                setIsVisible(true);
                setIsAnimating(true);
                document.body.style.overflow = 'hidden';
            } else {
                setIsAnimating(false);
                setTimeout({
                    "Modal.useEffect": ()=>{
                        setIsVisible(false);
                        document.body.style.overflow = '';
                        afterClose?.();
                    }
                }["Modal.useEffect"], 300);
            }
        }
    }["Modal.useEffect"], [
        visible,
        afterClose
    ]);
    const handleMaskClick = (e)=>{
        if (e.target === e.currentTarget && maskClosable) {
            onCancel?.();
        }
    };
    const handleOk = async ()=>{
        if (onOk) {
            try {
                await onOk();
            } catch (error) {
                console.error('Modal onOk error:', error);
            }
        }
    };
    const handleCancel = ()=>{
        onCancel?.();
    };
    const renderFooter = ()=>{
        if (footer === null) return null;
        if (footer) return footer;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "custom-modal-footer",
            children: [
                cancelText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    className: "custom-modal-btn custom-modal-btn-default",
                    onClick: handleCancel,
                    children: cancelText
                }, void 0, false, {
                    fileName: "[project]/src/components/Modal/index.tsx",
                    lineNumber: 93,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    className: `custom-modal-btn custom-modal-btn-${okType}`,
                    onClick: handleOk,
                    disabled: confirmLoading,
                    children: [
                        confirmLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "custom-modal-loading",
                            children: "⟳"
                        }, void 0, false, {
                            fileName: "[project]/src/components/Modal/index.tsx",
                            lineNumber: 105,
                            columnNumber: 30
                        }, this),
                        okText
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/Modal/index.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/Modal/index.tsx",
            lineNumber: 91,
            columnNumber: 7
        }, this);
    };
    if (!isVisible) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `custom-modal-mask ${isAnimating ? 'custom-modal-mask-show' : 'custom-modal-mask-hide'}`,
        onClick: handleMaskClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `custom-modal-wrap ${centered ? 'custom-modal-centered' : ''}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `custom-modal ${className} ${isAnimating ? 'custom-modal-show' : 'custom-modal-hide'}`,
                style: {
                    width,
                    ...style
                },
                children: [
                    (title || closable) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "custom-modal-header",
                        children: [
                            title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "custom-modal-title",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/Modal/index.tsx",
                                lineNumber: 126,
                                columnNumber: 25
                            }, this),
                            closable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "custom-modal-close",
                                onClick: handleCancel,
                                "aria-label": "Close",
                                children: "×"
                            }, void 0, false, {
                                fileName: "[project]/src/components/Modal/index.tsx",
                                lineNumber: 128,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Modal/index.tsx",
                        lineNumber: 125,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "custom-modal-body",
                        children: content || children
                    }, void 0, false, {
                        fileName: "[project]/src/components/Modal/index.tsx",
                        lineNumber: 139,
                        columnNumber: 11
                    }, this),
                    renderFooter()
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Modal/index.tsx",
                lineNumber: 120,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/Modal/index.tsx",
            lineNumber: 119,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Modal/index.tsx",
        lineNumber: 115,
        columnNumber: 5
    }, this);
};
_s(Modal, "HDnh4duRQj53Uz0LTHWpc0iExXc=");
_c = Modal;
// 确认对话框管理器
class ConfirmManager {
    container = null;
    root = null;
    getContainer() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'custom-modal-container';
            document.body.appendChild(this.container);
            this.root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRoot"])(this.container);
        }
        return this.container;
    }
    confirm(config) {
        return new Promise((resolve, reject)=>{
            let isResolved = false;
            const handleOk = async ()=>{
                if (isResolved) return;
                try {
                    if (config.onOk) {
                        await config.onOk();
                    }
                    isResolved = true;
                    this.destroy();
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            const handleCancel = ()=>{
                if (isResolved) return;
                isResolved = true;
                config.onCancel?.();
                this.destroy();
                reject(new Error('User cancelled'));
            };
            this.getContainer();
            this.root.render(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Modal, {
                visible: true,
                title: config.title,
                content: config.content,
                okText: config.okText,
                cancelText: config.cancelText,
                okType: config.okType,
                width: config.width,
                centered: config.centered,
                maskClosable: config.maskClosable,
                onOk: handleOk,
                onCancel: handleCancel,
                afterClose: ()=>this.destroy()
            }, void 0, false, {
                fileName: "[project]/src/components/Modal/index.tsx",
                lineNumber: 209,
                columnNumber: 9
            }, this));
        });
    }
    destroy() {
        if (this.container && document.body.contains(this.container)) {
            document.body.removeChild(this.container);
            this.container = null;
            this.root = null;
        }
    }
}
// 创建带有静态方法的Modal组件
const ModalWithStatic = Modal;
// 静态方法
ModalWithStatic.confirm = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: config.okType || 'primary'
    });
};
ModalWithStatic.info = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'primary',
        cancelText: undefined
    });
};
ModalWithStatic.success = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'primary',
        cancelText: undefined
    });
};
ModalWithStatic.error = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'danger',
        cancelText: undefined
    });
};
ModalWithStatic.warning = (config)=>{
    const manager = new ConfirmManager();
    return manager.confirm({
        ...config,
        okType: 'primary',
        cancelText: undefined
    });
};
const __TURBOPACK__default__export__ = ModalWithStatic;
var _c;
__turbopack_context__.k.register(_c, "Modal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Message/index.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "message": (()=>message)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/client.js [app-client] (ecmascript)");
;
;
;
// 消息容器组件
const MessageContainer = ({ messages })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "custom-message-container",
        children: messages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `custom-message custom-message-${message.type} ${message.visible ? 'custom-message-show' : 'custom-message-hide'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "custom-message-icon",
                        children: [
                            message.type === 'success' && '✓',
                            message.type === 'error' && '✕',
                            message.type === 'warning' && '⚠',
                            message.type === 'info' && 'ℹ'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Message/index.tsx",
                        lineNumber: 28,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "custom-message-content",
                        children: message.content
                    }, void 0, false, {
                        fileName: "[project]/src/components/Message/index.tsx",
                        lineNumber: 34,
                        columnNumber: 11
                    }, this)
                ]
            }, message.id, true, {
                fileName: "[project]/src/components/Message/index.tsx",
                lineNumber: 22,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/Message/index.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
};
_c = MessageContainer;
// 消息管理器
class MessageManager {
    messages = [];
    container = null;
    root = null;
    getContainer() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'custom-message-wrapper';
            document.body.appendChild(this.container);
            this.root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRoot"])(this.container);
        }
        return this.container;
    }
    render() {
        if (this.root) {
            this.root.render(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MessageContainer, {
                messages: this.messages
            }, void 0, false, {
                fileName: "[project]/src/components/Message/index.tsx",
                lineNumber: 59,
                columnNumber: 24
            }, this));
        }
    }
    generateId() {
        return `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    show(config) {
        const id = config.key || this.generateId();
        const duration = config.duration ?? 3000;
        // 如果已存在相同key的消息，先移除
        if (config.key) {
            this.messages = this.messages.filter((msg)=>msg.id !== config.key);
        }
        const messageItem = {
            ...config,
            id,
            visible: true
        };
        this.messages.push(messageItem);
        this.getContainer();
        this.render();
        // 自动移除
        if (duration > 0) {
            setTimeout(()=>{
                this.hide(id);
            }, duration);
        }
        return id;
    }
    hide(id) {
        const messageIndex = this.messages.findIndex((msg)=>msg.id === id);
        if (messageIndex > -1) {
            this.messages[messageIndex].visible = false;
            this.render();
            // 动画结束后移除
            setTimeout(()=>{
                this.messages = this.messages.filter((msg)=>msg.id !== id);
                this.render();
                // 如果没有消息了，清理容器
                if (this.messages.length === 0 && this.container) {
                    document.body.removeChild(this.container);
                    this.container = null;
                    this.root = null;
                }
            }, 300);
        }
    }
    destroy() {
        this.messages = [];
        if (this.container) {
            document.body.removeChild(this.container);
            this.container = null;
            this.root = null;
        }
    }
}
// 全局消息管理器实例
const messageManager = new MessageManager();
const message = {
    success: (content, duration)=>messageManager.show({
            content,
            type: 'success',
            duration
        }),
    error: (content, duration)=>messageManager.show({
            content,
            type: 'error',
            duration
        }),
    warning: (content, duration)=>messageManager.show({
            content,
            type: 'warning',
            duration
        }),
    info: (content, duration)=>messageManager.show({
            content,
            type: 'info',
            duration
        }),
    destroy: ()=>messageManager.destroy()
};
const __TURBOPACK__default__export__ = message;
var _c;
__turbopack_context__.k.register(_c, "MessageContainer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// 自定义组件统一导出
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Modal/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript)");
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Modal/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/Modal/index.tsx [app-client] (ecmascript) <export default as Modal>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Modal": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Modal/index.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/Message/index.tsx [app-client] (ecmascript) <export default as message>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "message": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript)");
}}),
"[project]/src/config/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// API配置
__turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG),
    "ENV_CONFIG": (()=>ENV_CONFIG)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const API_CONFIG = {
    // 基础URL - 可以根据环境变量动态设置
    BASE_URL: (("TURBOPACK compile-time truthy", 1) ? ("TURBOPACK compile-time value", "http://localhost:18891") : ("TURBOPACK unreachable", undefined)) || "http://localhost:18891",
    // 超时时间
    TIMEOUT: 10000,
    // API版本前缀（注意：当前使用/admin/前缀，此配置保留用于兼容性）
    API_PREFIX: "/api/v1/admin",
    // 完整的API基础URL
    get FULL_BASE_URL () {
        return `${this.BASE_URL}${this.API_PREFIX}`;
    }
};
const ENV_CONFIG = {
    isDevelopment: ("TURBOPACK compile-time value", "development") === "development",
    isProduction: ("TURBOPACK compile-time value", "development") === "production",
    isTest: ("TURBOPACK compile-time value", "development") === "test"
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/request.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "api": (()=>api),
    "default": (()=>__TURBOPACK__default__export__),
    "silentApi": (()=>silentApi),
    "successApi": (()=>successApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript) <export default as message>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/api.ts [app-client] (ecmascript)");
;
;
;
// 创建axios实例
const request = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].FULL_BASE_URL,
    timeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT,
    headers: {
        "Content-Type": "application/json"
    }
});
// 请求拦截器
request.interceptors.request.use((config)=>{
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem("admin_token");
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // 添加详细的请求日志
    console.log("🚀 发送请求:", {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL,
        fullURL: `${config.baseURL}${config.url}`,
        data: config.data,
        headers: config.headers
    });
    return config;
}, (error)=>{
    console.error("请求拦截器错误:", error);
    return Promise.reject(error);
});
// 响应拦截器
request.interceptors.response.use((response)=>{
    const config = response.config;
    // 添加响应日志
    console.log("✅ 请求成功:", {
        method: config.method?.toUpperCase(),
        url: config.url,
        status: response.status,
        statusText: response.statusText,
        data: response.data
    });
    // 处理成功提示
    if (config.showSuccess && config.successMessage) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success(config.successMessage);
    }
    return response;
}, (error)=>{
    console.error("❌ 请求失败:", {
        method: error.config?.method?.toUpperCase(),
        url: error.config?.url,
        baseURL: error.config?.baseURL,
        fullURL: `${error.config?.baseURL}${error.config?.url}`,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
    });
    const config = error.config;
    const showError = config?.showError !== false; // 默认显示错误
    if (!showError) {
        return Promise.reject(error);
    }
    // 处理常见错误
    if (error.response) {
        const { status, data } = error.response;
        let errorMessage = "";
        switch(status){
            case 401:
                errorMessage = "登录已过期，请重新登录";
                localStorage.removeItem("admin_token");
                // 可以在这里添加跳转到登录页的逻辑
                window.location.href = "/login";
                break;
            case 403:
                errorMessage = data?.message || "没有权限访问该资源";
                break;
            case 404:
                errorMessage = data?.message || "请求的资源不存在";
                break;
            case 422:
                errorMessage = data?.message || "请求参数验证失败";
                break;
            case 500:
                errorMessage = data?.message || "服务器内部错误";
                break;
            default:
                errorMessage = data?.message || `请求失败 (${status})`;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(errorMessage);
    } else if (error.request) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error("网络连接失败，请检查网络");
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error("请求配置错误");
    }
    return Promise.reject(error);
});
const api = {
    // GET请求
    get: (url, config)=>{
        return request.get(url, config);
    },
    // POST请求
    post: (url, data, config)=>{
        return request.post(url, data, config);
    },
    // PUT请求
    put: (url, data, config)=>{
        return request.put(url, data, config);
    },
    // PATCH请求
    patch: (url, data, config)=>{
        return request.patch(url, data, config);
    },
    // DELETE请求
    delete: (url, config)=>{
        return request.delete(url, config);
    }
};
const silentApi = {
    get: (url, config)=>api.get(url, {
            ...config,
            showError: false
        }),
    post: (url, data, config)=>api.post(url, data, {
            ...config,
            showError: false
        }),
    put: (url, data, config)=>api.put(url, data, {
            ...config,
            showError: false
        }),
    patch: (url, data, config)=>api.patch(url, data, {
            ...config,
            showError: false
        }),
    delete: (url, config)=>api.delete(url, {
            ...config,
            showError: false
        })
};
const successApi = {
    post: (url, data, successMessage, config)=>api.post(url, data, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "操作成功"
        }),
    put: (url, data, successMessage, config)=>api.put(url, data, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "更新成功"
        }),
    patch: (url, data, successMessage, config)=>api.patch(url, data, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "更新成功"
        }),
    delete: (url, successMessage, config)=>api.delete(url, {
            ...config,
            showSuccess: true,
            successMessage: successMessage || "删除成功"
        })
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/authService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authService": (()=>authService),
    "login": (()=>login)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const authService = {
    // 登录
    login: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/auth/login', params);
        return response.data;
    },
    // 登出
    logout: ()=>{
        localStorage.removeItem('admin_token');
        window.location.href = '/login';
    },
    // 获取当前token
    getToken: ()=>{
        return localStorage.getItem('admin_token');
    },
    // 设置token
    setToken: (token)=>{
        localStorage.setItem('admin_token', token);
    },
    // 检查是否已登录
    isLoggedIn: ()=>{
        return !!localStorage.getItem('admin_token');
    }
};
const login = authService.login;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/phraseService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "phraseService": (()=>phraseService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const phraseService = {
    // 获取所有词组
    getAll: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/phrases');
        return response.data;
    },
    // 根据ID获取词组
    getById: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/phrases/${id}`);
        return response.data;
    },
    // 创建词组
    create: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/phrases', params);
        return response.data;
    },
    // 更新词组
    update: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/phrases/${id}`, params);
        return response.data;
    },
    // 删除词组
    delete: async (id)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/phrases/${id}`);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/thesaurusService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createThesaurus": (()=>createThesaurus),
    "deleteThesaurus": (()=>deleteThesaurus),
    "deleteThesaurusById": (()=>deleteThesaurusById),
    "getAllThesauruses": (()=>getAllThesauruses),
    "getThesaurusById": (()=>getThesaurusById),
    "getThesauruses": (()=>getThesauruses),
    "thesaurusService": (()=>thesaurusService),
    "updateThesaurus": (()=>updateThesaurus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const thesaurusService = {
    // 获取所有词库
    getAll: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/thesauruses');
        return response.data;
    },
    // 根据ID获取词库
    getById: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/thesauruses/${id}`);
        return response.data;
    },
    // 创建词库
    create: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/thesauruses', params);
        return response.data;
    },
    // 更新词库
    update: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/thesauruses/${id}`, params);
        return response.data;
    },
    // 删除词库
    delete: async (id)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/thesauruses/${id}`);
    },
    // 向词库添加词组
    addPhrase: async (thesaurusId, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/thesauruses/${thesaurusId}/phrases`, params);
        return response.data;
    },
    // 从词库移除词组
    removePhrase: async (thesaurusId, phraseId)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/thesauruses/${thesaurusId}/phrases/${phraseId}`);
        return response.data;
    }
};
const createThesaurus = thesaurusService.create;
const updateThesaurus = thesaurusService.update;
const deleteThesaurus = thesaurusService.delete;
const deleteThesaurusById = thesaurusService.delete; // 别名
const getThesaurusById = thesaurusService.getById;
const getAllThesauruses = thesaurusService.getAll;
const getThesauruses = thesaurusService.getAll; // 别名
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/levelService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getLevels": (()=>getLevels),
    "levelService": (()=>levelService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const levelService = {
    // 获取关卡列表（支持筛选和分页）
    getAll: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/levels", {
            params
        });
        return response.data;
    },
    // 获取所有关卡（简单版本，保持向后兼容）
    getAllSimple: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/levels");
        return response.data;
    },
    // 根据ID获取关卡
    getById: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/levels/${id}`);
        return response.data;
    },
    // 创建关卡
    create: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/levels", params);
        return response.data;
    },
    // 更新关卡
    update: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/levels/${id}`, params);
        return response.data;
    },
    // 删除关卡
    delete: async (id)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/levels/${id}`);
    },
    // 向关卡添加词组
    addPhrase: async (levelId, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/levels/${levelId}/phrases`, params);
        return response.data;
    },
    // 从关卡移除词组
    removePhrase: async (levelId, phraseId)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/levels/${levelId}/phrases/${phraseId}`);
        return response.data;
    },
    // 获取关卡统计
    getCount: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/levels/count");
        return response.data;
    },
    // 根据难度获取关卡
    getByDifficulty: async (difficulty)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/levels/difficulty/${difficulty}`);
        return response.data;
    },
    // 获取关卡详细信息（包含词组详情）
    getWithPhrases: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/levels/${id}`);
        return response.data;
    },
    // 获取关卡星级统计
    getLevelStarAnalytics: async (levelId)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/levels/${levelId}/star-analytics`);
        return response.data;
    }
};
const getLevels = levelService.getAll;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/userService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "userService": (()=>userService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const userService = {
    // 获取所有用户
    getAll: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get("/users");
        return response.data;
    },
    // 根据ID获取用户
    getById: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/users/${id}`);
        return response.data;
    },
    // 根据openid获取用户
    getByOpenid: async (openid)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/users/by-openid?openid=${openid}`);
        return response.data;
    },
    // 根据手机号获取用户
    getByPhone: async (phone)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/users/by-phone?phone=${phone}`);
        return response.data;
    },
    // 创建用户
    create: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].post("/users", params, "用户创建成功");
        return response.data;
    },
    // 更新用户
    update: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].patch(`/users/${id}`, params, "用户更新成功");
        return response.data;
    },
    // 删除用户
    delete: async (id)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].delete(`/users/${id}`, "用户删除成功");
    },
    // 用户完成关卡
    completeLevel: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].post(`/users/${id}/complete-level`, params);
        return response.data;
    },
    // 用户开始游戏
    startGame: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].post(`/users/${id}/start-game`);
        return response.data;
    },
    // 获取用户统计
    getStats: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/users/${id}/stats`);
        return response.data;
    },
    // 重置用户进度
    resetProgress: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].post(`/users/${id}/reset-progress`, {}, "用户进度重置成功");
        return response.data;
    },
    // 设置用户VIP套餐
    setVipStatus: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].post(`/users/${id}/set-vip-package`, {
            packageId: params.packageId,
            reason: params.reason || "管理员手动设置"
        }, "设置VIP成功");
        return response.data;
    },
    // 取消用户VIP状态
    cancelVipStatus: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].post(`/users/${id}/cancel-vip`, params, "取消VIP成功");
        return response.data;
    },
    // 批量VIP操作（统一接口）
    batchVipOperation: async (params)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].post("/users/batch-vip-package", params);
    },
    // 批量设置用户VIP套餐
    batchSetVipStatus: async (userIds, params)=>{
        await userService.batchVipOperation({
            userIds,
            packageId: params.packageId,
            reason: params.reason || "管理员批量设置"
        });
    },
    // 批量取消用户VIP状态（使用取消VIP接口）
    batchCancelVipStatus: async (userIds, params)=>{
        // 批量取消VIP需要循环调用单个取消接口
        const promises = userIds.map((userId)=>userService.cancelVipStatus(userId, params));
        await Promise.all(promises);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/shareService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SHARE_PATH_TEMPLATES": (()=>SHARE_PATH_TEMPLATES),
    "SHARE_TYPE_OPTIONS": (()=>SHARE_TYPE_OPTIONS),
    "ShareService": (()=>ShareService),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
class ShareService {
    // 获取所有分享配置
    static async getAllShareConfigs() {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/share');
        return response.data;
    }
    // 获取启用的分享配置
    static async getActiveShareConfigs() {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/share/active');
        return response.data;
    }
    // 获取默认分享配置
    static async getDefaultShareConfig() {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/share/default');
        return response.data;
    }
    // 根据类型获取分享配置
    static async getShareConfigByType(type) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/share/type/${type}`);
        return response.data;
    }
    // 根据ID获取分享配置
    static async getShareConfigById(id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/share/${id}`);
        return response.data;
    }
    // 创建分享配置
    static async createShareConfig(data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/share', data);
        return response.data;
    }
    // 更新分享配置
    static async updateShareConfig(id, data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/share/${id}`, data);
        return response.data;
    }
    // 启用/禁用分享配置
    static async toggleShareConfig(id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/share/${id}/toggle`);
        return response.data;
    }
    // 删除分享配置
    static async deleteShareConfig(id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/share/${id}`);
        return response.data;
    }
}
const SHARE_TYPE_OPTIONS = [
    {
        value: 'default',
        label: '默认分享',
        description: '通用分享，适用于首页、游戏页等'
    },
    {
        value: 'result',
        label: '结果分享',
        description: '展示用户成绩的分享'
    },
    {
        value: 'level',
        label: '关卡分享',
        description: '邀请好友挑战特定关卡'
    },
    {
        value: 'achievement',
        label: '成就分享',
        description: '展示用户获得的成就'
    },
    {
        value: 'custom',
        label: '自定义分享',
        description: '特殊活动或自定义场景'
    }
];
const SHARE_PATH_TEMPLATES = [
    {
        label: '首页',
        value: '/pages/index/index',
        description: '小程序首页'
    },
    {
        label: '游戏页',
        value: '/pages/game/game',
        description: '游戏主页面'
    },
    {
        label: '关卡页',
        value: '/pages/level/level?id={levelId}',
        description: '特定关卡页面，{levelId}会被替换为实际关卡ID'
    },
    {
        label: '结果页',
        value: '/pages/result/result?score={score}',
        description: '结果展示页面，{score}会被替换为实际分数'
    },
    {
        label: '排行榜',
        value: '/pages/rank/rank',
        description: '排行榜页面'
    }
];
const __TURBOPACK__default__export__ = ShareService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/vipService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "paymentOrderService": (()=>paymentOrderService),
    "vipPackageService": (()=>vipPackageService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const vipPackageService = {
    // 获取VIP套餐列表
    async getList (params) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/payment/packages", {
            params
        });
        return response.data;
    },
    // 根据ID获取VIP套餐
    async getById (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/payment/packages/${id}`);
        return response.data;
    },
    // 创建VIP套餐
    async create (params) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/payment/packages", params);
        return response.data;
    },
    // 更新VIP套餐
    async update (id, params) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/payment/packages/${id}`, params);
        return response.data;
    },
    // 删除VIP套餐
    async delete (id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/payment/packages/${id}`);
    },
    // 切换套餐状态（通过更新接口实现）
    async toggleStatus (id, isActive) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/payment/packages/${id}`, {
            isActive
        });
        return response.data;
    }
};
const paymentOrderService = {
    // 获取支付订单列表
    async getList (params) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/payment/orders", {
            params
        });
        return response.data;
    },
    // 删除支付订单统计方法
    // 根据ID获取支付订单
    async getById (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/payment/orders/${id}`);
        return response.data;
    },
    // 申请退款
    async refund (id, reason) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/payment/orders/${id}/refund`, {
            reason
        });
        return response.data;
    },
    // 取消订单
    async cancel (out_trade_no, userId) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/payment/cancel/${out_trade_no}`, {
            userId
        });
    }
};
const __TURBOPACK__default__export__ = {
    vipPackageService,
    paymentOrderService
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/settingsService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "settingsService": (()=>settingsService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const settingsService = {
    // 获取所有设置
    async getAll () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/settings');
        return response.data;
    },
    // 根据ID获取设置
    async getById (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/settings/${id}`);
        return response.data;
    },
    // 根据键名获取设置
    async getByKey (key) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/settings/key/${key}`);
        return response.data;
    },
    // 更新设置
    async update (id, params) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/settings/${id}`, params);
        return response.data;
    },
    // 根据键名更新设置值
    async updateByKey (key, value) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/settings/key/${key}`, {
            value
        });
        return response.data;
    },
    // 初始化默认设置（保留用于手动触发）
    async initializeDefaults () {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/settings/initialize');
    },
    // 批量更新小程序配置
    async updateAppConfig (config) {
        const promises = [];
        if (config.helpUrl !== undefined) {
            promises.push(this.updateByKey('help_url', config.helpUrl));
        }
        if (config.backgroundMusicUrl !== undefined) {
            promises.push(this.updateByKey('background_music_url', config.backgroundMusicUrl));
        }
        await Promise.all(promises);
    },
    // 获取小程序配置
    async getAppConfig () {
        try {
            const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([
                this.getByKey('help_url').catch(()=>null),
                this.getByKey('background_music_url').catch(()=>null)
            ]);
            return {
                helpUrl: helpUrlSetting?.value || '',
                backgroundMusicUrl: backgroundMusicSetting?.value || ''
            };
        } catch (error) {
            console.error('获取小程序配置失败:', error);
            // 如果设置不存在，返回默认值
            return {
                helpUrl: '',
                backgroundMusicUrl: ''
            };
        }
    },
    // 测试微信小程序获取设置接口
    async testWeixinAppSettings () {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/weixin/app-settings');
            return response.data;
        } catch (error) {
            console.error('测试微信小程序设置接口失败:', error);
            throw error;
        }
    },
    // 测试微信小程序全局配置接口
    async testWeixinGlobalConfig () {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/weixin/global-config');
            return response.data;
        } catch (error) {
            console.error('测试微信小程序全局配置接口失败:', error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/levelTagService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createLevelTag": (()=>createLevelTag),
    "deleteLevelTag": (()=>deleteLevelTag),
    "getLevelTags": (()=>getLevelTags),
    "levelTagService": (()=>levelTagService),
    "updateLevelTag": (()=>updateLevelTag)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
// 标签数据处理函数，确保所有字段都有合适的默认值
const processTagData = (tag)=>({
        id: tag.id || '',
        name: tag.name || '',
        description: tag.description || '',
        color: tag.color || '#1890ff',
        isVip: Boolean(tag.isVip),
        status: tag.status || 'active',
        icon: tag.icon,
        createdAt: tag.createdAt || new Date().toISOString(),
        updatedAt: tag.updatedAt || new Date().toISOString()
    });
const levelTagService = {
    // 获取所有标签
    getAll: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/tags');
        // 确保每个标签都有正确的字段和默认值
        const tags = response.data || [];
        return tags.map(processTagData);
    },
    // 根据ID获取标签
    getById: async (id)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/tags/${id}`);
        return processTagData(response.data);
    },
    // 创建标签
    create: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/tags', params);
        return processTagData(response.data);
    },
    // 更新标签
    update: async (id, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/tags/${id}`, params);
        return processTagData(response.data);
    },
    // 删除标签
    delete: async (id)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/tags/${id}`);
    }
};
const getLevelTags = levelTagService.getAll;
const createLevelTag = levelTagService.create;
const updateLevelTag = levelTagService.update;
const deleteLevelTag = levelTagService.delete;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/userStarService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getUserStars": (()=>getUserStars),
    "userStarService": (()=>userStarService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const userStarService = {
    // 获取用户星级数据（基于API文档：GET /api/v1/admin/user-stars）
    getAll: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/user-stars", {
            params
        });
        return response.data;
    },
    // 删除用户星级统计方法
    // 导出用户星级数据（基于API文档：GET /api/v1/admin/user-stars/export）
    exportData: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/user-stars/export", {
            params,
            responseType: "blob"
        });
        return response.data;
    }
};
const getUserStars = userStarService.getAll;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/userFavoriteService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getUserFavorites": (()=>getUserFavorites),
    "userFavoriteService": (()=>userFavoriteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const userFavoriteService = {
    // 获取用户收藏数据（基于API文档：GET /api/v1/admin/user-favorites）
    getAll: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/user-favorites", {
            params
        });
        return response.data;
    },
    // 删除收藏统计方法
    // 导出收藏数据（基于API文档：GET /api/v1/admin/user-favorites/export）
    exportData: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/user-favorites/export", {
            params,
            responseType: "blob"
        });
        return response.data;
    }
};
const getUserFavorites = userFavoriteService.getAll;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/activationCodeService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "activationCodeService": (()=>activationCodeService),
    "batchGenerateActivationCodes": (()=>batchGenerateActivationCodes),
    "createActivationCode": (()=>createActivationCode),
    "disableActivationCode": (()=>disableActivationCode),
    "enableActivationCode": (()=>enableActivationCode),
    "generateActivationCodes": (()=>generateActivationCodes),
    "getActivationCodes": (()=>getActivationCodes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
;
const activationCodeService = {
    // 获取所有激活码
    getAll: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get("/activation-codes", {
            params
        });
        return response.data;
    },
    // 根据激活码获取详情
    getByCode: async (code)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/activation-codes/${code}`);
        return response.data;
    },
    // 生成激活码
    generate: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].post("/activation-codes/generate", params, "激活码生成成功");
        return response.data;
    },
    // 手动创建单个激活码
    create: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].post("/activation-codes/create", params, "激活码创建成功");
        return response.data;
    },
    // 批量生成激活码
    batchGenerate: async (params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].post("/activation-codes/generate", params, "批量生成激活码成功");
        return response.data;
    },
    // 禁用激活码
    disable: async (code)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].put(`/activation-codes/${code}/disable`, {}, "激活码已禁用");
    },
    // 启用激活码
    enable: async (code)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].put(`/activation-codes/${code}/enable`, {}, "激活码已启用");
    },
    // 删除统计相关方法
    // 获取所有套餐（VIP套餐，用于激活码兑换）
    getAllPackages: async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get("/payment/packages");
        return response.data || [];
    },
    // 获取热门套餐
    getPopularPackages: async (limit)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get("/payment/packages/popular", {
            params: {
                limit
            }
        });
        return response.data;
    },
    // 删除激活码
    delete: async (code, params)=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["successApi"].delete(`/activation-codes/${code}`, "激活码删除成功", {
            data: params || {}
        });
        return response.data;
    }
};
const getActivationCodes = activationCodeService.getAll;
const generateActivationCodes = activationCodeService.generate;
const createActivationCode = activationCodeService.create;
const batchGenerateActivationCodes = activationCodeService.batchGenerate;
const disableActivationCode = activationCodeService.disable;
const enableActivationCode = activationCodeService.enable;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// 统一导出所有API服务
__turbopack_context__.s({
    "services": (()=>services)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/authService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$phraseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/phraseService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$thesaurusService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/thesaurusService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/levelService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$shareService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/shareService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$vipService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/vipService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$settingsService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/settingsService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelTagService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/levelTagService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userStarService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userStarService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userFavoriteService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userFavoriteService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$activationCodeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/activationCodeService.ts [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const services = {
    auth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"],
    phrase: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$phraseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["phraseService"],
    thesaurus: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$thesaurusService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["thesaurusService"],
    level: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelService"],
    user: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userService"],
    share: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$shareService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShareService"],
    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$settingsService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["settingsService"],
    levelTag: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelTagService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelTagService"],
    userStar: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userStarService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userStarService"],
    userFavorite: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userFavoriteService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userFavoriteService"],
    activationCode: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$activationCodeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["activationCodeService"]
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$request$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/request.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/authService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$phraseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/phraseService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$thesaurusService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/thesaurusService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/levelService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$shareService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/shareService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$vipService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/vipService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$settingsService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/settingsService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelTagService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/levelTagService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userStarService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userStarService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$userFavoriteService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/userFavoriteService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$activationCodeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/activationCodeService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/app/(admin)/levels/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LevelsPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/card/index.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$table$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/table/index.js [app-client] (ecmascript) <export default as Table>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/index.js [app-client] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$statistic$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Statistic$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/statistic/index.js [app-client] (ecmascript) <export default as Statistic>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$row$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/row/index.js [app-client] (ecmascript) <export default as Row>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/col/index.js [app-client] (ecmascript) <export default as Col>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Select$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/index.js [app-client] (ecmascript) <export default as Select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Popconfirm$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/popconfirm/index.js [app-client] (ecmascript) <export default as Popconfirm>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$rate$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Rate$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/rate/index.js [app-client] (ecmascript) <export default as Rate>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tooltip$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tooltip$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tooltip/index.js [app-client] (ecmascript) <export default as Tooltip>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/input/index.js [app-client] (ecmascript) <export default as Input>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/src/components/Modal/index.tsx [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_context__.i("[project]/src/components/Message/index.tsx [app-client] (ecmascript) <export default as message>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlusOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlusOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/PlusOutlined.js [app-client] (ecmascript) <export default as PlusOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EditOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EditOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/EditOutlined.js [app-client] (ecmascript) <export default as EditOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DeleteOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DeleteOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/DeleteOutlined.js [app-client] (ecmascript) <export default as DeleteOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EyeOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/EyeOutlined.js [app-client] (ecmascript) <export default as EyeOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$BarChartOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChartOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/BarChartOutlined.js [app-client] (ecmascript) <export default as BarChartOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$StarOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__StarOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/StarOutlined.js [app-client] (ecmascript) <export default as StarOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SearchOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/SearchOutlined.js [app-client] (ecmascript) <export default as SearchOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ReloadOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ReloadOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/ReloadOutlined.js [app-client] (ecmascript) <export default as ReloadOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/levelService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelTagService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/levelTagService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
function LevelsPageContent() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const [levels, setLevels] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [levelStats, setLevelStats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [tags, setTags] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [starAnalytics, setStarAnalytics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [loadingStars, setLoadingStars] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [highlightLevelId, setHighlightLevelId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // 筛选状态
    const [searchText, setSearchText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [difficultyFilter, setDifficultyFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    const [tagFilter, setTagFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    // 分页状态
    const [pagination, setPagination] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        current: 1,
        pageSize: 20,
        total: 0,
        totalPages: 0
    });
    // 获取关卡列表
    const fetchLevels = async (params)=>{
        setLoading(true);
        try {
            const queryParams = {
                search: searchText || undefined,
                difficulty: difficultyFilter,
                tagId: tagFilter,
                page: params?.page || pagination.current,
                pageSize: params?.pageSize || pagination.pageSize,
                ...params
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelService"].getAll(queryParams);
            setLevels(response.levels);
            setPagination({
                current: response.page,
                pageSize: response.pageSize,
                total: response.total,
                totalPages: response.totalPages
            });
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error('获取关卡列表失败');
            // 使用模拟数据作为后备
            setLevels([]);
        } finally{
            setLoading(false);
        }
    };
    // 获取关卡统计
    const fetchLevelStats = async ()=>{
        try {
            const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelService"].getCount();
            setLevelStats(stats);
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error('获取关卡统计失败');
        }
    };
    // 获取标签列表
    const fetchTags = async ()=>{
        try {
            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelTagService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelTagService"].getAll();
            setTags(data.filter((tag)=>tag.status === 'active'));
        } catch (error) {
            console.error('获取标签列表失败:', error);
        }
    };
    // 获取关卡星级统计
    const fetchLevelStarAnalytics = async (levelId)=>{
        if (starAnalytics[levelId] || loadingStars[levelId]) {
            return; // 已有数据或正在加载，避免重复请求
        }
        setLoadingStars((prev)=>({
                ...prev,
                [levelId]: true
            }));
        try {
            const analytics = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelService"].getLevelStarAnalytics(levelId);
            setStarAnalytics((prev)=>({
                    ...prev,
                    [levelId]: analytics
                }));
        } catch (error) {
            console.error('获取关卡星级统计失败:', error);
        // 静默失败，不显示错误消息，因为这是辅助信息
        } finally{
            setLoadingStars((prev)=>({
                    ...prev,
                    [levelId]: false
                }));
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LevelsPageContent.useEffect": ()=>{
            fetchLevels();
            fetchLevelStats();
            fetchTags();
        }
    }["LevelsPageContent.useEffect"], []); // eslint-disable-line react-hooks/exhaustive-deps
    // 当筛选条件改变时重新获取数据
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LevelsPageContent.useEffect": ()=>{
            fetchLevels({
                page: 1
            }); // 重置到第一页
        }
    }["LevelsPageContent.useEffect"], [
        searchText,
        difficultyFilter,
        tagFilter
    ]); // eslint-disable-line react-hooks/exhaustive-deps
    // 处理URL参数中的highlight
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LevelsPageContent.useEffect": ()=>{
            const highlightParam = searchParams.get('highlight');
            if (highlightParam) {
                setHighlightLevelId(highlightParam);
                // 3秒后清除高亮
                setTimeout({
                    "LevelsPageContent.useEffect": ()=>{
                        setHighlightLevelId(null);
                    }
                }["LevelsPageContent.useEffect"], 3000);
            }
        }
    }["LevelsPageContent.useEffect"], [
        searchParams
    ]);
    // 处理删除关卡
    const handleDelete = async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelService"].delete(id);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('关卡删除成功');
            fetchLevels();
            fetchLevelStats();
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error('删除关卡失败');
        }
    };
    // 处理搜索
    const handleSearch = (value)=>{
        setSearchText(value);
    };
    // 重置筛选
    const handleReset = ()=>{
        setSearchText('');
        setDifficultyFilter(undefined);
        setTagFilter(undefined);
    };
    // 处理分页变化
    const handleTableChange = (page, pageSize)=>{
        fetchLevels({
            page,
            pageSize
        });
    };
    // 查看关卡详情
    const handleViewDetails = async (id)=>{
        try {
            console.log('正在获取关卡详情，ID:', id);
            const levelWithPhrases = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$levelService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["levelService"].getWithPhrases(id);
            console.log('获取到的关卡详情:', levelWithPhrases);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Modal$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].info({
                title: `关卡详情 - ${levelWithPhrases.name}`,
                width: 800,
                content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "难度:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 168,
                                    columnNumber: 16
                                }, this),
                                " ",
                                levelWithPhrases.difficulty
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 168,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "描述:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 169,
                                    columnNumber: 16
                                }, this),
                                " ",
                                levelWithPhrases.description || '无'
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 169,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "词组数量:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 170,
                                    columnNumber: 16
                                }, this),
                                " ",
                                levelWithPhrases.phrases?.length || 0
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 170,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "关卡标签:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 174,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        marginTop: 8
                                    },
                                    children: levelWithPhrases.tagIds && levelWithPhrases.tagIds.length > 0 ? levelWithPhrases.tagIds.map((tagId)=>{
                                        const tag = tags.find((t)=>t.id === tagId);
                                        return tag ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                            color: tag.color,
                                            style: {
                                                margin: 4
                                            },
                                            children: [
                                                tag.name,
                                                tag.isVip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    style: {
                                                        marginLeft: 4,
                                                        fontSize: '10px'
                                                    },
                                                    children: "VIP"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                                    lineNumber: 186,
                                                    columnNumber: 39
                                                }, this)
                                            ]
                                        }, tagId, true, {
                                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                            lineNumber: 180,
                                            columnNumber: 23
                                        }, this) : null;
                                    }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            color: '#999'
                                        },
                                        children: "无标签"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 191,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 175,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 173,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "包含的词组:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 197,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        marginTop: 8,
                                        maxHeight: 300,
                                        overflow: 'auto'
                                    },
                                    children: levelWithPhrases.phrases && levelWithPhrases.phrases.length > 0 ? levelWithPhrases.phrases.map((phrase)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                            style: {
                                                margin: 4
                                            },
                                            children: phrase.text
                                        }, phrase.id, false, {
                                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                            lineNumber: 201,
                                            columnNumber: 21
                                        }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            color: '#999'
                                        },
                                        children: "暂无词组"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 206,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 198,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 196,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                    lineNumber: 167,
                    columnNumber: 11
                }, this)
            });
        } catch (error) {
            console.error('获取关卡详情失败:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Message$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(`获取关卡详情失败: ${error.response?.data?.message || error.message || '未知错误'}`);
        }
    };
    const getDifficultyColor = (difficulty)=>{
        const colors = [
            '',
            'green',
            'blue',
            'orange',
            'red',
            'purple'
        ];
        return colors[difficulty] || 'default';
    };
    const columns = [
        {
            title: '关卡名称',
            dataIndex: 'name',
            key: 'name',
            width: 200
        },
        {
            title: '词组数量',
            dataIndex: 'phraseIds',
            key: 'phraseCount',
            width: 100,
            render: (phraseIds)=>phraseIds.length
        },
        {
            title: '标签',
            dataIndex: 'tagIds',
            key: 'tags',
            width: 200,
            render: (tagIds)=>{
                if (!tagIds || tagIds.length === 0) {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        style: {
                            color: '#999'
                        },
                        children: "无标签"
                    }, void 0, false, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 245,
                        columnNumber: 18
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        tagIds.slice(0, 3).map((tagId)=>{
                            const tag = tags.find((t)=>t.id === tagId);
                            if (!tag) return null;
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                color: tag.color,
                                style: {
                                    marginBottom: 4
                                },
                                children: [
                                    tag.name,
                                    tag.isVip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            marginLeft: 4,
                                            fontSize: '10px'
                                        },
                                        children: "VIP"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 259,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, tagId, true, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 253,
                                columnNumber: 17
                            }, this);
                        }),
                        tagIds.length > 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                            color: "default",
                            children: [
                                "+",
                                tagIds.length - 3
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 264,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                    lineNumber: 248,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            key: 'createdAt',
            width: 180
        },
        {
            title: '星级统计',
            key: 'starAnalytics',
            width: 150,
            render: (_, record)=>{
                const analytics = starAnalytics[record.id];
                const isLoading = loadingStars[record.id];
                if (isLoading) {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        style: {
                            color: '#999'
                        },
                        children: "加载中..."
                    }, void 0, false, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 285,
                        columnNumber: 18
                    }, this);
                }
                if (!analytics) {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                        type: "link",
                        size: "small",
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$StarOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__StarOutlined$3e$__["StarOutlined"], {}, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 293,
                            columnNumber: 21
                        }, void 0),
                        onClick: ()=>fetchLevelStarAnalytics(record.id),
                        children: "查看星级"
                    }, void 0, false, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 290,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                alignItems: 'center',
                                marginBottom: 4
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$rate$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Rate$3e$__["Rate"], {
                                    disabled: true,
                                    value: analytics.averageStars,
                                    allowHalf: true,
                                    style: {
                                        fontSize: 12
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 304,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    style: {
                                        marginLeft: 4,
                                        fontSize: 12,
                                        color: '#666'
                                    },
                                    children: analytics.averageStars.toFixed(1)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 310,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 303,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                fontSize: 11,
                                color: '#999'
                            },
                            children: [
                                "完成率: ",
                                (analytics.completionRate * 100).toFixed(1),
                                "%"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 314,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                    lineNumber: 302,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 250,
            render: (_, record)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                    size: "small",
                    wrap: true,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EyeOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeOutlined$3e$__["EyeOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 330,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleViewDetails(record.id),
                            children: "详情"
                        }, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 327,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$EditOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__EditOutlined$3e$__["EditOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 338,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>router.push(`/levels/${record.id}/edit`),
                            children: "编辑"
                        }, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 335,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tooltip$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tooltip$3e$__["Tooltip"], {
                            title: "查看该关卡的星级数据",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                                type: "link",
                                size: "small",
                                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$BarChartOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChartOutlined$3e$__["BarChartOutlined"], {}, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 347,
                                    columnNumber: 21
                                }, void 0),
                                onClick: ()=>router.push(`/user-stars?levelId=${record.id}`),
                                children: "星级"
                            }, void 0, false, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 344,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 343,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Popconfirm$3e$__["Popconfirm"], {
                            title: "确定要删除这个关卡吗？",
                            onConfirm: ()=>handleDelete(record.id),
                            okText: "确定",
                            cancelText: "取消",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                                type: "link",
                                danger: true,
                                size: "small",
                                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DeleteOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DeleteOutlined$3e$__["DeleteOutlined"], {}, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 359,
                                    columnNumber: 59
                                }, void 0),
                                children: "删除"
                            }, void 0, false, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 359,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 353,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                    lineNumber: 326,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "jsx-1b5ee15408482720",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                id: "1b5ee15408482720",
                children: ".highlighted-row{transition:background-color 3s ease-out;background-color:#fff7e6!important}.highlighted-row:hover{background-color:#ffd591!important}"
            }, void 0, false, void 0, this),
            levelStats && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$row$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], {
                gutter: 16,
                style: {
                    marginBottom: 16
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                        span: 8,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$statistic$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Statistic$3e$__["Statistic"], {
                                title: "当前关卡总数",
                                value: levelStats.total,
                                prefix: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$BarChartOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChartOutlined$3e$__["BarChartOutlined"], {}, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 388,
                                    columnNumber: 25
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 385,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 384,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 383,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                        span: 8,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$statistic$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Statistic$3e$__["Statistic"], {
                                title: "最大关卡限制",
                                value: levelStats.maxLevels,
                                prefix: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$BarChartOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChartOutlined$3e$__["BarChartOutlined"], {}, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 397,
                                    columnNumber: 25
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 394,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 393,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 392,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                        span: 8,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$statistic$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Statistic$3e$__["Statistic"], {
                                title: "剩余可创建",
                                value: levelStats.remaining,
                                prefix: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$BarChartOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChartOutlined$3e$__["BarChartOutlined"], {}, void 0, false, {
                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                    lineNumber: 406,
                                    columnNumber: 25
                                }, void 0),
                                valueStyle: {
                                    color: levelStats.remaining > 0 ? '#3f8600' : '#cf1322'
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 403,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                            lineNumber: 402,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 401,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                lineNumber: 382,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginBottom: 16
                        },
                        className: "jsx-1b5ee15408482720",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    marginBottom: 16
                                },
                                className: "jsx-1b5ee15408482720",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "jsx-1b5ee15408482720",
                                        children: "关卡管理"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 418,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlusOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlusOutlined$3e$__["PlusOutlined"], {}, void 0, false, {
                                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                            lineNumber: 421,
                                            columnNumber: 21
                                        }, void 0),
                                        onClick: ()=>router.push('/levels/create'),
                                        disabled: levelStats?.remaining === 0,
                                        children: "创建关卡"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 419,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 417,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    display: 'flex',
                                    gap: 16,
                                    alignItems: 'center',
                                    flexWrap: 'wrap'
                                },
                                className: "jsx-1b5ee15408482720",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__["Input"].Search, {
                                        placeholder: "搜索关卡名称或描述",
                                        allowClear: true,
                                        style: {
                                            width: 250
                                        },
                                        value: searchText,
                                        onChange: (e)=>setSearchText(e.target.value),
                                        onSearch: handleSearch,
                                        enterButton: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SearchOutlined$3e$__["SearchOutlined"], {}, void 0, false, {
                                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                            lineNumber: 438,
                                            columnNumber: 28
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 431,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Select$3e$__["Select"], {
                                        placeholder: "按难度筛选",
                                        allowClear: true,
                                        style: {
                                            width: 120
                                        },
                                        value: difficultyFilter,
                                        onChange: setDifficultyFilter,
                                        options: [
                                            {
                                                label: '1级',
                                                value: 1
                                            },
                                            {
                                                label: '2级',
                                                value: 2
                                            },
                                            {
                                                label: '3级',
                                                value: 3
                                            },
                                            {
                                                label: '4级',
                                                value: 4
                                            },
                                            {
                                                label: '5级',
                                                value: 5
                                            }
                                        ]
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 440,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Select$3e$__["Select"], {
                                        placeholder: "按标签筛选",
                                        allowClear: true,
                                        style: {
                                            width: 150
                                        },
                                        value: tagFilter,
                                        onChange: setTagFilter,
                                        options: tags.map((tag)=>({
                                                label: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: 4
                                                    },
                                                    className: "jsx-1b5ee15408482720",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                                            color: tag.color,
                                                            style: {
                                                                margin: 0
                                                            },
                                                            children: tag.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                                            lineNumber: 463,
                                                            columnNumber: 21
                                                        }, void 0),
                                                        tag.isVip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                                            color: "gold",
                                                            style: {
                                                                margin: 0,
                                                                fontSize: '10px',
                                                                padding: '0 4px'
                                                            },
                                                            children: "VIP"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                                            lineNumber: 464,
                                                            columnNumber: 35
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                                    lineNumber: 462,
                                                    columnNumber: 19
                                                }, void 0),
                                                value: tag.id
                                            }))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 454,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ReloadOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ReloadOutlined$3e$__["ReloadOutlined"], {}, void 0, false, {
                                            fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                            lineNumber: 470,
                                            columnNumber: 27
                                        }, void 0),
                                        onClick: handleReset,
                                        children: "重置"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                        lineNumber: 470,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                                lineNumber: 430,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 416,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$table$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__["Table"], {
                        columns: columns,
                        dataSource: levels,
                        rowKey: "id",
                        loading: loading,
                        rowClassName: (record)=>highlightLevelId === record.id ? 'highlighted-row' : '',
                        pagination: {
                            current: pagination.current,
                            pageSize: pagination.pageSize,
                            total: pagination.total,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
                            onChange: handleTableChange,
                            onShowSizeChange: handleTableChange,
                            pageSizeOptions: [
                                '10',
                                '20',
                                '50',
                                '100'
                            ]
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/app/(admin)/levels/page.tsx",
                        lineNumber: 476,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/(admin)/levels/page.tsx",
                lineNumber: 415,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/(admin)/levels/page.tsx",
        lineNumber: 369,
        columnNumber: 5
    }, this);
}
_s(LevelsPageContent, "SoEqddYUevMb69EOh5rVfL3V8nk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c = LevelsPageContent;
function LevelsPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: "Loading..."
        }, void 0, false, {
            fileName: "[project]/src/app/(admin)/levels/page.tsx",
            lineNumber: 503,
            columnNumber: 25
        }, void 0),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LevelsPageContent, {}, void 0, false, {
            fileName: "[project]/src/app/(admin)/levels/page.tsx",
            lineNumber: 504,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/(admin)/levels/page.tsx",
        lineNumber: 503,
        columnNumber: 5
    }, this);
}
_c1 = LevelsPage;
var _c, _c1;
__turbopack_context__.k.register(_c, "LevelsPageContent");
__turbopack_context__.k.register(_c1, "LevelsPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_cd0abfd3._.js.map