(()=>{var e={};e.id=7655,e.ids=[7655],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3404:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var l=r(80828),n=r(43210);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};var o=r(21898);let a=n.forwardRef(function(e,t){return n.createElement(o.A,(0,l.A)({},e,{ref:t,icon:s}))})},4691:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=r(7565).A},5754:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var l=r(80828),n=r(43210);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var o=r(21898);let a=n.forwardRef(function(e,t){return n.createElement(o.A,(0,l.A)({},e,{ref:t,icon:s}))})},7077:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var l=r(80828),n=r(43210);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};var o=r(21898);let a=n.forwardRef(function(e,t){return n.createElement(o.A,(0,l.A)({},e,{ref:t,icon:s}))})},10313:(e,t,r)=>{"use strict";r.d(t,{A:()=>h,d:()=>d});var l=r(43210),n=r.n(l),s=r(15693),o=r(44666),a=r(48232),i=r(10491),c=r(97058);function d(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function u(e){let{closable:t,closeIcon:r}=e||{};return n().useMemo(()=>{if(!t&&(!1===t||!1===r||null===r))return!1;if(void 0===t&&void 0===r)return null;let e={closeIcon:"boolean"!=typeof r&&null!==r?r:void 0};return t&&"object"==typeof t&&(e=Object.assign(Object.assign({},e),t)),e},[t,r])}let p={};function h(e,t,r=p){let l=u(e),d=u(t),[g]=(0,a.A)("global",i.A.global),v="boolean"!=typeof l&&!!(null==l?void 0:l.disabled),x=n().useMemo(()=>Object.assign({closeIcon:n().createElement(s.A,null)},r),[r]),m=n().useMemo(()=>!1!==l&&(l?(0,c.A)(x,d,l):!1!==d&&(d?(0,c.A)(x,d):!!x.closable&&x)),[l,d,x]);return n().useMemo(()=>{var e,t;if(!1===m)return[!1,null,v,{}];let{closeIconRender:r}=x,{closeIcon:l}=m,s=l,a=(0,o.A)(m,!0);return null!=s&&(r&&(s=r(l)),s=n().isValidElement(s)?n().cloneElement(s,Object.assign(Object.assign(Object.assign({},s.props),{"aria-label":null!=(t=null==(e=s.props)?void 0:e["aria-label"])?t:g.close}),a)):n().createElement("span",Object.assign({"aria-label":g.close},a),s)),[!0,s,v,a]},[m,x])}},10814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});let l=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15444:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,r)=>{Promise.resolve().then(r.bind(r,10814))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var l=r(60687),n=r(43210),s=r(85814),o=r.n(s),a=r(16189),i=r(98836),c=r(99053),d=r(63736),u=r(56072),p=r(78620);r(15444);var h=r(60203),g=r(81945),v=r(53788),x=r(9242),m=r(3788),f=r(73237),b=r(47453),y=r(31189),j=r(62727),A=r(14723),k=r(72061),C=r(80461),w=r(71103);let{Header:S,Content:P,Sider:z,Footer:I}=i.A,$=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,l.jsx)(h.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,l.jsx)(g.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,l.jsx)(v.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,l.jsx)(x.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,l.jsx)(m.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,l.jsx)(f.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,l.jsx)(b.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,l.jsx)(y.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,l.jsx)(j.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,l.jsx)(A.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,l.jsx)(k.A,{})}];function O({children:e}){let t=(0,a.useRouter)(),r=(0,a.usePathname)(),[s,h]=(0,n.useState)(!1),g=[{key:"logout",icon:(0,l.jsx)(C.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],v=$.find(e=>r.startsWith(e.path))?.key||"dashboard";return(0,l.jsxs)(i.A,{style:{minHeight:"100vh"},children:[(0,l.jsxs)(z,{collapsible:!0,collapsed:s,onCollapse:e=>h(e),children:[(0,l.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,l.jsx)(c.A.Text,{style:{color:"white",fontSize:s?"10px":"16px",transition:"font-size 0.2s"},children:s?"后台":"游戏管理后台"})}),(0,l.jsx)(d.A,{theme:"dark",selectedKeys:[v],mode:"inline",items:$.map(e=>({key:e.key,icon:e.icon,label:(0,l.jsx)(o(),{href:e.path,children:e.label})}))})]}),(0,l.jsxs)(i.A,{children:[(0,l.jsxs)(S,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,l.jsx)(u.A,{menu:{items:g},placement:"bottomRight",children:(0,l.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,l.jsx)(w.A,{})})}),(0,l.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,l.jsx)(P,{style:{margin:"16px"},children:e}),(0,l.jsxs)(I,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},52378:(e,t,r)=>{"use strict";r.d(t,{A:()=>$});var l=r(43210),n=r(69662),s=r.n(n),o=r(11056),a=r(41414),i=r(10313),c=r(56883),d=r(17727),u=r(71802),p=r(42411),h=r(73117),g=r(32476),v=r(60254),x=r(13581);let m=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:l,componentCls:n,calc:s}=e,o=s(l).sub(r).equal(),a=s(t).sub(r).equal();return{[n]:Object.assign(Object.assign({},(0,g.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:o,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${n}-close-icon`]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:o}}),[`${n}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},f=e=>{let{lineWidth:t,fontSizeIcon:r,calc:l}=e,n=e.fontSizeSM;return(0,v.oX)(e,{tagFontSize:n,tagLineHeight:(0,p.zA)(l(e.lineHeightSM).mul(n).equal()),tagIconSize:l(r).sub(l(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new h.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),y=(0,x.OF)("Tag",e=>m(f(e)),b);var j=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,l=Object.getOwnPropertySymbols(e);n<l.length;n++)0>t.indexOf(l[n])&&Object.prototype.propertyIsEnumerable.call(e,l[n])&&(r[l[n]]=e[l[n]]);return r};let A=l.forwardRef((e,t)=>{let{prefixCls:r,style:n,className:o,checked:a,onChange:i,onClick:c}=e,d=j(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:h}=l.useContext(u.QO),g=p("tag",r),[v,x,m]=y(g),f=s()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:a},null==h?void 0:h.className,o,x,m);return v(l.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},n),null==h?void 0:h.style),className:f,onClick:e=>{null==i||i(!a),null==c||c(e)}})))});var k=r(21821);let C=e=>(0,k.A)(e,(t,{textColor:r,lightBorderColor:l,lightColor:n,darkColor:s})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:n,borderColor:l,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),w=(0,x.bf)(["Tag","preset"],e=>C(f(e)),b),S=(e,t,r)=>{let l=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${l}Bg`],borderColor:e[`color${l}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},P=(0,x.bf)(["Tag","status"],e=>{let t=f(e);return[S(t,"success","Success"),S(t,"processing","Info"),S(t,"error","Error"),S(t,"warning","Warning")]},b);var z=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,l=Object.getOwnPropertySymbols(e);n<l.length;n++)0>t.indexOf(l[n])&&Object.prototype.propertyIsEnumerable.call(e,l[n])&&(r[l[n]]=e[l[n]]);return r};let I=l.forwardRef((e,t)=>{let{prefixCls:r,className:n,rootClassName:p,style:h,children:g,icon:v,color:x,onClose:m,bordered:f=!0,visible:b}=e,j=z(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:A,direction:k,tag:C}=l.useContext(u.QO),[S,I]=l.useState(!0),$=(0,o.A)(j,["closeIcon","closable"]);l.useEffect(()=>{void 0!==b&&I(b)},[b]);let O=(0,a.nP)(x),D=(0,a.ZZ)(x),E=O||D,q=Object.assign(Object.assign({backgroundColor:x&&!E?x:void 0},null==C?void 0:C.style),h),T=A("tag",r),[L,M,R]=y(T),B=s()(T,null==C?void 0:C.className,{[`${T}-${x}`]:E,[`${T}-has-color`]:x&&!E,[`${T}-hidden`]:!S,[`${T}-rtl`]:"rtl"===k,[`${T}-borderless`]:!f},n,p,M,R),_=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||I(!1)},[,H]=(0,i.A)((0,i.d)(e),(0,i.d)(C),{closable:!1,closeIconRender:e=>{let t=l.createElement("span",{className:`${T}-close-icon`,onClick:_},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null==(r=null==e?void 0:e.onClick)||r.call(e,t),_(t)},className:s()(null==e?void 0:e.className,`${T}-close-icon`)}))}}),V="function"==typeof j.onClick||g&&"a"===g.type,N=v||null,Y=N?l.createElement(l.Fragment,null,N,g&&l.createElement("span",null,g)):g,F=l.createElement("span",Object.assign({},$,{ref:t,className:B,style:q}),Y,H,O&&l.createElement(w,{key:"preset",prefixCls:T}),D&&l.createElement(P,{key:"status",prefixCls:T}));return L(V?l.createElement(d.A,{component:"Tag"},F):F)});I.CheckableTag=A;let $=I},54116:(e,t,r)=>{Promise.resolve().then(r.bind(r,85523))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59448:(e,t,r)=>{Promise.resolve().then(r.bind(r,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67620:(e,t,r)=>{Promise.resolve().then(r.bind(r,75257))},74075:e=>{"use strict";e.exports=require("zlib")},75257:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var l=r(60687),n=r(43210),s=r(70084),o=r(28243),a=r(35899),i=r(48111),c=r(78620),d=r(33519),u=r(77833),p=r(52378),h=r(70553),g=r(42585),v=r(96625),x=r(4691),m=r(94733),f=r(27783),b=r(71103),y=r(7077),j=r(79505),A=r(3404),k=r(92950),C=r(5754),w=r(16189),S=r(89900);let{Option:P}=s.A,{RangePicker:z}=o.A;function I(){let e=(0,w.useRouter)();(0,w.useSearchParams)();let[t,r]=(0,n.useState)([]),[o,I]=(0,n.useState)(!1),[$,O]=(0,n.useState)(null),[D,E]=(0,n.useState)(void 0),[q,T]=(0,n.useState)(void 0),[L,M]=(0,n.useState)(void 0),[R,B]=(0,n.useState)({current:1,pageSize:20,total:0,totalPages:0}),_=async e=>{I(!0);try{let t=await S.r.getAll({...e,page:e?.page||R.current,pageSize:e?.pageSize||R.pageSize});r(t.data||[]),B({current:t.page,pageSize:t.pageSize,total:t.total,totalPages:t.totalPages})}catch(e){console.error("Error fetching favorites:",e),r([{id:"1",userId:"user1",levelId:"level1",createdAt:"2024-01-10T10:30:00Z",user:{id:"user1",nickname:"张三",avatar:""},level:{id:"level1",title:"基础词汇练习1",difficulty:1,isVip:!1}},{id:"2",userId:"user2",levelId:"level2",createdAt:"2024-01-10T11:15:00Z",user:{id:"user2",nickname:"李四",avatar:""},level:{id:"level2",title:"商务英语入门",difficulty:2,isVip:!0}},{id:"3",userId:"user1",levelId:"level3",createdAt:"2024-01-10T14:20:00Z",user:{id:"user1",nickname:"张三",avatar:""},level:{id:"level3",title:"高级语法练习",difficulty:3,isVip:!0}}])}finally{I(!1)}},H=async()=>{try{let e={startDate:$?.[0],endDate:$?.[1],levelId:D,userId:L,difficulty:q,format:"excel"},t=await S.r.exportData(e),r=window.URL.createObjectURL(t),l=document.createElement("a");l.href=r,l.download=`user-favorites-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(r),a.Ay.success("导出成功")}catch(e){console.error("Export error:",e),a.Ay.error("导出失败")}},V=(e,t)=>{let r={page:e,pageSize:t||R.pageSize};$&&(r.startDate=$[0],r.endDate=$[1]),D&&(r.levelId=D),q&&(r.difficulty=q),L&&(r.userId=L),_(r)},N=e=>e<=1?"green":e<=2?"orange":e>=3?"red":"default",Y=e=>e<=1?"简单":e<=2?"中等":e>=3?"困难":"未知",F=[{title:"用户",key:"user",render:(e,t)=>(0,l.jsxs)(i.A,{children:[(0,l.jsx)(c.A,{src:t.user?.avatar,icon:(0,l.jsx)(b.A,{})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:t.user?.nickname}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",t.userId]})]})]})},{title:"收藏关卡",key:"level",render:(t,r)=>(0,l.jsxs)("div",{children:[(0,l.jsxs)(i.A,{children:[(0,l.jsx)("span",{children:r.level?.title}),(0,l.jsx)(d.A,{title:"跳转到关卡管理",children:(0,l.jsx)(u.Ay,{type:"link",size:"small",icon:(0,l.jsx)(y.A,{}),onClick:()=>e.push(`/levels?highlight=${r.levelId}`)})})]}),(0,l.jsxs)("div",{style:{marginTop:4},children:[(0,l.jsx)(p.A,{color:N(r.level?.difficulty||1),children:Y(r.level?.difficulty||1)}),r.level?.isVip&&(0,l.jsx)(p.A,{color:"purple",children:"VIP关卡"})]})]})},{title:"收藏时间",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleString(),sorter:(e,t)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()},{title:"操作",key:"action",render:(t,r)=>(0,l.jsxs)(i.A,{children:[(0,l.jsx)(d.A,{title:"查看用户详情",children:(0,l.jsx)(u.Ay,{type:"link",size:"small",icon:(0,l.jsx)(b.A,{}),onClick:()=>e.push(`/users?highlight=${r.userId}`),children:"用户"})}),(0,l.jsx)(d.A,{title:"查看关卡详情",children:(0,l.jsx)(u.Ay,{type:"link",size:"small",icon:(0,l.jsx)(j.A,{}),onClick:()=>e.push(`/levels?highlight=${r.levelId}`),children:"关卡"})})]})}];return o&&0===t.length?(0,l.jsxs)("div",{style:{textAlign:"center",padding:"100px 0"},children:[(0,l.jsx)(h.A,{size:"large"}),(0,l.jsx)("div",{style:{marginTop:16},children:"加载收藏数据中..."})]}):(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{style:{marginBottom:24},children:[(0,l.jsx)("h2",{children:"收藏管理"}),(0,l.jsx)("p",{children:"查看和分析用户的关卡收藏情况"})]}),(0,l.jsx)(g.A,{size:"small",style:{marginBottom:16},children:(0,l.jsxs)(v.A,{gutter:16,align:"middle",children:[(0,l.jsx)(x.A,{span:5,children:(0,l.jsx)(z,{value:$?[$[0],$[1]]:null,onChange:e=>{e?O([e[0].format("YYYY-MM-DD"),e[1].format("YYYY-MM-DD")]):O(null)},placeholder:["开始日期","结束日期"],style:{width:"100%"}})}),(0,l.jsx)(x.A,{span:4,children:(0,l.jsxs)(s.A,{placeholder:"选择关卡",value:D,onChange:E,allowClear:!0,style:{width:"100%"},children:[(0,l.jsx)(P,{value:"level1",children:"基础词汇练习1"}),(0,l.jsx)(P,{value:"level2",children:"商务英语入门"}),(0,l.jsx)(P,{value:"level3",children:"高级语法练习"})]})}),(0,l.jsx)(x.A,{span:3,children:(0,l.jsxs)(s.A,{placeholder:"难度筛选",value:q,onChange:T,allowClear:!0,style:{width:"100%"},children:[(0,l.jsx)(P,{value:1,children:"简单"}),(0,l.jsx)(P,{value:2,children:"中等"}),(0,l.jsx)(P,{value:3,children:"困难"}),(0,l.jsx)(P,{value:4,children:"很难"}),(0,l.jsx)(P,{value:5,children:"极难"})]})}),(0,l.jsx)(x.A,{span:4,children:(0,l.jsx)(m.A,{placeholder:"用户ID",value:L,onChange:e=>M(e.target.value||void 0),allowClear:!0,style:{width:"100%"}})}),(0,l.jsx)(x.A,{span:8,children:(0,l.jsxs)(i.A,{children:[(0,l.jsx)(u.Ay,{type:"primary",icon:(0,l.jsx)(A.A,{}),onClick:()=>{let e={page:1};$&&(e.startDate=$[0],e.endDate=$[1]),D&&(e.levelId=D),q&&(e.difficulty=q),L&&(e.userId=L),_(e)},children:"筛选"}),(0,l.jsx)(u.Ay,{icon:(0,l.jsx)(k.A,{}),onClick:()=>{O(null),E(void 0),T(void 0),M(void 0),_({page:1})},children:"重置"}),(0,l.jsx)(u.Ay,{icon:(0,l.jsx)(C.A,{}),onClick:H,children:"导出"})]})})]})}),(0,l.jsx)(g.A,{children:(0,l.jsx)(f.A,{columns:F,dataSource:t,rowKey:"id",loading:o,pagination:{current:R.current,pageSize:R.pageSize,total:R.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条，共 ${e} 条记录`,onChange:V,onShowSizeChange:V,pageSizeOptions:["10","20","50","100"]}})})]})}function $(){return(0,l.jsx)(n.Suspense,{fallback:(0,l.jsx)("div",{children:"Loading..."}),children:(0,l.jsx)(I,{})})}},77593:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var l=r(65239),n=r(48088),s=r(88170),o=r.n(s),a=r(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);r.d(t,i);let c={children:["",{children:["(admin)",{children:["user-favorites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85523)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-favorites\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-favorites\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new l.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(admin)/user-favorites/page",pathname:"/user-favorites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});let l=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\user-favorites\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\user-favorites\\page.tsx","default")},89900:(e,t,r)=>{"use strict";r.d(t,{r:()=>n});var l=r(49895);let n={getAll:async e=>(await l.Ay.get("/user-favorites",{params:e})).data,exportData:async e=>(await l.Ay.get("/user-favorites/export",{params:e,responseType:"blob"})).data};n.getAll},94735:e=>{"use strict";e.exports=require("events")},96625:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=r(20775).A}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),l=t.X(0,[4447,6267,1658,8161,675,5336,9196,5899,8331,553,84,7783,7436,976],()=>r(77593));module.exports=l})();