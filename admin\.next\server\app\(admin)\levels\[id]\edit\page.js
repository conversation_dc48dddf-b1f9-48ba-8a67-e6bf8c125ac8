(()=>{var e={};e.id=5144,e.ids=[5144],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5948:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var o=r(43210),n=r(69662),i=r.n(n),l=r(71802),a=r(40908),s=r(42411),d=r(32476),c=r(13581),p=r(60254);let u=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},m=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:o,lineWidth:n,textPaddingInline:i,orientationMargin:l,verticalMarginInline:a}=e;return{[t]:Object.assign(Object.assign({},(0,d.dF)(e)),{borderBlockStart:`${(0,s.zA)(n)} solid ${o}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:a,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,s.zA)(n)} solid ${o}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,s.zA)(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,s.zA)(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${o}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,s.zA)(n)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${l} * 100%)`},"&::after":{width:`calc(100% - ${l} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${l} * 100%)`},"&::after":{width:`calc(${l} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:i},"&-dashed":{background:"none",borderColor:o,borderStyle:"dashed",borderWidth:`${(0,s.zA)(n)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:n,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:o,borderStyle:"dotted",borderWidth:`${(0,s.zA)(n)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:n,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:r}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:r}}})}},g=(0,c.OF)("Divider",e=>{let t=(0,p.oX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[m(t),u(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var h=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let b={small:"sm",middle:"md"},f=e=>{let{getPrefixCls:t,direction:r,className:n,style:s}=(0,l.TP)("divider"),{prefixCls:d,type:c="horizontal",orientation:p="center",orientationMargin:u,className:m,rootClassName:f,children:y,dashed:x,variant:v="solid",plain:$,style:A,size:j}=e,w=h(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),C=t("divider",d),[k,S,I]=g(C),O=b[(0,a.A)(j)],z=!!y,E=o.useMemo(()=>"left"===p?"rtl"===r?"end":"start":"right"===p?"rtl"===r?"start":"end":p,[r,p]),P="start"===E&&null!=u,B="end"===E&&null!=u,N=i()(C,n,S,I,`${C}-${c}`,{[`${C}-with-text`]:z,[`${C}-with-text-${E}`]:z,[`${C}-dashed`]:!!x,[`${C}-${v}`]:"solid"!==v,[`${C}-plain`]:!!$,[`${C}-rtl`]:"rtl"===r,[`${C}-no-default-orientation-margin-start`]:P,[`${C}-no-default-orientation-margin-end`]:B,[`${C}-${O}`]:!!O},m,f),M=o.useMemo(()=>"number"==typeof u?u:/^\d+$/.test(u)?Number(u):u,[u]);return k(o.createElement("div",Object.assign({className:N,style:Object.assign(Object.assign({},s),A)},w,{role:"separator"}),y&&"vertical"!==c&&o.createElement("span",{className:`${C}-inner-text`,style:{marginInlineStart:P?M:void 0,marginInlineEnd:B?M:void 0}},y)))}},9146:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var o=r(60687),n=r(43210),i=r(16189),l=r(99053),a=r(94733),s=r(10411),d=r(35899),c=r(70553),p=r(29220),u=r(42585),m=r(5948),g=r(77833),h=r(27783),b=r(70084),f=r(52378),y=r(48111),x=r(10678),v=r(53082),$=r(23575),A=r(23870),j=r(70216);let{Title:w}=l.A,{TextArea:C}=a.A;function k(){let[e]=s.A.useForm(),t=(0,i.useRouter)(),r=(0,i.useParams)(),[l,k]=(0,n.useState)(!1),[S,I]=(0,n.useState)(!0),[O,z]=(0,n.useState)([]),[E,P]=(0,n.useState)([]),[B,N]=(0,n.useState)(null),[M,q]=(0,n.useState)(null),[T]=s.A.useForm(),[H,L]=(0,n.useState)(!1),_=r.id,R=e=>{z(t=>t.filter(t=>t.id!==e)),d.Ay.success("词组删除成功")},D=async e=>{if(!_)return void d.Ay.error("关卡ID不存在");if(0===O.length)return void d.Ay.error("请添加词组");k(!0);try{let r=O.filter(e=>!e.id.startsWith("temp_")).map(e=>e.id),o=O.filter(e=>e.id.startsWith("temp_"));console.log("现有词组ID:",r),console.log("新词组:",o);let n=o.map(e=>({text:e.text,meaning:e.meaning})),i={name:e.name,difficulty:e.difficulty,description:e.description,tagIds:e.tagIds||[],phraseIds:r,phrases:n.length>0?n:void 0,thesaurusIds:M?.thesaurusIds||[]};console.log("更新参数:",i),console.log("请求URL:",`${j.i.FULL_BASE_URL}/levels/${_}`),console.log("请求方法: PATCH");let l=await A.k3.update(_,i);console.log("更新结果:",l),o.length>0?d.Ay.success(`关卡更新成功！已创建 ${o.length} 个新词组并添加到关卡中`):d.Ay.success("关卡更新成功"),t.push("/levels")}catch(t){console.error("更新关卡失败:",t);let e=t.response?.data?.message||t.message||"更新关卡失败";d.Ay.error(`更新关卡失败: ${e}`)}finally{k(!1)}};return S?(0,o.jsxs)("div",{style:{padding:"24px",textAlign:"center"},children:[(0,o.jsx)(c.A,{size:"large"}),(0,o.jsx)("div",{style:{marginTop:16},children:"正在加载关卡详情..."})]}):(0,o.jsxs)("div",{children:[M&&(0,o.jsx)(p.A,{message:`正在编辑关卡：${M.name} (难度: ${M.difficulty})`,type:"info",style:{marginBottom:16},showIcon:!0}),(0,o.jsxs)(u.A,{children:[(0,o.jsx)(w,{level:2,children:"编辑关卡"}),(0,o.jsxs)(s.A,{form:e,layout:"vertical",onFinish:D,initialValues:{difficulty:3},children:[(0,o.jsx)(s.A.Item,{name:"name",label:"关卡名称",rules:[{required:!0,message:"请输入关卡名称"}],children:(0,o.jsx)(a.A,{placeholder:"请输入关卡名称"})}),(0,o.jsx)(s.A.Item,{name:"description",label:"关卡描述",children:(0,o.jsx)(C,{rows:3,placeholder:"请输入关卡描述（可选）"})}),(0,o.jsx)(m.A,{children:"添加关卡词组"}),(0,o.jsx)(p.A,{message:"提示：请添加要包含在关卡中的词组。",type:"info",style:{marginBottom:16}}),(0,o.jsx)("div",{style:{marginBottom:16},children:(0,o.jsx)(g.Ay,{type:"dashed",icon:(0,o.jsx)(v.A,{}),onClick:()=>L(!0),style:{width:"100%"},children:"添加词组"})}),(0,o.jsx)("div",{style:{marginBottom:16},children:(0,o.jsx)(h.A,{dataSource:O,rowKey:"id",pagination:!1,size:"small",columns:[{title:"英文",dataIndex:"text",key:"text",render:e=>e||"未设置"},{title:"中文",dataIndex:"meaning",key:"meaning",render:e=>e||"未设置"},{title:"操作",key:"action",width:80,render:(e,t)=>(0,o.jsx)(g.Ay,{type:"link",danger:!0,size:"small",icon:(0,o.jsx)($.A,{}),onClick:()=>R(t.id)})}],locale:{emptyText:"暂无词组，请点击上方按钮添加"}})}),(0,o.jsx)(m.A,{children:"关卡标签"}),(0,o.jsx)(s.A.Item,{name:"tagIds",label:"选择标签",help:"为关卡添加标签，便于分类和筛选",children:(0,o.jsx)(b.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择关卡标签",filterOption:(e,t)=>t?.label?.toLowerCase().includes(e.toLowerCase()),optionRender:e=>(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,o.jsx)(f.A,{color:E.find(t=>t.id===e.value)?.color,children:e.label}),E.find(t=>t.id===e.value)?.isVip&&(0,o.jsx)(f.A,{color:"gold",style:{fontSize:"10px",padding:"0 4px"},children:"VIP"})]}),options:E.map(e=>({label:e.name,value:e.id}))})}),(0,o.jsx)(s.A.Item,{children:(0,o.jsxs)(y.A,{children:[(0,o.jsx)(g.Ay,{type:"primary",htmlType:"submit",loading:l,children:"更新关卡"}),(0,o.jsx)(g.Ay,{onClick:()=>t.back(),children:"取消"})]})})]})]}),(0,o.jsx)(x.A,{title:"添加词组",open:H,onCancel:()=>L(!1),footer:null,width:500,children:(0,o.jsxs)(s.A,{form:T,layout:"vertical",onFinish:e=>{if(O.some(t=>t.text.toLowerCase().trim()===e.text.toLowerCase().trim()))return void d.Ay.error("该英文词组已存在，请勿重复添加");let t={id:`temp_${Date.now()}`,text:e.text.trim(),meaning:e.meaning.trim()};z(e=>[...e,t]),T.resetFields(),L(!1),d.Ay.success("词组添加成功")},children:[(0,o.jsx)(s.A.Item,{name:"text",label:"英文",rules:[{required:!0,message:"请输入英文"},{validator:(e,t)=>t&&O.some(e=>e.text.toLowerCase().trim()===t.toLowerCase().trim())?Promise.reject(Error("该英文词组已存在")):Promise.resolve()}],children:(0,o.jsx)(a.A,{placeholder:"请输入英文词组"})}),(0,o.jsx)(s.A.Item,{name:"meaning",label:"中文",rules:[{required:!0,message:"请输入中文"}],children:(0,o.jsx)(a.A,{placeholder:"请输入中文意思"})}),(0,o.jsx)(s.A.Item,{children:(0,o.jsxs)(y.A,{children:[(0,o.jsx)(g.Ay,{type:"primary",htmlType:"submit",children:"添加词组"}),(0,o.jsx)(g.Ay,{onClick:()=>L(!1),children:"取消"})]})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23575:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(80828),n=r(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var l=r(21898);let a=n.forwardRef(function(e,t){return n.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29220:(e,t,r)=>{"use strict";r.d(t,{A:()=>q});var o=r(43210),n=r(91039),i=r(41514),l=r(15693),a=r(51297),s=r(74550),d=r(69662),c=r.n(d),p=r(13934),u=r(44666),m=r(7224),g=r(56883),h=r(71802),b=r(42411),f=r(32476),y=r(13581);let x=(e,t,r,o,n)=>({background:e,border:`${(0,b.zA)(o.lineWidth)} ${o.lineType} ${t}`,[`${n}-icon`]:{color:r}}),v=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:o,marginSM:n,fontSize:i,fontSizeLG:l,lineHeight:a,borderRadiusLG:s,motionEaseInOutCirc:d,withDescriptionIconSize:c,colorText:p,colorTextHeading:u,withDescriptionPadding:m,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:s,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:o,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:a},"&-message":{color:u},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${d}, opacity ${r} ${d},
        padding-top ${r} ${d}, padding-bottom ${r} ${d},
        margin-bottom ${r} ${d}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:m,[`${t}-icon`]:{marginInlineEnd:n,fontSize:c,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:o,color:u,fontSize:l},[`${t}-description`]:{display:"block",color:p}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},$=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:o,colorSuccessBg:n,colorWarning:i,colorWarningBorder:l,colorWarningBg:a,colorError:s,colorErrorBorder:d,colorErrorBg:c,colorInfo:p,colorInfoBorder:u,colorInfoBg:m}=e;return{[t]:{"&-success":x(n,o,r,e,t),"&-info":x(m,u,p,e,t),"&-warning":x(a,l,i,e,t),"&-error":Object.assign(Object.assign({},x(c,d,s,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},A=e=>{let{componentCls:t,iconCls:r,motionDurationMid:o,marginXS:n,fontSizeIcon:i,colorIcon:l,colorIconHover:a}=e;return{[t]:{"&-action":{marginInlineStart:n},[`${t}-close-icon`]:{marginInlineStart:n,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,b.zA)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:l,transition:`color ${o}`,"&:hover":{color:a}}},"&-close-text":{color:l,transition:`color ${o}`,"&:hover":{color:a}}}}},j=(0,y.OF)("Alert",e=>[v(e),$(e),A(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var w=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let C={success:n.A,info:s.A,error:i.A,warning:a.A},k=e=>{let{icon:t,prefixCls:r,type:n}=e,i=C[n]||null;return t?(0,g.fx)(t,o.createElement("span",{className:`${r}-icon`},t),()=>({className:c()(`${r}-icon`,t.props.className)})):o.createElement(i,{className:`${r}-icon`})},S=e=>{let{isClosable:t,prefixCls:r,closeIcon:n,handleClose:i,ariaProps:a}=e,s=!0===n||void 0===n?o.createElement(l.A,null):n;return t?o.createElement("button",Object.assign({type:"button",onClick:i,className:`${r}-close-icon`,tabIndex:0},a),s):null},I=o.forwardRef((e,t)=>{let{description:r,prefixCls:n,message:i,banner:l,className:a,rootClassName:s,style:d,onMouseEnter:g,onMouseLeave:b,onClick:f,afterClose:y,showIcon:x,closable:v,closeText:$,closeIcon:A,action:C,id:I}=e,O=w(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[z,E]=o.useState(!1),P=o.useRef(null);o.useImperativeHandle(t,()=>({nativeElement:P.current}));let{getPrefixCls:B,direction:N,closable:M,closeIcon:q,className:T,style:H}=(0,h.TP)("alert"),L=B("alert",n),[_,R,D]=j(L),F=t=>{var r;E(!0),null==(r=e.onClose)||r.call(e,t)},W=o.useMemo(()=>void 0!==e.type?e.type:l?"warning":"info",[e.type,l]),G=o.useMemo(()=>"object"==typeof v&&!!v.closeIcon||!!$||("boolean"==typeof v?v:!1!==A&&null!=A||!!M),[$,A,v,M]),X=!!l&&void 0===x||x,U=c()(L,`${L}-${W}`,{[`${L}-with-description`]:!!r,[`${L}-no-icon`]:!X,[`${L}-banner`]:!!l,[`${L}-rtl`]:"rtl"===N},T,a,s,D,R),V=(0,u.A)(O,{aria:!0,data:!0}),K=o.useMemo(()=>"object"==typeof v&&v.closeIcon?v.closeIcon:$||(void 0!==A?A:"object"==typeof M&&M.closeIcon?M.closeIcon:q),[A,v,$,q]),Q=o.useMemo(()=>{let e=null!=v?v:M;if("object"==typeof e){let{closeIcon:t}=e;return w(e,["closeIcon"])}return{}},[v,M]);return _(o.createElement(p.Ay,{visible:!z,motionName:`${L}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:y},({className:t,style:n},l)=>o.createElement("div",Object.assign({id:I,ref:(0,m.K4)(P,l),"data-show":!z,className:c()(U,t),style:Object.assign(Object.assign(Object.assign({},H),d),n),onMouseEnter:g,onMouseLeave:b,onClick:f,role:"alert"},V),X?o.createElement(k,{description:r,icon:e.icon,prefixCls:L,type:W}):null,o.createElement("div",{className:`${L}-content`},i?o.createElement("div",{className:`${L}-message`},i):null,r?o.createElement("div",{className:`${L}-description`},r):null),C?o.createElement("div",{className:`${L}-action`},C):null,o.createElement(S,{isClosable:G,prefixCls:L,closeIcon:K,handleClose:F,ariaProps:Q}))))});var O=r(67737),z=r(49617),E=r(30402),P=r(85764),B=r(1630),N=r(69561);let M=function(e){function t(){var e,r,o;return(0,O.A)(this,t),r=t,o=arguments,r=(0,E.A)(r),(e=(0,B.A)(this,(0,P.A)()?Reflect.construct(r,o||[],(0,E.A)(this).constructor):r.apply(this,o))).state={error:void 0,info:{componentStack:""}},e}return(0,N.A)(t,e),(0,z.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:n}=this.props,{error:i,info:l}=this.state,a=(null==l?void 0:l.componentStack)||null,s=void 0===e?(i||"").toString():e;return i?o.createElement(I,{id:r,type:"error",message:s,description:o.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?a:t)}):n}}])}(o.Component);I.ErrorBoundary=M;let q=I},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var o=r(65239),n=r(48088),i=r(88170),l=r.n(i),a=r(30893),s={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);r.d(t,s);let d={children:["",{children:["(admin)",{children:["levels",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,91028)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(admin)/levels/[id]/edit/page",pathname:"/levels/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52378:(e,t,r)=>{"use strict";r.d(t,{A:()=>z});var o=r(43210),n=r(69662),i=r.n(n),l=r(11056),a=r(41414),s=r(10313),d=r(56883),c=r(17727),p=r(71802),u=r(42411),m=r(73117),g=r(32476),h=r(60254),b=r(13581);let f=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:n,calc:i}=e,l=i(o).sub(r).equal(),a=i(t).sub(r).equal();return{[n]:Object.assign(Object.assign({},(0,g.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,u.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${n}-close-icon`]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${n}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:r,calc:o}=e,n=e.fontSizeSM;return(0,h.oX)(e,{tagFontSize:n,tagLineHeight:(0,u.zA)(o(e.lineHeightSM).mul(n).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},x=e=>({defaultBg:new m.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),v=(0,b.OF)("Tag",e=>f(y(e)),x);var $=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let A=o.forwardRef((e,t)=>{let{prefixCls:r,style:n,className:l,checked:a,onChange:s,onClick:d}=e,c=$(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:u,tag:m}=o.useContext(p.QO),g=u("tag",r),[h,b,f]=v(g),y=i()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:a},null==m?void 0:m.className,l,b,f);return h(o.createElement("span",Object.assign({},c,{ref:t,style:Object.assign(Object.assign({},n),null==m?void 0:m.style),className:y,onClick:e=>{null==s||s(!a),null==d||d(e)}})))});var j=r(21821);let w=e=>(0,j.A)(e,(t,{textColor:r,lightBorderColor:o,lightColor:n,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:n,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),C=(0,b.bf)(["Tag","preset"],e=>w(y(e)),x),k=(e,t,r)=>{let o=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},S=(0,b.bf)(["Tag","status"],e=>{let t=y(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]},x);var I=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let O=o.forwardRef((e,t)=>{let{prefixCls:r,className:n,rootClassName:u,style:m,children:g,icon:h,color:b,onClose:f,bordered:y=!0,visible:x}=e,$=I(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:A,direction:j,tag:w}=o.useContext(p.QO),[k,O]=o.useState(!0),z=(0,l.A)($,["closeIcon","closable"]);o.useEffect(()=>{void 0!==x&&O(x)},[x]);let E=(0,a.nP)(b),P=(0,a.ZZ)(b),B=E||P,N=Object.assign(Object.assign({backgroundColor:b&&!B?b:void 0},null==w?void 0:w.style),m),M=A("tag",r),[q,T,H]=v(M),L=i()(M,null==w?void 0:w.className,{[`${M}-${b}`]:B,[`${M}-has-color`]:b&&!B,[`${M}-hidden`]:!k,[`${M}-rtl`]:"rtl"===j,[`${M}-borderless`]:!y},n,u,T,H),_=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||O(!1)},[,R]=(0,s.A)((0,s.d)(e),(0,s.d)(w),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:`${M}-close-icon`,onClick:_},e);return(0,d.fx)(e,t,e=>({onClick:t=>{var r;null==(r=null==e?void 0:e.onClick)||r.call(e,t),_(t)},className:i()(null==e?void 0:e.className,`${M}-close-icon`)}))}}),D="function"==typeof $.onClick||g&&"a"===g.type,F=h||null,W=F?o.createElement(o.Fragment,null,F,g&&o.createElement("span",null,g)):g,G=o.createElement("span",Object.assign({},z,{ref:t,className:L,style:N}),W,R,E&&o.createElement(C,{key:"preset",prefixCls:M}),P&&o.createElement(S,{key:"status",prefixCls:M}));return q(D?o.createElement(c.A,{component:"Tag"},G):G)});O.CheckableTag=A;let z=O},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77471:(e,t,r)=>{Promise.resolve().then(r.bind(r,9146))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91028:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\levels\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},95623:(e,t,r)=>{Promise.resolve().then(r.bind(r,91028))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,6267,1658,8161,675,5336,9196,5899,8331,553,84,7783,411,7503,678,976,9778],()=>r(40387));module.exports=o})();