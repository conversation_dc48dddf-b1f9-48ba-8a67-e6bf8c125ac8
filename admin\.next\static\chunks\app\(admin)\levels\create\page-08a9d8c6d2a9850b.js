(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1368],{4438:(e,t,l)=>{Promise.resolve().then(l.bind(l,41842))},41842:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>b});var i=l(95155),s=l(12115),r=l(35695),n=l(97605),a=l(56020),o=l(86615),d=l(19868),c=l(95108),m=l(6124),x=l(94600),h=l(77325),u=l(51087),y=l(20778),A=l(37974),p=l(12320),j=l(46002),g=l(46996),v=l(56170),f=l(49179);let{Title:w}=n.A,{TextArea:I}=a.A;function b(){let[e]=o.A.useForm(),t=(0,r.useRouter)(),[l,n]=(0,s.useState)(!1),[b,k]=(0,s.useState)([]),[C,S]=(0,s.useState)([]),[L,_]=(0,s.useState)(null),[B]=o.A.useForm(),[E,F]=(0,s.useState)(!1),z=e=>{k(t=>t.filter(t=>t.id!==e)),d.Ay.success("词组删除成功")},P=async()=>{try{let e=await f.m9.getAll();S(e.filter(e=>"active"===e.status))}catch(e){d.Ay.error("获取标签列表失败")}},q=async()=>{try{let e=await f.k3.getCount();_(e)}catch(e){d.Ay.error("获取关卡统计失败")}};(0,s.useEffect)(()=>{P(),q()},[]);let N=async e=>{if(0===b.length)return void d.Ay.error("请添加词组");n(!0);try{let l={name:e.name,difficulty:1,description:e.description,thesaurusIds:[],phrases:b.map(e=>({text:e.text,meaning:e.meaning})),tagIds:e.tagIds||[]};await f.k3.create(l),d.Ay.success("关卡创建成功"),t.push("/levels")}catch(e){d.Ay.error("创建关卡失败")}finally{n(!1)}};return(0,i.jsxs)("div",{children:[L&&(0,i.jsx)(c.A,{message:"当前已创建 ".concat(L.total," 个关卡，还可以创建 ").concat(L.remaining," 个关卡（最大限制：").concat(L.maxLevels,"）"),type:L.remaining>0?"info":"warning",style:{marginBottom:16},showIcon:!0}),(0,i.jsxs)(m.A,{children:[(0,i.jsx)(w,{level:2,children:"创建新关卡"}),(0,i.jsxs)(o.A,{form:e,layout:"vertical",onFinish:N,initialValues:{difficulty:3},children:[(0,i.jsx)(o.A.Item,{name:"name",label:"关卡名称",rules:[{required:!0,message:"请输入关卡名称"}],children:(0,i.jsx)(a.A,{placeholder:"例如：第1关 - 基础词汇"})}),(0,i.jsx)(o.A.Item,{name:"description",label:"关卡描述",children:(0,i.jsx)(I,{rows:3,placeholder:"请输入关卡描述（可选）"})}),(0,i.jsx)(x.A,{children:"添加关卡词组"}),(0,i.jsx)(c.A,{message:"提示：请添加要包含在关卡中的词组。",type:"info",style:{marginBottom:16}}),(0,i.jsx)("div",{style:{marginBottom:16},children:(0,i.jsx)(h.Ay,{type:"dashed",icon:(0,i.jsx)(g.A,{}),onClick:()=>F(!0),style:{width:"100%"},children:"添加词组"})}),(0,i.jsxs)("div",{style:{marginBottom:16},children:[(0,i.jsxs)("div",{style:{marginBottom:8,fontSize:"12px",color:"#666"},children:["当前词组数量: ",b.length]}),(0,i.jsx)(u.A,{dataSource:b,rowKey:e=>e.id,pagination:!1,size:"small",columns:[{title:"英文",dataIndex:"text",key:"text",render:e=>e||"未设置"},{title:"中文",dataIndex:"meaning",key:"meaning",render:e=>e||"未设置"},{title:"操作",key:"action",width:80,render:(e,t)=>(0,i.jsx)(h.Ay,{type:"link",danger:!0,size:"small",icon:(0,i.jsx)(v.A,{}),onClick:()=>z(t.id)})}],locale:{emptyText:"暂无词组，请点击上方按钮添加"}})]}),(0,i.jsx)(x.A,{children:"关卡标签"}),(0,i.jsx)(o.A.Item,{name:"tagIds",label:"选择标签",help:"为关卡添加标签，便于分类和筛选",children:(0,i.jsx)(y.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择关卡标签",filterOption:(e,t)=>{var l;return null==t||null==(l=t.label)?void 0:l.toLowerCase().includes(e.toLowerCase())},optionRender:e=>{var t,l;return(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,i.jsx)(A.A,{color:null==(t=C.find(t=>t.id===e.value))?void 0:t.color,children:e.label}),(null==(l=C.find(t=>t.id===e.value))?void 0:l.isVip)&&(0,i.jsx)(A.A,{color:"gold",style:{fontSize:"10px",padding:"0 4px"},children:"VIP"})]})},options:C.map(e=>({label:e.name,value:e.id}))})}),(0,i.jsx)(o.A.Item,{children:(0,i.jsxs)(p.A,{children:[(0,i.jsx)(h.Ay,{type:"primary",htmlType:"submit",loading:l,disabled:(null==L?void 0:L.remaining)===0,children:"创建关卡"}),(0,i.jsx)(h.Ay,{onClick:()=>t.back(),children:"取消"})]})})]})]}),(0,i.jsx)(j.A,{title:"添加词组",open:E,onCancel:()=>F(!1),footer:null,width:500,children:(0,i.jsxs)(o.A,{form:B,layout:"vertical",onFinish:e=>{if(b.some(t=>t.text.toLowerCase().trim()===e.text.toLowerCase().trim()))return void d.Ay.error("该英文词组已存在，请勿重复添加");let t={id:"temp_".concat(Date.now()),text:e.text.trim(),meaning:e.meaning.trim()};k(e=>[...e,t]),B.resetFields(),F(!1),d.Ay.success("词组添加成功")},children:[(0,i.jsx)(o.A.Item,{name:"text",label:"英文",rules:[{required:!0,message:"请输入英文"},{validator:(e,t)=>t&&b.some(e=>e.text.toLowerCase().trim()===t.toLowerCase().trim())?Promise.reject(Error("该英文词组已存在")):Promise.resolve()}],children:(0,i.jsx)(a.A,{placeholder:"请输入英文词组"})}),(0,i.jsx)(o.A.Item,{name:"meaning",label:"中文",rules:[{required:!0,message:"请输入中文"}],children:(0,i.jsx)(a.A,{placeholder:"请输入中文意思"})}),(0,i.jsx)(o.A.Item,{children:(0,i.jsxs)(p.A,{children:[(0,i.jsx)(h.Ay,{type:"primary",htmlType:"submit",children:"添加词组"}),(0,i.jsx)(h.Ay,{onClick:()=>F(!1),children:"取消"})]})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[1291,5573,4492,5669,3464,9484,9868,9301,6312,778,2343,1087,6615,7605,404,6002,2552,3884,9179,8441,1684,7358],()=>t(4438)),_N_E=e.O()}]);