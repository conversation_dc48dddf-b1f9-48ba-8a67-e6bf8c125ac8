(()=>{var e={};e.id=5496,e.ids=[5496],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10814:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15444:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,s)=>{Promise.resolve().then(s.bind(s,10814))},25227:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["(admin)",{children:["thesauruses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57470)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(admin)/thesauruses/page",pathname:"/thesauruses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25901:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(60687),a=s(43210),n=s(35899),i=s(10678),o=s(48111),l=s(77833),d=s(42585),c=s(99053),u=s(27783),p=s(80828);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var m=s(21898),x=a.forwardRef(function(e,t){return a.createElement(m.A,(0,p.A)({},e,{ref:t,icon:h}))}),y=s(53082),f=s(16189),b=s(52931);let g=()=>{let[e,t]=(0,a.useState)([]),[s,p]=(0,a.useState)(!1),h=(0,f.useRouter)(),m=(0,a.useCallback)(async()=>{p(!0);try{let e=await (0,b.zN)();t(e)}catch(e){console.error("获取词库列表失败:",e),n.Ay.error(e.message||"获取词库列表失败！")}finally{p(!1)}},[]);(0,a.useEffect)(()=>{m()},[m]);let g=(0,a.useCallback)(async e=>{try{await (0,b.Io)(e),n.Ay.success("词库删除成功！"),m()}catch(t){console.error(`删除词库 ${e} 失败:`,t),n.Ay.error(t.message||"词库删除失败！")}},[m]),A=(0,a.useCallback)(e=>{i.A.confirm({title:`确定要删除词库 "${e.name}" 吗?`,icon:(0,r.jsx)(x,{}),content:"此操作不可撤销。如果有关联的关卡，请谨慎操作。",okText:"删除",okType:"danger",cancelText:"取消",onOk(){g(e.id)}})},[g]),v=[{title:"名称",dataIndex:"name",key:"name"},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"词组数",dataIndex:"phraseIds",key:"phraseCount",render:e=>e?.length||0},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleString()},{title:"更新时间",dataIndex:"updatedAt",key:"updatedAt",render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",render:(e,t)=>(0,r.jsxs)(o.A,{size:"middle",children:[(0,r.jsx)(l.Ay,{size:"small",onClick:()=>{n.Ay.info(`编辑词库 ${t.id} (功能待实现)`)},children:"编辑"}),(0,r.jsx)(l.Ay,{size:"small",danger:!0,onClick:()=>A(t),children:"删除"})]})}];return(0,r.jsxs)(d.A,{children:[(0,r.jsx)(c.A.Title,{level:2,children:"词库管理"}),(0,r.jsx)(l.Ay,{type:"primary",icon:(0,r.jsx)(y.A,{}),onClick:()=>h.push("/thesauruses/create"),style:{marginBottom:16},children:"创建词库"}),(0,r.jsx)(u.A,{columns:v,dataSource:e,rowKey:"id",loading:s})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30421:(e,t,s)=>{Promise.resolve().then(s.bind(s,57470))},33873:e=>{"use strict";e.exports=require("path")},37912:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),o=s(16189),l=s(98836),d=s(99053),c=s(63736),u=s(56072),p=s(78620);s(15444);var h=s(60203),m=s(81945),x=s(53788),y=s(9242),f=s(3788),b=s(73237),g=s(47453),A=s(31189),v=s(62727),k=s(14723),j=s(72061),w=s(80461),P=s(71103);let{Header:C,Content:q,Sider:z,Footer:I}=l.A,_=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,r.jsx)(h.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,r.jsx)(m.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,r.jsx)(x.A,{})},{key:"level-tags",label:"标签管理",path:"/level-tags",icon:(0,r.jsx)(y.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,r.jsx)(f.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,r.jsx)(b.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,r.jsx)(g.A,{})},{key:"activation-codes",label:"激活码管理",path:"/activation-codes",icon:(0,r.jsx)(A.A,{})},{key:"user-stars",label:"星级管理",path:"/user-stars",icon:(0,r.jsx)(v.A,{})},{key:"user-favorites",label:"收藏管理",path:"/user-favorites",icon:(0,r.jsx)(k.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,r.jsx)(j.A,{})}];function D({children:e}){let t=(0,o.useRouter)(),s=(0,o.usePathname)(),[n,h]=(0,a.useState)(!1),m=[{key:"logout",icon:(0,r.jsx)(w.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],x=_.find(e=>s.startsWith(e.path))?.key||"dashboard";return(0,r.jsxs)(l.A,{style:{minHeight:"100vh"},children:[(0,r.jsxs)(z,{collapsible:!0,collapsed:n,onCollapse:e=>h(e),children:[(0,r.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,r.jsx)(d.A.Text,{style:{color:"white",fontSize:n?"10px":"16px",transition:"font-size 0.2s"},children:n?"后台":"游戏管理后台"})}),(0,r.jsx)(c.A,{theme:"dark",selectedKeys:[x],mode:"inline",items:_.map(e=>({key:e.key,icon:e.icon,label:(0,r.jsx)(i(),{href:e.path,children:e.label})}))})]}),(0,r.jsxs)(l.A,{children:[(0,r.jsxs)(C,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,r.jsx)(u.A,{menu:{items:m},placement:"bottomRight",children:(0,r.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,r.jsx)(P.A,{})})}),(0,r.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,r.jsx)(q,{style:{margin:"16px"},children:e}),(0,r.jsxs)(I,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},52931:(e,t,s)=>{"use strict";s.d(t,{Au:()=>a,Io:()=>i,LQ:()=>n,zN:()=>o});var r=s(49895);let a={getAll:async()=>(await r.Ay.get("/thesauruses")).data,getById:async e=>(await r.Ay.get(`/thesauruses/${e}`)).data,create:async e=>(await r.Ay.post("/thesauruses",e)).data,update:async(e,t)=>(await r.Ay.patch(`/thesauruses/${e}`,t)).data,delete:async e=>{await r.Ay.delete(`/thesauruses/${e}`)},addPhrase:async(e,t)=>(await r.Ay.post(`/thesauruses/${e}/phrases`,t)).data,removePhrase:async(e,t)=>(await r.Ay.delete(`/thesauruses/${e}/phrases/${t}`)).data},n=a.create;a.update,a.delete;let i=a.delete;a.getById,a.getAll;let o=a.getAll},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57470:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\thesauruses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx","default")},59448:(e,t,s)=>{Promise.resolve().then(s.bind(s,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72277:(e,t,s)=>{Promise.resolve().then(s.bind(s,25901))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,6267,1658,8161,675,5336,9196,5899,553,84,7783,7503,678,976],()=>s(25227));module.exports=r})();