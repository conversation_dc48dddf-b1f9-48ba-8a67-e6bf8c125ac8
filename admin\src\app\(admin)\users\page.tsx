'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Form, Input, Popconfirm, Card, Tag, Avatar,
  Statistic, Row, Col, Switch, InputNumber, Tooltip, Select, DatePicker, Drawer,
  Timeline, Descriptions, Progress, Tabs, Badge, Checkbox, Upload, Divider
} from 'antd';
import { Modal, message } from '@/components';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, ReloadOutlined,
  TrophyOutlined, CrownOutlined, GiftOutlined, SearchOutlined, FilterOutlined,
  ExportOutlined, ImportOutlined, EyeOutlined, HistoryOutlined, SettingOutlined,
  DownloadOutlined, UploadOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import request from '@/services/request';
import { userService, User, CreateUserParams, UpdateUserParams, UserStats, SetVipParams, CancelVipParams, vipPackageService, VipPackage } from '@/services';

const { Option } = Select;
const { RangePicker } = DatePicker;

// VIP统计数据类型
interface VipStats {
  totalUsers: number;
  vipUsers: number;
  normalUsers: number;
  vipRate: number;
  totalRevenue: number;
  avgDailyUnlocks: number;
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [statsModalVisible, setStatsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [selectedUserStats, setSelectedUserStats] = useState<UserStats | null>(null);
  const [form] = Form.useForm();

  // 新增状态变量
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterVip, setFilterVip] = useState<boolean | undefined>(undefined);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [userDetailVisible, setUserDetailVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userActivityVisible, setUserActivityVisible] = useState(false);
  const [userActivity, setUserActivity] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  // 删除不再使用的userStatistics

  // VIP统计数据
  const [vipStats, setVipStats] = useState<VipStats>({
    totalUsers: 0,
    vipUsers: 0,
    normalUsers: 0,
    vipRate: 0,
    totalRevenue: 0,
    avgDailyUnlocks: 0,
  });

  // VIP设置相关状态
  const [vipModalVisible, setVipModalVisible] = useState(false);
  const [vipSettingUser, setVipSettingUser] = useState<User | null>(null);
  const [vipPackages, setVipPackages] = useState<VipPackage[]>([]);
  const [vipForm] = Form.useForm();

  // 获取VIP套餐列表
  const fetchVipPackages = async () => {
    try {
      const packages = await vipPackageService.getList({ isActive: true }) || [];
      // 确保返回的是数组
      setVipPackages(Array.isArray(packages) ? packages : []);
    } catch (error) {
      console.error('获取VIP套餐失败:', error);
      setVipPackages([]); // 出错时设置为空数组
    }
  };

  // 获取用户列表（支持搜索和分页）
  const fetchUsers = async (params?: {
    search?: string;
    isVip?: boolean;
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  }) => {
    setLoading(true);
    try {
      const requestParams = {
        ...(params?.search && { search: params.search }),
        ...(params?.isVip !== undefined && { isVip: params.isVip.toString() }),
        ...(params?.startDate && { startDate: params.startDate }),
        ...(params?.endDate && { endDate: params.endDate }),
        page: (params?.page || pagination.current).toString(),
        pageSize: (params?.pageSize || pagination.pageSize).toString(),
      };

      const response = await request.get('/users', { params: requestParams });
      const result = response.data;

      setUsers(result.users || []);
      setPagination(prev => ({
        ...prev,
        total: result.total || 0,
        current: params?.page || prev.current,
      }));

      // 计算VIP统计数据
      const newVipStats = calculateVipStats(result.users || []);
      setVipStats(newVipStats);
    } catch (error) {
      message.error('获取用户列表失败');
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  // 计算VIP统计数据
  const calculateVipStats = (userList: User[]): VipStats => {
    const totalUsers = userList.length;
    const vipUsers = userList.filter(u => u.isVip).length;
    const normalUsers = totalUsers - vipUsers;
    const vipRate = totalUsers > 0 ? (vipUsers / totalUsers) * 100 : 0;
    const totalRevenue = 0; // 这里需要从支付订单中计算
    const avgDailyUnlocks = totalUsers > 0 ? userList.reduce((sum, u) => sum + u.dailyUnlockCount, 0) / totalUsers : 0;

    return {
      totalUsers,
      vipUsers,
      normalUsers,
      vipRate,
      totalRevenue,
      avgDailyUnlocks,
    };
  };

  // 删除不再使用的fetchUserStatistics函数

  useEffect(() => {
    fetchUsers();
    fetchVipPackages();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    const params: any = {};
    if (searchText) params.search = searchText;
    if (filterVip !== undefined) params.isVip = filterVip;
    if (dateRange) {
      params.startDate = dateRange[0];
      params.endDate = dateRange[1];
    }
    params.page = 1;

    setPagination(prev => ({ ...prev, current: 1 }));
    fetchUsers(params);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    setFilterVip(undefined);
    setDateRange(null);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchUsers({ page: 1 });
  };

  // 分页处理
  const handleTableChange = (page: number, pageSize?: number) => {
    const params: any = { page, pageSize };
    if (searchText) params.search = searchText;
    if (filterVip !== undefined) params.isVip = filterVip;
    if (dateRange) {
      params.startDate = dateRange[0];
      params.endDate = dateRange[1];
    }

    setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || prev.pageSize }));
    fetchUsers(params);
  };

  // 查看用户详情
  const handleViewUser = async (user: User) => {
    setSelectedUser(user);
    setUserDetailVisible(true);
  };

  // 查看用户活动日志
  const handleViewActivity = async (user: User) => {
    try {
      const response = await request.get(`/users/${user.id}/activity-log`);
      setUserActivity(response.data.logs || []);
      setSelectedUser(user);
      setUserActivityVisible(true);
    } catch (error) {
      message.error('获取用户活动日志失败');
    }
  };

  // 批量更新VIP状态
  const handleBatchUpdateVip = async (isVip: boolean) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的用户');
      return;
    }

    try {
      if (isVip) {
        // 批量设置VIP时，使用默认套餐或让用户选择
        // 这里先获取第一个可用的套餐作为默认套餐
        const availablePackages = vipPackages.filter(pkg => pkg.isActive);
        if (availablePackages.length === 0) {
          message.error('没有可用的VIP套餐');
          return;
        }

        const defaultPackage = availablePackages[0];

        const params: SetVipParams = {
          packageId: defaultPackage.id,
          reason: "管理员批量设置"
        };
        await userService.batchSetVipStatus(selectedRowKeys, params);
      } else {
        // 批量取消VIP时只传递必要参数
        const params: CancelVipParams = {
          reason: "管理员批量设置",
          immediate: true
        };
        await userService.batchCancelVipStatus(selectedRowKeys, params);
      }
      message.success(`批量${isVip ? '设置' : '取消'}VIP成功`);
      setSelectedRowKeys([]);
      fetchUsers();
    } catch (error) {
      console.error('批量更新VIP状态失败:', error);
      // 错误信息已由request统一处理
    }
  };

  // 批量重置进度
  const handleBatchResetProgress = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的用户');
      return;
    }

    Modal.confirm({
      title: '确认重置用户进度',
      content: `确定要重置选中的 ${selectedRowKeys.length} 个用户的游戏进度吗？此操作不可恢复。`,
      onOk: async () => {
        try {
          const promises = selectedRowKeys.map(userId =>
            request.post(`/users/${userId}/reset-progress`)
          );

          await Promise.all(promises);
          message.success('批量重置进度成功');
          setSelectedRowKeys([]);
          fetchUsers();
        } catch (error) {
          message.error('批量重置进度失败');
        }
      },
    });
  };

  // 处理创建/编辑用户
  const handleSubmit = async (values: CreateUserParams | UpdateUserParams) => {
    try {
      if (editingUser) {
        await userService.update(editingUser.id, values as UpdateUserParams);
        message.success('用户更新成功');
      } else {
        await userService.create(values as CreateUserParams);
        message.success('用户创建成功');
      }

      setModalVisible(false);
      setEditingUser(null);
      form.resetFields();
      fetchUsers();
    } catch {
      message.error(editingUser ? '更新用户失败' : '创建用户失败');
    }
  };

  // 处理删除用户
  const handleDelete = async (id: string) => {
    try {
      await userService.delete(id);
      message.success('用户删除成功');
      fetchUsers();
    } catch {
      message.error('删除用户失败');
    }
  };

  // 重置用户进度
  const handleResetProgress = async (id: string) => {
    try {
      await userService.resetProgress(id);
      message.success('用户进度重置成功');
      fetchUsers();
    } catch {
      message.error('重置用户进度失败');
    }
  };

  // 查看用户统计
  const handleViewStats = async (user: User) => {
    try {
      const stats = await userService.getStats(user.id);
      setSelectedUserStats(stats);
      setStatsModalVisible(true);
    } catch {
      message.error('获取用户统计失败');
    }
  };

  // 打开VIP设置弹窗
  const handleOpenVipModal = (user: User) => {
    if (user.isVip) {
      // 如果用户已经是VIP，直接取消VIP
      handleCancelVip(user);
    } else {
      // 如果用户不是VIP，打开设置弹窗
      setVipSettingUser(user);
      setVipModalVisible(true);
      vipForm.resetFields();
    }
  };

  // 取消VIP
  const handleCancelVip = async (user: User) => {
    try {
      const params: CancelVipParams = {
        reason: "管理员手动取消",
        immediate: true
      };

      await userService.cancelVipStatus(user.id, params);
      fetchUsers();
    } catch (error) {
      console.error('取消VIP失败:', error);
      // 错误信息已由request统一处理
    }
  };

  // 设置VIP
  const handleSetVip = async (values: any) => {
    if (!vipSettingUser) return;

    try {
      if (!values.packageId) {
        message.error('请选择VIP套餐');
        return;
      }

      const params: SetVipParams = {
        packageId: values.packageId,
        reason: values.reason || "管理员手动设置"
      };

      await userService.setVipStatus(vipSettingUser.id, params);
      setVipModalVisible(false);
      setVipSettingUser(null);
      vipForm.resetFields();
      fetchUsers();
    } catch (error) {
      console.error('设置VIP失败:', error);
      // 错误信息已由request统一处理
    }
  };

  // 打开编辑模态框
  const handleEdit = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue({
      phone: user.phone,
      openid: user.openid,
      nickname: user.nickname,
      avatarUrl: user.avatarUrl,
      unlockedLevels: user.unlockedLevels,
      isVip: user.isVip,
      dailyUnlockLimit: user.dailyUnlockLimit,
    });
    setModalVisible(true);
  };

  // 打开创建模态框
  const handleCreate = () => {
    setEditingUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  const columns: ColumnsType<User> = [
    {
      title: '用户信息',
      key: 'user',
      width: 250,
      render: (_, record) => (
        <Space>
          <Avatar src={record.avatarUrl} icon={<UserOutlined />} />
          <div>
            <div>{record.nickname || '未设置昵称'}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ID: {record.id}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              手机: {record.phone}
            </div>
            {record.openid && (
              <div style={{ fontSize: '12px', color: '#999' }}>
                OpenID: {record.openid.substring(0, 8)}...
              </div>
            )}
            {record.isVip && (
              <Tag color="gold" icon={<CrownOutlined />} style={{ marginTop: 4 }}>
                VIP
              </Tag>
            )}
          </div>
        </Space>
      ),
    },
    {
      title: '游戏进度',
      key: 'progress',
      width: 150,
      render: (_, record) => (
        <div>
          <div>已开启: {record.unlockedLevels} 关</div>
          <div>已通关: {record.completedLevelIds.length} 关</div>
        </div>
      ),
    },
    {
      title: '游戏统计',
      key: 'stats',
      width: 150,
      render: (_, record) => (
        <div>
          <div>总游戏: {record.totalGames} 次</div>
          <div>总通关: {record.totalCompletions} 次</div>
        </div>
      ),
    },
    {
      title: '每日解锁',
      key: 'dailyUnlock',
      width: 150,
      render: (_, record) => (
        <div>
          <div>
            今日: {record.dailyUnlockCount || 0}/{record.dailyUnlockLimit || 15}
            {record.isVip && (
              <Tag color="gold" style={{ marginLeft: 4, fontSize: '12px' }}>
                无限制
              </Tag>
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            分享: {record.totalShares || 0} 次
          </div>
          {record.dailyShared && (
            <Tag color="green" style={{ fontSize: '12px' }}>
              今日已分享
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '最后游戏',
      dataIndex: 'lastPlayTime',
      key: 'lastPlayTime',
      width: 180,
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 280,
      render: (_, record) => (
        <Space size="small" wrap>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewUser(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<HistoryOutlined />}
            onClick={() => handleViewActivity(record)}
          >
            日志
          </Button>
          <Button
            type="link"
            size="small"
            icon={<TrophyOutlined />}
            onClick={() => handleViewStats(record)}
          >
            统计
          </Button>
          <Tooltip title={record.isVip ? '取消VIP' : '设为VIP'}>
            <Button
              type="link"
              size="small"
              icon={<CrownOutlined />}
              style={{ color: record.isVip ? '#faad14' : '#d9d9d9' }}
              onClick={() => handleOpenVipModal(record)}
            >
              VIP
            </Button>
          </Tooltip>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要重置用户进度吗？"
            onConfirm={() => handleResetProgress(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" icon={<ReloadOutlined />}>
              重置
            </Button>
          </Popconfirm>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger size="small" icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[]) => {
      setSelectedRowKeys(selectedKeys as string[]);
    },
  };

  return (
    <div>
      {/* VIP统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={vipStats.totalUsers}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="VIP用户"
              value={vipStats.vipUsers}
              valueStyle={{ color: '#faad14' }}
              prefix={<CrownOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="VIP转化率"
              value={vipStats.vipRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: vipStats.vipRate >= 10 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均解锁数"
              value={vipStats.avgDailyUnlocks}
              precision={1}
              suffix="次/日"
              prefix={<GiftOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2>用户管理</h2>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
              创建用户
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选栏 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                placeholder="搜索用户昵称、手机号"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                prefix={<SearchOutlined />}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="筛选VIP状态"
                value={filterVip}
                onChange={setFilterVip}
                allowClear
                style={{ width: '100%' }}
              >
                <Option value={true}>
                  <CrownOutlined style={{ color: '#faad14', marginRight: 4 }} />
                  VIP用户
                </Option>
                <Option value={false}>
                  <UserOutlined style={{ color: '#d9d9d9', marginRight: 4 }} />
                  普通用户
                </Option>
              </Select>
            </Col>
            <Col span={6}>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={dateRange as any}
                onChange={(dates) => {
                  if (dates) {
                    setDateRange([dates[0]!.format('YYYY-MM-DD'), dates[1]!.format('YYYY-MM-DD')]);
                  } else {
                    setDateRange(null);
                  }
                }}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 批量操作栏 */}
        {selectedRowKeys.length > 0 && (
          <Card size="small" style={{ marginBottom: 16, backgroundColor: '#f6ffed' }}>
            <Row justify="space-between" align="middle">
              <Col>
                <span>已选择 {selectedRowKeys.length} 个用户</span>
              </Col>
              <Col>
                <Space>
                  <Button
                    type="primary"
                    size="small"
                    icon={<CrownOutlined />}
                    onClick={() => handleBatchUpdateVip(true)}
                  >
                    批量设为VIP
                  </Button>
                  <Button
                    size="small"
                    onClick={() => handleBatchUpdateVip(false)}
                  >
                    批量取消VIP
                  </Button>
                  <Button
                    danger
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={handleBatchResetProgress}
                  >
                    批量重置进度
                  </Button>
                  <Button
                    size="small"
                    onClick={() => setSelectedRowKeys([])}
                  >
                    取消选择
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>
        )}

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
        />
      </Card>

      {/* 创建/编辑用户模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '创建用户'}
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {!editingUser && (
            <Form.Item
              name="phone"
              label="手机号"
              rules={[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
              ]}
            >
              <Input placeholder="请输入手机号" />
            </Form.Item>
          )}

          {editingUser && (
            <Form.Item
              name="phone"
              label="手机号"
              rules={[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
              ]}
            >
              <Input placeholder="请输入手机号" />
            </Form.Item>
          )}

          <Form.Item
            name="openid"
            label="微信OpenID"
          >
            <Input placeholder="请输入微信用户OpenID（可选）" />
          </Form.Item>

          <Form.Item
            name="nickname"
            label="昵称"
          >
            <Input placeholder="请输入用户昵称" />
          </Form.Item>

          <Form.Item
            name="avatarUrl"
            label="头像URL"
          >
            <Input placeholder="请输入头像URL" />
          </Form.Item>

          {editingUser && (
            <>
              <Form.Item
                name="unlockedLevels"
                label="已开启关卡数"
              >
                <InputNumber min={1} max={1000} placeholder="已开启关卡数" style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="isVip"
                label="VIP状态"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren={<CrownOutlined />}
                  unCheckedChildren="普通"
                />
              </Form.Item>

              <Form.Item
                name="dailyUnlockLimit"
                label="每日解锁限制"
                tooltip="VIP用户无限制，普通用户默认15次"
              >
                <InputNumber
                  min={1}
                  max={999}
                  placeholder="每日解锁限制次数"
                  style={{ width: '100%' }}
                  addonAfter="次/天"
                />
              </Form.Item>
            </>
          )}

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingUser ? '更新' : '创建'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingUser(null);
                form.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 用户统计模态框 */}
      <Modal
        title="用户游戏统计"
        visible={statsModalVisible}
        onCancel={() => setStatsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setStatsModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedUserStats && (
          <div>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Statistic title="总游戏次数" value={selectedUserStats.totalGames} />
              </Col>
              <Col span={12}>
                <Statistic title="总通关次数" value={selectedUserStats.totalCompletions} />
              </Col>
              <Col span={12}>
                <Statistic title="已解锁关卡" value={selectedUserStats.unlockedLevels} />
              </Col>
              <Col span={12}>
                <Statistic title="已完成关卡" value={selectedUserStats.completedLevels} />
              </Col>
              <Col span={24}>
                <Statistic
                  title="通关率"
                  value={selectedUserStats.completionRate}
                  precision={2}
                  suffix="%"
                />
              </Col>
            </Row>

            <Card title="每日解锁统计" size="small">
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="今日解锁"
                    value={selectedUserStats.dailyUnlockCount}
                    suffix={`/ ${selectedUserStats.isVip ? '∞' : selectedUserStats.dailyUnlockLimit}`}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="剩余次数"
                    value={selectedUserStats.isVip ? '∞' : selectedUserStats.remainingUnlocks}
                  />
                </Col>
                <Col span={8}>
                  <Statistic title="总分享次数" value={selectedUserStats.totalShares} />
                </Col>
                <Col span={24} style={{ marginTop: 16 }}>
                  {selectedUserStats.isVip ? (
                    <Tag color="gold" icon={<CrownOutlined />} style={{ fontSize: '14px', padding: '4px 8px' }}>
                      VIP用户 - 无限制解锁
                    </Tag>
                  ) : (
                    <Tag color="blue" icon={<GiftOutlined />} style={{ fontSize: '14px', padding: '4px 8px' }}>
                      普通用户 - 每日{selectedUserStats.dailyUnlockLimit}次解锁
                    </Tag>
                  )}
                </Col>
              </Row>
            </Card>
          </div>
        )}
      </Modal>

      {/* VIP设置弹窗 */}
      <Modal
        title={`设置VIP - ${vipSettingUser?.nickname || '未设置昵称'}`}
        visible={vipModalVisible}
        onCancel={() => {
          setVipModalVisible(false);
          setVipSettingUser(null);
          vipForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={vipForm}
          layout="vertical"
          onFinish={handleSetVip}
        >
          <Form.Item
            name="packageId"
            label="选择VIP套餐"
            rules={[{ required: true, message: '请选择VIP套餐' }]}
          >
            <Select placeholder="请选择VIP套餐" size="large">
              {Array.isArray(vipPackages) && vipPackages.map(pkg => (
                <Select.Option key={pkg.id} value={pkg.id}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{pkg.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>{pkg.description}</div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div style={{ color: '#f50', fontWeight: 'bold' }}>¥{(pkg.price / 100).toFixed(2)}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>{pkg.duration}天</div>
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="reason"
            label="操作原因"
            initialValue="管理员手动设置"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入设置VIP的原因"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setVipModalVisible(false);
                setVipSettingUser(null);
                vipForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认设置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 用户详情抽屉 */}
      <Drawer
        title="用户详情"
        placement="right"
        width={600}
        open={userDetailVisible}
        onClose={() => setUserDetailVisible(false)}
      >
        {selectedUser && (
          <Tabs
            defaultActiveKey="basic"
            items={[
              {
                key: 'basic',
                label: '基本信息',
                children: (
                  <Descriptions column={1} bordered>
                    <Descriptions.Item label="用户ID">{selectedUser.id}</Descriptions.Item>
                    <Descriptions.Item label="昵称">{selectedUser.nickname || '未设置'}</Descriptions.Item>
                    <Descriptions.Item label="手机号">{selectedUser.phone}</Descriptions.Item>
                    <Descriptions.Item label="OpenID">{selectedUser.openid}</Descriptions.Item>
                    <Descriptions.Item label="头像">
                      <Avatar src={selectedUser.avatarUrl} size={64} icon={<UserOutlined />} />
                    </Descriptions.Item>
                    <Descriptions.Item label="VIP状态">
                      <Badge
                        status={selectedUser.isVip ? "success" : "default"}
                        text={selectedUser.isVip ? "VIP用户" : "普通用户"}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="注册时间">
                      {new Date(selectedUser.createdAt).toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="最后登录">
                      {selectedUser.lastPlayTime ? new Date(selectedUser.lastPlayTime).toLocaleString() : '从未登录'}
                    </Descriptions.Item>
                  </Descriptions>
                ),
              },
              {
                key: 'progress',
                label: '游戏进度',
                children: (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Card size="small">
                      <Statistic title="已解锁关卡" value={selectedUser.unlockedLevels} suffix="关" />
                    </Card>
                    <Card size="small">
                      <Statistic title="已完成关卡" value={selectedUser.completedLevelIds.length} suffix="关" />
                    </Card>
                    <Card size="small">
                      <div>
                        <div style={{ marginBottom: 8 }}>游戏进度</div>
                        <Progress
                          percent={selectedUser.unlockedLevels > 0 ? Math.round((selectedUser.completedLevelIds.length / selectedUser.unlockedLevels) * 100) : 0}
                          status="active"
                        />
                      </div>
                    </Card>
                  </Space>
                ),
              },
              {
                key: 'stats',
                label: '使用统计',
                children: (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Card size="small">
                        <Statistic title="总游戏次数" value={selectedUser.totalGames} />
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small">
                        <Statistic title="总通关次数" value={selectedUser.totalCompletions} />
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small">
                        <Statistic title="分享次数" value={selectedUser.totalShares || 0} />
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small">
                        <Statistic
                          title="每日解锁"
                          value={selectedUser.dailyUnlockCount || 0}
                          suffix={`/ ${selectedUser.dailyUnlockLimit || 15}`}
                        />
                      </Card>
                    </Col>
                  </Row>
                ),
              },
            ]}
          />
        )}
      </Drawer>

      {/* 用户活动日志抽屉 */}
      <Drawer
        title="用户活动日志"
        placement="right"
        width={800}
        open={userActivityVisible}
        onClose={() => setUserActivityVisible(false)}
      >
        {selectedUser && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Avatar src={selectedUser.avatarUrl} icon={<UserOutlined />} />
                <div>
                  <div>{selectedUser.nickname || '未设置昵称'}</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>ID: {selectedUser.id}</div>
                </div>
              </Space>
            </div>

            <Timeline>
              {userActivity.map((activity, index) => (
                <Timeline.Item key={index} color={activity.action === 'login' ? 'green' : 'blue'}>
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{activity.description}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {new Date(activity.timestamp).toLocaleString()}
                    </div>
                    {activity.ip && (
                      <div style={{ fontSize: '12px', color: '#999' }}>
                        IP: {activity.ip}
                      </div>
                    )}
                    {activity.details && (
                      <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                        {JSON.stringify(activity.details)}
                      </div>
                    )}
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>

            {userActivity.length === 0 && (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                暂无活动记录
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
}
