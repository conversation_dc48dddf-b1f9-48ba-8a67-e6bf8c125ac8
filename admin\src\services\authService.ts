import request from './request';

// 登录请求参数类型
export interface LoginParams {
  username: string;
  password: string;
}

// 登录响应类型
export interface LoginResponse {
  message: string;
  accessToken: string;
}

// 认证服务
export const authService = {
  // 登录
  login: async (params: LoginParams): Promise<LoginResponse> => {
    const response = await request.post<LoginResponse>('/auth/login', params);
    return response.data;
  },

  // 登出
  logout: () => {
    localStorage.removeItem('admin_token');
    window.location.href = '/login';
  },

  // 获取当前token
  getToken: (): string | null => {
    return localStorage.getItem('admin_token');
  },

  // 设置token
  setToken: (token: string): void => {
    localStorage.setItem('admin_token', token);
  },

  // 检查是否已登录
  isLoggedIn: (): boolean => {
    return !!localStorage.getItem('admin_token');
  },
};

// 保持向后兼容的导出
export const login = authService.login;
