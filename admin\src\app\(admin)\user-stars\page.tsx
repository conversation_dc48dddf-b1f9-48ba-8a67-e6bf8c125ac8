'use client';

import React, { useState, useEffect, Suspense } from 'react';
import {
  Table, Card, Row, Col, Statistic, Select, DatePicker, Button, Space,
  Tag, Progress, Tooltip, message, Input, Spin
} from 'antd';
import {
  StarOutlined, TrophyOutlined, UserOutlined, Bar<PERSON><PERSON>Outlined,
  ReloadOutlined, DownloadOutlined, FilterOutlined, LinkOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  userStarService,
  type UserStar as UserStarType,
  type UserStarQueryParams
} from '@/services/userStarService';
import { useRouter, useSearchParams } from 'next/navigation';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 移除本地类型定义，直接使用API返回的统计数据类型

function UserStarsPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [stars, setStars] = useState<UserStarType[]>([]);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<string | undefined>(undefined);
  const [selectedStars, setSelectedStars] = useState<number | undefined>(undefined);
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>(undefined);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  // 获取星级数据
  const fetchStars = async (params?: UserStarQueryParams) => {
    setLoading(true);
    try {
      const result = await userStarService.getAll({
        ...params,
        page: params?.page || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize
      });

      setStars(result.data || []);
      setPagination({
        current: result.page,
        pageSize: result.pageSize,
        total: result.total,
        totalPages: result.totalPages
      });
    } catch (error) {
      console.error('Error fetching stars:', error);
      // 使用模拟数据
      setStars([
        {
          id: '1',
          userId: 'user1',
          levelId: 'level1',
          stars: 5,
          completedAt: '2024-01-10T10:30:00Z',
          playTime: 120,
          user: {
            id: 'user1',
            nickname: '张三',
            avatar: '',
          },
          level: {
            id: 'level1',
            title: '基础词汇练习1',
            difficulty: 1,
          },
        },
        {
          id: '2',
          userId: 'user2',
          levelId: 'level1',
          stars: 3,
          completedAt: '2024-01-10T11:15:00Z',
          playTime: 180,
          user: {
            id: 'user2',
            nickname: '李四',
            avatar: '',
          },
          level: {
            id: 'level1',
            title: '基础词汇练习1',
            difficulty: 1,
          },
        },
        {
          id: '3',
          userId: 'user3',
          levelId: 'level2',
          stars: 4,
          completedAt: '2024-01-10T14:20:00Z',
          playTime: 150,
          user: {
            id: 'user3',
            nickname: '王五',
            avatar: '',
          },
          level: {
            id: 'level2',
            title: '商务英语入门',
            difficulty: 2,
          },
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 删除统计数据获取功能

  useEffect(() => {
    // 处理URL参数
    const levelIdParam = searchParams.get('levelId');
    if (levelIdParam) {
      setSelectedLevel(levelIdParam);
    }

    fetchStars();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 当筛选条件改变时重新获取数据
  useEffect(() => {
    if (selectedLevel) {
      handleFilter();
    }
  }, [selectedLevel]); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理筛选
  const handleFilter = () => {
    const params: UserStarQueryParams = {
      page: 1, // 重置到第一页
    };
    if (dateRange) {
      params.startDate = dateRange[0];
      params.endDate = dateRange[1];
    }
    if (selectedLevel) params.levelId = selectedLevel;
    if (selectedStars) params.stars = selectedStars;
    if (selectedUserId) params.userId = selectedUserId;

    fetchStars(params);
  };

  // 重置筛选
  const handleReset = () => {
    setDateRange(null);
    setSelectedLevel(undefined);
    setSelectedStars(undefined);
    setSelectedUserId(undefined);
    fetchStars({ page: 1 });
  };

  // 导出数据
  const handleExport = async () => {
    try {
      const params = {
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        levelId: selectedLevel,
        stars: selectedStars,
        userId: selectedUserId,
        format: 'excel' as const,
      };

      const blob = await userStarService.exportData(params);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `user-stars-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      console.error('Export error:', error);
      message.error('导出失败');
    }
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize?: number) => {
    const params: UserStarQueryParams = {
      page,
      pageSize: pageSize || pagination.pageSize,
    };

    if (dateRange) {
      params.startDate = dateRange[0];
      params.endDate = dateRange[1];
    }
    if (selectedLevel) params.levelId = selectedLevel;
    if (selectedStars) params.stars = selectedStars;
    if (selectedUserId) params.userId = selectedUserId;

    fetchStars(params);
  };

  // 渲染星级
  const renderStars = (stars: number, maxStars: number) => {
    const percentage = (stars / maxStars) * 100;
    return (
      <Space>
        <span>{stars}/{maxStars}</span>
        <div style={{ display: 'flex', gap: '2px' }}>
          {Array.from({ length: maxStars }, (_, index) => (
            <StarOutlined
              key={index}
              style={{
                color: index < stars ? '#fadb14' : '#d9d9d9',
                fontSize: '14px',
              }}
            />
          ))}
        </div>
        <Progress
          percent={percentage}
          size="small"
          showInfo={false}
          strokeColor={percentage >= 80 ? '#52c41a' : percentage >= 60 ? '#faad14' : '#ff4d4f'}
        />
      </Space>
    );
  };

  // 表格列定义
  const columns: ColumnsType<UserStarType> = [
    {
      title: '用户',
      key: 'user',
      render: (_, record) => (
        <Space>
          <div>
            <div>{record.user?.nickname}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ID: {record.userId}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '关卡',
      key: 'level',
      render: (_, record) => (
        <Space>
          <span>{record.level?.title}</span>
          <Tooltip title="跳转到关卡管理">
            <Button
              type="link"
              size="small"
              icon={<LinkOutlined />}
              onClick={() => router.push(`/levels?highlight=${record.levelId}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '星级评分',
      key: 'stars',
      render: (_, record) => renderStars(record.stars, 5),
    },
    {
      title: '游戏时长',
      dataIndex: 'playTime',
      key: 'playTime',
      render: (time) => {
        const minutes = Math.floor(time / 60);
        const seconds = time % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
      },
    },
    {
      title: '完成时间',
      dataIndex: 'completedAt',
      key: 'completedAt',
      render: (date) => new Date(date).toLocaleString(),
    },
  ];

  if (loading && stars.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载星级数据中...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <h2>星级管理</h2>
        <p>查看和分析用户的关卡完成星级评分</p>
      </div>

      {/* 删除统计卡片和星级分布 */}

      {/* 筛选和操作栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={5}>
            <RangePicker
              value={dateRange ? [dateRange[0], dateRange[1]] as any : null}
              onChange={(dates) => {
                if (dates) {
                  setDateRange([dates[0]!.format('YYYY-MM-DD'), dates[1]!.format('YYYY-MM-DD')]);
                } else {
                  setDateRange(null);
                }
              }}
              placeholder={['开始日期', '结束日期']}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择关卡"
              value={selectedLevel}
              onChange={setSelectedLevel}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="level1">基础词汇练习1</Option>
              <Option value="level2">商务英语入门</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="星级筛选"
              value={selectedStars}
              onChange={setSelectedStars}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value={1}>1星</Option>
              <Option value={2}>2星</Option>
              <Option value={3}>3星</Option>
              <Option value={4}>4星</Option>
              <Option value={5}>5星</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Input
              placeholder="用户ID"
              value={selectedUserId}
              onChange={(e) => setSelectedUserId(e.target.value || undefined)}
              allowClear
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Space>
              <Button type="primary" icon={<FilterOutlined />} onClick={handleFilter}>
                筛选
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 星级记录表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={stars}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
        />
      </Card>

      {/* 删除关卡表现分析 */}
    </div>
  );
}

export default function UserStarsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <UserStarsPageContent />
    </Suspense>
  );
}
