'use client';

import React, { useState, useEffect } from 'react';
import {
  Card, Typography, Row, Col, Button, message
} from 'antd';
import {
  UserOutlined,
  ReadOutlined,
  UnorderedListOutlined,
  DollarOutlined,
  SettingOutlined,
  TagOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Paragraph } = Typography;

export default function DashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  // 简化数据获取
  const fetchStats = async () => {
    setLoading(true);
    try {
      // 简单的加载完成
      message.success('欢迎使用管理后台');
    } catch (error) {
      message.error('加载失败');
      console.error('Error loading dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  // 删除统计计算代码

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>主控面板</Title>
        <Paragraph>
          欢迎使用趣护游戏管理后台！这里展示了系统的核心统计信息和快捷操作入口。
        </Paragraph>
      </div>

      {/* 删除系统警报 */}

      {/* 简化的欢迎信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Title level={3}>欢迎使用管理后台</Title>
              <Paragraph>
                这里是趣护游戏的管理中心，您可以通过下方的快捷操作进行各项管理工作。
              </Paragraph>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快捷操作 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="快捷操作">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button
                  type="primary"
                  icon={<UserOutlined />}
                  block
                  size="large"
                  onClick={() => router.push('/users')}
                >
                  用户管理
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button
                  icon={<ReadOutlined />}
                  block
                  size="large"
                  onClick={() => router.push('/phrases')}
                >
                  词组管理
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button
                  icon={<UnorderedListOutlined />}
                  block
                  size="large"
                  onClick={() => router.push('/levels')}
                >
                  关卡管理
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button
                  icon={<DollarOutlined />}
                  block
                  size="large"
                  onClick={() => router.push('/payment')}
                >
                  支付管理
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button
                  icon={<SettingOutlined />}
                  block
                  size="large"
                  onClick={() => router.push('/settings')}
                >
                  系统设置
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button
                  icon={<TagOutlined />}
                  block
                  size="large"
                  onClick={() => router.push('/level-tags')}
                >
                  标签管理
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 删除最近活动和详细统计部分 */}
    </div>
  );
}