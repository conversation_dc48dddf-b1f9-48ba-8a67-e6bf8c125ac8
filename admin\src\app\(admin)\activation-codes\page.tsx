'use client';

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Form, Input, Card, Tag,
  Select, InputNumber, DatePicker
} from 'antd';
import { Modal, message } from '@/components';
import {
  PlusOutlined, CopyOutlined, ReloadOutlined, AppstoreAddOutlined, DeleteOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { activationCodeService, type ActivationCode as ActivationCodeType, type CreateActivationCodeParams, type BatchGenerateParams } from '@/services/activationCodeService';
import dayjs from 'dayjs';

const { Option } = Select;



export default function ActivationCodesPage() {
  const [codes, setCodes] = useState<ActivationCodeType[]>([]);
  const [packages, setPackages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 手动创建激活码相关状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [createLoading, setCreateLoading] = useState(false);

  // 批量生成激活码相关状态
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [batchForm] = Form.useForm();
  const [batchLoading, setBatchLoading] = useState(false);


  // 获取激活码列表
  const fetchActivationCodes = async () => {
    setLoading(true);
    try {
      const result = await activationCodeService.getAll();
      setCodes(result.codes || []);
    } catch (error) {
      console.error('Error fetching activation codes:', error);
      message.error('获取激活码列表失败');
      // 使用模拟数据
      setCodes([
        {
          code: 'VIP2024001',
          packageId: 'pkg_vip_monthly_30d_a1b2',
          packageName: 'VIP月卡',
          status: 'unused',
          maxRedemptions: 1,
          currentRedemptions: 0,
          expireDate: '2024-12-31T23:59:59Z',
          source: '双十一活动',
          batchId: 'batch_20241101_001',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          code: 'UNLOCK50',
          packageId: 'pkg_unlock_levels_50',
          packageName: '解锁50关卡',
          status: 'used',
          maxRedemptions: 3,
          currentRedemptions: 2,
          redemptionHistory: ['user123', 'user456'],
          usedBy: '12345678',
          usedAt: '2024-06-15T10:30:00Z',
          expireDate: '2024-06-30T23:59:59Z',
          source: '新用户福利',
          batchId: 'batch_20240601_002',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-06-15T10:30:00Z',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };



  // 获取套餐列表
  const fetchPackages = async () => {
    try {
      const result = await activationCodeService.getAllPackages();
      setPackages(result || []);
    } catch (error) {
      console.error('Error fetching packages:', error);
      // 使用模拟数据
      setPackages([
        { id: 'pkg_vip_monthly_30d_a1b2', name: 'VIP月卡', type: 'vip', duration: 30 },
        { id: 'pkg_unlock_levels_50', name: '解锁50关卡', type: 'unlock', duration: 0 },
        { id: 'pkg_bonus_points_1000', name: '1000积分', type: 'points', duration: 0 },
      ]);
    }
  };

  useEffect(() => {
    fetchActivationCodes();
    fetchPackages();
  }, []);



  // 复制激活码
  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code).then(() => {
      message.success('激活码已复制到剪贴板');
    });
  };

  // 禁用激活码
  const handleDisable = async (code: string) => {
    try {
      await activationCodeService.disable(code);
      message.success('激活码已禁用');
      fetchActivationCodes();

    } catch (error) {
      message.error('禁用激活码失败');
    }
  };

  // 启用激活码
  const handleEnable = async (code: string) => {
    try {
      await activationCodeService.enable(code);
      message.success('激活码已启用');
      fetchActivationCodes();

    } catch (error) {
      message.error('启用激活码失败');
    }
  };

  // 删除激活码
  const handleDelete = (code: string) => {
    // 使用兼容的方式调用Modal.confirm
    try {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除激活码 ${code} 吗？此操作不可撤销。`,
        okText: '确认删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          try {
            await activationCodeService.delete(code, {
              reason: '管理员手动删除'
            });
            fetchActivationCodes();
          } catch (error) {
            console.error('删除激活码失败:', error);
            // 错误信息已由request统一处理
          }
        },
      });
    } catch (modalError) {
      // 如果Modal.confirm出现兼容性问题，使用原生confirm作为降级方案
      console.warn('Modal.confirm compatibility issue, using native confirm');
      if (window.confirm(`确定要删除激活码 ${code} 吗？此操作不可撤销。`)) {
        deleteActivationCode(code);
      }
    }
  };

  // 执行删除操作的辅助函数
  const deleteActivationCode = async (code: string) => {
    try {
      await activationCodeService.delete(code, {
        reason: '管理员手动删除'
      });
      fetchActivationCodes();
    } catch (error) {
      console.error('删除激活码失败:', error);
      // 错误信息已由request统一处理
    }
  };

  // 手动创建激活码
  const handleCreateCode = async () => {
    try {
      const values = await createForm.validateFields();
      setCreateLoading(true);

      const params: CreateActivationCodeParams = {
        packageId: values.packageId,
        customCode: values.customCode || undefined,
        expireDate: values.expireDate ? dayjs(values.expireDate).format('YYYY-MM-DD') : undefined,
        source: values.source || 'manual',
        maxRedemptions: values.maxRedemptions || 1
      };

      await activationCodeService.create(params);
      setCreateModalVisible(false);
      createForm.resetFields();
      fetchActivationCodes();
    } catch (error) {
      console.error('创建激活码失败:', error);
      // 错误信息已由request统一处理
    } finally {
      setCreateLoading(false);
    }
  };

  // 批量生成激活码
  const handleBatchGenerate = async () => {
    try {
      const values = await batchForm.validateFields();
      setBatchLoading(true);

      const params: BatchGenerateParams = {
        packageId: values.packageId,
        count: values.count,
        expireDate: values.expireDate ? dayjs(values.expireDate).format('YYYY-MM-DD') : undefined,
        source: values.source || 'batch',
        prefix: values.prefix || undefined,
        maxRedemptions: values.maxRedemptions || 1
      };

      const result = await activationCodeService.batchGenerate(params);
      // 显示详细的成功信息
      message.success(`批量生成成功！成功生成 ${result.successCount} 个激活码${result.failedCount > 0 ? `，失败 ${result.failedCount} 个` : ''}`);
      setBatchModalVisible(false);
      batchForm.resetFields();
      fetchActivationCodes();
    } catch (error) {
      console.error('批量生成激活码失败:', error);
      // 错误信息已由request统一处理
    } finally {
      setBatchLoading(false);
    }
  };

  // 表格列定义
  const columns: ColumnsType<ActivationCodeType> = [
    {
      title: '激活码',
      dataIndex: 'code',
      key: 'code',
      render: (code) => (
        <Space>
          <code style={{ backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px' }}>
            {code}
          </code>
          <Button
            type="link"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopyCode(code)}
          />
        </Space>
      ),
    },
    {
      title: '套餐',
      dataIndex: 'packageName',
      key: 'packageName',
      render: (packageName) => (
        <Tag color="blue">{packageName}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          unused: { text: '未使用', color: 'green' },
          used: { text: '已使用', color: 'blue' },
          expired: { text: '已过期', color: 'red' },
          disabled: { text: '已禁用', color: 'default' },
        };
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '兑换次数',
      key: 'redemptions',
      render: (_, record) => {
        const { maxRedemptions, currentRedemptions } = record;
        const isUnlimited = maxRedemptions === -1;
        return (
          <span>
            {currentRedemptions} / {isUnlimited ? '∞' : maxRedemptions}
            {!isUnlimited && currentRedemptions >= maxRedemptions && (
              <Tag color="red" style={{ marginLeft: 4, fontSize: '12px' }}>已满</Tag>
            )}
          </span>
        );
      },
    },
    {
      title: '使用者',
      dataIndex: 'usedBy',
      key: 'usedBy',
      render: (usedBy) => usedBy || '-',
    },
    {
      title: '过期时间',
      dataIndex: 'expireDate',
      key: 'expireDate',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.status === 'unused' ? (
            <Button
              type="link"
              size="small"
              onClick={() => handleDisable(record.code)}
            >
              禁用
            </Button>
          ) : record.status === 'disabled' ? (
            <Button
              type="link"
              size="small"
              onClick={() => handleEnable(record.code)}
            >
              启用
            </Button>
          ) : null}
          <Button
            type="link"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopyCode(record.code)}
          >
            复制
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.code)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>


      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2>激活码管理</h2>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
              手动创建
            </Button>
            <Button icon={<AppstoreAddOutlined />} onClick={() => setBatchModalVisible(true)}>
              批量生成
            </Button>
            <Button icon={<ReloadOutlined />} onClick={() => {
              fetchActivationCodes();
            }}>
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={codes}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 手动创建激活码模态框 */}
      <Modal
        title="手动创建激活码"
        visible={createModalVisible}
        onOk={handleCreateCode}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        confirmLoading={createLoading}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          initialValues={{
            source: 'manual'
          }}
        >
          <Form.Item
            label="套餐"
            name="packageId"
            rules={[{ required: true, message: '请选择套餐' }]}
          >
            <Select placeholder="选择套餐">
              {packages.map(pkg => (
                <Option key={pkg.id} value={pkg.id}>{pkg.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="自定义激活码"
            name="customCode"
            help="留空则自动生成"
          >
            <Input placeholder="输入自定义激活码（可选）" />
          </Form.Item>

          <Form.Item
            label="过期时间"
            name="expireDate"
            help="留空则使用套餐默认过期时间"
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="选择过期时间（可选）"
              disabledDate={(current) => current && current < dayjs().endOf('day')}
            />
          </Form.Item>

          <Form.Item
            label="兑换次数限制"
            name="maxRedemptions"
            help="设置激活码最大兑换次数，-1表示无限次，默认为1次"
            initialValue={1}
          >
            <InputNumber
              min={-1}
              max={999}
              style={{ width: '100%' }}
              placeholder="输入最大兑换次数"
              formatter={(value) => value === -1 ? '无限次' : `${value}`}
              parser={(value) => {
                if (value === '无限次') return -1 as any;
                const parsed = parseInt(value || '1');
                return (isNaN(parsed) ? 1 : Math.max(-1, Math.min(999, parsed))) as any;
              }}
            />
          </Form.Item>

          <Form.Item
            label="来源标识"
            name="source"
          >
            <Input placeholder="如：手动创建、客服处理等" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量生成激活码模态框 */}
      <Modal
        title="批量生成激活码"
        visible={batchModalVisible}
        onOk={handleBatchGenerate}
        onCancel={() => {
          setBatchModalVisible(false);
          batchForm.resetFields();
        }}
        confirmLoading={batchLoading}
        width={600}
      >
        <Form
          form={batchForm}
          layout="vertical"
          initialValues={{
            count: 10,
            source: 'batch'
          }}
        >
          <Form.Item
            label="套餐"
            name="packageId"
            rules={[{ required: true, message: '请选择套餐' }]}
          >
            <Select placeholder="选择套餐">
              {packages.map(pkg => (
                <Option key={pkg.id} value={pkg.id}>{pkg.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="生成数量"
            name="count"
            rules={[
              { required: true, message: '请输入生成数量' },
              { type: 'number', min: 1, max: 1000, message: '数量必须在1-1000之间' }
            ]}
          >
            <InputNumber
              min={1}
              max={1000}
              style={{ width: '100%' }}
              placeholder="输入生成数量"
            />
          </Form.Item>

          <Form.Item
            label="激活码前缀"
            name="prefix"
            help="可选，为生成的激活码添加统一前缀"
          >
            <Input placeholder="如：VIP2024、SALE等（可选）" />
          </Form.Item>

          <Form.Item
            label="过期时间"
            name="expireDate"
            help="留空则使用套餐默认过期时间"
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="选择过期时间（可选）"
              disabledDate={(current) => current && current < dayjs().endOf('day')}
            />
          </Form.Item>

          <Form.Item
            label="兑换次数限制"
            name="maxRedemptions"
            help="设置激活码最大兑换次数，-1表示无限次，默认为1次"
            initialValue={1}
          >
            <InputNumber
              min={-1}
              max={999}
              style={{ width: '100%' }}
              placeholder="输入最大兑换次数"
              formatter={(value) => value === -1 ? '无限次' : `${value}`}
              parser={(value) => {
                if (value === '无限次') return -1 as any;
                const parsed = parseInt(value || '1');
                return (isNaN(parsed) ? 1 : Math.max(-1, Math.min(999, parsed))) as any;
              }}
            />
          </Form.Item>

          <Form.Item
            label="来源标识"
            name="source"
          >
            <Input placeholder="如：双十一活动、新用户福利等" />
          </Form.Item>
        </Form>
      </Modal>

    </div>
  );
}
